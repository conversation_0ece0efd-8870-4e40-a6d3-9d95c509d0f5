import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { getProjectActivities, formatActivity } from '../../utils/activity-logger';
import LoadingAnimation from '../layout/LoadingAnimation';

/**
 * ProjectActivityFeed Component
 *
 * Displays a feed of project activities
 */
const ProjectActivityFeed = ({ projectId, limit = 10 }) => {
  const { currentUser } = useContext(UserContext);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Fetch activities
  useEffect(() => {
    const fetchActivities = async () => {
      if (!projectId || !currentUser) return;

      try {
        setLoading(true);

        const { data, error } = await getProjectActivities(projectId, limit, page);

        if (error) {
          if (error === 'Table does not exist') {
            // Handle the case where the table doesn't exist yet
            setActivities([]);
            setHasMore(false);
          } else {
            throw error;
          }
        } else {
          // If we got fewer results than the limit, there are no more pages
          if (data.length < limit) {
            setHasMore(false);
          }

          // If this is the first page, replace activities, otherwise append
          if (page === 1) {
            setActivities(data);
          } else {
            setActivities(prev => [...prev, ...data]);
          }
        }
      } catch (err) {
        console.error('Error fetching project activities:', err);
        setError('Failed to load project activities');
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, [projectId, currentUser, limit, page]);

  // Load more activities
  const loadMore = () => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  // Format relative time
  const formatRelativeTime = (timestamp) => {
    const now = new Date();
    const diff = now - new Date(timestamp);

    // Convert to seconds
    const seconds = Math.floor(diff / 1000);

    if (seconds < 60) {
      return 'just now';
    }

    // Convert to minutes
    const minutes = Math.floor(seconds / 60);

    if (minutes < 60) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    }

    // Convert to hours
    const hours = Math.floor(minutes / 60);

    if (hours < 24) {
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    }

    // Convert to days
    const days = Math.floor(hours / 24);

    if (days < 30) {
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }

    // Convert to months
    const months = Math.floor(days / 30);

    if (months < 12) {
      return `${months} month${months !== 1 ? 's' : ''} ago`;
    }

    // Convert to years
    const years = Math.floor(months / 12);

    return `${years} year${years !== 1 ? 's' : ''} ago`;
  };

  // Format date
  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading && activities.length === 0) {
    return <LoadingAnimation />;
  }

  if (error) {
    return (
      <div className="activity-feed-error">
        <i className="bi bi-exclamation-triangle"></i>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="project-activity-feed">
      <h3 className="activity-feed-title">Project Activity</h3>

      {activities.length === 0 ? (
        <div className="no-activities">
          <p>No activities recorded yet</p>
        </div>
      ) : (
        <>
          <div className="activity-list">
            {activities.map((activity) => {
              const formattedActivity = formatActivity(activity);

              return (
                <div className="activity-item" key={activity.id}>
                  <div className="activity-icon">
                    <i className={`bi ${formattedActivity.icon}`} style={{ color: `var(--${formattedActivity.color}-color)` }}></i>
                  </div>
                  <div className="activity-content">
                    <div className="activity-header">
                      <div className="activity-title">{formattedActivity.title}</div>
                      <div className="activity-time" title={formatDate(formattedActivity.timestamp)}>
                        {formatRelativeTime(formattedActivity.timestamp)}
                      </div>
                    </div>
                    <div className="activity-description">{formattedActivity.description}</div>
                    <div className="activity-user">
                      <div className="user-avatar">
                        {formattedActivity.user && formattedActivity.user.avatar_url ? (
                          <img src={formattedActivity.user.avatar_url} alt="User avatar" />
                        ) : (
                          <div className="default-avatar">
                            {((formattedActivity.user && formattedActivity.user.display_name) || 'U')[0].toUpperCase()}
                          </div>
                        )}
                      </div>
                      <div className="user-name">
                        {formattedActivity.user && formattedActivity.user.display_name ?
                          formattedActivity.user.display_name : 'Unknown User'}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {hasMore && (
            <div className="load-more-container">
              <button
                className="load-more-button"
                onClick={loadMore}
                disabled={loading}
              >
                {loading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProjectActivityFeed;
