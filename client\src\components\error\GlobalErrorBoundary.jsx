/**
 * Global Error Boundary Component
 * 
 * Production-ready error boundary providing:
 * - Comprehensive error catching and handling
 * - User-friendly error display
 * - Error reporting and logging
 * - Recovery mechanisms
 * - Development vs production error display
 */

import React from 'react';
import { Card, CardBody, Button } from '@heroui/react';
import { motion } from 'framer-motion';

class GlobalErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({
      error,
      errorInfo
    });

    // Report error to monitoring service
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    const errorReport = {
      id: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      userId: this.props.userId,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      environment: import.meta.env.MODE
    };

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('Global Error Boundary caught an error:', errorReport);
    }

    // In production, send to error monitoring service
    if (import.meta.env.PROD) {
      try {
        // Send to error monitoring service (implement based on your service)
        fetch('/.netlify/functions/error-reporting', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(errorReport)
        }).catch(err => {
          console.error('Failed to report error:', err);
        });
      } catch (reportingError) {
        console.error('Error reporting failed:', reportingError);
      }
    }
  };

  handleRetry = () => {
    // Reset error state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-2xl"
          >
            <Card className="shadow-xl">
              <CardBody className="p-8 text-center">
                {/* Error Icon */}
                <div className="text-6xl mb-6">⚠️</div>
                
                {/* Error Title */}
                <h1 className="text-3xl font-bold text-danger mb-4">
                  Oops! Something went wrong
                </h1>
                
                {/* Error Description */}
                <p className="text-lg text-default-600 mb-6">
                  We encountered an unexpected error. Our team has been notified and is working to fix this issue.
                </p>
                
                {/* Error ID */}
                <div className="bg-default-100 rounded-lg p-4 mb-6">
                  <p className="text-sm text-default-500 mb-1">Error ID</p>
                  <p className="font-mono text-sm text-default-700">
                    {this.state.errorId}
                  </p>
                </div>
                
                {/* Development Error Details */}
                {import.meta.env.DEV && this.state.error && (
                  <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 mb-6 text-left">
                    <h3 className="font-semibold text-red-700 dark:text-red-300 mb-2">
                      Development Error Details:
                    </h3>
                    <div className="text-sm text-red-600 dark:text-red-400 font-mono">
                      <p className="mb-2">
                        <strong>Message:</strong> {this.state.error.message}
                      </p>
                      {this.state.error.stack && (
                        <details className="mb-2">
                          <summary className="cursor-pointer hover:text-red-700 dark:hover:text-red-300">
                            Stack Trace
                          </summary>
                          <pre className="mt-2 text-xs overflow-auto max-h-40 bg-red-100 dark:bg-red-800/30 p-2 rounded">
                            {this.state.error.stack}
                          </pre>
                        </details>
                      )}
                      {this.state.errorInfo?.componentStack && (
                        <details>
                          <summary className="cursor-pointer hover:text-red-700 dark:hover:text-red-300">
                            Component Stack
                          </summary>
                          <pre className="mt-2 text-xs overflow-auto max-h-40 bg-red-100 dark:bg-red-800/30 p-2 rounded">
                            {this.state.errorInfo.componentStack}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                )}
                
                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    color="primary"
                    size="lg"
                    onClick={this.handleRetry}
                    className="min-w-32"
                  >
                    Try Again
                  </Button>
                  
                  <Button
                    color="default"
                    variant="bordered"
                    size="lg"
                    onClick={this.handleReload}
                    className="min-w-32"
                  >
                    Reload Page
                  </Button>
                  
                  <Button
                    color="secondary"
                    variant="light"
                    size="lg"
                    onClick={() => window.location.href = '/'}
                    className="min-w-32"
                  >
                    Go Home
                  </Button>
                </div>
                
                {/* Help Text */}
                <div className="mt-8 pt-6 border-t border-default-200">
                  <p className="text-sm text-default-500">
                    If this problem persists, please{' '}
                    <a 
                      href="/contact" 
                      className="text-primary hover:underline"
                      onClick={(e) => {
                        e.preventDefault();
                        window.location.href = '/contact';
                      }}
                    >
                      contact our support team
                    </a>
                    {' '}with the error ID above.
                  </p>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default GlobalErrorBoundary;
