import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Progress, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Contribution Progress Component
 *
 * Displays visual progress indicators for user contributions,
 * including daily/weekly goals and recent activity feed.
 * Follows the spatial-first design with smooth animations.
 */
const ContributionProgress = ({ projectId = null, className = "", refreshTrigger = 0 }) => {
  const { currentUser } = useContext(UserContext);

  const [stats, setStats] = useState({
    today: { hours: 0, tasks: 0 },
    week: { hours: 0, tasks: 0 },
    month: { hours: 0, tasks: 0 },
    total: { hours: 0, tasks: 0 }
  });

  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);

  // Goals (could be user-configurable)
  const goals = {
    dailyHours: 4,
    weeklyHours: 20,
    dailyTasks: 3,
    weeklyTasks: 15
  };

  // Load contribution stats - triggers on refreshTrigger changes
  useEffect(() => {
    if (currentUser) {
      loadContributionStats();
    }
  }, [currentUser, projectId, refreshTrigger]);

  const loadContributionStats = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);

      // Get date ranges
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      // Build query
      let query = supabase
        .from('contributions')
        .select('*')
        .eq('user_id', currentUser.id)
        .eq('status', 'approved'); // Only count approved contributions

      if (projectId) {
        query = query.eq('project_id', projectId);
      }

      const { data: contributions, error } = await query;

      if (error) throw error;

      // Calculate stats
      const newStats = {
        today: calculatePeriodStats(contributions, today),
        week: calculatePeriodStats(contributions, weekStart),
        month: calculatePeriodStats(contributions, monthStart),
        total: calculatePeriodStats(contributions, new Date(0))
      };

      setStats(newStats);

      // Get recent activity (last 10 contributions)
      const recent = contributions
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 10);

      setRecentActivity(recent);
    } catch (error) {
      console.error('Error loading contribution stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculatePeriodStats = (contributions, startDate) => {
    const filtered = contributions.filter(c => new Date(c.created_at) >= startDate);
    return {
      hours: filtered.reduce((sum, c) => sum + (c.hours_tracked || 0), 0),
      tasks: filtered.length
    };
  };

  // Format time display
  const formatHours = (hours) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    }
    return `${hours.toFixed(1)}h`;
  };

  // Get progress color
  const getProgressColor = (current, goal) => {
    const percentage = (current / goal) * 100;
    if (percentage >= 100) return 'success';
    if (percentage >= 75) return 'warning';
    return 'primary';
  };

  // Get contribution type icon
  const getContributionIcon = (type) => {
    const icons = {
      development: '💻',
      design: '🎨',
      content: '📝',
      testing: '🧪',
      research: '🔬',
      management: '📋',
      marketing: '📢',
      time_tracking: '⏱️',
      other: '🔧'
    };
    return icons[type] || '📝';
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card className="bg-white/5 border border-white/10">
          <CardBody className="p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-6 bg-white/10 rounded w-1/3"></div>
              <div className="space-y-3">
                <div className="h-4 bg-white/10 rounded"></div>
                <div className="h-4 bg-white/10 rounded w-2/3"></div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Progress Overview */}
      <Card className="bg-gradient-to-br from-green-500/10 to-blue-500/10 border border-white/10">
        <CardBody className="p-6">
          <h3 className="text-xl font-semibold text-white mb-6">Your Progress</h3>

          <div className="grid grid-cols-2 gap-6">
            {/* Daily Progress */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-white">Today</h4>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Hours</span>
                    <span className="text-white">{formatHours(stats.today.hours)} / {formatHours(goals.dailyHours)}</span>
                  </div>
                  <Progress
                    value={(stats.today.hours / goals.dailyHours) * 100}
                    color={getProgressColor(stats.today.hours, goals.dailyHours)}
                    className="max-w-full"
                  />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Tasks</span>
                    <span className="text-white">{stats.today.tasks} / {goals.dailyTasks}</span>
                  </div>
                  <Progress
                    value={(stats.today.tasks / goals.dailyTasks) * 100}
                    color={getProgressColor(stats.today.tasks, goals.dailyTasks)}
                    className="max-w-full"
                  />
                </div>
              </div>
            </div>

            {/* Weekly Progress */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-white">This Week</h4>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Hours</span>
                    <span className="text-white">{formatHours(stats.week.hours)} / {formatHours(goals.weeklyHours)}</span>
                  </div>
                  <Progress
                    value={(stats.week.hours / goals.weeklyHours) * 100}
                    color={getProgressColor(stats.week.hours, goals.weeklyHours)}
                    className="max-w-full"
                  />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-white/70">Tasks</span>
                    <span className="text-white">{stats.week.tasks} / {goals.weeklyTasks}</span>
                  </div>
                  <Progress
                    value={(stats.week.tasks / goals.weeklyTasks) * 100}
                    color={getProgressColor(stats.week.tasks, goals.weeklyTasks)}
                    className="max-w-full"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Overall Stats */}
          <div className="mt-6 pt-6 border-t border-white/10">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-400">{formatHours(stats.month.hours)}</div>
                <div className="text-white/70 text-sm">This Month</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-400">{stats.total.tasks}</div>
                <div className="text-white/70 text-sm">Total Tasks</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-400">{formatHours(stats.total.hours)}</div>
                <div className="text-white/70 text-sm">Total Hours</div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Recent Activity */}
      <Card className="bg-white/5 border border-white/10">
        <CardBody className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Recent Activity</h3>

          {recentActivity.length === 0 ? (
            <div className="text-white/50 text-center py-8">
              No contributions yet. Start tracking your work to see activity here!
            </div>
          ) : (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {recentActivity.map((activity, index) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className="text-xl">
                      {getContributionIcon(activity.contribution_type)}
                    </div>
                    <div>
                      <div className="text-white text-sm font-medium">
                        {activity.task_description}
                      </div>
                      <div className="text-white/60 text-xs">
                        {new Date(activity.created_at).toLocaleDateString()} • {formatHours(activity.hours_tracked)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Chip
                      size="sm"
                      color={activity.status === 'approved' ? 'success' : 'warning'}
                      variant="flat"
                    >
                      {activity.status}
                    </Chip>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default ContributionProgress;
