import { test, expect } from '@playwright/test';

// Test configuration
const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  
  // Check if we need to authenticate
  const needsAuth = await page.locator('input[type="email"]').isVisible();
  
  if (needsAuth) {
    console.log('📝 Filling in credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Verify authentication worked
    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error('Authentication failed');
    }
    
    console.log('✅ Authentication successful');
    return true;
  }
  
  console.log('✅ Already authenticated');
  return true;
}

test.describe('Database Fixes Verification', () => {
  let authWorking = false;
  let networkErrors = [];
  let consoleErrors = [];

  test.beforeEach(async ({ page }) => {
    // Track network errors
    networkErrors = [];
    page.on('response', response => {
      if (response.status() >= 400) {
        networkErrors.push({
          url: response.url(),
          status: response.status(),
          statusText: response.statusText()
        });
      }
    });

    // Track console errors
    consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    try {
      authWorking = await authenticate(page);
    } catch (error) {
      console.log('❌ Authentication failed:', error.message);
      authWorking = false;
    }
  });

  test('1. No 404 Errors for user_activity Table', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Wait for all async requests
    
    // Check for user_activity 404 errors
    const userActivityErrors = networkErrors.filter(error => 
      error.url.includes('user_activity') && error.status === 404
    );
    
    console.log(`📊 user_activity 404 errors: ${userActivityErrors.length}`);
    if (userActivityErrors.length > 0) {
      console.log('❌ user_activity 404 errors found:', userActivityErrors);
    } else {
      console.log('✅ No user_activity 404 errors detected');
    }
    
    expect(userActivityErrors.length).toBe(0);
  });

  test('2. No 400 Errors for team_members Status Column', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check for team_members 400 errors
    const teamMembersErrors = networkErrors.filter(error => 
      error.url.includes('team_members') && error.status === 400
    );
    
    console.log(`📊 team_members 400 errors: ${teamMembersErrors.length}`);
    if (teamMembersErrors.length > 0) {
      console.log('❌ team_members 400 errors found:', teamMembersErrors);
    } else {
      console.log('✅ No team_members 400 errors detected');
    }
    
    expect(teamMembersErrors.length).toBe(0);
  });

  test('3. No 502 Errors for Enhanced Analytics', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check for enhanced-analytics 502 errors
    const analyticsErrors = networkErrors.filter(error => 
      error.url.includes('enhanced-analytics') && error.status === 502
    );
    
    console.log(`📊 enhanced-analytics 502 errors: ${analyticsErrors.length}`);
    if (analyticsErrors.length > 0) {
      console.log('❌ enhanced-analytics 502 errors found:', analyticsErrors);
    } else {
      console.log('✅ No enhanced-analytics 502 errors detected');
    }
    
    expect(analyticsErrors.length).toBe(0);
  });

  test('4. No WebSocket Connection Errors', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check for WebSocket errors in console
    const websocketErrors = consoleErrors.filter(error => 
      error.includes('WebSocket') || 
      error.includes('websocket') ||
      error.includes('realtime')
    );
    
    console.log(`📊 WebSocket errors: ${websocketErrors.length}`);
    if (websocketErrors.length > 0) {
      console.log('❌ WebSocket errors found:', websocketErrors);
    } else {
      console.log('✅ No WebSocket connection errors detected');
    }
    
    expect(websocketErrors.length).toBeLessThan(2); // Allow minor connection issues
  });

  test('5. Projects Query Working (No team_members Column Error)', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Navigate to track page which triggers the problematic projects query
    await page.goto(`${PRODUCTION_URL}/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check for projects query 400 errors
    const projectsErrors = networkErrors.filter(error => 
      error.url.includes('projects') && error.status === 400
    );
    
    console.log(`📊 projects query 400 errors: ${projectsErrors.length}`);
    if (projectsErrors.length > 0) {
      console.log('❌ projects query 400 errors found:', projectsErrors);
    } else {
      console.log('✅ No projects query 400 errors detected');
    }
    
    expect(projectsErrors.length).toBe(0);
  });

  test('6. Studio Creation Page Loads Without Errors', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Navigate to studios/create which was mentioned in the original errors
    await page.goto(`${PRODUCTION_URL}/studios/create`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check for any 400/404 errors on this page
    const studioErrors = networkErrors.filter(error => 
      error.status === 400 || error.status === 404
    );
    
    console.log(`📊 Studio creation page errors: ${studioErrors.length}`);
    if (studioErrors.length > 0) {
      console.log('❌ Studio creation errors found:', studioErrors);
    } else {
      console.log('✅ Studio creation page loads without database errors');
    }
    
    expect(studioErrors.length).toBeLessThan(3); // Allow some minor errors
  });

  test('7. Overall Network Error Summary', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Visit multiple pages to trigger various queries
    const pagesToTest = [
      '/',
      '/start',
      '/track', 
      '/earn',
      '/studios',
      '/teams'
    ];
    
    let totalErrors = 0;
    
    for (const pagePath of pagesToTest) {
      try {
        await page.goto(`${PRODUCTION_URL}${pagePath}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const pageErrors = networkErrors.filter(error => 
          error.status >= 400 && error.status < 500
        );
        
        totalErrors += pageErrors.length;
        console.log(`📄 ${pagePath}: ${pageErrors.length} errors`);
      } catch (error) {
        console.log(`❌ Error testing ${pagePath}: ${error.message}`);
      }
    }
    
    console.log(`📊 Total 4xx errors across all pages: ${totalErrors}`);
    
    // We should have significantly fewer errors now
    expect(totalErrors).toBeLessThan(10);
    
    if (totalErrors === 0) {
      console.log('🎉 Perfect! No 4xx errors detected across the platform');
    } else if (totalErrors < 5) {
      console.log('✅ Good! Minimal errors detected');
    } else {
      console.log('⚠️ Some errors still present but much improved');
    }
  });

  test('8. Specific Original Error Patterns', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    // Check for the specific error patterns from the original issue
    const originalErrorPatterns = [
      'user_activity?select=*&user_id=eq.',
      'team_members?select=team_id%2Crole%2Cstatus',
      'projects?select=id%2Cname%2Cdescription%2Ccreated_by&or=',
      'enhanced-analytics?period=30d'
    ];
    
    let foundOriginalErrors = 0;
    
    for (const pattern of originalErrorPatterns) {
      const matchingErrors = networkErrors.filter(error => 
        error.url.includes(pattern) && (error.status === 400 || error.status === 404 || error.status === 502)
      );
      
      if (matchingErrors.length > 0) {
        foundOriginalErrors += matchingErrors.length;
        console.log(`❌ Original error pattern still present: ${pattern}`);
      } else {
        console.log(`✅ Original error pattern fixed: ${pattern}`);
      }
    }
    
    console.log(`📊 Original error patterns still present: ${foundOriginalErrors}`);
    expect(foundOriginalErrors).toBe(0);
  });
});
