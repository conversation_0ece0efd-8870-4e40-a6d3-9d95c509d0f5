import { test, expect } from '@playwright/test';

/**
 * Test Project Selection Functionality in Track Page
 * 
 * This test verifies that users can:
 * 1. See which project they're currently working on
 * 2. Switch between projects when they have multiple projects
 * 3. See project-specific context in UI elements
 */

test.describe('Project Selection in Track Page', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete and dashboard to load
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 10000 });
    
    // Navigate to Track page
    await page.click('text=Track');
    await page.waitForSelector('[data-testid="task-board"]', { timeout: 10000 });
  });

  test('should display current project context in Track page', async ({ page }) => {
    // Wait for the page to fully load
    await page.waitForTimeout(2000);
    
    // Check if project name is displayed in the Task Board header
    const taskBoardHeader = page.locator('[data-testid="task-board"]');
    await expect(taskBoardHeader).toBeVisible();
    
    // Look for project name indicator in the task board
    const projectIndicator = page.locator('.bg-white\\/10.px-3.py-1.rounded-full');
    if (await projectIndicator.count() > 0) {
      await expect(projectIndicator).toBeVisible();
      console.log('✅ Project name indicator found in Task Board');
    }
    
    // Check Quick Actions for project-specific descriptions
    const quickActions = page.locator('[data-testid="quick-actions"]');
    await expect(quickActions).toBeVisible();
    
    // Look for "Create Task" button with project-specific description
    const createTaskButton = page.locator('text=Create Task').first();
    await expect(createTaskButton).toBeVisible();
    
    // Take screenshot for visual verification
    await page.screenshot({ 
      path: 'test-results/project-context-track-page.png',
      fullPage: true 
    });
    
    console.log('✅ Project context is displayed in Track page');
  });

  test('should show project selector when user has multiple projects', async ({ page }) => {
    // Wait for the page to fully load
    await page.waitForTimeout(3000);
    
    // Check if project selector is visible (only shows when user has multiple projects)
    const projectSelector = page.locator('text=Current Project:').locator('..').locator('select, [role="combobox"]');
    
    if (await projectSelector.count() > 0) {
      await expect(projectSelector).toBeVisible();
      console.log('✅ Project selector found - user has multiple projects');
      
      // Take screenshot showing project selector
      await page.screenshot({ 
        path: 'test-results/project-selector-visible.png',
        fullPage: true 
      });
    } else {
      console.log('ℹ️ Project selector not visible - user likely has only one project');
      
      // Take screenshot showing single project state
      await page.screenshot({ 
        path: 'test-results/single-project-state.png',
        fullPage: true 
      });
    }
  });

  test('should show project-aware task creation modal', async ({ page }) => {
    // Wait for the page to fully load
    await page.waitForTimeout(2000);
    
    // Click on Create Task button
    const createTaskButton = page.locator('text=Create Task').first();
    await createTaskButton.click();
    
    // Wait for modal to appear
    await page.waitForSelector('[role="dialog"]', { timeout: 5000 });
    
    // Check if modal shows project-specific context
    const modalHeader = page.locator('[role="dialog"] h2:has-text("Create New Task")');
    await expect(modalHeader).toBeVisible();
    
    // Look for project-specific description in modal
    const modalDescription = page.locator('[role="dialog"] p');
    const descriptionText = await modalDescription.textContent();
    
    if (descriptionText && descriptionText.includes('Add a new task to')) {
      console.log('✅ Task creation modal shows project-specific context:', descriptionText);
    }
    
    // Take screenshot of the modal
    await page.screenshot({ 
      path: 'test-results/task-creation-modal-project-context.png',
      fullPage: true 
    });
    
    // Close modal
    await page.keyboard.press('Escape');
    
    console.log('✅ Task creation modal displays project context');
  });

  test('should maintain project context throughout user flow', async ({ page }) => {
    // Wait for the page to fully load
    await page.waitForTimeout(2000);
    
    // Check header shows project management context
    const pageHeader = page.locator('h1:has-text("Project Management Hub")');
    await expect(pageHeader).toBeVisible();
    
    // Verify Quick Actions show project-specific descriptions
    const quickActionsPanel = page.locator('[data-testid="quick-actions"]');
    await expect(quickActionsPanel).toBeVisible();
    
    // Check if any action descriptions mention specific project names
    const actionDescriptions = await page.locator('[data-testid="quick-actions"] .text-sm').allTextContents();
    const hasProjectSpecificDescriptions = actionDescriptions.some(desc => 
      desc.includes('to ') && !desc.includes('to your project')
    );
    
    if (hasProjectSpecificDescriptions) {
      console.log('✅ Quick Actions show project-specific descriptions');
    } else {
      console.log('ℹ️ Quick Actions show generic descriptions (likely single project)');
    }
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/project-context-complete-flow.png',
      fullPage: true 
    });
    
    console.log('✅ Project context maintained throughout user flow');
  });
});
