-- Check what columns actually exist in your tables
-- This will show us the real structure vs what the app expects

-- Projects table columns
SELECT 'PROJECTS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'projects'
ORDER BY ordinal_position;

-- Project contributors table columns
SELECT 'PROJECT_CONTRIBUTORS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'project_contributors'
ORDER BY ordinal_position;

-- Active timers table columns (newly created)
SELECT 'ACTIVE_TIMERS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'active_timers'
ORDER BY ordinal_position;

-- Tasks table columns (newly created)
SELECT 'TASKS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'tasks'
ORDER BY ordinal_position;
