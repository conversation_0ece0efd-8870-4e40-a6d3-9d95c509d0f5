import React, { useState, useEffect, useContext, useRef, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import SimpleLoading from '../../components/layout/SimpleLoading';
import { debounce } from 'lodash';
import { Button, Card, CardBody, CardHeader, Input, Select, SelectItem, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { Search, Filter, Plus, Briefcase, Calendar, Eye } from 'lucide-react';

const ProjectsList = () => {
  const { currentUser } = useContext(UserContext);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // 'all', 'my', 'public'
  const [searchQuery, setSearchQuery] = useState('');
  const [projectTypes, setProjectTypes] = useState([]);
  const [selectedProjectType, setSelectedProjectType] = useState('all');
  const [sortBy, setSortBy] = useState('newest'); // 'newest', 'oldest', 'name_asc', 'name_desc'
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Refs for debouncing search
  const searchTimeoutRef = useRef(null);

  // Fetch project types for filter
  useEffect(() => {
    const fetchProjectTypes = async () => {
      if (!currentUser) return;

      try {
        const { data, error } = await supabase
          .from('projects')
          .select('project_type')
          .not('project_type', 'is', null);

        if (error) throw error;

        // Extract unique project types
        const types = [...new Set(data.map(p => p.project_type).filter(Boolean))];
        setProjectTypes(types);
      } catch (error) {
        console.error('Error fetching project types:', error);
      }
    };

    fetchProjectTypes();
  }, [currentUser]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((query) => {
      fetchProjects();
    }, 500),
    [filter, selectedProjectType, sortBy]
  );

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  // Fetch projects
  useEffect(() => {
    const fetchProjects = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // First, get the total count for all projects (for pagination later)
        const countQuery = supabase.from('projects').select('id', { count: 'exact' });

        // Build the main query
        let query = supabase.from('projects').select('*');

        // Apply visibility filters
        if (filter === 'my') {
          // Fetch projects where user is a contributor
          const { data: contributorProjects, error: contributorError } = await supabase
            .from('project_contributors')
            .select('project_id')
            .eq('user_id', currentUser.id);

          if (contributorError) throw contributorError;

          if (contributorProjects && contributorProjects.length > 0) {
            const projectIds = contributorProjects.map(p => p.project_id);
            query = query.in('id', projectIds);
            countQuery.in('id', projectIds);
          } else {
            // No projects found
            setProjects([]);
            setTotalCount(0);
            setLoading(false);
            return;
          }
        } else if (filter === 'public') {
          // Fetch only public projects
          query = query.eq('is_public', true);
          countQuery.eq('is_public', true);
        }

        // Apply project type filter
        if (selectedProjectType !== 'all') {
          query = query.eq('project_type', selectedProjectType);
          countQuery.eq('project_type', selectedProjectType);
        }

        // Apply search query if present
        if (searchQuery && searchQuery.trim() !== '') {
          const searchTerm = `%${searchQuery.trim()}%`;
          query = query.or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`);
          countQuery.or(`name.ilike.${searchTerm},description.ilike.${searchTerm}`);
        }

        // Apply sorting
        switch (sortBy) {
          case 'oldest':
            query = query.order('created_at', { ascending: true });
            break;
          case 'name_asc':
            query = query.order('name', { ascending: true });
            break;
          case 'name_desc':
            query = query.order('name', { ascending: false });
            break;
          case 'newest':
          default:
            query = query.order('created_at', { ascending: false });
            break;
        }

        // Get the count first
        const { count, error: countError } = await countQuery;

        if (countError) throw countError;

        setTotalCount(count || 0);

        // Execute the main query
        const { data, error } = await query;

        if (error) throw error;

        setProjects(data || []);
      } catch (error) {
        console.error('Error fetching projects:', error);
        toast.error('Failed to load projects');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [currentUser, filter, searchQuery, selectedProjectType, sortBy]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return <SimpleLoading text="Loading projects..." fullPage={true} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              Projects
            </h1>
            <p className="text-lg text-default-600">
              Discover and manage your creative projects
            </p>
          </div>
          <Button
            as={Link}
            to="/project/wizard"
            color="primary"
            size="lg"
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold mt-4 sm:mt-0"
            startContent={<Plus size={20} />}
          >
            New Project
          </Button>
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardBody className="p-6">
              <div className="flex flex-col lg:flex-row gap-4 items-center">
                <div className="flex-1 w-full">
                  <Input
                    placeholder="Search projects..."
                    value={searchQuery}
                    onChange={(e) => handleSearchChange(e)}
                    startContent={<Search size={20} className="text-default-400" />}
                    classNames={{
                      input: "text-sm",
                      inputWrapper: "bg-default-100 border-0"
                    }}
                    size="lg"
                  />
                </div>
                <Button
                  variant="flat"
                  color="default"
                  onPress={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  startContent={<Filter size={20} />}
                  className="w-full lg:w-auto"
                >
                  {showAdvancedFilters ? 'Hide Filters' : 'Show Filters'}
                </Button>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex flex-wrap gap-3 mb-4">
            <Button
              variant={filter === 'all' ? 'solid' : 'flat'}
              color={filter === 'all' ? 'primary' : 'default'}
              onPress={() => setFilter('all')}
              className="flex-1 sm:flex-none"
            >
              All Projects
            </Button>
            <Button
              variant={filter === 'my' ? 'solid' : 'flat'}
              color={filter === 'my' ? 'primary' : 'default'}
              onPress={() => setFilter('my')}
              className="flex-1 sm:flex-none"
            >
              My Projects
            </Button>
            <Button
              variant={filter === 'public' ? 'solid' : 'flat'}
              color={filter === 'public' ? 'primary' : 'default'}
              onPress={() => setFilter('public')}
              className="flex-1 sm:flex-none"
              startContent={<Eye size={16} />}
            >
              Public Projects
            </Button>
          </div>

          {showAdvancedFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border-0">
                <CardBody className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-default-700 mb-2">
                        Project Type:
                      </label>
                      <Select
                        selectedKeys={[selectedProjectType]}
                        onSelectionChange={(keys) => setSelectedProjectType(Array.from(keys)[0])}
                        className="w-full"
                        classNames={{
                          trigger: "bg-default-100 border-0"
                        }}
                      >
                        <SelectItem key="all">All Types</SelectItem>
                        {projectTypes.map(type => (
                          <SelectItem key={type}>{type}</SelectItem>
                        ))}
                      </Select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-default-700 mb-2">
                        Sort By:
                      </label>
                      <Select
                        selectedKeys={[sortBy]}
                        onSelectionChange={(keys) => setSortBy(Array.from(keys)[0])}
                        className="w-full"
                        classNames={{
                          trigger: "bg-default-100 border-0"
                        }}
                      >
                        <SelectItem key="newest">Newest First</SelectItem>
                        <SelectItem key="oldest">Oldest First</SelectItem>
                        <SelectItem key="name_asc">Name (A-Z)</SelectItem>
                        <SelectItem key="name_desc">Name (Z-A)</SelectItem>
                      </Select>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          )}
        </motion.div>

        {/* Search Results Info */}
        {searchQuery && (
          <motion.div
            className="mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              <CardBody className="p-4">
                <div className="flex items-center justify-between">
                  <p className="text-blue-800 dark:text-blue-200">
                    Found {totalCount} {totalCount === 1 ? 'project' : 'projects'} matching "{searchQuery}"
                  </p>
                  <Button
                    size="sm"
                    variant="flat"
                    color="primary"
                    onPress={() => {
                      setSearchQuery('');
                      debouncedSearch('');
                    }}
                  >
                    Clear Search
                  </Button>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}

        {/* Projects Grid or Empty State */}
        {projects.length === 0 ? (
          <motion.div
            className="text-center py-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-lg max-w-md mx-auto">
              <CardBody className="p-8">
                <div className="text-6xl mb-4">📁</div>
                <h3 className="text-2xl font-bold text-default-800 mb-4">No projects found</h3>
                <p className="text-default-600 mb-6">
                  {filter === 'my'
                    ? "You haven't created or joined any projects yet."
                    : "No projects match your current filters."
                  }
                </p>
                {filter === 'my' && (
                  <Button
                    as={Link}
                    to="/project/wizard"
                    color="primary"
                    size="lg"
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold"
                    startContent={<Plus size={20} />}
                  >
                    Create your first project
                  </Button>
                )}
              </CardBody>
            </Card>
          </motion.div>
        ) : (
          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {projects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ y: -4 }}
              >
                <Card
                  as={Link}
                  to={`/project/${project.id}`}
                  className="h-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer"
                  isPressable
                >
                  <CardHeader className="p-0">
                    <div className="w-full h-48 bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/20 dark:to-blue-900/20 flex items-center justify-center overflow-hidden">
                      {project.thumbnail_url ? (
                        <img
                          src={project.thumbnail_url}
                          alt={project.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Briefcase size={48} className="text-default-400" />
                      )}
                    </div>
                  </CardHeader>
                  <CardBody className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="text-xl font-bold text-default-800 line-clamp-2">
                        {project.name}
                      </h3>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-4">
                      <Chip size="sm" variant="flat" color="primary">
                        {project.project_type}
                      </Chip>
                      {project.is_public && (
                        <Chip size="sm" variant="flat" color="success" startContent={<Eye size={12} />}>
                          Public
                        </Chip>
                      )}
                    </div>

                    <p className="text-default-600 text-sm mb-4 line-clamp-3">
                      {project.description || 'No description provided.'}
                    </p>

                    <div className="flex items-center text-xs text-default-500">
                      <Calendar size={12} className="mr-1" />
                      Created: {formatDate(project.created_at)}
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ProjectsList;
