import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../lib/utils';

/**
 * Breadcrumbs component for navigation
 * Automatically generates breadcrumbs based on the current URL path
 */
const Breadcrumbs = ({ customPaths }) => {
  const location = useLocation();

  // Skip rendering breadcrumbs on the home page
  if (location.pathname === '/') {
    return null;
  }

  // Get path segments
  const pathSegments = location.pathname.split('/').filter(segment => segment);

  // Define path mappings for better display names
  const pathMappings = {
    'profile': 'Profile',
    'project': 'Projects',
    'wizard': 'Create Project',
    'admin': 'Admin',
    'roadmap': 'Roadmap',
    'roadmap-manager': 'Roadmap Manager',
    'password-reset': 'Reset Password',
    'reset-password': 'Update Password',
    'settings': 'Settings',
    'start': 'Start',
    'track': 'Track',
    'earn': 'Earn',
    'learn': 'Learn',
    'analytics': 'Analytics',
    'contributions': 'Contributions',
    'validation': 'Validation',
    'metrics': 'Metrics',
    'revenue': 'Revenue',
    'royalty-calculator': 'Royalty Calculator',
    'tasks': 'Tasks',
    'agreements': 'Agreements',
    ...customPaths
  };

  // Define custom paths for specific segments
  const customPathOverrides = {
    'project': '/projects',
    'projects': '/track',
    'analytics': '/track',
    'validation': '/track'
  };

  // Build breadcrumb items
  const breadcrumbItems = [];

  // Always add Home
  breadcrumbItems.push({
    name: 'Home',
    path: '/',
    isLast: pathSegments.length === 0
  });

  // Add path segments
  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;

    // Special case for dynamic segments (like IDs)
    let name = pathMappings[segment] || segment;
    if (segment.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      name = 'Details';
    }

    // Use custom path override if available
    const customPath = customPathOverrides[segment];

    breadcrumbItems.push({
      name,
      path: customPath || currentPath,
      isLast: index === pathSegments.length - 1
    });
  });

  return (
    <nav aria-label="breadcrumb" className="py-2 px-4 bg-gray-100 dark:bg-gray-800 rounded-md mb-4 shadow-sm">
      <ol className="flex flex-wrap list-none p-0 m-0">
        {breadcrumbItems.map((item, index) => (
          <li
            key={index}
            className={cn(
              "flex items-center",
              index > 0 && "pl-2 before:content-['/'] before:pr-2 before:text-muted-foreground"
            )}
            aria-current={item.isLast ? 'page' : undefined}
          >
            {item.isLast ? (
              <span className="text-muted-foreground font-medium">{item.name}</span>
            ) : (
              <Link
                to={item.path}
                className="text-primary hover:underline"
              >
                {item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
