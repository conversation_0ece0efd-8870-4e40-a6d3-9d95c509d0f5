# Database Management Scripts

This directory contains all database-related scripts for the Royaltea platform.

## 🗄️ Database Information
- **Provider**: Supabase (PostgreSQL)
- **URL**: https://hqqlrrqvjcetoxbdjgzx.supabase.co
- **Dashboard**: https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx
- **Environment**: Production

## 📋 Script Categories

### **Migration Scripts**
- `apply-migration-direct.js` - Apply migrations directly to database
- `apply-migration-sql.js` - Apply SQL migration files
- `apply-migration-via-api.js` - Apply migrations via Supabase API
- `check-migration.js` - Check migration status

### **Validation Scripts**
- `check-supabase-tables.js` - Validate all table structures
- `check-agreements-status.js` - Check agreement data integrity
- `check-projects.js` - Validate project data
- `check-contributions-schema.js` - Check contribution tracking
- `check-database.js` - General database health check
- `check-production.js` - Production environment validation

### **Data Management Scripts**
- `create-fresh-test-users.js` - Create test user data
- `create-final-test.js` - Create comprehensive test data
- `create-test-invitation.js` - Create test invitations
- `cleanup-all-test-data.js` - Remove all test data

### **Maintenance Scripts**
- `fix-database-issues.js` - Fix common database issues
- `update-roadmap-database.js` - Update roadmap data
- `generate-roadmap-update-sql.js` - Generate roadmap SQL

## 🚀 Common Usage

### Apply Migrations
```bash
# Using Supabase CLI (recommended)
supabase db push

# Using custom scripts
node scripts/database/apply-migration-direct.js [migration-file]
```

### Check Database Health
```bash
# Check all tables
node scripts/database/check-supabase-tables.js

# Check specific features
node scripts/database/check-agreements-status.js
node scripts/database/check-projects.js
```

### Manage Test Data
```bash
# Create test data
node scripts/database/create-fresh-test-users.js

# Clean test data
node scripts/database/cleanup-all-test-data.js
```

## ⚠️ Important Notes

- Always backup before running migration scripts
- Test scripts on development environment first
- Use Supabase CLI for official migrations when possible
- Check database status after running any scripts

## 📚 Related Documentation

- [DEPLOYMENT_DATABASE_GUIDE.md](../../DEPLOYMENT_DATABASE_GUIDE.md) - Complete database procedures
- [Supabase Documentation](https://supabase.com/docs)
- [Database Architecture](../../docs/database-architecture-specification.md)
