import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import { useVettingSystem } from '../../hooks/useVettingSystem';
import { getAllSkills, getUserSkills, calculateSkillScore } from '../../utils/skills/skills.utils';
import LearningHub from './LearningHub';
import PeerReviewSystem from './PeerReviewSystem';
import ExpertPanel from './ExpertPanel';
import AssessmentInterface from './AssessmentInterface';

/**
 * Skill Verification Dashboard Component - 6-Level Skill Verification System
 * 
 * Features:
 * - Bento grid layout following exact wireframe specifications
 * - 6-level skill verification system (Levels 0-5)
 * - Real-time skill data with automatic updates
 * - Integration with vetting system APIs and LinkedIn Learning
 * - Progressive skill advancement with peer and expert validation
 * - Technology-specific learning paths and assessments
 */
const SkillVerificationDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const vettingSystem = useVettingSystem();
  
  // State management
  const [skillData, setSkillData] = useState({
    userSkills: [],
    availableSkills: [],
    verificationLevel: 0,
    overallProgress: 0,
    recentAchievements: [],
    nextMilestones: []
  });
  
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showLearningHub, setShowLearningHub] = useState(false);
  const [showPeerReview, setShowPeerReview] = useState(false);
  const [showExpertPanel, setShowExpertPanel] = useState(false);
  const [showAssessment, setShowAssessment] = useState(false);

  // Verification level definitions
  const verificationLevels = [
    {
      level: 0,
      name: 'Unverified',
      description: 'New to the platform',
      color: 'default',
      icon: '👤',
      requirements: 'Complete profile setup'
    },
    {
      level: 1,
      name: 'Learning',
      description: 'Completed learning path',
      color: 'primary',
      icon: '📚',
      requirements: 'Complete technology learning path'
    },
    {
      level: 2,
      name: 'Peer Verified',
      description: 'Validated by community',
      color: 'success',
      icon: '🤝',
      requirements: 'Pass peer review process'
    },
    {
      level: 3,
      name: 'Project Verified',
      description: 'Proven through client work',
      color: 'warning',
      icon: '🏗️',
      requirements: 'Complete successful client projects'
    },
    {
      level: 4,
      name: 'Expert Verified',
      description: 'Validated by industry experts',
      color: 'secondary',
      icon: '⭐',
      requirements: 'Pass expert assessment panel'
    },
    {
      level: 5,
      name: 'Industry Certified',
      description: 'Industry-recognized expertise',
      color: 'danger',
      icon: '👑',
      requirements: 'Industry certifications and recognition'
    }
  ];

  // Load skill verification data
  const loadSkillData = async () => {
    try {
      setLoading(true);
      
      // Fetch user skills and available skills
      const [userSkills, availableSkills] = await Promise.all([
        getUserSkills(currentUser?.id),
        getAllSkills()
      ]);

      // Calculate overall verification level and progress
      const skillScores = userSkills.map(skill => calculateSkillScore(skill));
      const averageScore = skillScores.length > 0 
        ? skillScores.reduce((sum, score) => sum + score, 0) / skillScores.length 
        : 0;
      
      // Determine verification level based on highest verified skill
      const highestVerificationLevel = Math.max(
        ...userSkills.map(skill => skill.verification_level || 0),
        0
      );

      // Mock recent achievements and next milestones
      const recentAchievements = [
        {
          id: 1,
          title: 'React Fundamentals',
          type: 'learning_completion',
          date: new Date('2025-01-15'),
          level: 1
        },
        {
          id: 2,
          title: 'Peer Review Passed',
          type: 'peer_validation',
          date: new Date('2025-01-14'),
          level: 2
        }
      ];

      const nextMilestones = [
        {
          id: 1,
          title: 'Complete TypeScript Assessment',
          type: 'assessment',
          progress: 60,
          estimatedCompletion: '2 days'
        },
        {
          id: 2,
          title: 'Expert Panel Review',
          type: 'expert_review',
          progress: 0,
          estimatedCompletion: '1 week'
        }
      ];

      setSkillData({
        userSkills,
        availableSkills,
        verificationLevel: highestVerificationLevel,
        overallProgress: Math.round(averageScore),
        recentAchievements,
        nextMilestones
      });
      
    } catch (error) {
      console.error('Error loading skill data:', error);
      toast.error('Failed to load skill verification data');
    } finally {
      setLoading(false);
    }
  };

  // Get verification level info
  const getCurrentLevelInfo = () => {
    return verificationLevels.find(level => level.level === skillData.verificationLevel) || verificationLevels[0];
  };

  // Get next level info
  const getNextLevelInfo = () => {
    const nextLevel = skillData.verificationLevel + 1;
    return verificationLevels.find(level => level.level === nextLevel);
  };

  // Handle skill verification start
  const handleStartVerification = async (skillId) => {
    try {
      // Start verification process for specific skill
      toast.info('Starting skill verification process...');
      setShowAssessment(true);
    } catch (error) {
      console.error('Error starting verification:', error);
      toast.error('Failed to start verification process');
    }
  };

  // Initialize component
  useEffect(() => {
    if (currentUser) {
      loadSkillData();
    }
  }, [currentUser]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading skill verification dashboard...</p>
        </div>
      </div>
    );
  }

  const currentLevel = getCurrentLevelInfo();
  const nextLevel = getNextLevelInfo();

  return (
    <div className={`skill-verification-dashboard ${className}`}>
      {/* Dashboard Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
              🎓 Skill Verification
            </h1>
            <p className="text-default-600">
              Progress through our 6-level verification system
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              color="primary"
              variant="flat"
              onClick={() => setShowLearningHub(true)}
              startContent={<span>📚</span>}
            >
              Learning Hub
            </Button>
            <Button
              color="secondary"
              variant="flat"
              onClick={() => setShowAssessment(true)}
              startContent={<span>📝</span>}
            >
              Take Assessment
            </Button>
          </div>
        </div>
      </div>

      {/* Bento Grid Layout - Following Wireframe Specifications */}
      <div className="grid grid-cols-12 gap-6">
        {/* Top Row - Verification Level Overview */}
        <div className="col-span-12 lg:col-span-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Current Level */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-800/20 hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-2xl">{currentLevel.icon}</span>
                    <Chip color={currentLevel.color} variant="flat" size="sm">
                      Level {currentLevel.level}
                    </Chip>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-default-600">Current Level</div>
                      <div className="text-lg font-bold text-blue-600">
                        {currentLevel.name}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">
                        {currentLevel.description}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Overall Progress */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20 hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-2xl">📊</span>
                    <Chip color="success" variant="flat" size="sm">Progress</Chip>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-default-600">Overall Score</div>
                      <div className="text-2xl font-bold text-green-600">
                        {skillData.overallProgress}%
                      </div>
                    </div>
                    <Progress 
                      value={skillData.overallProgress} 
                      color="success" 
                      size="sm"
                      className="mt-2"
                    />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            {/* Next Level */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20 hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-2xl">{nextLevel?.icon || '🎯'}</span>
                    <Chip color="warning" variant="flat" size="sm">Next Level</Chip>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <div className="text-sm text-default-600">Target</div>
                      <div className="text-lg font-bold text-orange-600">
                        {nextLevel?.name || 'Max Level Reached'}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">
                        {nextLevel?.requirements || 'Congratulations!'}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>

          {/* Verification Level Progression */}
          <Card className="mb-6">
            <CardHeader className="pb-3">
              <h3 className="text-lg font-semibold">Verification Level Progression</h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {verificationLevels.map((level) => (
                  <motion.div
                    key={level.level}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: level.level * 0.1 }}
                    className={`text-center p-4 rounded-lg border-2 transition-all ${
                      level.level <= skillData.verificationLevel
                        ? 'border-primary bg-primary-50 dark:bg-primary-900/20'
                        : level.level === skillData.verificationLevel + 1
                        ? 'border-warning bg-warning-50 dark:bg-warning-900/20'
                        : 'border-default-200 bg-default-50 dark:bg-default-900/20'
                    }`}
                  >
                    <div className="text-2xl mb-2">{level.icon}</div>
                    <div className="font-semibold text-sm">{level.name}</div>
                    <div className="text-xs text-default-600 mt-1">
                      Level {level.level}
                    </div>
                    {level.level <= skillData.verificationLevel && (
                      <Chip color="success" size="sm" variant="flat" className="mt-2">
                        ✓ Achieved
                      </Chip>
                    )}
                    {level.level === skillData.verificationLevel + 1 && (
                      <Chip color="warning" size="sm" variant="flat" className="mt-2">
                        In Progress
                      </Chip>
                    )}
                  </motion.div>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Right Sidebar - Next Steps & Quick Actions */}
        <div className="col-span-12 lg:col-span-4">
          <Card className="h-full">
            <CardHeader className="pb-3">
              <h3 className="text-lg font-semibold">Next Steps</h3>
            </CardHeader>
            <CardBody className="space-y-4">
              {/* Next Milestones */}
              <div className="space-y-3">
                {skillData.nextMilestones.map((milestone) => (
                  <div key={milestone.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{milestone.title}</h4>
                      <Chip color="primary" size="sm" variant="flat">
                        {milestone.progress}%
                      </Chip>
                    </div>
                    <Progress
                      value={milestone.progress}
                      color="primary"
                      size="sm"
                      className="mb-2"
                    />
                    <div className="text-xs text-default-500">
                      ETA: {milestone.estimatedCompletion}
                    </div>
                  </div>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="border-t pt-4 space-y-3">
                <h4 className="font-semibold text-sm">Quick Actions</h4>

                <Button
                  color="primary"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>📚</span>}
                  onClick={() => setShowLearningHub(true)}
                >
                  Learning Hub
                </Button>

                <Button
                  color="success"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>🤝</span>}
                  onClick={() => setShowPeerReview(true)}
                >
                  Peer Review
                </Button>

                <Button
                  color="secondary"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>⭐</span>}
                  onClick={() => setShowExpertPanel(true)}
                >
                  Expert Panel
                </Button>

                <Button
                  color="warning"
                  variant="flat"
                  className="w-full justify-start"
                  startContent={<span>📝</span>}
                  onClick={() => setShowAssessment(true)}
                >
                  Take Assessment
                </Button>
              </div>

              {/* Verification Tips */}
              <div className="border-t pt-4">
                <h4 className="font-semibold text-sm mb-3">Verification Tips</h4>
                <div className="space-y-2">
                  <div className="text-xs text-default-600 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                    💡 Complete learning paths to advance to Level 1
                  </div>
                  <div className="text-xs text-default-600 p-2 bg-green-50 dark:bg-green-900/20 rounded">
                    🤝 Get peer reviews to reach Level 2
                  </div>
                  <div className="text-xs text-default-600 p-2 bg-purple-50 dark:bg-purple-900/20 rounded">
                    ⭐ Expert validation unlocks Level 4
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Modals */}
      {showLearningHub && (
        <LearningHub
          isOpen={showLearningHub}
          onClose={() => setShowLearningHub(false)}
          currentUser={currentUser}
          userSkills={skillData.userSkills}
          onUpdate={() => loadSkillData()}
        />
      )}

      {showPeerReview && (
        <PeerReviewSystem
          isOpen={showPeerReview}
          onClose={() => setShowPeerReview(false)}
          currentUser={currentUser}
          userSkills={skillData.userSkills}
          onUpdate={() => loadSkillData()}
        />
      )}

      {showExpertPanel && (
        <ExpertPanel
          isOpen={showExpertPanel}
          onClose={() => setShowExpertPanel(false)}
          currentUser={currentUser}
          userSkills={skillData.userSkills}
          onUpdate={() => loadSkillData()}
        />
      )}

      {showAssessment && (
        <AssessmentInterface
          isOpen={showAssessment}
          onClose={() => setShowAssessment(false)}
          currentUser={currentUser}
          availableSkills={skillData.availableSkills}
          onUpdate={() => loadSkillData()}
        />
      )}
    </div>
  );
};

export default SkillVerificationDashboard;
