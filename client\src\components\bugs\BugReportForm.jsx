import React, { useState, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Button, Input, Textarea } from '../ui/heroui';
import { Upload, X, Copy, Download, Image } from 'lucide-react';

const BugReportForm = ({ onSuccess, onCancel }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    solution: '',
  });
  const [screenshots, setScreenshots] = useState([]);
  const [uploadingScreenshot, setUploadingScreenshot] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle file upload
  const handleFileUpload = async (file) => {
    if (!file || !file.type.startsWith('image/')) {
      toast.error('Please select a valid image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB');
      return;
    }

    setUploadingScreenshot(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `bug-screenshots/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('bug-attachments')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('bug-attachments')
        .getPublicUrl(filePath);

      const newScreenshot = {
        id: Date.now(),
        url: publicUrl,
        name: file.name,
        path: filePath
      };

      setScreenshots(prev => [...prev, newScreenshot]);
      toast.success('Screenshot uploaded successfully');
    } catch (error) {
      console.error('Error uploading screenshot:', error);
      toast.error('Failed to upload screenshot');
    } finally {
      setUploadingScreenshot(false);
    }
  };

  // Handle paste from clipboard
  const handlePaste = async (e) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        e.preventDefault();
        const file = item.getAsFile();
        if (file) {
          await handleFileUpload(file);
        }
        break;
      }
    }
  };

  // Remove screenshot
  const removeScreenshot = async (screenshot) => {
    try {
      // Delete from storage
      const { error } = await supabase.storage
        .from('bug-attachments')
        .remove([screenshot.path]);

      if (error) throw error;

      setScreenshots(prev => prev.filter(s => s.id !== screenshot.id));
      toast.success('Screenshot removed');
    } catch (error) {
      console.error('Error removing screenshot:', error);
      toast.error('Failed to remove screenshot');
    }
  };

  // Copy screenshot to clipboard
  const copyScreenshot = async (screenshotUrl) => {
    try {
      const response = await fetch(screenshotUrl);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);
      toast.success('Screenshot copied to clipboard');
    } catch (error) {
      console.error('Error copying screenshot:', error);
      toast.error('Failed to copy screenshot');
    }
  };

  // Download screenshot
  const downloadScreenshot = (screenshotUrl, fileName) => {
    const link = document.createElement('a');
    link.href = screenshotUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      toast.error('You must be logged in to submit a bug report');
      return;
    }

    if (!formData.title.trim() || !formData.description.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setLoading(true);

    try {
      const bugReportData = {
        ...formData,
        reported_by: currentUser.id,
        status: 'open',
        screenshots: screenshots.map(s => ({ url: s.url, name: s.name, path: s.path }))
      };

      const { data, error } = await supabase
        .from('bug_reports')
        .insert([bugReportData])
        .select()
        .single();

      if (error) throw error;

      toast.success('Bug report submitted successfully');

      // Reset form
      setFormData({
        title: '',
        description: '',
        solution: ''
      });
      setScreenshots([]);

      // Call success callback
      if (onSuccess) {
        onSuccess(data);
      }

    } catch (error) {
      console.error('Error submitting bug report:', error);
      toast.error(error.message || 'Failed to submit bug report');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4" onPaste={handlePaste}>
        <div className="space-y-2">
          <Input
            type="text"
            label="Bug Title"
            name="title"
            value={formData.title}
            onValueChange={(value) => setFormData(prev => ({ ...prev, title: value }))}
            placeholder="Brief description of the issue"
            isRequired
          />
        </div>

        <div className="space-y-2">
          <Textarea
            label="Description"
            name="description"
            value={formData.description}
            onValueChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
            minRows={5}
            placeholder="Please describe what happened, what you expected to happen, and steps to reproduce the issue"
            isRequired
          />
        </div>

        <div className="space-y-2">
          <Textarea
            label="Solution (if known)"
            name="solution"
            value={formData.solution}
            onValueChange={(value) => setFormData(prev => ({ ...prev, solution: value }))}
            minRows={3}
            placeholder="If you know a workaround or solution, please describe it here"
          />
        </div>

        {/* Screenshots Section */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Screenshots</label>
          <div className="space-y-3">
            {/* Upload Area */}
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
              <div className="space-y-2">
                <Image className="h-8 w-8 mx-auto text-muted-foreground" />
                <div className="text-sm text-muted-foreground">
                  <p><strong>Paste screenshots</strong> directly with Ctrl+V</p>
                  <p>or</p>
                </div>
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={(e) => {
                      Array.from(e.target.files).forEach(handleFileUpload);
                      e.target.value = '';
                    }}
                    className="hidden"
                    id="screenshot-upload"
                  />
                  <Button
                    type="button"
                    variant="bordered"
                    onPress={() => document.getElementById('screenshot-upload').click()}
                    isDisabled={uploadingScreenshot}
                    className="flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    {uploadingScreenshot ? 'Uploading...' : 'Choose Files'}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  PNG, JPG, GIF up to 5MB each
                </p>
              </div>
            </div>

            {/* Screenshot Preview */}
            {screenshots.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Uploaded Screenshots:</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {screenshots.map((screenshot) => (
                    <div key={screenshot.id} className="relative border rounded-lg p-3 space-y-2">
                      <img
                        src={screenshot.url}
                        alt={screenshot.name}
                        className="w-full h-32 object-cover rounded"
                      />
                      <div className="flex items-center justify-between">
                        <p className="text-xs text-muted-foreground truncate flex-1">
                          {screenshot.name}
                        </p>
                        <div className="flex gap-1">
                          <Button
                            type="button"
                            variant="light"
                            size="sm"
                            onPress={() => copyScreenshot(screenshot.url)}
                            title="Copy to clipboard"
                            isIconOnly
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            type="button"
                            variant="light"
                            size="sm"
                            onPress={() => downloadScreenshot(screenshot.url, screenshot.name)}
                            title="Download"
                            isIconOnly
                          >
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button
                            type="button"
                            variant="light"
                            size="sm"
                            onPress={() => removeScreenshot(screenshot)}
                            title="Remove"
                            isIconOnly
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="bordered"
            onPress={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isDisabled={loading}
          >
            {loading ? 'Submitting...' : 'Submit Bug Report'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default BugReportForm;
