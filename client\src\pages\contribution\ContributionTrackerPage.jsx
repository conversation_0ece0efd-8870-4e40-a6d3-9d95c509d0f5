import React, { useState, useEffect, useContext } from 'react';
import { useParams, Navigate, Link } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import ContributionTracker from '../../components/contribution/ContributionTracker';
import LoadingAnimation from '../../components/layout/LoadingAnimation';

const ContributionTrackerPage = () => {
  const { projectId } = useParams();
  const { currentUser } = useContext(UserContext);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  // Fetch project and check access
  useEffect(() => {
    const fetchProjectAndCheckAccess = async () => {
      if (!projectId || !currentUser) return;

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;

        setProject(projectData);

        // Check if user has access to this project
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .eq('status', 'active')
          .single();

        if (contributorError && contributorError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which is expected if user is not a contributor
          throw contributorError;
        }

        // User has access if they are the project creator or a contributor
        const isCreator = projectData.created_by === currentUser.id;
        const isContributor = !!contributorData;

        setHasAccess(isCreator || isContributor);
      } catch (error) {
        console.error('Error fetching project:', error);
        toast.error('Failed to load project data');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectAndCheckAccess();
  }, [projectId, currentUser]);

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (loading) {
    return <LoadingAnimation />;
  }

  if (!project) {
    return (
      <div className="error-container">
        <h2>Project Not Found</h2>
        <p>The project you're looking for doesn't exist or has been deleted.</p>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <div className="error-container">
        <h2>Access Denied</h2>
        <p>You don't have permission to access this project's contributions.</p>
      </div>
    );
  }

  return (
    <div className="contribution-tracker-page">
      <div className="page-header">
        <div className="project-info">
          <h1 className="project-title">{project.name}</h1>
          <p className="project-description">{project.description}</p>

          <div className="project-actions">
            <Link to={`/project/${projectId}`} className="btn btn-outline-secondary">
              <i className="bi bi-arrow-left"></i> Back to Project
            </Link>
            <Link to={`/validation/metrics/${projectId}`} className="btn btn-outline-primary">
              <i className="bi bi-graph-up"></i> Validation Metrics
            </Link>
            <Link to={`/analytics/contributions/${projectId}`} className="btn btn-outline-info">
              <i className="bi bi-bar-chart-line"></i> Analytics
            </Link>
          </div>
        </div>

        <div className="project-thumbnail">
          {project.thumbnail_url ? (
            <img
              src={project.thumbnail_url}
              alt={project.name}
              className="thumbnail-image"
            />
          ) : (
            <div className="thumbnail-placeholder">
              <i className="bi bi-image"></i>
            </div>
          )}
        </div>
      </div>

      <div className="page-content">
        <ContributionTracker projectId={projectId} />
      </div>
    </div>
  );
};

export default ContributionTrackerPage;
