/**
 * Database Integration Manager
 * 
 * Comprehensive database integration utility that combines:
 * - Database health monitoring from DatabaseHealthMonitor
 * - Query optimization from DatabaseOptimizer
 * - Performance monitoring from QueryOptimizer
 * - Real-time monitoring and alerting
 * - Automated optimization recommendations
 * - Integration with performance monitoring dashboard
 */

import { 
  startDatabaseMonitoring, 
  getCurrentHealth, 
  getHealthTrends, 
  exportHealthData,
  addHealthCheck,
  removeHealthCheck
} from './DatabaseHealthMonitor';

import { 
  getOptimizationRecommendations, 
  trackQueryPerformance,
  analyzeQueryPatterns,
  generateOptimizationReport
} from './DatabaseOptimizer';

import { OptimizedSupabaseClient } from './QueryOptimizer';

/**
 * Database Integration Manager Class
 */
class DatabaseIntegrationManager {
  constructor(supabaseClient, options = {}) {
    this.supabase = supabaseClient;
    this.optimizedClient = new OptimizedSupabaseClient(supabaseClient);
    this.options = {
      enableHealthMonitoring: true,
      enableQueryOptimization: true,
      enablePerformanceTracking: true,
      enableAutomaticOptimization: true,
      healthCheckInterval: 30000, // 30 seconds
      optimizationInterval: 300000, // 5 minutes
      alertThresholds: {
        queryDuration: 1000, // 1 second
        errorRate: 0.05, // 5%
        connectionCount: 80 // 80% of max connections
      },
      ...options
    };

    this.isInitialized = false;
    this.intervals = [];
    this.alerts = [];
    this.metrics = {
      totalQueries: 0,
      averageQueryTime: 0,
      errorCount: 0,
      optimizationsApplied: 0
    };

    this.init();
  }

  /**
   * Initialize database integration
   */
  async init() {
    if (this.isInitialized) return;

    try {
      // Start health monitoring
      if (this.options.enableHealthMonitoring) {
        await this.initializeHealthMonitoring();
      }

      // Start query optimization
      if (this.options.enableQueryOptimization) {
        await this.initializeQueryOptimization();
      }

      // Start performance tracking
      if (this.options.enablePerformanceTracking) {
        await this.initializePerformanceTracking();
      }

      // Setup automatic optimization
      if (this.options.enableAutomaticOptimization) {
        await this.setupAutomaticOptimization();
      }

      this.isInitialized = true;
      console.log('[DatabaseIntegrationManager] Initialized successfully');
    } catch (error) {
      console.error('[DatabaseIntegrationManager] Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Initialize health monitoring
   */
  async initializeHealthMonitoring() {
    // Start database health monitoring
    startDatabaseMonitoring(this.supabase);

    // Add custom health checks
    addHealthCheck('connection_pool', async () => {
      try {
        const { data, error } = await this.supabase
          .from('users')
          .select('count')
          .limit(1);
        
        return {
          status: error ? 'unhealthy' : 'healthy',
          message: error ? `Connection error: ${error.message}` : 'Connection pool healthy',
          timestamp: new Date().toISOString()
        };
      } catch (err) {
        return {
          status: 'unhealthy',
          message: `Connection pool check failed: ${err.message}`,
          timestamp: new Date().toISOString()
        };
      }
    });

    // Setup periodic health checks
    const healthInterval = setInterval(() => {
      this.checkDatabaseHealth();
    }, this.options.healthCheckInterval);

    this.intervals.push(healthInterval);
  }

  /**
   * Initialize query optimization
   */
  async initializeQueryOptimization() {
    // Setup query performance tracking
    const originalFrom = this.supabase.from.bind(this.supabase);
    
    this.supabase.from = (tableName) => {
      const query = originalFrom(tableName);
      
      // Wrap query execution to track performance
      const originalSelect = query.select.bind(query);
      query.select = (...args) => {
        const startTime = performance.now();
        const selectQuery = originalSelect(...args);
        
        // Track query performance
        const originalThen = selectQuery.then.bind(selectQuery);
        selectQuery.then = (onFulfilled, onRejected) => {
          return originalThen(
            (result) => {
              const duration = performance.now() - startTime;
              this.trackQueryMetrics(tableName, 'select', duration, !result.error);
              trackQueryPerformance(tableName, 'select', duration, result.data?.length || 0);
              return onFulfilled ? onFulfilled(result) : result;
            },
            (error) => {
              const duration = performance.now() - startTime;
              this.trackQueryMetrics(tableName, 'select', duration, false);
              return onRejected ? onRejected(error) : Promise.reject(error);
            }
          );
        };
        
        return selectQuery;
      };
      
      return query;
    };
  }

  /**
   * Initialize performance tracking
   */
  async initializePerformanceTracking() {
    // Setup performance metrics collection
    const metricsInterval = setInterval(() => {
      this.collectPerformanceMetrics();
    }, 60000); // Every minute

    this.intervals.push(metricsInterval);
  }

  /**
   * Setup automatic optimization
   */
  async setupAutomaticOptimization() {
    const optimizationInterval = setInterval(async () => {
      await this.runAutomaticOptimization();
    }, this.options.optimizationInterval);

    this.intervals.push(optimizationInterval);
  }

  /**
   * Check database health and generate alerts
   */
  async checkDatabaseHealth() {
    try {
      const health = getCurrentHealth();
      const recommendations = getOptimizationRecommendations();

      // Check for critical issues
      if (health?.overallStatus === 'unhealthy') {
        this.generateAlert('critical', 'Database health is unhealthy', {
          health,
          timestamp: new Date().toISOString()
        });
      }

      // Check for high-priority recommendations
      const criticalRecommendations = recommendations.filter(r => r.priority === 'high');
      if (criticalRecommendations.length > 0) {
        this.generateAlert('warning', `${criticalRecommendations.length} critical database optimizations needed`, {
          recommendations: criticalRecommendations,
          timestamp: new Date().toISOString()
        });
      }

    } catch (error) {
      console.error('[DatabaseIntegrationManager] Health check failed:', error);
      this.generateAlert('error', 'Database health check failed', {
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Track query metrics
   */
  trackQueryMetrics(table, operation, duration, success) {
    this.metrics.totalQueries++;
    
    // Update average query time
    this.metrics.averageQueryTime = (
      (this.metrics.averageQueryTime * (this.metrics.totalQueries - 1) + duration) / 
      this.metrics.totalQueries
    );

    if (!success) {
      this.metrics.errorCount++;
    }

    // Check for slow queries
    if (duration > this.options.alertThresholds.queryDuration) {
      this.generateAlert('warning', `Slow query detected: ${table}.${operation}`, {
        table,
        operation,
        duration,
        timestamp: new Date().toISOString()
      });
    }

    // Check error rate
    const errorRate = this.metrics.errorCount / this.metrics.totalQueries;
    if (errorRate > this.options.alertThresholds.errorRate) {
      this.generateAlert('critical', `High error rate detected: ${(errorRate * 100).toFixed(2)}%`, {
        errorRate,
        totalQueries: this.metrics.totalQueries,
        errorCount: this.metrics.errorCount,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Collect performance metrics
   */
  async collectPerformanceMetrics() {
    try {
      const health = getCurrentHealth();
      const trends = getHealthTrends();
      const recommendations = getOptimizationRecommendations();
      const queryPatterns = analyzeQueryPatterns();

      // Store metrics for reporting
      const metrics = {
        timestamp: new Date().toISOString(),
        health,
        trends: trends.slice(-10), // Last 10 trends
        recommendations,
        queryPatterns,
        performance: {
          totalQueries: this.metrics.totalQueries,
          averageQueryTime: this.metrics.averageQueryTime,
          errorCount: this.metrics.errorCount,
          errorRate: this.metrics.errorCount / Math.max(this.metrics.totalQueries, 1),
          optimizationsApplied: this.metrics.optimizationsApplied
        }
      };

      // Store in local storage for dashboard
      try {
        const existingMetrics = JSON.parse(localStorage.getItem('database_metrics') || '[]');
        existingMetrics.push(metrics);
        
        // Keep only last 100 metrics
        if (existingMetrics.length > 100) {
          existingMetrics.splice(0, existingMetrics.length - 100);
        }
        
        localStorage.setItem('database_metrics', JSON.stringify(existingMetrics));
      } catch (e) {
        console.warn('[DatabaseIntegrationManager] Failed to store metrics:', e);
      }

    } catch (error) {
      console.error('[DatabaseIntegrationManager] Failed to collect metrics:', error);
    }
  }

  /**
   * Run automatic optimization
   */
  async runAutomaticOptimization() {
    try {
      const recommendations = getOptimizationRecommendations();
      const autoApplicable = recommendations.filter(r => 
        r.autoApplicable && 
        r.priority !== 'low' && 
        r.risk === 'low'
      );

      for (const recommendation of autoApplicable) {
        try {
          // Apply safe optimizations automatically
          await this.applyOptimization(recommendation);
          this.metrics.optimizationsApplied++;
          
          console.log(`[DatabaseIntegrationManager] Applied optimization: ${recommendation.title}`);
        } catch (error) {
          console.error(`[DatabaseIntegrationManager] Failed to apply optimization:`, error);
        }
      }

    } catch (error) {
      console.error('[DatabaseIntegrationManager] Automatic optimization failed:', error);
    }
  }

  /**
   * Apply optimization recommendation
   */
  async applyOptimization(recommendation) {
    // This would implement specific optimization logic
    // For now, we'll just log the recommendation
    console.log('[DatabaseIntegrationManager] Optimization applied:', recommendation);
  }

  /**
   * Generate alert
   */
  generateAlert(severity, message, data = {}) {
    const alert = {
      id: Date.now().toString(),
      severity,
      message,
      data,
      timestamp: new Date().toISOString()
    };

    this.alerts.push(alert);
    
    // Keep only last 50 alerts
    if (this.alerts.length > 50) {
      this.alerts.shift();
    }

    // Log alert
    const logMethod = severity === 'critical' ? 'error' : severity === 'warning' ? 'warn' : 'info';
    console[logMethod](`[DatabaseIntegrationManager] ${severity.toUpperCase()}: ${message}`, data);

    // Dispatch custom event for UI components
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('database-alert', { detail: alert }));
    }
  }

  /**
   * Get comprehensive database status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      health: getCurrentHealth(),
      trends: getHealthTrends(),
      recommendations: getOptimizationRecommendations(),
      metrics: { ...this.metrics },
      alerts: this.alerts.slice(-10), // Last 10 alerts
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get performance report
   */
  async getPerformanceReport() {
    try {
      const report = generateOptimizationReport();
      const status = this.getStatus();
      
      return {
        ...report,
        integration: status,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DatabaseIntegrationManager] Failed to generate report:', error);
      return null;
    }
  }

  /**
   * Export all data
   */
  async exportData() {
    try {
      const healthData = exportHealthData();
      const status = this.getStatus();
      const report = await this.getPerformanceReport();
      
      return {
        health: healthData,
        status,
        report,
        exportedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('[DatabaseIntegrationManager] Failed to export data:', error);
      return null;
    }
  }

  /**
   * Cleanup and destroy
   */
  destroy() {
    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
    
    // Clear alerts
    this.alerts = [];
    
    // Reset metrics
    this.metrics = {
      totalQueries: 0,
      averageQueryTime: 0,
      errorCount: 0,
      optimizationsApplied: 0
    };
    
    this.isInitialized = false;
    console.log('[DatabaseIntegrationManager] Destroyed');
  }
}

// Create singleton instance
let databaseIntegrationManager = null;

/**
 * Initialize database integration
 */
export const initializeDatabaseIntegration = (supabaseClient, options = {}) => {
  if (!databaseIntegrationManager) {
    databaseIntegrationManager = new DatabaseIntegrationManager(supabaseClient, options);
  }
  return databaseIntegrationManager;
};

/**
 * Get database integration manager instance
 */
export const getDatabaseIntegrationManager = () => {
  return databaseIntegrationManager;
};

// Export utilities
export default DatabaseIntegrationManager;
export { DatabaseIntegrationManager };

/**
 * React hook for database integration
 */
export const useDatabaseIntegration = (supabaseClient) => {
  const [status, setStatus] = React.useState(null);
  const [alerts, setAlerts] = React.useState([]);

  React.useEffect(() => {
    // Initialize integration
    const manager = initializeDatabaseIntegration(supabaseClient);
    
    // Update status periodically
    const updateStatus = () => {
      setStatus(manager.getStatus());
    };
    
    updateStatus();
    const interval = setInterval(updateStatus, 30000); // Every 30 seconds
    
    // Listen for alerts
    const handleAlert = (event) => {
      setAlerts(prev => [...prev.slice(-9), event.detail]); // Keep last 10 alerts
    };
    
    window.addEventListener('database-alert', handleAlert);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('database-alert', handleAlert);
    };
  }, [supabaseClient]);

  return {
    status,
    alerts,
    manager: databaseIntegrationManager
  };
};
