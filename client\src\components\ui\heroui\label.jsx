import React from "react";
import { cn } from "../../../lib/utils";

/**
 * Label Component - HeroUI Implementation
 *
 * A form label component for form controls.
 * Compatible with shadcn/ui Label API for easy migration.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Label content
 * @returns {React.ReactElement} - Label component
 */
const Label = React.forwardRef(({ className, ...props }, ref) => (
  <label
    ref={ref}
    className={cn(
      "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
      className
    )}
    {...props}
  />
));
Label.displayName = "Label";

export { Label };
