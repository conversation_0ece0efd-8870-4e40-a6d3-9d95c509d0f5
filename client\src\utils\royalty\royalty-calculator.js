/**
 * Royalty Calculator Utility
 * 
 * This utility provides functions for calculating royalty distributions
 * based on different models (equal split, task-based, time-based, role-based, and custom CoG model).
 */

/**
 * Calculate royalty distribution using equal split model
 * @param {number} totalAmount - Total amount to distribute
 * @param {Array} contributors - Array of contributor objects
 * @returns {Array} Array of distribution objects with contributor ID and amount
 */
export const calculateEqualSplit = (totalAmount, contributors) => {
  if (!contributors || contributors.length === 0) return [];
  
  const equalShare = totalAmount / contributors.length;
  
  return contributors.map(contributor => ({
    contributor_id: contributor.id,
    user_id: contributor.user_id,
    amount: equalShare,
    percentage: 100 / contributors.length,
    calculation_method: 'equal_split',
    calculation_details: {
      total_contributors: contributors.length,
      equal_share: equalShare
    }
  }));
};

/**
 * Calculate royalty distribution using task-based model
 * @param {number} totalAmount - Total amount to distribute
 * @param {Array} contributors - Array of contributor objects
 * @param {Array} contributions - Array of contribution objects
 * @returns {Array} Array of distribution objects with contributor ID and amount
 */
export const calculateTaskBased = (totalAmount, contributors, contributions) => {
  if (!contributors || contributors.length === 0 || !contributions || contributions.length === 0) return [];
  
  // Count tasks per contributor
  const taskCounts = {};
  let totalTasks = 0;
  
  contributions.forEach(contribution => {
    const contributorId = contribution.user_id;
    if (!taskCounts[contributorId]) {
      taskCounts[contributorId] = 0;
    }
    taskCounts[contributorId] += 1;
    totalTasks += 1;
  });
  
  // Calculate distribution based on task count
  return contributors.map(contributor => {
    const contributorId = contributor.user_id;
    const taskCount = taskCounts[contributorId] || 0;
    const percentage = totalTasks > 0 ? (taskCount / totalTasks) * 100 : 0;
    const amount = totalAmount * (percentage / 100);
    
    return {
      contributor_id: contributor.id,
      user_id: contributorId,
      amount,
      percentage,
      calculation_method: 'task_based',
      calculation_details: {
        total_tasks: totalTasks,
        contributor_tasks: taskCount
      }
    };
  });
};

/**
 * Calculate royalty distribution using time-based model
 * @param {number} totalAmount - Total amount to distribute
 * @param {Array} contributors - Array of contributor objects
 * @param {Array} contributions - Array of contribution objects
 * @returns {Array} Array of distribution objects with contributor ID and amount
 */
export const calculateTimeBased = (totalAmount, contributors, contributions) => {
  if (!contributors || contributors.length === 0 || !contributions || contributions.length === 0) return [];
  
  // Sum hours per contributor
  const hourSums = {};
  let totalHours = 0;
  
  contributions.forEach(contribution => {
    const contributorId = contribution.user_id;
    const hours = parseFloat(contribution.hours_spent) || 0;
    
    if (!hourSums[contributorId]) {
      hourSums[contributorId] = 0;
    }
    hourSums[contributorId] += hours;
    totalHours += hours;
  });
  
  // Calculate distribution based on hours
  return contributors.map(contributor => {
    const contributorId = contributor.user_id;
    const hours = hourSums[contributorId] || 0;
    const percentage = totalHours > 0 ? (hours / totalHours) * 100 : 0;
    const amount = totalAmount * (percentage / 100);
    
    return {
      contributor_id: contributor.id,
      user_id: contributorId,
      amount,
      percentage,
      calculation_method: 'time_based',
      calculation_details: {
        total_hours: totalHours,
        contributor_hours: hours
      }
    };
  });
};

/**
 * Calculate royalty distribution using role-based model
 * @param {number} totalAmount - Total amount to distribute
 * @param {Array} contributors - Array of contributor objects with role and weight properties
 * @returns {Array} Array of distribution objects with contributor ID and amount
 */
export const calculateRoleBased = (totalAmount, contributors) => {
  if (!contributors || contributors.length === 0) return [];
  
  // Calculate total weight
  let totalWeight = 0;
  contributors.forEach(contributor => {
    totalWeight += parseFloat(contributor.weight) || 1; // Default weight is 1
  });
  
  // Calculate distribution based on role weights
  return contributors.map(contributor => {
    const weight = parseFloat(contributor.weight) || 1;
    const percentage = totalWeight > 0 ? (weight / totalWeight) * 100 : 0;
    const amount = totalAmount * (percentage / 100);
    
    return {
      contributor_id: contributor.id,
      user_id: contributor.user_id,
      amount,
      percentage,
      calculation_method: 'role_based',
      calculation_details: {
        total_weight: totalWeight,
        contributor_weight: weight,
        contributor_role: contributor.role
      }
    };
  });
};

/**
 * Calculate royalty distribution using custom CoG model (Tasks-Time-Difficulty)
 * @param {number} totalAmount - Total amount to distribute
 * @param {Array} contributors - Array of contributor objects
 * @param {Array} contributions - Array of contribution objects with task_type, hours_spent, and difficulty
 * @param {Object} weights - Object with weights for tasks, time, and difficulty
 * @returns {Array} Array of distribution objects with contributor ID and amount
 */
export const calculateCoGModel = (totalAmount, contributors, contributions, weights = { tasks: 1, time: 1, difficulty: 1 }) => {
  if (!contributors || contributors.length === 0 || !contributions || contributions.length === 0) return [];
  
  // Calculate points per contributor
  const contributorPoints = {};
  let totalPoints = 0;
  
  // Initialize points for all contributors
  contributors.forEach(contributor => {
    contributorPoints[contributor.user_id] = {
      taskPoints: 0,
      timePoints: 0,
      difficultyPoints: 0,
      totalPoints: 0
    };
  });
  
  // Calculate task points
  contributions.forEach(contribution => {
    const contributorId = contribution.user_id;
    if (!contributorPoints[contributorId]) return;
    
    const hours = parseFloat(contribution.hours_spent) || 0;
    const difficulty = parseFloat(contribution.difficulty) || 1;
    
    // Task points (1 point per task)
    contributorPoints[contributorId].taskPoints += 1 * weights.tasks;
    
    // Time points (hours worked)
    contributorPoints[contributorId].timePoints += hours * weights.time;
    
    // Difficulty points (hours * difficulty)
    contributorPoints[contributorId].difficultyPoints += (hours * difficulty) * weights.difficulty;
    
    // Update total points
    contributorPoints[contributorId].totalPoints = 
      contributorPoints[contributorId].taskPoints + 
      contributorPoints[contributorId].timePoints + 
      contributorPoints[contributorId].difficultyPoints;
    
    totalPoints += 1 * weights.tasks + hours * weights.time + (hours * difficulty) * weights.difficulty;
  });
  
  // Calculate distribution based on points
  return contributors.map(contributor => {
    const contributorId = contributor.user_id;
    const points = contributorPoints[contributorId]?.totalPoints || 0;
    const percentage = totalPoints > 0 ? (points / totalPoints) * 100 : 0;
    const amount = totalAmount * (percentage / 100);
    
    return {
      contributor_id: contributor.id,
      user_id: contributorId,
      amount,
      percentage,
      calculation_method: 'cog_model',
      calculation_details: {
        total_points: totalPoints,
        contributor_points: points,
        task_points: contributorPoints[contributorId]?.taskPoints || 0,
        time_points: contributorPoints[contributorId]?.timePoints || 0,
        difficulty_points: contributorPoints[contributorId]?.difficultyPoints || 0,
        weights
      }
    };
  });
};

/**
 * Apply manual adjustments to a distribution
 * @param {Array} distribution - Original distribution array
 * @param {Array} adjustments - Array of adjustment objects with contributor_id and adjustment_percentage
 * @param {number} totalAmount - Total amount to distribute
 * @returns {Array} Adjusted distribution array
 */
export const applyManualAdjustments = (distribution, adjustments, totalAmount) => {
  if (!distribution || distribution.length === 0 || !adjustments || adjustments.length === 0) {
    return distribution;
  }
  
  // Create a map of adjustments by contributor ID
  const adjustmentMap = {};
  adjustments.forEach(adjustment => {
    adjustmentMap[adjustment.contributor_id] = parseFloat(adjustment.adjustment_percentage) || 0;
  });
  
  // Apply adjustments
  const adjustedDistribution = distribution.map(item => {
    const adjustment = adjustmentMap[item.contributor_id] || 0;
    const newPercentage = item.percentage + adjustment;
    const newAmount = totalAmount * (newPercentage / 100);
    
    return {
      ...item,
      original_percentage: item.percentage,
      original_amount: item.amount,
      percentage: newPercentage,
      amount: newAmount,
      adjustment_percentage: adjustment,
      calculation_details: {
        ...item.calculation_details,
        manual_adjustment: adjustment
      }
    };
  });
  
  // Normalize percentages to ensure they sum to 100%
  const totalPercentage = adjustedDistribution.reduce((sum, item) => sum + item.percentage, 0);
  if (totalPercentage !== 100) {
    const normalizationFactor = 100 / totalPercentage;
    return adjustedDistribution.map(item => ({
      ...item,
      percentage: item.percentage * normalizationFactor,
      amount: totalAmount * (item.percentage * normalizationFactor / 100),
      calculation_details: {
        ...item.calculation_details,
        normalization_factor: normalizationFactor
      }
    }));
  }
  
  return adjustedDistribution;
};

/**
 * Calculate royalty distribution based on the specified model
 * @param {string} model - Calculation model ('equal_split', 'task_based', 'time_based', 'role_based', 'cog_model')
 * @param {number} totalAmount - Total amount to distribute
 * @param {Array} contributors - Array of contributor objects
 * @param {Array} contributions - Array of contribution objects
 * @param {Object} options - Additional options for calculation
 * @returns {Array} Array of distribution objects with contributor ID and amount
 */
export const calculateRoyaltyDistribution = (model, totalAmount, contributors, contributions, options = {}) => {
  switch (model) {
    case 'equal_split':
      return calculateEqualSplit(totalAmount, contributors);
    
    case 'task_based':
      return calculateTaskBased(totalAmount, contributors, contributions);
    
    case 'time_based':
      return calculateTimeBased(totalAmount, contributors, contributions);
    
    case 'role_based':
      return calculateRoleBased(totalAmount, contributors);
    
    case 'cog_model':
      return calculateCoGModel(totalAmount, contributors, contributions, options.weights);
    
    default:
      // Default to equal split if model is not recognized
      return calculateEqualSplit(totalAmount, contributors);
  }
};

/**
 * Format currency for display
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: 'USD')
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = 'USD') => {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
  
  return formatter.format(amount);
};

/**
 * Format percentage for display
 * @param {number} percentage - Percentage to format
 * @returns {string} Formatted percentage string
 */
export const formatPercentage = (percentage) => {
  return `${percentage.toFixed(2)}%`;
};
