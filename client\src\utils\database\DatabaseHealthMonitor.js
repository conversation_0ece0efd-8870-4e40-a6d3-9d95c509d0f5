// Database Health Monitor Utility
// Integration & Services Agent: Real-time database health monitoring and alerting

class DatabaseHealthMonitor {
  constructor() {
    this.healthChecks = new Map();
    this.alertThresholds = {
      responseTime: 1000, // 1 second
      errorRate: 0.05, // 5%
      connectionFailures: 3,
      slowQueryThreshold: 2000 // 2 seconds
    };
    this.monitoringInterval = 30000; // 30 seconds
    this.isMonitoring = false;
    this.healthHistory = [];
    this.maxHistorySize = 100;
    
    this.initializeHealthChecks();
  }

  // Initialize health check definitions
  initializeHealthChecks() {
    this.healthChecks.set('connection', {
      name: 'Database Connection',
      check: this.checkConnection.bind(this),
      critical: true,
      timeout: 5000
    });

    this.healthChecks.set('response_time', {
      name: 'Response Time',
      check: this.checkResponseTime.bind(this),
      critical: false,
      timeout: 10000
    });

    this.healthChecks.set('table_access', {
      name: 'Table Access',
      check: this.checkTableAccess.bind(this),
      critical: true,
      timeout: 15000
    });

    this.healthChecks.set('rls_policies', {
      name: 'RLS Policies',
      check: this.checkRLSPolicies.bind(this),
      critical: true,
      timeout: 10000
    });

    this.healthChecks.set('indexes', {
      name: 'Index Performance',
      check: this.checkIndexPerformance.bind(this),
      critical: false,
      timeout: 20000
    });
  }

  // Start health monitoring
  startMonitoring(supabaseClient) {
    if (this.isMonitoring) return;
    
    this.supabaseClient = supabaseClient;
    this.isMonitoring = true;
    
    console.log('🏥 Starting database health monitoring...');
    
    this.runHealthCheck();
    
    this.monitoringTimer = setInterval(() => {
      this.runHealthCheck();
    }, this.monitoringInterval);
  }

  // Stop health monitoring
  stopMonitoring() {
    if (\!this.isMonitoring) return;
    
    this.isMonitoring = false;
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }
    
    console.log('🏥 Database health monitoring stopped');
  }

  // Run comprehensive health check
  async runHealthCheck() {
    const healthReport = {
      timestamp: new Date().toISOString(),
      overall: 'healthy',
      checks: {},
      metrics: {},
      alerts: []
    };

    let criticalFailures = 0;
    let totalChecks = 0;

    for (const [key, checkConfig] of this.healthChecks.entries()) {
      totalChecks++;
      
      try {
        const startTime = Date.now();
        const result = await Promise.race([
          checkConfig.check(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), checkConfig.timeout)
          )
        ]);
        
        const duration = Date.now() - startTime;
        
        healthReport.checks[key] = {
          name: checkConfig.name,
          status: result.status || 'healthy',
          duration,
          details: result.details || {},
          critical: checkConfig.critical
        };

        if (result.status === 'unhealthy' && checkConfig.critical) {
          criticalFailures++;
        }

        if (result.alerts) {
          healthReport.alerts.push(...result.alerts);
        }

      } catch (error) {
        healthReport.checks[key] = {
          name: checkConfig.name,
          status: 'error',
          error: error.message,
          critical: checkConfig.critical
        };

        if (checkConfig.critical) {
          criticalFailures++;
        }

        healthReport.alerts.push({
          type: 'error',
          severity: checkConfig.critical ? 'critical' : 'warning',
          message: `Health check failed: ${checkConfig.name}`,
          details: error.message
        });
      }
    }

    // Determine overall health
    if (criticalFailures > 0) {
      healthReport.overall = 'critical';
    } else if (healthReport.alerts.length > 0) {
      healthReport.overall = 'warning';
    }

    // Calculate health score
    const healthyChecks = Object.values(healthReport.checks)
      .filter(check => check.status === 'healthy').length;
    healthReport.metrics.healthScore = (healthyChecks / totalChecks) * 100;

    this.addToHistory(healthReport);
    this.logHealthStatus(healthReport);

    return healthReport;
  }

  // Check database connection
  async checkConnection() {
    try {
      const { data, error } = await this.supabaseClient
        .from('users')
        .select('count')
        .limit(1);

      if (error) {
        return {
          status: 'unhealthy',
          details: { error: error.message },
          alerts: [{
            type: 'connection',
            severity: 'critical',
            message: 'Database connection failed'
          }]
        };
      }

      return {
        status: 'healthy',
        details: { connected: true }
      };
    } catch (error) {
      return {
        status: 'error',
        details: { error: error.message }
      };
    }
  }

  // Check response time
  async checkResponseTime() {
    const startTime = Date.now();
    
    try {
      await this.supabaseClient
        .from('users')
        .select('id')
        .limit(1);
      
      const responseTime = Date.now() - startTime;
      const alerts = [];

      if (responseTime > this.alertThresholds.responseTime) {
        alerts.push({
          type: 'performance',
          severity: 'warning',
          message: `Slow response time: ${responseTime}ms`
        });
      }

      return {
        status: responseTime > this.alertThresholds.responseTime * 2 ? 'unhealthy' : 'healthy',
        details: { responseTime },
        alerts
      };
    } catch (error) {
      return {
        status: 'error',
        details: { error: error.message }
      };
    }
  }

  // Check table access
  async checkTableAccess() {
    const criticalTables = ['users', 'projects', 'teams', 'skills'];
    const results = {};
    const alerts = [];
    let failedTables = 0;

    for (const table of criticalTables) {
      try {
        const { error } = await this.supabaseClient
          .from(table)
          .select('count')
          .limit(1);

        if (error) {
          results[table] = { accessible: false, error: error.message };
          failedTables++;
          alerts.push({
            type: 'table_access',
            severity: 'critical',
            message: `Cannot access table: ${table}`
          });
        } else {
          results[table] = { accessible: true };
        }
      } catch (error) {
        results[table] = { accessible: false, error: error.message };
        failedTables++;
      }
    }

    return {
      status: failedTables === 0 ? 'healthy' : 'unhealthy',
      details: { tables: results, failedCount: failedTables },
      alerts
    };
  }

  // Check RLS policies
  async checkRLSPolicies() {
    const tablesWithRLS = ['users', 'projects', 'teams', 'skills'];
    const results = {};
    const alerts = [];

    for (const table of tablesWithRLS) {
      try {
        const { error } = await this.supabaseClient
          .from(table)
          .select('*')
          .limit(1);

        results[table] = { 
          rlsActive: true, 
          accessible: \!error || error.code \!== 'PGRST116' 
        };
      } catch (error) {
        results[table] = { rlsActive: false, error: error.message };
        alerts.push({
          type: 'security',
          severity: 'warning',
          message: `RLS check failed for table: ${table}`
        });
      }
    }

    return {
      status: alerts.length === 0 ? 'healthy' : 'warning',
      details: { tables: results },
      alerts
    };
  }

  // Check index performance
  async checkIndexPerformance() {
    const testQueries = [
      { table: 'users', column: 'created_at', description: 'User creation index' },
      { table: 'projects', column: 'updated_at', description: 'Project update index' }
    ];

    const results = {};
    const alerts = [];

    for (const query of testQueries) {
      try {
        const startTime = Date.now();
        
        await this.supabaseClient
          .from(query.table)
          .select('id')
          .order(query.column, { ascending: false })
          .limit(10);
        
        const duration = Date.now() - startTime;
        results[`${query.table}_${query.column}`] = {
          duration,
          description: query.description,
          performant: duration < this.alertThresholds.slowQueryThreshold
        };

        if (duration > this.alertThresholds.slowQueryThreshold) {
          alerts.push({
            type: 'performance',
            severity: 'warning',
            message: `Slow index query: ${query.description} (${duration}ms)`
          });
        }
      } catch (error) {
        results[`${query.table}_${query.column}`] = {
          error: error.message,
          description: query.description,
          performant: false
        };
      }
    }

    return {
      status: alerts.length === 0 ? 'healthy' : 'warning',
      details: { indexes: results },
      alerts
    };
  }

  // Add health report to history
  addToHistory(healthReport) {
    this.healthHistory.push(healthReport);
    
    if (this.healthHistory.length > this.maxHistorySize) {
      this.healthHistory.shift();
    }
  }

  // Log health status
  logHealthStatus(healthReport) {
    const emoji = {
      healthy: '✅',
      warning: '⚠️',
      critical: '🚨',
      error: '❌'
    };

    console.log(
      `🏥 Database Health: ${emoji[healthReport.overall]} ${healthReport.overall.toUpperCase()} ` +
      `(Score: ${healthReport.metrics.healthScore.toFixed(1)}%)`
    );

    if (healthReport.alerts.length > 0) {
      console.group('🚨 Health Alerts:');
      healthReport.alerts.forEach(alert => {
        console.log(`${emoji[alert.severity] || '⚠️'} ${alert.message}`);
      });
      console.groupEnd();
    }
  }

  // Get current health status
  getCurrentHealth() {
    return this.healthHistory.length > 0 
      ? this.healthHistory[this.healthHistory.length - 1]
      : null;
  }

  // Get health trends
  getHealthTrends(timeRange = 24) {
    const cutoffTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
    
    const recentHistory = this.healthHistory.filter(report => 
      new Date(report.timestamp) > cutoffTime
    );

    if (recentHistory.length === 0) return null;

    return {
      healthScore: {
        current: recentHistory[recentHistory.length - 1].metrics.healthScore,
        average: recentHistory.reduce((sum, r) => sum + r.metrics.healthScore, 0) / recentHistory.length,
        trend: 'stable'
      },
      alerts: {
        total: recentHistory.reduce((sum, r) => sum + r.alerts.length, 0),
        critical: recentHistory.reduce((sum, r) => 
          sum + r.alerts.filter(a => a.severity === 'critical').length, 0
        )
      },
      uptime: (recentHistory.filter(r => r.overall \!== 'critical').length / recentHistory.length) * 100
    };
  }

  // Export health data
  exportHealthData() {
    return {
      timestamp: new Date().toISOString(),
      currentHealth: this.getCurrentHealth(),
      trends: this.getHealthTrends(),
      history: this.healthHistory,
      configuration: {
        alertThresholds: this.alertThresholds,
        monitoringInterval: this.monitoringInterval,
        isMonitoring: this.isMonitoring
      }
    };
  }

  // Update alert thresholds
  updateThresholds(newThresholds) {
    this.alertThresholds = { ...this.alertThresholds, ...newThresholds };
  }
}

// Create singleton instance
const databaseHealthMonitor = new DatabaseHealthMonitor();

// Export utilities
export const startDatabaseMonitoring = (supabaseClient) => 
  databaseHealthMonitor.startMonitoring(supabaseClient);

export const stopDatabaseMonitoring = () => 
  databaseHealthMonitor.stopMonitoring();

export const runHealthCheck = () => 
  databaseHealthMonitor.runHealthCheck();

export const getCurrentHealth = () => 
  databaseHealthMonitor.getCurrentHealth();

export const getHealthTrends = (timeRange) => 
  databaseHealthMonitor.getHealthTrends(timeRange);

export const exportHealthData = () => 
  databaseHealthMonitor.exportHealthData();

export const updateHealthThresholds = (thresholds) => 
  databaseHealthMonitor.updateThresholds(thresholds);

export default databaseHealthMonitor;
