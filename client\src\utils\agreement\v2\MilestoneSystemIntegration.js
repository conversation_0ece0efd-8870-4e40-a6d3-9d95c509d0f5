/**
 * Milestone System Integration
 * 
 * Integrates the milestone tracking system with Agreement Generation V2
 * to provide real-time milestone data for dynamic Exhibit II generation.
 */

import { supabase } from '../../supabase/supabase.utils.js';
import { AgreementIntegrationService } from './AgreementIntegrationService.js';

export class MilestoneSystemIntegration {
  constructor() {
    this.agreementService = new AgreementIntegrationService();
  }

  /**
   * Fetch milestone data for agreement generation
   */
  async fetchMilestoneDataForAgreement(ventureId) {
    console.log('📅 Fetching milestone data for agreement generation...');
    
    try {
      // Fetch all milestones for the venture
      const { data: milestones, error: milestonesError } = await supabase
        .from('venture_milestones')
        .select('*')
        .eq('venture_id', ventureId)
        .order('due_date', { ascending: true });
      
      if (milestonesError) throw milestonesError;
      
      // Fetch milestone phases if they exist
      const { data: phases, error: phasesError } = await supabase
        .from('milestone_phases')
        .select(`
          *,
          milestones:venture_milestones!milestone_phases_phase_id_fkey(*)
        `)
        .eq('venture_id', ventureId)
        .order('order_index', { ascending: true });
      
      // Phases are optional, so don't throw on error
      
      // Process and structure the data
      const structuredData = this.structureMilestoneData(milestones, phases || []);
      
      console.log(`✅ Milestone data fetched: ${milestones.length} milestones, ${phases?.length || 0} phases`);
      
      return structuredData;
      
    } catch (error) {
      console.error('❌ Failed to fetch milestone data:', error);
      throw new Error(`Failed to fetch milestone data: ${error.message}`);
    }
  }

  /**
   * Structure milestone data for exhibit generation
   */
  structureMilestoneData(milestones, phases) {
    const structuredData = {
      milestones: milestones.map(milestone => ({
        id: milestone.id,
        title: milestone.title,
        name: milestone.title, // Alias for compatibility
        description: milestone.description,
        due_date: milestone.due_date,
        deadline: milestone.due_date, // Alias for compatibility
        phase: milestone.phase,
        status: milestone.status,
        deliverables: milestone.deliverables || [],
        acceptance_criteria: milestone.acceptance_criteria || [],
        order_index: milestone.order_index,
        completion_percentage: milestone.completion_percentage || 0
      })),
      phases: []
    };

    // Process phases if they exist
    if (phases && phases.length > 0) {
      structuredData.phases = phases.map(phase => ({
        id: phase.id,
        name: phase.name,
        description: phase.description,
        duration: phase.duration,
        start_date: phase.start_date,
        end_date: phase.end_date,
        order_index: phase.order_index,
        status: phase.status,
        tasks: phase.tasks || [],
        milestones: (phase.milestones || []).map(milestone => ({
          title: milestone.title,
          due_date: milestone.due_date,
          deliverables: milestone.deliverables || [],
          acceptance_criteria: milestone.acceptance_criteria || []
        }))
      }));
    } else {
      // Group milestones into phases if no explicit phases exist
      structuredData.phases = this.groupMilestonesIntoPhases(structuredData.milestones);
    }

    return structuredData;
  }

  /**
   * Group milestones into logical phases
   */
  groupMilestonesIntoPhases(milestones) {
    if (milestones.length === 0) return [];

    const phaseGroups = {};
    
    milestones.forEach(milestone => {
      const phaseName = milestone.phase || this.inferPhaseFromMilestone(milestone);
      
      if (!phaseGroups[phaseName]) {
        phaseGroups[phaseName] = {
          name: phaseName,
          milestones: [],
          tasks: [],
          duration: null
        };
      }
      
      phaseGroups[phaseName].milestones.push(milestone);
      
      // Add milestone title as a task
      phaseGroups[phaseName].tasks.push(milestone.title);
    });

    return Object.values(phaseGroups);
  }

  /**
   * Infer phase from milestone data
   */
  inferPhaseFromMilestone(milestone) {
    const title = (milestone.title || '').toLowerCase();
    const description = (milestone.description || '').toLowerCase();
    const text = `${title} ${description}`;
    
    if (text.includes('planning') || text.includes('setup') || text.includes('design') || text.includes('research')) {
      return 'Phase 1: Planning and Design';
    } else if (text.includes('development') || text.includes('implementation') || text.includes('build') || text.includes('coding')) {
      return 'Phase 2: Development';
    } else if (text.includes('testing') || text.includes('qa') || text.includes('polish') || text.includes('review')) {
      return 'Phase 3: Testing and Polish';
    } else if (text.includes('launch') || text.includes('deploy') || text.includes('release') || text.includes('delivery')) {
      return 'Phase 4: Launch and Delivery';
    } else {
      // Default based on milestone order
      return 'Phase 2: Development';
    }
  }

  /**
   * Handle milestone updates and trigger agreement regeneration
   */
  async handleMilestoneUpdate(milestoneId, updateData, options = {}) {
    console.log('🔄 Processing milestone update...');
    
    try {
      // Update the milestone
      const { data: updatedMilestone, error: updateError } = await supabase
        .from('venture_milestones')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', milestoneId)
        .select('venture_id')
        .single();
      
      if (updateError) throw updateError;
      
      // Regenerate agreements if requested
      if (options.regenerateAgreements !== false) {
        await this.regenerateAgreementsForVenture(updatedMilestone.venture_id);
      }
      
      console.log('✅ Milestone update processed');
      
      return updatedMilestone;
      
    } catch (error) {
      console.error('❌ Failed to process milestone update:', error);
      throw new Error(`Failed to process milestone update: ${error.message}`);
    }
  }

  /**
   * Handle milestone completion
   */
  async handleMilestoneCompletion(milestoneId, completionData = {}) {
    console.log('🎉 Processing milestone completion...');
    
    try {
      // Mark milestone as completed
      const { data: completedMilestone, error: completionError } = await supabase
        .from('venture_milestones')
        .update({
          status: 'completed',
          completion_percentage: 100,
          completed_at: new Date().toISOString(),
          completion_notes: completionData.notes,
          actual_deliverables: completionData.deliverables || [],
          updated_at: new Date().toISOString()
        })
        .eq('id', milestoneId)
        .select('*')
        .single();
      
      if (completionError) throw completionError;
      
      // Log milestone completion activity
      await this.logMilestoneActivity(milestoneId, 'completed', completionData);
      
      // Check if this triggers any phase completion
      await this.checkPhaseCompletion(completedMilestone.venture_id, completedMilestone.phase);
      
      // Regenerate agreements to reflect milestone completion
      await this.regenerateAgreementsForVenture(completedMilestone.venture_id);
      
      console.log('✅ Milestone completion processed');
      
      return completedMilestone;
      
    } catch (error) {
      console.error('❌ Failed to process milestone completion:', error);
      throw new Error(`Failed to process milestone completion: ${error.message}`);
    }
  }

  /**
   * Create new milestone with agreement regeneration
   */
  async createMilestone(ventureId, milestoneData, options = {}) {
    console.log('📝 Creating new milestone...');
    
    try {
      const milestoneRecord = {
        venture_id: ventureId,
        title: milestoneData.title,
        description: milestoneData.description,
        due_date: milestoneData.due_date,
        phase: milestoneData.phase || this.inferPhaseFromMilestone(milestoneData),
        deliverables: milestoneData.deliverables || [],
        acceptance_criteria: milestoneData.acceptance_criteria || [],
        status: 'pending',
        order_index: milestoneData.order_index,
        created_at: new Date().toISOString()
      };
      
      const { data: createdMilestone, error } = await supabase
        .from('venture_milestones')
        .insert([milestoneRecord])
        .select()
        .single();
      
      if (error) throw error;
      
      // Regenerate agreements if requested
      if (options.regenerateAgreements !== false) {
        await this.regenerateAgreementsForVenture(ventureId);
      }
      
      console.log('✅ Milestone created');
      
      return createdMilestone;
      
    } catch (error) {
      console.error('❌ Failed to create milestone:', error);
      throw new Error(`Failed to create milestone: ${error.message}`);
    }
  }

  /**
   * Regenerate agreements for a venture when milestones change
   */
  async regenerateAgreementsForVenture(ventureId) {
    console.log('🔄 Regenerating agreements for venture...');
    
    try {
      // Get all active agreements for this venture
      const { data: agreements, error } = await supabase
        .from('generated_agreements')
        .select('id')
        .eq('venture_id', ventureId)
        .eq('is_active', true);
      
      if (error) throw error;
      
      // Regenerate each agreement
      const regenerationPromises = agreements.map(agreement =>
        this.agreementService.regenerateAgreement(agreement.id, {
          metadata: {
            regenerationReason: 'milestone_update',
            regeneratedAt: new Date().toISOString()
          }
        })
      );
      
      const results = await Promise.allSettled(regenerationPromises);
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      console.log(`✅ Agreement regeneration completed: ${successful} successful, ${failed} failed`);
      
      return {
        total: agreements.length,
        successful,
        failed
      };
      
    } catch (error) {
      console.error('❌ Failed to regenerate agreements:', error);
      // Don't throw - this is a background operation
      return {
        total: 0,
        successful: 0,
        failed: 1,
        error: error.message
      };
    }
  }

  /**
   * Check if phase is completed
   */
  async checkPhaseCompletion(ventureId, phaseName) {
    try {
      if (!phaseName) return;
      
      // Get all milestones in this phase
      const { data: phaseMilestones, error } = await supabase
        .from('venture_milestones')
        .select('status')
        .eq('venture_id', ventureId)
        .eq('phase', phaseName);
      
      if (error) throw error;
      
      // Check if all milestones are completed
      const allCompleted = phaseMilestones.every(m => m.status === 'completed');
      
      if (allCompleted && phaseMilestones.length > 0) {
        // Log phase completion
        await this.logPhaseCompletion(ventureId, phaseName);
      }
      
    } catch (error) {
      console.error('❌ Failed to check phase completion:', error);
      // Don't throw - this is a background check
    }
  }

  /**
   * Log milestone activity
   */
  async logMilestoneActivity(milestoneId, action, data = {}) {
    try {
      const activityRecord = {
        milestone_id: milestoneId,
        action: action,
        data: data,
        timestamp: new Date().toISOString()
      };
      
      await supabase
        .from('milestone_activity_log')
        .insert([activityRecord]);
        
    } catch (error) {
      console.error('⚠️  Failed to log milestone activity:', error);
      // Don't throw - logging is non-critical
    }
  }

  /**
   * Log phase completion
   */
  async logPhaseCompletion(ventureId, phaseName) {
    try {
      const phaseCompletionRecord = {
        venture_id: ventureId,
        phase_name: phaseName,
        completed_at: new Date().toISOString()
      };
      
      await supabase
        .from('phase_completions')
        .insert([phaseCompletionRecord]);
        
      console.log(`🎉 Phase completed: ${phaseName}`);
      
    } catch (error) {
      console.error('⚠️  Failed to log phase completion:', error);
      // Don't throw - logging is non-critical
    }
  }

  /**
   * Get milestone system integration status
   */
  getIntegrationStatus() {
    return {
      version: '2.0.0',
      capabilities: {
        milestoneDataFetching: true,
        dynamicExhibitGeneration: true,
        milestoneUpdateHandling: true,
        phaseManagement: true,
        agreementRegeneration: true,
        activityLogging: true
      },
      status: 'active'
    };
  }
}
