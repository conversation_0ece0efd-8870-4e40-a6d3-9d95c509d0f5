import { test, expect } from '@playwright/test';

test.describe('Track Page Debug Test', () => {
  test('Check Track page loading and console output', async ({ page }) => {
    console.log('🧪 Testing Track page with debugging...');
    
    // Capture console messages
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push(`${msg.type()}: ${msg.text()}`);
    });
    
    // Navigate to login page
    await page.goto('https://royalty.technology/login');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
    
    console.log('✅ Successfully logged in');
    
    // Navigate to Track page
    console.log('🎯 Navigating to Track page...');
    await page.goto('https://royalty.technology/track');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for React to render and console messages to appear
    await page.waitForTimeout(5000);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'track-page-debug.png', fullPage: true });
    
    // Log all console messages
    console.log('📊 Console messages:');
    consoleMessages.forEach((msg, index) => {
      console.log(`  ${index + 1}. ${msg}`);
    });
    
    // Check page title
    const pageTitle = await page.title();
    console.log('📄 Page title:', pageTitle);
    
    // Check if we can find the loading text or main content
    const loadingText = await page.locator('text=Loading Track Page').isVisible().catch(() => false);
    const mainContent = await page.locator('text=Project Management Hub').isVisible().catch(() => false);
    const authRequired = await page.locator('text=Authentication Required').isVisible().catch(() => false);
    
    console.log('🔍 Loading text visible:', loadingText);
    console.log('🔍 Main content visible:', mainContent);
    console.log('🔍 Auth required visible:', authRequired);
    
    // Check for specific debug messages
    const hasDebugMessages = consoleMessages.some(msg => 
      msg.includes('EnhancedTrackPage:')
    );
    console.log('🐛 Debug messages found:', hasDebugMessages);
    
    // Filter for error messages
    const errorMessages = consoleMessages.filter(msg => 
      msg.startsWith('error:') || msg.includes('Error')
    );
    
    if (errorMessages.length > 0) {
      console.log('❌ Error messages:');
      errorMessages.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // The page should either show loading, main content, or auth required
    const pageIsResponding = loadingText || mainContent || authRequired;
    expect(pageIsResponding).toBeTruthy();
    
    // Should not be stuck in loading state forever
    if (loadingText) {
      console.log('⚠️ Page is still in loading state - this may indicate an issue');
    }
    
    // Should have some debug messages if our changes are working
    if (hasDebugMessages) {
      console.log('✅ Debug messages are working');
    } else {
      console.log('⚠️ No debug messages found - changes may not be deployed');
    }
  });
});
