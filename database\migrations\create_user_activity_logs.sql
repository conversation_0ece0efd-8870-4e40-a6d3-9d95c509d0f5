-- User Activity Logging System
-- This creates comprehensive logging for navigation, interactions, and debugging

-- Main activity log table
CREATE TABLE IF NOT EXISTS user_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL, -- Browser session identifier
    timestamp TIMESTAMPTZ DEFAULT NOW(),

    -- Event categorization
    event_type TEXT NOT NULL, -- 'navigation', 'interaction', 'error', 'debug'
    event_category TEXT NOT NULL, -- 'page_view', 'click', 'hover', 'drag', 'keyboard', etc.
    event_action TEXT NOT NULL, -- specific action taken

    -- Navigation specific
    from_page TEXT,
    to_page TEXT,
    navigation_method TEXT, -- 'click', 'keyboard', 'url', 'back_button'
    view_mode TEXT, -- 'grid', 'overworld', 'content'
    zoom_level DECIMAL,

    -- Interaction details
    element_type TEXT, -- 'card', 'button', 'background', 'debug_ui'
    element_id TEXT,
    mouse_position JSONB, -- {x: number, y: number}
    viewport_size JSONB, -- {width: number, height: number}

    -- Debug information
    debug_data JSONB, -- flexible field for any debug info
    user_agent TEXT,
    url TEXT,
    referrer TEXT,

    -- Performance metrics
    page_load_time INTEGER, -- milliseconds
    interaction_duration INTEGER, -- milliseconds

    -- Error tracking
    error_message TEXT,
    error_stack TEXT,

    -- Metadata
    metadata JSONB DEFAULT '{}',

    -- Indexes for performance
    CONSTRAINT valid_event_type CHECK (event_type IN ('navigation', 'interaction', 'error', 'debug', 'performance'))
);

-- Navigation flow tracking table
CREATE TABLE IF NOT EXISTS navigation_flows (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    flow_start TIMESTAMPTZ DEFAULT NOW(),
    flow_end TIMESTAMPTZ,

    -- Flow details
    start_page TEXT NOT NULL,
    end_page TEXT,
    total_steps INTEGER DEFAULT 0,
    successful_completion BOOLEAN DEFAULT FALSE,

    -- Path taken
    navigation_path JSONB DEFAULT '[]', -- Array of page transitions
    interaction_count INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,

    -- Performance
    total_duration INTEGER, -- milliseconds
    average_step_duration INTEGER,

    -- Context
    user_intent TEXT, -- inferred or explicit user goal
    completion_rate DECIMAL, -- percentage of intended flow completed

    metadata JSONB DEFAULT '{}'
);

-- Debug session tracking
CREATE TABLE IF NOT EXISTS debug_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    started_at TIMESTAMPTZ DEFAULT NOW(),
    ended_at TIMESTAMPTZ,

    -- Debug settings
    debug_ui_enabled BOOLEAN DEFAULT FALSE,
    crosshair_enabled BOOLEAN DEFAULT FALSE,
    console_commands_used JSONB DEFAULT '[]',

    -- Navigation testing
    pages_visited JSONB DEFAULT '[]',
    navigation_methods_used JSONB DEFAULT '[]',
    errors_encountered INTEGER DEFAULT 0,

    -- Feedback
    user_feedback TEXT,
    issues_reported JSONB DEFAULT '[]',

    metadata JSONB DEFAULT '{}'
);

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_metrics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW(),

    -- Page performance
    page_name TEXT NOT NULL,
    load_time INTEGER, -- milliseconds
    first_contentful_paint INTEGER,
    largest_contentful_paint INTEGER,
    cumulative_layout_shift DECIMAL,

    -- Navigation performance
    navigation_start_time INTEGER,
    navigation_end_time INTEGER,
    transition_duration INTEGER,

    -- Resource metrics
    memory_usage JSONB,
    network_requests INTEGER,
    bundle_size INTEGER,

    -- User experience
    interaction_delay INTEGER,
    scroll_performance JSONB,
    animation_frame_rate DECIMAL,

    metadata JSONB DEFAULT '{}'
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_user_session ON user_activity_logs(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_timestamp ON user_activity_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_event_type ON user_activity_logs(event_type, event_category);
CREATE INDEX IF NOT EXISTS idx_user_activity_logs_navigation ON user_activity_logs(from_page, to_page) WHERE event_type = 'navigation';

CREATE INDEX IF NOT EXISTS idx_navigation_flows_user_session ON navigation_flows(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_navigation_flows_completion ON navigation_flows(successful_completion, completion_rate);

CREATE INDEX IF NOT EXISTS idx_debug_sessions_user ON debug_sessions(user_id, started_at DESC);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_page ON performance_metrics(page_name, timestamp DESC);

-- Enable Row Level Security
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE navigation_flows ENABLE ROW LEVEL SECURITY;
ALTER TABLE debug_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policies (users can only see their own logs, admins can see all)
CREATE POLICY "Users can view own activity logs" ON user_activity_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own activity logs" ON user_activity_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all activity logs" ON user_activity_logs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND is_admin = true
        )
    );

-- Similar policies for other tables
CREATE POLICY "Users can manage own navigation flows" ON navigation_flows
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all navigation flows" ON navigation_flows
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND is_admin = true
        )
    );

CREATE POLICY "Users can manage own debug sessions" ON debug_sessions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all debug sessions" ON debug_sessions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND is_admin = true
        )
    );

CREATE POLICY "Users can manage own performance metrics" ON performance_metrics
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all performance metrics" ON performance_metrics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND is_admin = true
        )
    );

-- Database functions for analytics
CREATE OR REPLACE FUNCTION get_activity_stats(time_range_hours INTEGER DEFAULT 24)
RETURNS TABLE (
    total_events BIGINT,
    unique_users BIGINT,
    unique_sessions BIGINT,
    navigation_events BIGINT,
    interaction_events BIGINT,
    error_events BIGINT,
    debug_events BIGINT,
    performance_events BIGINT,
    avg_session_duration INTERVAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_events,
        COUNT(DISTINCT user_id) as unique_users,
        COUNT(DISTINCT session_id) as unique_sessions,
        COUNT(*) FILTER (WHERE event_type = 'navigation') as navigation_events,
        COUNT(*) FILTER (WHERE event_type = 'interaction') as interaction_events,
        COUNT(*) FILTER (WHERE event_type = 'error') as error_events,
        COUNT(*) FILTER (WHERE event_type = 'debug') as debug_events,
        COUNT(*) FILTER (WHERE event_type = 'performance') as performance_events,
        AVG(
            CASE
                WHEN session_duration.max_time IS NOT NULL AND session_duration.min_time IS NOT NULL
                THEN session_duration.max_time - session_duration.min_time
                ELSE NULL
            END
        ) as avg_session_duration
    FROM user_activity_logs
    LEFT JOIN (
        SELECT
            session_id,
            MIN(timestamp) as min_time,
            MAX(timestamp) as max_time
        FROM user_activity_logs
        WHERE timestamp >= NOW() - INTERVAL '1 hour' * time_range_hours
        GROUP BY session_id
    ) session_duration ON user_activity_logs.session_id = session_duration.session_id
    WHERE timestamp >= NOW() - INTERVAL '1 hour' * time_range_hours;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get navigation flow analysis
CREATE OR REPLACE FUNCTION get_navigation_flow_analysis(time_range_hours INTEGER DEFAULT 24)
RETURNS TABLE (
    from_page TEXT,
    to_page TEXT,
    transition_count BIGINT,
    avg_duration INTERVAL,
    success_rate DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ual.from_page,
        ual.to_page,
        COUNT(*) as transition_count,
        AVG(
            CASE
                WHEN ual.interaction_duration IS NOT NULL
                THEN INTERVAL '1 millisecond' * ual.interaction_duration
                ELSE NULL
            END
        ) as avg_duration,
        (COUNT(*) FILTER (WHERE ual.metadata->>'navigation_success' = 'true')::DECIMAL / COUNT(*)) * 100 as success_rate
    FROM user_activity_logs ual
    WHERE ual.event_type = 'navigation'
        AND ual.from_page IS NOT NULL
        AND ual.to_page IS NOT NULL
        AND ual.timestamp >= NOW() - INTERVAL '1 hour' * time_range_hours
    GROUP BY ual.from_page, ual.to_page
    ORDER BY transition_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user session summary
CREATE OR REPLACE FUNCTION get_user_session_summary(p_user_id UUID, p_session_id TEXT)
RETURNS TABLE (
    session_start TIMESTAMPTZ,
    session_end TIMESTAMPTZ,
    total_events BIGINT,
    pages_visited TEXT[],
    navigation_methods TEXT[],
    error_count BIGINT,
    interaction_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        MIN(timestamp) as session_start,
        MAX(timestamp) as session_end,
        COUNT(*) as total_events,
        ARRAY_AGG(DISTINCT to_page) FILTER (WHERE to_page IS NOT NULL) as pages_visited,
        ARRAY_AGG(DISTINCT navigation_method) FILTER (WHERE navigation_method IS NOT NULL) as navigation_methods,
        COUNT(*) FILTER (WHERE event_type = 'error') as error_count,
        COUNT(*) FILTER (WHERE event_type = 'interaction') as interaction_count
    FROM user_activity_logs
    WHERE user_id = p_user_id
        AND session_id = p_session_id
    GROUP BY user_id, session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
