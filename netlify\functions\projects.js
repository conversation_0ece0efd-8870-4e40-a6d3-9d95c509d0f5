// Project-related functions using Supabase
const { createClient } = require('@supabase/supabase-js');
const { NetlifyJwtVerifier } = require('@serverless-jwt/netlify');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Initialize the JWT verifier
const verifyJwt = NetlifyJwtVerifier({
  issuer: process.env.SITE_URL,
  audience: process.env.SITE_URL
});

// Get all projects
const getProjects = async () => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*');

    if (error) throw error;

    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Error getting projects", error: error.message })
    };
  }
};

// Get a single project
const getProject = async (event) => {
  const id = event.path.split('/').pop();

  try {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        contributions(*),
        contributions.user:users(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: "Error getting project", error: error.message })
    };
  }
};

// Create a new project
const createProject = async (event, context) => {
  const user = context.clientContext.user;
  const data = JSON.parse(event.body);

  try {
    const { data: result, error } = await supabase
      .from('projects')
      .insert([
        {
          ...data,
          created_by: user.sub,
          date_created: new Date().toISOString(),
          has_started: false,
          is_active: true,
          is_complete: false
        }
      ])
      .select()
      .single();

    if (error) throw error;

    return {
      statusCode: 201,
      body: JSON.stringify(result)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Error creating project", error: error.message })
    };
  }
};

// Route requests to the appropriate handler
exports.handler = verifyJwt(async (event, context) => {
  const path = event.path.replace('/.netlify/functions/projects', '');

  if (event.httpMethod === 'GET') {
    if (path === '' || path === '/') {
      return getProjects();
    } else {
      return getProject(event);
    }
  } else if (event.httpMethod === 'POST') {
    return createProject(event, context);
  }

  return {
    statusCode: 405,
    body: JSON.stringify({ message: "Method not allowed" })
  };
});
