import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button,
  Chip,
  Progress,
  Avatar,
  Divider,
  Textarea,
  Input,
  Select,
  SelectItem,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@heroui/react';
import { 
  Clock, 
  User, 
  Star, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Eye,
  MessageSquare,
  Calendar,
  Award
} from 'lucide-react';
import { useAuth } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { formatDate, formatDateTime } from '../../utils/formatters';

const PendingReview = () => {
  const { currentUser: user } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [reviewData, setReviewData] = useState({
    decision: '',
    overall_score: '',
    strengths: '',
    weaknesses: '',
    recommendations: '',
    criteria_scores: {}
  });
  
  const [applications, setApplications] = useState([]);
  const [criteria, setCriteria] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (user) {
      fetchPendingApplications();
      fetchVettingCriteria();
    }
  }, [user]);

  const fetchPendingApplications = async () => {
    try {
      // Fetch applications assigned to current user for review
      const { data: assignments, error: assignError } = await supabase
        .from('reviewer_assignments')
        .select(`
          *,
          application:application_id(
            *,
            user:user_id(full_name, avatar_url, email),
            reviews:vetting_reviews(*)
          )
        `)
        .eq('reviewer_id', user.id)
        .is('completed_at', null)
        .order('assigned_at', { ascending: true });

      if (assignError) throw assignError;

      setApplications(assignments?.map(a => a.application) || []);
    } catch (error) {
      console.error('Error fetching pending applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchVettingCriteria = async () => {
    try {
      const { data, error } = await supabase
        .from('vetting_criteria')
        .select('*')
        .eq('is_active', true)
        .order('weight', { ascending: false });

      if (error) throw error;
      setCriteria(data || []);
    } catch (error) {
      console.error('Error fetching criteria:', error);
    }
  };

  const handleReviewApplication = (application) => {
    setSelectedApplication(application);
    setReviewData({
      decision: '',
      overall_score: '',
      strengths: '',
      weaknesses: '',
      recommendations: '',
      criteria_scores: {}
    });
    onOpen();
  };

  const handleCriteriaScoreChange = (criterionId, score) => {
    setReviewData(prev => ({
      ...prev,
      criteria_scores: {
        ...prev.criteria_scores,
        [criterionId]: parseFloat(score)
      }
    }));
  };

  const calculateOverallScore = () => {
    const relevantCriteria = criteria.filter(c => 
      c.application_type === selectedApplication?.application_type
    );
    
    if (relevantCriteria.length === 0) return 0;
    
    let totalWeightedScore = 0;
    let totalWeight = 0;
    
    relevantCriteria.forEach(criterion => {
      const score = reviewData.criteria_scores[criterion.id];
      if (score !== undefined) {
        totalWeightedScore += score * criterion.weight;
        totalWeight += criterion.weight;
      }
    });
    
    return totalWeight > 0 ? (totalWeightedScore / totalWeight).toFixed(2) : 0;
  };

  const handleSubmitReview = async () => {
    if (!selectedApplication || !reviewData.decision) return;
    
    setSubmitting(true);
    try {
      const overallScore = calculateOverallScore();
      
      // Create review record
      const { data: review, error: reviewError } = await supabase
        .from('vetting_reviews')
        .insert({
          application_id: selectedApplication.id,
          reviewer_id: user.id,
          decision: reviewData.decision,
          overall_score: overallScore,
          criteria_scores: reviewData.criteria_scores,
          strengths: reviewData.strengths,
          weaknesses: reviewData.weaknesses,
          recommendations: reviewData.recommendations,
          review_completed_at: new Date().toISOString(),
          is_final_review: true
        })
        .select()
        .single();

      if (reviewError) throw reviewError;

      // Update reviewer assignment
      const { error: assignmentError } = await supabase
        .from('reviewer_assignments')
        .update({ completed_at: new Date().toISOString() })
        .eq('application_id', selectedApplication.id)
        .eq('reviewer_id', user.id);

      if (assignmentError) throw assignmentError;

      // Update application status based on decision
      let newStatus = 'under_review';
      if (reviewData.decision === 'approve') {
        newStatus = 'approved';
      } else if (reviewData.decision === 'reject') {
        newStatus = 'rejected';
      }

      const { error: statusError } = await supabase
        .from('vetting_applications')
        .update({ 
          status: newStatus,
          reviewed_at: new Date().toISOString(),
          ...(newStatus === 'approved' && { approved_at: new Date().toISOString() })
        })
        .eq('id', selectedApplication.id);

      if (statusError) throw statusError;

      // Log workflow change
      const { error: logError } = await supabase
        .from('vetting_workflow_logs')
        .insert({
          application_id: selectedApplication.id,
          user_id: user.id,
          action: 'review_completed',
          from_status: selectedApplication.status,
          to_status: newStatus,
          details: { review_id: review.id, decision: reviewData.decision }
        });

      if (logError) throw logError;

      // Refresh applications list
      await fetchPendingApplications();
      onClose();
      
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'warning',
      under_review: 'primary',
      approved: 'success',
      rejected: 'danger'
    };
    return colors[status] || 'default';
  };

  const getApplicationTypeIcon = (type) => {
    const icons = {
      developer: '💻',
      designer: '🎨',
      project_manager: '📋',
      qa_tester: '🔍',
      business_analyst: '📊'
    };
    return icons[type] || '👤';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardBody className="p-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Pending Reviews</h2>
          <p className="text-gray-600">Applications waiting for your review</p>
        </div>
        <Chip color="primary" variant="flat">
          {applications.length} Pending
        </Chip>
      </div>

      {/* Applications List */}
      {applications.length === 0 ? (
        <Card>
          <CardBody className="text-center py-12">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">All caught up!</h3>
            <p className="text-gray-600">No applications pending your review at the moment.</p>
          </CardBody>
        </Card>
      ) : (
        <div className="space-y-4">
          {applications.map((application) => (
            <Card key={application.id} className="hover:shadow-md transition-shadow">
              <CardBody className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4 flex-1">
                    <Avatar
                      src={application.user?.avatar_url}
                      name={application.user?.full_name}
                      size="lg"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-lg font-semibold">
                          {application.user?.full_name}
                        </h3>
                        <span className="text-2xl">
                          {getApplicationTypeIcon(application.application_type)}
                        </span>
                        <Chip size="sm" variant="flat">
                          {application.application_type.replace('_', ' ')}
                        </Chip>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">Experience</p>
                          <p className="font-medium">{application.years_experience} years</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Rate</p>
                          <p className="font-medium">${application.hourly_rate}/hour</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Submitted</p>
                          <p className="font-medium">{formatDate(application.submitted_at)}</p>
                        </div>
                      </div>
                      
                      {application.primary_skills && application.primary_skills.length > 0 && (
                        <div className="mb-4">
                          <p className="text-sm text-gray-600 mb-2">Primary Skills</p>
                          <div className="flex flex-wrap gap-2">
                            {application.primary_skills.slice(0, 5).map((skill, index) => (
                              <Chip key={index} size="sm" variant="flat" color="primary">
                                {skill}
                              </Chip>
                            ))}
                            {application.primary_skills.length > 5 && (
                              <Chip size="sm" variant="flat">
                                +{application.primary_skills.length - 5} more
                              </Chip>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {application.motivation_statement && (
                        <div>
                          <p className="text-sm text-gray-600 mb-1">Motivation</p>
                          <p className="text-sm line-clamp-2">
                            {application.motivation_statement}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end gap-2">
                    <Chip 
                      color={getStatusColor(application.status)}
                      variant="flat"
                    >
                      {application.status.replace('_', ' ')}
                    </Chip>
                    
                    <Button
                      color="primary"
                      variant="flat"
                      startContent={<Eye className="w-4 h-4" />}
                      onClick={() => handleReviewApplication(application)}
                    >
                      Review
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      )}

      {/* Review Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="4xl" scrollBehavior="inside">
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-3">
              <Avatar
                src={selectedApplication?.user?.avatar_url}
                name={selectedApplication?.user?.full_name}
                size="md"
              />
              <div>
                <h3 className="text-lg font-semibold">
                  Review Application - {selectedApplication?.user?.full_name}
                </h3>
                <p className="text-sm text-gray-600">
                  {selectedApplication?.application_type.replace('_', ' ')}
                </p>
              </div>
            </div>
          </ModalHeader>
          <ModalBody>
            {selectedApplication && (
              <div className="space-y-6">
                {/* Criteria Scoring */}
                <div>
                  <h4 className="text-lg font-semibold mb-4">Evaluation Criteria</h4>
                  <div className="space-y-4">
                    {criteria
                      .filter(c => c.application_type === selectedApplication.application_type)
                      .map((criterion) => (
                        <div key={criterion.id} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div>
                              <h5 className="font-medium">{criterion.criterion_name}</h5>
                              <p className="text-sm text-gray-600">{criterion.criterion_description}</p>
                            </div>
                            <Chip size="sm" variant="flat">
                              Weight: {criterion.weight}x
                            </Chip>
                          </div>
                          <Input
                            type="number"
                            placeholder={`Score (${criterion.min_score}-${criterion.max_score})`}
                            min={criterion.min_score}
                            max={criterion.max_score}
                            step="0.1"
                            value={reviewData.criteria_scores[criterion.id] || ''}
                            onChange={(e) => handleCriteriaScoreChange(criterion.id, e.target.value)}
                          />
                        </div>
                      ))}
                  </div>
                  
                  {Object.keys(reviewData.criteria_scores).length > 0 && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                      <p className="font-medium">Overall Score: {calculateOverallScore()}/10</p>
                      <Progress 
                        value={calculateOverallScore() * 10} 
                        className="mt-2"
                        color="primary"
                      />
                    </div>
                  )}
                </div>

                <Divider />

                {/* Review Decision */}
                <div>
                  <h4 className="text-lg font-semibold mb-4">Review Decision</h4>
                  <Select
                    placeholder="Select decision"
                    selectedKeys={reviewData.decision ? [reviewData.decision] : []}
                    onSelectionChange={(keys) => setReviewData(prev => ({ 
                      ...prev, 
                      decision: Array.from(keys)[0] 
                    }))}
                  >
                    <SelectItem key="approve">Approve</SelectItem>
                    <SelectItem key="reject">Reject</SelectItem>
                    <SelectItem key="request_more_info">Request More Information</SelectItem>
                    <SelectItem key="escalate">Escalate to Senior Reviewer</SelectItem>
                  </Select>
                </div>

                {/* Review Comments */}
                <div className="space-y-4">
                  <Textarea
                    label="Strengths"
                    placeholder="What are the applicant's key strengths?"
                    value={reviewData.strengths}
                    onChange={(e) => setReviewData(prev => ({ ...prev, strengths: e.target.value }))}
                  />
                  
                  <Textarea
                    label="Areas for Improvement"
                    placeholder="What areas could the applicant improve?"
                    value={reviewData.weaknesses}
                    onChange={(e) => setReviewData(prev => ({ ...prev, weaknesses: e.target.value }))}
                  />
                  
                  <Textarea
                    label="Recommendations"
                    placeholder="Any recommendations for the applicant or next steps?"
                    value={reviewData.recommendations}
                    onChange={(e) => setReviewData(prev => ({ ...prev, recommendations: e.target.value }))}
                  />
                </div>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleSubmitReview}
              isLoading={submitting}
              isDisabled={!reviewData.decision}
            >
              Submit Review
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default PendingReview;
