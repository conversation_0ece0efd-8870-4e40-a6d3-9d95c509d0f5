# Color System
**Royaltea Platform Color Palette & Usage Guidelines**

## 🎨 **Primary Color Palette**

### **Brand Colors**
```
Royal Purple (Primary)
- royal-50:  #faf7ff
- royal-100: #f0e8ff  
- royal-200: #e1d4ff
- royal-300: #c8b5ff
- royal-400: #a688ff
- royal-500: #8b5cf6  ← Primary brand color
- royal-600: #7c3aed
- royal-700: #6d28d9
- royal-800: #5b21b6
- royal-900: #4c1d95

Tea Green (Secondary)
- tea-50:  #f0fdf4
- tea-100: #dcfce7
- tea-200: #bbf7d0
- tea-300: #86efac
- tea-400: #4ade80
- tea-500: #22c55e  ← Secondary brand color
- tea-600: #16a34a
- tea-700: #15803d
- tea-800: #166534
- tea-900: #14532d
```

### **Functional Colors**
```
Success (Green)
- success-50:  #f0fdf4
- success-500: #22c55e
- success-600: #16a34a

Warning (Amber)
- warning-50:  #fffbeb
- warning-500: #f59e0b
- warning-600: #d97706

Error (Red)
- error-50:  #fef2f2
- error-500: #ef4444
- error-600: #dc2626

Info (Blue)
- info-50:  #eff6ff
- info-500: #3b82f6
- info-600: #2563eb
```

### **Neutral Colors**
```
Gray Scale
- gray-50:  #f9fafb
- gray-100: #f3f4f6
- gray-200: #e5e7eb
- gray-300: #d1d5db
- gray-400: #9ca3af
- gray-500: #6b7280
- gray-600: #4b5563
- gray-700: #374151
- gray-800: #1f2937
- gray-900: #111827
```

---

## 🌓 **Theme System**

### **Light Theme**
```
Background Colors:
- bg-primary: white (#ffffff)
- bg-secondary: gray-50 (#f9fafb)
- bg-tertiary: gray-100 (#f3f4f6)

Text Colors:
- text-primary: gray-900 (#111827)
- text-secondary: gray-600 (#4b5563)
- text-tertiary: gray-400 (#9ca3af)

Border Colors:
- border-primary: gray-200 (#e5e7eb)
- border-secondary: gray-300 (#d1d5db)
```

### **Dark Theme**
```
Background Colors:
- bg-primary: gray-900 (#111827)
- bg-secondary: gray-800 (#1f2937)
- bg-tertiary: gray-700 (#374151)

Text Colors:
- text-primary: white (#ffffff)
- text-secondary: gray-300 (#d1d5db)
- text-tertiary: gray-400 (#9ca3af)

Border Colors:
- border-primary: gray-700 (#374151)
- border-secondary: gray-600 (#4b5563)
```

---

## 🎯 **Color Usage Guidelines**

### **Brand Color Usage**
- **Royal Purple**: Primary actions, links, focus states, brand elements
- **Tea Green**: Success states, positive actions, growth indicators
- **Never use**: Brand colors for text on colored backgrounds

### **Functional Color Usage**
- **Success Green**: Completed tasks, positive feedback, growth metrics
- **Warning Amber**: Pending actions, caution states, attention needed
- **Error Red**: Failed states, destructive actions, critical alerts
- **Info Blue**: Informational content, neutral actions, help text

### **Bento Grid Widget Colors**
```
Widget Backgrounds:
- Default: bg-primary (white/gray-900)
- Hover: bg-secondary (gray-50/gray-800)
- Active: bg-tertiary (gray-100/gray-700)
- Selected: royal-50/royal-900

Widget Borders:
- Default: border-primary
- Hover: border-secondary
- Focus: royal-500
- Error: error-500
```

---

## 🔧 **Implementation Instructions**

### **For Coding Agents**
When this file is updated, automatically:

1. **Update Tailwind Config**
```javascript
// tailwind.config.js
theme: {
  extend: {
    colors: {
      royal: {
        50: '#faf7ff',
        // ... all royal colors
      },
      tea: {
        50: '#f0fdf4', 
        // ... all tea colors
      }
    }
  }
}
```

2. **Update CSS Variables**
```css
/* globals.css */
:root {
  --color-royal-500: #8b5cf6;
  --color-tea-500: #22c55e;
  /* ... all color variables */
}

[data-theme="dark"] {
  --color-bg-primary: #111827;
  /* ... dark theme overrides */
}
```

3. **Update HeroUI Theme**
```javascript
// heroui theme override
const theme = {
  colors: {
    primary: {
      50: '#faf7ff',
      500: '#8b5cf6',
      // ... royal color mapping
    }
  }
}
```

### **Color Testing Requirements**
- Ensure 4.5:1 contrast ratio for all text
- Test all color combinations in light/dark themes
- Validate accessibility with screen readers
- Check color blindness compatibility

---

## 📱 **Responsive Color Behavior**

### **Mobile Adaptations**
- Maintain color hierarchy on smaller screens
- Increase contrast for touch targets
- Use stronger colors for important actions

### **Desktop Enhancements**
- Subtle hover states with color transitions
- More nuanced color variations
- Enhanced focus indicators

---

## ✅ **Design Team Checklist**

When updating colors:
- [ ] Specify exact hex values
- [ ] Include both light and dark theme variants
- [ ] Document usage guidelines for each color
- [ ] Test accessibility contrast ratios
- [ ] Provide examples of proper usage
- [ ] Note any breaking changes from previous versions

**Last Updated**: January 16, 2025
**Version**: 1.0
**Status**: Ready for implementation
