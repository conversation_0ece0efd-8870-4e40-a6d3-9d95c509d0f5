import React from 'react';

const WizardNavigation = ({ currentStep, completedSteps, setCurrentStep, totalSteps }) => {
  const steps = [
    {
      number: 1,
      title: 'Project Basics',
      icon: 'bi-info-circle-fill',
      description: 'Define your project name, type, and basic details'
    },
    {
      number: 2,
      title: 'Team & Contributors',
      icon: 'bi-people-fill',
      description: 'Add team members and set their roles'
    },
    {
      number: 3,
      title: 'Royalty Model',
      icon: 'bi-pie-chart-fill',
      description: 'Choose how revenue will be distributed'
    },
    {
      number: 4,
      title: 'Revenue Tranches',
      icon: 'bi-cash-stack',
      description: 'Set up revenue phases and thresholds'
    },
    {
      number: 5,
      title: 'Contribution Tracking',
      icon: 'bi-list-check',
      description: 'Define how contributions will be tracked'
    },
    {
      number: 6,
      title: 'Milestones',
      icon: 'bi-flag-fill',
      description: 'Set project milestones and deliverables'
    },
    {
      number: 7,
      title: 'Review & Agreement',
      icon: 'bi-file-earmark-text-fill',
      description: 'Review project details and generate agreement'
    }
  ];

  const handleStepClick = (stepNumber) => {
    // Only allow navigation to completed steps or the current step
    if (completedSteps.includes(stepNumber) || stepNumber === currentStep) {
      setCurrentStep(stepNumber);
    }
  };

  // Calculate progress percentage
  const progressPercentage = ((completedSteps.length + (currentStep > Math.max(...completedSteps) ? 1 : 0)) / totalSteps) * 100;

  return (
    <div className="wizard-navigation-container">
      {/* Mobile Step Indicator */}
      <div className="mobile-step-indicator">
        <div className="current-step-info">
          <div className="step-icon">
            <i className={`bi ${steps[currentStep - 1].icon}`}></i>
          </div>
          <div className="step-details">
            <div className="step-number-mobile">Step {currentStep} of {totalSteps}</div>
            <div className="step-title-mobile">{steps[currentStep - 1].title}</div>
          </div>
        </div>
        <div className="mobile-progress-bar">
          <div
            className="mobile-progress"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Desktop Wizard Navigation */}
      <div className="wizard-navigation">
        <div className="wizard-steps">
          {steps.map((step) => {
            const isActive = step.number === currentStep;
            const isCompleted = completedSteps.includes(step.number);
            const isClickable = isCompleted || step.number === currentStep;

            return (
              <div
                key={step.number}
                className={`wizard-step ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''} ${isClickable ? 'clickable' : ''}`}
                onClick={() => handleStepClick(step.number)}
                title={isClickable ? `Go to ${step.title}` : 'Complete previous steps first'}
              >
                <div className="step-number">
                  {isCompleted ? (
                    <i className="bi bi-check-lg"></i>
                  ) : (
                    <i className={`bi ${step.icon}`}></i>
                  )}
                </div>
                <div className="step-title">{step.title}</div>
                {step.number < totalSteps && (
                  <div className={`step-connector ${isCompleted ? 'completed' : ''}`}></div>
                )}

                {/* Tooltip with step description */}
                <div className="step-tooltip">
                  <div className="tooltip-content">
                    <div className="tooltip-title">{step.title}</div>
                    <div className="tooltip-description">{step.description}</div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="wizard-progress">
          <div
            className="progress-bar"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Step Description */}
      <div className="step-description-container">
        <div className="step-description-icon">
          <i className={`bi ${steps[currentStep - 1].icon}`}></i>
        </div>
        <div className="step-description-text">
          {steps[currentStep - 1].description}
        </div>
      </div>
    </div>
  );
};

export default WizardNavigation;
