// CRITICAL COMPLIANCE: Company Management API
// Day 1 - Developer 2: API endpoints for legal entity management

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers for all responses
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Validation functions
function validateCompanyData(data) {
  const errors = [];

  // Required fields
  if (!data.legal_name?.trim()) errors.push('Legal name is required');
  if (!data.tax_id?.trim()) errors.push('Tax ID is required');
  if (!data.company_type) errors.push('Company type is required');
  if (!data.primary_email?.trim()) errors.push('Primary email is required');
  if (!data.primary_address) errors.push('Primary address is required');

  // Validate company type
  const validTypes = ['corporation', 'llc', 'partnership', 'sole_proprietorship'];
  if (data.company_type && !validTypes.includes(data.company_type)) {
    errors.push('Invalid company type');
  }

  // Validate US EIN format
  if (data.incorporation_country === 'US' && data.tax_id) {
    const einRegex = /^\d{2}-\d{7}$/;
    if (!einRegex.test(data.tax_id)) {
      errors.push('Invalid US EIN format. Must be XX-XXXXXXX');
    }
  }

  // Validate email format
  if (data.primary_email) {
    const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
    if (!emailRegex.test(data.primary_email)) {
      errors.push('Invalid email format');
    }
  }

  // Validate address structure
  if (data.primary_address) {
    const addr = data.primary_address;
    if (!addr.street?.trim()) errors.push('Address street is required');
    if (!addr.city?.trim()) errors.push('Address city is required');
    if (!addr.state?.trim()) errors.push('Address state is required');
    if (!addr.zip?.trim()) errors.push('Address zip is required');
  }

  return errors;
}

// Get user from authorization header
async function getUser(authHeader) {
  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  const { data: { user }, error } = await supabase.auth.getUser(token);

  if (error || !user) {
    throw new Error('Invalid authentication token');
  }

  return user;
}

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const { httpMethod, path, headers, body } = event;
    const authHeader = headers.authorization || headers.Authorization;

    // Get authenticated user
    const user = await getUser(authHeader);

    // Set user context for RLS
    await supabase.rpc('set_user_context', { user_id: user.id });

    // Route handling
    const pathParts = path.split('/').filter(p => p);
    const companyId = pathParts[pathParts.length - 1];

    switch (httpMethod) {
      case 'GET':
        if (companyId && companyId !== 'companies') {
          // Get specific company
          return await getCompany(companyId);
        } else {
          // List companies
          return await listCompanies(user.id);
        }

      case 'POST':
        // Create company
        const createData = JSON.parse(body);
        return await createCompany(createData, user.id);

      case 'PUT':
        // Update company
        const updateData = JSON.parse(body);
        return await updateCompany(companyId, updateData, user.id);

      case 'DELETE':
        // Delete company (soft delete)
        return await deleteCompany(companyId, user.id);

      default:
        return {
          statusCode: 405,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

  } catch (error) {
    console.error('Company API Error:', error);
    return {
      statusCode: error.message.includes('authentication') ? 401 : 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString()
      })
    };
  }
};

// List companies for user
async function listCompanies(userId) {
  const { data, error } = await supabase
    .from('companies')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch companies: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      companies: data,
      count: data.length,
      timestamp: new Date().toISOString()
    })
  };
}

// Get specific company
async function getCompany(companyId) {
  const { data, error } = await supabase
    .from('companies')
    .select(`
      *,
      teams:teams(id, name, is_business_entity)
    `)
    .eq('id', companyId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Company not found' })
      };
    }
    throw new Error(`Failed to fetch company: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      company: data,
      timestamp: new Date().toISOString()
    })
  };
}

// Create new company
async function createCompany(data, userId) {
  // Validate input data
  const errors = validateCompanyData(data);
  if (errors.length > 0) {
    return {
      statusCode: 400,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Validation failed',
        details: errors
      })
    };
  }

  // Prepare company data
  const companyData = {
    legal_name: data.legal_name.trim(),
    tax_id: data.tax_id.trim(),
    company_type: data.company_type,
    incorporation_state: data.incorporation_state,
    incorporation_country: data.incorporation_country || 'US',
    incorporation_date: data.incorporation_date,
    doing_business_as: data.doing_business_as?.trim(),
    industry_classification: data.industry_classification,
    business_description: data.business_description?.trim(),
    website_url: data.website_url?.trim(),
    primary_address: data.primary_address,
    mailing_address: data.mailing_address,
    primary_email: data.primary_email.trim(),
    primary_phone: data.primary_phone?.trim(),
    fiscal_year_end: data.fiscal_year_end,
    accounting_method: data.accounting_method || 'accrual',
    base_currency: data.base_currency || 'USD',
    created_by: userId
  };

  const { data: company, error } = await supabase
    .from('companies')
    .insert(companyData)
    .select()
    .single();

  if (error) {
    if (error.code === '23505') { // Unique constraint violation
      return {
        statusCode: 409,
        headers: corsHeaders,
        body: JSON.stringify({
          error: 'Company with this Tax ID already exists'
        })
      };
    }
    throw new Error(`Failed to create company: ${error.message}`);
  }

  return {
    statusCode: 201,
    headers: corsHeaders,
    body: JSON.stringify({
      company,
      message: 'Company created successfully',
      timestamp: new Date().toISOString()
    })
  };
}

// Update company
async function updateCompany(companyId, data, userId) {
  // Validate input data
  const errors = validateCompanyData(data);
  if (errors.length > 0) {
    return {
      statusCode: 400,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Validation failed',
        details: errors
      })
    };
  }

  // Remove fields that shouldn't be updated
  const { created_by, created_at, ...updateData } = data;

  const { data: company, error } = await supabase
    .from('companies')
    .update(updateData)
    .eq('id', companyId)
    .select()
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Company not found' })
      };
    }
    throw new Error(`Failed to update company: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      company,
      message: 'Company updated successfully',
      timestamp: new Date().toISOString()
    })
  };
}

// Delete company (soft delete)
async function deleteCompany(companyId, userId) {
  const { data: company, error } = await supabase
    .from('companies')
    .update({
      is_active: false,
      compliance_status: 'dissolved',
      dissolution_date: new Date().toISOString().split('T')[0]
    })
    .eq('id', companyId)
    .select()
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Company not found' })
      };
    }
    throw new Error(`Failed to delete company: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      company,
      message: 'Company deactivated successfully',
      timestamp: new Date().toISOString()
    })
  };
}
