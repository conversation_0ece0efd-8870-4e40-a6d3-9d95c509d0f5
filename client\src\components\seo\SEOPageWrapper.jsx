import React from 'react';
import SEOMeta from './SEOMeta';
import { SkipLink } from '../../utils/accessibility';

/**
 * SEO Page Wrapper - Optimized Page Container
 * 
 * This wrapper provides SEO optimization and accessibility
 * features for all pages in the application.
 */
const SEOPageWrapper = ({
  title,
  description,
  keywords,
  image,
  structuredData,
  children,
  className = '',
  skipLinkTarget = '#main-content'
}) => {
  return (
    <>
      <SEOMeta
        title={title}
        description={description}
        keywords={keywords}
        image={image}
        structuredData={structuredData}
      />
      
      <SkipLink href={skipLinkTarget} />
      
      <div className={`min-h-screen ${className}`}>
        <main id="main-content" tabIndex="-1">
          {children}
        </main>
      </div>
    </>
  );
};

export default SEOPageWrapper;