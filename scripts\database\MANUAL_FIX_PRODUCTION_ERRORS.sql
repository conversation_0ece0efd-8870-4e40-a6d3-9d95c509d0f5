-- MANUAL FIX FOR PRODUCTION ERRORS
-- Run this SQL directly in Supabase Dashboard > SQL Editor

-- ============================================================================
-- 1. CREATE MISSING user_activity TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.user_activity (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    activity_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON public.user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON public.user_activity(created_at DESC);

-- Enable RLS
ALTER TABLE public.user_activity ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Users can view own activity" ON public.user_activity
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own activity" ON public.user_activity
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- 2. ADD MISSING status COLUMN TO team_members
-- ============================================================================

-- Add status column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'team_members' 
        AND column_name = 'status'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.team_members 
        ADD COLUMN status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending', 'removed'));
    END IF;
END $$;

-- ============================================================================
-- 3. CREATE MISSING activity_feeds TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.activity_feeds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    actor_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    alliance_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    conversation_id UUID,
    activity_type TEXT NOT NULL,
    activity_title TEXT NOT NULL,
    activity_description TEXT,
    view_count INTEGER DEFAULT 0,
    reaction_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 4. CREATE MISSING activity_reactions TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.activity_reactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_id UUID REFERENCES public.activity_feeds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction_type TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(activity_id, user_id, reaction_type)
);

-- ============================================================================
-- 5. CREATE MISSING conversations TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    conversation_type TEXT DEFAULT 'general',
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- 6. ADD INDEXES FOR PERFORMANCE
-- ============================================================================

-- Activity feeds indexes
CREATE INDEX IF NOT EXISTS idx_activity_feeds_actor ON public.activity_feeds(actor_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_target ON public.activity_feeds(target_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_project ON public.activity_feeds(project_id, created_at DESC);

-- Activity reactions indexes
CREATE INDEX IF NOT EXISTS idx_activity_reactions_activity ON public.activity_reactions(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_reactions_user ON public.activity_reactions(user_id);

-- Team members indexes
CREATE INDEX IF NOT EXISTS idx_team_members_status ON public.team_members(status);

-- ============================================================================
-- 7. ENABLE RLS ON NEW TABLES
-- ============================================================================

ALTER TABLE public.activity_feeds ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversations ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Users can view activity feeds" ON public.activity_feeds
    FOR SELECT USING (true);

CREATE POLICY "Users can create activity feeds" ON public.activity_feeds
    FOR INSERT WITH CHECK (auth.uid() = actor_id);

CREATE POLICY "Users can view reactions" ON public.activity_reactions
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own reactions" ON public.activity_reactions
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view conversations" ON public.conversations
    FOR SELECT USING (true);

CREATE POLICY "Users can create conversations" ON public.conversations
    FOR INSERT WITH CHECK (auth.uid() = created_by);

-- ============================================================================
-- 8. VERIFICATION QUERIES
-- ============================================================================

-- Check if tables were created
SELECT 'Tables created successfully' as status;

SELECT table_name, 
       (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
FROM information_schema.tables t
WHERE t.table_schema = 'public' 
AND t.table_name IN ('user_activity', 'activity_feeds', 'activity_reactions', 'conversations')
ORDER BY t.table_name;

-- Check if status column was added to team_members
SELECT 'team_members status column' as check_name,
       CASE WHEN EXISTS (
           SELECT 1 FROM information_schema.columns 
           WHERE table_name = 'team_members' 
           AND column_name = 'status'
       ) THEN 'EXISTS' ELSE 'MISSING' END as status;

-- Show sample data structure
SELECT 'user_activity structure' as table_name, column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'user_activity' AND table_schema = 'public'
ORDER BY ordinal_position;

COMMENT ON TABLE public.user_activity IS 'User activity tracking for analytics and debugging';
COMMENT ON TABLE public.activity_feeds IS 'Activity feed for user interactions and notifications';
COMMENT ON TABLE public.activity_reactions IS 'Reactions to activity feed items';
COMMENT ON TABLE public.conversations IS 'Conversation threads for collaboration';
