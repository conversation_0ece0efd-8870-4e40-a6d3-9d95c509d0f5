import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Chip, Avatar } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import BountyCard from './BountyCard';
import BountyApplicationModal from './BountyApplicationModal';
import BountyPostingModal from './BountyPostingModal';

/**
 * Bounty Board Component - Public Task Marketplace
 * 
 * Features:
 * - Competitive marketplace for high-value bounties
 * - Skill verification and portfolio requirements
 * - Application process with qualification assessment
 * - Category-based organization and filtering
 * - Real-time bounty updates and notifications
 * - Integration with existing task and payment systems
 */
const BountyBoard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [bounties, setBounties] = useState([]);
  const [filteredBounties, setFilteredBounties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBounty, setSelectedBounty] = useState(null);
  const [showApplicationModal, setShowApplicationModal] = useState(false);
  const [showPostingModal, setShowPostingModal] = useState(false);
  
  // Filter states
  const [filters, setFilters] = useState({
    category: 'all',
    valueRange: 'all',
    timeline: 'all',
    difficulty: 'all',
    requirements: []
  });
  
  // Bounty marketplace statistics
  const [bountyStats, setBountyStats] = useState({
    totalBounties: 0,
    totalValue: 0,
    myApplications: 0,
    myEarnings: 0,
    successRate: 0
  });

  // Load bounties from marketplace (mock data for now)
  const loadBounties = async () => {
    try {
      setLoading(true);
      
      // Mock bounty marketplace data
      const mockBounties = [
        {
          id: '1',
          title: 'AI-Powered Analytics Dashboard',
          description: 'Build comprehensive AI analytics dashboard with machine learning models for sales forecasting and customer behavior analysis.',
          value: 15000,
          paymentType: 'milestone',
          timeline: '8 weeks',
          difficulty: 9,
          category: 'development',
          subcategory: 'full-stack',
          skillsRequired: ['AI/ML', 'Python', 'React', 'Data Science', 'APIs'],
          poster: {
            id: 'poster1',
            name: 'TechCorp Projects',
            rating: 4.9,
            reviewCount: 127,
            verified: true
          },
          applicantCount: 12,
          viewCount: 45,
          postedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
          featured: true,
          urgent: false,
          requirements: {
            portfolio: true,
            skillVerification: true,
            certification: true,
            minRating: 4.5,
            experience: 'expert'
          },
          milestones: [
            { name: 'Project Start', percentage: 25, amount: 3750 },
            { name: 'Milestone Completion', percentage: 50, amount: 7500 },
            { name: 'Final Delivery', percentage: 25, amount: 3750 }
          ],
          tags: ['AI', 'Dashboard', 'Machine Learning']
        },
        {
          id: '2',
          title: 'Mobile App Security Audit',
          description: 'Comprehensive security audit of React Native financial app with penetration testing and vulnerability assessment.',
          value: 8500,
          paymentType: 'fixed',
          timeline: '3 weeks',
          difficulty: 8,
          category: 'testing',
          subcategory: 'security',
          skillsRequired: ['Security', 'Mobile', 'Penetration Testing', 'React Native'],
          poster: {
            id: 'poster2',
            name: 'SecureFinance Studio',
            rating: 4.8,
            reviewCount: 89,
            verified: true
          },
          applicantCount: 8,
          viewCount: 32,
          postedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          deadline: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
          featured: true,
          urgent: true,
          requirements: {
            portfolio: true,
            skillVerification: true,
            certification: false,
            minRating: 4.0,
            experience: 'advanced'
          },
          tags: ['Security', 'Mobile', 'Audit']
        },
        {
          id: '3',
          title: 'E-commerce Platform Integration',
          description: 'Integrate Shopify API with custom React dashboard for inventory management and sales analytics.',
          value: 4200,
          paymentType: 'fixed',
          timeline: '4 weeks',
          difficulty: 6,
          category: 'development',
          subcategory: 'frontend',
          skillsRequired: ['React', 'APIs', 'E-commerce', 'Node.js'],
          poster: {
            id: 'poster3',
            name: 'ShopFlow Projects',
            rating: 4.6,
            reviewCount: 45,
            verified: true
          },
          applicantCount: 5,
          viewCount: 18,
          postedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
          featured: false,
          urgent: false,
          requirements: {
            portfolio: true,
            skillVerification: false,
            certification: false,
            minRating: 4.0,
            experience: 'intermediate'
          },
          tags: ['E-commerce', 'Integration', 'React']
        },
        {
          id: '4',
          title: 'Blockchain Smart Contract Audit',
          description: 'Security audit of DeFi smart contracts on Ethereum with comprehensive testing and documentation.',
          value: 12000,
          paymentType: 'milestone',
          timeline: '6 weeks',
          difficulty: 9,
          category: 'development',
          subcategory: 'blockchain',
          skillsRequired: ['Solidity', 'Security', 'Blockchain', 'Smart Contracts'],
          poster: {
            id: 'poster4',
            name: 'CryptoSecure Studio',
            rating: 4.9,
            reviewCount: 67,
            verified: true
          },
          applicantCount: 3,
          viewCount: 28,
          postedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
          deadline: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // 10 days from now
          featured: false,
          urgent: false,
          requirements: {
            portfolio: true,
            skillVerification: true,
            certification: true,
            minRating: 4.5,
            experience: 'expert'
          },
          tags: ['Blockchain', 'Smart Contracts', 'Security']
        },
        {
          id: '5',
          title: 'Real-time Chat System',
          description: 'Build scalable real-time chat system with WebSocket, Redis, and React for messaging platform.',
          value: 3800,
          paymentType: 'fixed',
          timeline: '6 weeks',
          difficulty: 7,
          category: 'development',
          subcategory: 'backend',
          skillsRequired: ['WebSocket', 'Redis', 'Node.js', 'React', 'Real-time'],
          poster: {
            id: 'poster5',
            name: 'ConnectApp Projects',
            rating: 4.7,
            reviewCount: 34,
            verified: true
          },
          applicantCount: 9,
          viewCount: 41,
          postedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          deadline: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
          featured: false,
          urgent: false,
          requirements: {
            portfolio: true,
            skillVerification: false,
            certification: false,
            minRating: 4.0,
            experience: 'advanced'
          },
          tags: ['Real-time', 'Chat', 'WebSocket']
        }
      ];
      
      setBounties(mockBounties);
      setFilteredBounties(mockBounties);
      updateBountyStats(mockBounties);
      
    } catch (error) {
      console.error('Error loading bounties:', error);
      toast.error('Failed to load bounty marketplace');
    } finally {
      setLoading(false);
    }
  };

  // Update bounty statistics
  const updateBountyStats = (bountyList) => {
    const stats = {
      totalBounties: bountyList.length,
      totalValue: bountyList.reduce((sum, bounty) => sum + bounty.value, 0),
      myApplications: 3, // Mock data - would come from user's application history
      myEarnings: 18400, // Mock data - would come from user's completed bounties
      successRate: 85 // Mock data - would be calculated from user's history
    };
    setBountyStats(stats);
  };

  // Format currency display
  const formatCurrency = (amount) => {
    if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(1)}K`;
    }
    return `$${amount}`;
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty) => {
    if (difficulty >= 9) return 'danger';
    if (difficulty >= 7) return 'warning';
    if (difficulty >= 5) return 'primary';
    return 'success';
  };

  // Get time since posted
  const getTimeSincePosted = (postedAt) => {
    const now = new Date();
    const diffMs = now - postedAt;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    return 'Just posted';
  };

  // Get time until deadline
  const getTimeUntilDeadline = (deadline) => {
    const now = new Date();
    const diffMs = deadline - now;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} left`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} left`;
    return 'Ending soon';
  };

  // Apply filters and search
  const applyFiltersAndSearch = () => {
    let filtered = [...bounties];

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(bounty =>
        bounty.title?.toLowerCase().includes(searchLower) ||
        bounty.description?.toLowerCase().includes(searchLower) ||
        bounty.skillsRequired.some(skill => skill.toLowerCase().includes(searchLower)) ||
        bounty.poster.name?.toLowerCase().includes(searchLower) ||
        bounty.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply category filter
    if (filters.category !== 'all') {
      filtered = filtered.filter(bounty => bounty.category === filters.category);
    }

    // Apply value range filter
    if (filters.valueRange !== 'all') {
      if (filters.valueRange === 'low') {
        filtered = filtered.filter(bounty => bounty.value < 5000);
      } else if (filters.valueRange === 'medium') {
        filtered = filtered.filter(bounty => bounty.value >= 5000 && bounty.value < 15000);
      } else if (filters.valueRange === 'high') {
        filtered = filtered.filter(bounty => bounty.value >= 15000);
      }
    }

    // Apply difficulty filter
    if (filters.difficulty !== 'all') {
      if (filters.difficulty === 'beginner') {
        filtered = filtered.filter(bounty => bounty.difficulty <= 4);
      } else if (filters.difficulty === 'intermediate') {
        filtered = filtered.filter(bounty => bounty.difficulty >= 5 && bounty.difficulty <= 6);
      } else if (filters.difficulty === 'advanced') {
        filtered = filtered.filter(bounty => bounty.difficulty >= 7 && bounty.difficulty <= 8);
      } else if (filters.difficulty === 'expert') {
        filtered = filtered.filter(bounty => bounty.difficulty >= 9);
      }
    }

    setFilteredBounties(filtered);
  };

  // Handle bounty application
  const handleBountyApplication = (bountyId, applicationData) => {
    try {
      // Mock application submission
      toast.success('Application submitted successfully!');

      // Update bounty applicant count
      setBounties(prevBounties =>
        prevBounties.map(bounty =>
          bounty.id === bountyId
            ? { ...bounty, applicantCount: bounty.applicantCount + 1 }
            : bounty
        )
      );

      setShowApplicationModal(false);

    } catch (error) {
      console.error('Error submitting application:', error);
      toast.error('Failed to submit application');
    }
  };

  // Initialize component
  useEffect(() => {
    loadBounties();
  }, []);

  // Apply filters when they change
  useEffect(() => {
    applyFiltersAndSearch();
  }, [bounties, searchTerm, filters]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading bounty marketplace...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bounty-board ${className}`}>
      {/* Bounty Board Header */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
          🎯 Bounty Board
        </h1>
        <p className="text-default-600">
          Discover high-value bounties and compete for premium opportunities
        </p>
      </div>

      {/* Bounty Statistics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{bountyStats.totalBounties}</div>
            <div className="text-sm text-default-600">Live Bounties</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{formatCurrency(bountyStats.totalValue)}</div>
            <div className="text-sm text-default-600">Total Value</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{bountyStats.myApplications}</div>
            <div className="text-sm text-default-600">My Applications</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{formatCurrency(bountyStats.myEarnings)}</div>
            <div className="text-sm text-default-600">Total Earned</div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{bountyStats.successRate}%</div>
            <div className="text-sm text-default-600">Success Rate</div>
          </CardBody>
        </Card>
      </div>

      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <Input
                placeholder="Search bounties by title, skills, or keywords..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<span className="text-default-400">🔍</span>}
                className="w-full"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex gap-2 flex-wrap">
              <Select
                placeholder="Category"
                selectedKeys={[filters.category]}
                onSelectionChange={(keys) => setFilters({...filters, category: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Categories</SelectItem>
                <SelectItem key="development">Development</SelectItem>
                <SelectItem key="design">Design</SelectItem>
                <SelectItem key="testing">Testing</SelectItem>
                <SelectItem key="writing">Writing</SelectItem>
              </Select>

              <Select
                placeholder="Value"
                selectedKeys={[filters.valueRange]}
                onSelectionChange={(keys) => setFilters({...filters, valueRange: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Values</SelectItem>
                <SelectItem key="low">Under $5K</SelectItem>
                <SelectItem key="medium">$5K-$15K</SelectItem>
                <SelectItem key="high">$15K+</SelectItem>
              </Select>

              <Select
                placeholder="Difficulty"
                selectedKeys={[filters.difficulty]}
                onSelectionChange={(keys) => setFilters({...filters, difficulty: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Levels</SelectItem>
                <SelectItem key="beginner">Beginner</SelectItem>
                <SelectItem key="intermediate">Intermediate</SelectItem>
                <SelectItem key="advanced">Advanced</SelectItem>
                <SelectItem key="expert">Expert</SelectItem>
              </Select>

              <Button
                color="primary"
                variant="flat"
                onClick={() => setShowPostingModal(true)}
                className="whitespace-nowrap"
              >
                Post Bounty
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Bounty Grid - Bento Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AnimatePresence>
          {filteredBounties.map((bounty, index) => (
            <BountyCard
              key={bounty.id}
              bounty={bounty}
              index={index}
              onApply={() => {
                setSelectedBounty(bounty);
                setShowApplicationModal(true);
              }}
              onView={() => {
                setSelectedBounty(bounty);
                // Could open a detailed view modal
              }}
              currentUser={currentUser}
              formatCurrency={formatCurrency}
              getDifficultyColor={getDifficultyColor}
              getTimeSincePosted={getTimeSincePosted}
              getTimeUntilDeadline={getTimeUntilDeadline}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredBounties.length === 0 && (
        <Card className="mt-8">
          <CardBody className="p-8 text-center">
            <div className="text-6xl mb-4">🎯</div>
            <h3 className="text-xl font-semibold mb-2">No bounties found</h3>
            <p className="text-default-600 mb-4">
              {searchTerm || Object.values(filters).some(f => f !== 'all')
                ? 'Try adjusting your search or filters'
                : 'No bounties available at the moment'
              }
            </p>
            {(searchTerm || Object.values(filters).some(f => f !== 'all')) && (
              <Button
                color="primary"
                variant="flat"
                onClick={() => {
                  setSearchTerm('');
                  setFilters({
                    category: 'all',
                    valueRange: 'all',
                    timeline: 'all',
                    difficulty: 'all',
                    requirements: []
                  });
                }}
              >
                Clear Filters
              </Button>
            )}
          </CardBody>
        </Card>
      )}

      {/* Application Modal */}
      {showApplicationModal && selectedBounty && (
        <BountyApplicationModal
          bounty={selectedBounty}
          isOpen={showApplicationModal}
          onClose={() => setShowApplicationModal(false)}
          onSubmit={(applicationData) => handleBountyApplication(selectedBounty.id, applicationData)}
          currentUser={currentUser}
        />
      )}

      {/* Posting Modal */}
      {showPostingModal && (
        <BountyPostingModal
          isOpen={showPostingModal}
          onClose={() => setShowPostingModal(false)}
          onSubmit={(bountyData) => {
            // Handle bounty posting
            toast.success('Bounty posted successfully!');
            setShowPostingModal(false);
            loadBounties(); // Reload to show new bounty
          }}
          currentUser={currentUser}
        />
      )}
    </div>
  );
};

export default BountyBoard;
