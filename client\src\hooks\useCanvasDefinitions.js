import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Canvas Definitions Hook
 *
 * Provides comprehensive mapping of all application areas to canvas definitions.
 * Each canvas represents a logical grouping of functionality with position,
 * connections, and visual properties for the experimental navigation system.
 *
 * Project-specific canvases are only included when in a project context.
 */
const useCanvasDefinitions = (currentUser, isAdmin = false) => {
  const location = useLocation();

  return useMemo(() => {
    // Check if we're currently in a project context
    const currentPath = location.pathname;
    const isInProjectContext = currentPath.startsWith('/project/') && currentPath.includes('/');
    const projectIdMatch = currentPath.match(/\/project\/([^\/]+)/);
    const hasProjectId = projectIdMatch && projectIdMatch[1] && projectIdMatch[1] !== 'wizard';
    const baseCanvases = {
      // Core Navigation Areas - Central hub
      home: {
        id: 'home',
        title: 'Dashboard',
        icon: '🏠',
        position: { x: 0, y: 0 },
        connections: ['start', 'track', 'earn', 'projects', 'contributions', 'revenue'],
        color: 'from-blue-500 to-purple-600',
        description: 'Your central hub',
        route: '/',
        sections: {
          overview: { component: 'DashboardOverview', props: {} },
          activity: { component: 'RecentActivity', props: {} },
          stats: { component: 'QuickStats', props: {} },
          notifications: { component: 'NotificationCenter', props: {} }
        }
      },

      // START JOURNEY - Left side of home
      start: {
        id: 'start',
        title: 'Start',
        icon: '🚀',
        position: { x: -300, y: 0 },
        connections: ['home', 'wizard', 'projects', 'learn'],
        color: 'from-green-500 to-emerald-600',
        description: 'Begin your journey',
        route: '/start'
        // Removed sections to use full-page StartPage component
      },

      // TRACK JOURNEY - Below home
      track: {
        id: 'track',
        title: 'Track',
        icon: '⏱️',
        position: { x: 0, y: 300 },
        connections: ['home', 'contributions', 'validation', 'kanban', 'missions'],
        color: 'from-orange-500 to-red-600',
        description: 'Track your work',
        route: '/track'
        // Removed sections to force full-page mode with TrackCanvas component
      },

      // EARN JOURNEY - Right side of home
      earn: {
        id: 'earn',
        title: 'Earn',
        icon: '💰',
        position: { x: 300, y: 0 },
        connections: ['home', 'revenue', 'royalty', 'escrow', 'analytics'],
        color: 'from-yellow-500 to-orange-600',
        description: 'Earn from your work',
        route: '/earn'
        // Removed sections to force full-page mode with EarnCanvas component
      },

      // Venture Management Area - Left cluster
      projects: {
        id: 'projects',
        title: 'Ventures',
        icon: '🚀',
        position: { x: -600, y: 0 },
        connections: ['home', 'wizard', 'kanban', 'agreements'],
        color: 'from-green-500 to-emerald-600',
        description: 'Venture management',
        route: '/projects'
        // Removed sections to force full-page mode with ProjectsList component
      },

      wizard: {
        id: 'wizard',
        title: 'Venture Wizard',
        icon: '🧙‍♂️',
        position: { x: -800, y: -200 },
        connections: ['projects', 'agreements'],
        color: 'from-violet-500 to-purple-600',
        description: 'Create new ventures',
        route: '/project/wizard',
        sections: {
          'basic-info': { component: 'BasicInfo', props: {} },
          'team-setup': { component: 'TeamSetup', props: {} },
          'revenue-model': { component: 'RevenueModel', props: {} },
          'review': { component: 'ReviewLaunch', props: {} }
        }
      },

      // Project-specific canvases - only show when in project context
      ...(hasProjectId ? {
        kanban: {
          id: 'kanban',
          title: 'Task Board',
          icon: '📋',
          position: { x: -800, y: 200 },
          connections: ['projects', 'contributions', 'missions'],
          color: 'from-cyan-500 to-blue-600',
          description: 'Project task management',
          route: `/project/${projectIdMatch[1]}/tasks`,
          sections: {
            backlog: { component: 'TaskBoard', props: { status: 'backlog' } },
            'in-progress': { component: 'TaskBoard', props: { status: 'in-progress' } },
            review: { component: 'TaskBoard', props: { status: 'review' } },
            completed: { component: 'TaskBoard', props: { status: 'completed' } }
          }
        }
      } : {}),

      missions: {
        id: 'missions',
        title: 'Mission Board',
        icon: '🎯',
        position: { x: -400, y: 200 },
        connections: ['home', 'kanban', 'contributions', 'track'],
        color: 'from-purple-500 to-indigo-600',
        description: 'Discover and claim missions',
        route: '/missions',
        sections: {
          'mission-board': { component: 'MissionBoard', props: {} },
          'my-missions': { component: 'MyMissions', props: {} },
          'mission-analytics': { component: 'MissionAnalytics', props: {} },
          'bounty-board': { component: 'BountyBoard', props: {} }
        }
      },

      ...(hasProjectId ? {
        agreements: {
          id: 'agreements',
          title: 'Agreements',
          icon: '📄',
          position: { x: -600, y: -400 },
          connections: ['projects', 'wizard', 'revenue'],
          color: 'from-amber-500 to-orange-600',
          description: 'Legal documents',
          route: `/project/${projectIdMatch[1]}/agreements`,
          sections: {
            templates: { component: 'AgreementTemplates', props: {} },
            'active-agreements': { component: 'ActiveAgreements', props: {} },
            signatures: { component: 'SignatureManager', props: {} },
            archive: { component: 'AgreementArchive', props: {} }
          }
        }
      } : {}),

      // Contribution Tracking Area - Right cluster
      contributions: {
        id: 'contributions',
        title: 'Contributions',
        icon: '⏱️',
        position: { x: 800, y: 0 },
        connections: ['home', 'kanban', 'validation', 'analytics'],
        color: 'from-orange-500 to-red-600',
        description: 'Track your work',
        route: '/contributions'
        // Removed sections to force full-page mode with TrackCanvas component
      },

      validation: {
        id: 'validation',
        title: 'Validation',
        icon: '✅',
        position: { x: 800, y: 300 },
        connections: ['contributions', 'analytics'],
        color: 'from-emerald-500 to-green-600',
        description: 'Validate contributions',
        route: '/validation/metrics',
        sections: {
          'pending-review': { component: 'PendingReview', props: {} },
          'validation-metrics': { component: 'ValidationMetrics', props: {} },
          'approval-workflow': { component: 'ApprovalWorkflow', props: {} },
          feedback: { component: 'FeedbackSystem', props: {} }
        }
      },

      // Revenue & Financial Area - Bottom cluster
      revenue: {
        id: 'revenue',
        title: 'Revenue',
        icon: '💰',
        position: { x: 0, y: 600 },
        connections: ['home', 'agreements', 'royalty', 'escrow'],
        color: 'from-yellow-500 to-orange-600',
        description: 'Revenue tracking',
        route: '/revenue',
        sections: {
          'revenue-dashboard': { component: 'RevenueDashboard', props: {} },
          'payment-history': { component: 'PaymentHistory', props: {} },
          projections: { component: 'RevenueProjections', props: {} },
          reports: { component: 'FinancialReports', props: {} }
        }
      },

      ...(hasProjectId ? {
        royalty: {
          id: 'royalty',
          title: 'Royalty Calculator',
          icon: '🧮',
          position: { x: -200, y: 600 },
          connections: ['revenue', 'escrow'],
          color: 'from-pink-500 to-rose-600',
          description: 'Calculate royalties',
          route: `/project/${projectIdMatch[1]}/royalty-calculator`,
          sections: {
            calculator: { component: 'RoyaltyCalculator', props: {} },
            'distribution-model': { component: 'DistributionModel', props: {} },
            'payment-schedule': { component: 'PaymentSchedule', props: {} },
            history: { component: 'RoyaltyHistory', props: {} }
          }
        }
      } : {}),

      ...(hasProjectId ? {
        escrow: {
          id: 'escrow',
          title: 'Escrow',
          icon: '🏦',
          position: { x: 200, y: 600 },
          connections: ['revenue', 'royalty'],
          color: 'from-indigo-500 to-purple-600',
          description: 'Escrow management',
          route: `/project/${projectIdMatch[1]}/revenue`,
          sections: {
            'escrow-accounts': { component: 'EscrowAccounts', props: {} },
            'release-conditions': { component: 'ReleaseConditions', props: {} },
            'transaction-log': { component: 'TransactionLog', props: {} },
            disputes: { component: 'DisputeManager', props: {} }
          }
        }
      } : {}),

      // Analytics Area - Far right cluster
      analytics: {
        id: 'analytics',
        title: 'Analytics',
        icon: '📊',
        position: { x: 1200, y: 0 },
        connections: ['home', 'contributions', 'validation', 'insights'],
        color: 'from-blue-500 to-cyan-600',
        description: 'Performance insights',
        route: '/analytics/contributions'
        // Removed sections to use full-page ContributionAnalyticsPage component
      },

      insights: {
        id: 'insights',
        title: 'AI Insights',
        icon: '🤖',
        position: { x: 1200, y: -300 },
        connections: ['analytics'],
        color: 'from-purple-500 to-violet-600',
        description: 'AI-powered insights',
        route: '/analytics/insights'
        // Removed sections to use full-page InsightsPage component
      },



      // User & Social Area - Top cluster
      profile: {
        id: 'profile',
        title: 'Profile',
        icon: '👤',
        position: { x: 0, y: -300 },
        connections: ['home', 'teams', 'social', 'settings'],
        color: 'from-purple-500 to-pink-600',
        description: 'Your profile',
        route: '/profile'
        // Removed sections to use full-page ProfilePage component
      },

      teams: {
        id: 'teams',
        title: 'Alliances',
        icon: '⚔️',
        position: { x: -200, y: -300 },
        connections: ['profile', 'social'],
        color: 'from-teal-500 to-cyan-600',
        description: 'Alliance management',
        route: '/teams',
        sections: {
          'my-teams': { component: 'MyTeams', props: {} },
          invitations: { component: 'TeamInvitations', props: {} },
          'team-creation': { component: 'TeamCreation', props: {} },
          collaboration: { component: 'CollaborationTools', props: {} }
        }
      },

      social: {
        id: 'social',
        title: 'Social',
        icon: '💬',
        position: { x: 200, y: -300 },
        connections: ['profile', 'teams', 'notifications'],
        color: 'from-rose-500 to-pink-600',
        description: 'Social features',
        route: '/social'
        // Removed sections to use full-page SocialPage component
      },

      // System & Configuration Area - Right side cluster
      settings: {
        id: 'settings',
        title: 'Settings',
        icon: '⚙️',
        position: { x: 400, y: -300 },
        connections: ['profile', 'notifications', 'bugs'],
        color: 'from-gray-500 to-slate-600',
        description: 'Configuration',
        route: '/settings'
        // Removed sections to use full-page SettingsPage component
      },

      notifications: {
        id: 'notifications',
        title: 'Notifications',
        icon: '🔔',
        position: { x: 400, y: -100 },
        connections: ['social', 'settings'],
        color: 'from-yellow-500 to-amber-600',
        description: 'Notifications',
        route: '/notifications'
        // Removed sections to use full-page NotificationsPage component
      },

      bugs: {
        id: 'bugs',
        title: 'Bug Reports',
        icon: '🐛',
        position: { x: 600, y: -300 },
        connections: ['settings'],
        color: 'from-red-500 to-orange-600',
        description: 'Report issues',
        route: '/bugs'
        // Removed sections to use full-page BugReportPage component
      },

      // Learning & Help Area - Left side cluster
      learn: {
        id: 'learn',
        title: 'Learning',
        icon: '🎓',
        position: { x: -400, y: -300 },
        connections: ['profile', 'help'],
        color: 'from-indigo-500 to-blue-600',
        description: 'Learning center',
        route: '/learn'
        // Removed sections to use full-page LearnPage component
      },

      help: {
        id: 'help',
        title: 'Help Center',
        icon: '❓',
        position: { x: -600, y: -300 },
        connections: ['learn'],
        color: 'from-blue-500 to-indigo-600',
        description: 'Get help',
        route: '/help'
        // Removed sections to use full-page HelpPage component
      }
    };

    // Add admin canvases if user is admin - Far right admin cluster
    if (isAdmin) {
      baseCanvases.admin = {
        id: 'admin',
        title: 'Admin',
        icon: '🔧',
        position: { x: 1600, y: 0 },
        connections: ['analytics', 'system'],
        color: 'from-red-500 to-rose-600',
        description: 'Admin tools',
        route: '/admin',
        sections: {
          dashboard: { component: 'AdminDashboard', props: {} },
          'user-management': { component: 'UserManagement', props: {} },
          'system-health': { component: 'SystemHealth', props: {} },
          roadmap: { component: 'RoadmapManager', props: {} }
        }
      };

      baseCanvases.system = {
        id: 'system',
        title: 'System',
        icon: '🖥️',
        position: { x: 1600, y: 300 },
        connections: ['admin'],
        color: 'from-slate-500 to-gray-600',
        description: 'System management',
        route: '/admin/system',
        sections: {
          database: { component: 'DatabaseManager', props: {} },
          migrations: { component: 'MigrationManager', props: {} },
          monitoring: { component: 'SystemMonitoring', props: {} },
          logs: { component: 'SystemLogs', props: {} }
        }
      };
    }

    return baseCanvases;
  }, [currentUser, isAdmin, location.pathname]);
};

export default useCanvasDefinitions;
