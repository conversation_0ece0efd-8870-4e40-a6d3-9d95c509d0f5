import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Switch, Select, SelectItem, Button, Divider } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Privacy Settings Component
 * 
 * Comprehensive privacy controls providing:
 * - Profile visibility settings
 * - Contact information privacy
 * - Activity and status visibility
 * - Search and discovery preferences
 * - Data sharing controls
 * - Communication preferences
 */
const PrivacySettings = ({ userId, isOwnProfile = false }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [privacySettings, setPrivacySettings] = useState({
    profile_visibility: 'public',
    show_email: false,
    show_phone: false,
    show_location: true,
    show_last_active: true,
    show_online_status: true,
    allow_search: true,
    allow_contact: true,
    show_portfolio: true,
    show_skills: true,
    show_experience: true,
    show_reviews: true,
    data_sharing: false,
    marketing_emails: true,
    project_notifications: true
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Visibility options
  const visibilityOptions = [
    { key: 'public', label: 'Public', description: 'Visible to everyone' },
    { key: 'members', label: 'Members Only', description: 'Visible to registered users' },
    { key: 'connections', label: 'Connections Only', description: 'Visible to your connections' },
    { key: 'private', label: 'Private', description: 'Only visible to you' }
  ];

  // Load privacy settings
  useEffect(() => {
    if (userId && isOwnProfile) {
      loadPrivacySettings();
    }
  }, [userId, isOwnProfile]);

  const loadPrivacySettings = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('user_privacy_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        throw error;
      }

      if (data) {
        setPrivacySettings(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      toast.error('Failed to load privacy settings');
    } finally {
      setLoading(false);
    }
  };

  // Save privacy settings
  const savePrivacySettings = async () => {
    try {
      setSaving(true);
      
      const { error } = await supabase
        .from('user_privacy_settings')
        .upsert({
          user_id: userId,
          ...privacySettings,
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast.success('Privacy settings saved successfully');
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      toast.error('Failed to save privacy settings');
    } finally {
      setSaving(false);
    }
  };

  // Update setting
  const updateSetting = (key, value) => {
    setPrivacySettings(prev => ({ ...prev, [key]: value }));
  };

  if (!isOwnProfile) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <span className="text-4xl mb-4 block">🔒</span>
          <h3 className="text-lg font-semibold mb-2">Privacy Settings</h3>
          <p className="text-default-600">
            Privacy settings are only available for your own profile
          </p>
        </CardBody>
      </Card>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="privacy-settings space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div>
              <h2 className="text-xl font-bold">Privacy Settings</h2>
              <p className="text-sm text-default-600">
                Control who can see your information and how you're contacted
              </p>
            </div>
            
            <Button
              color="primary"
              onPress={savePrivacySettings}
              isLoading={saving}
            >
              Save Changes
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Profile Visibility */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">👁️</span>
              <h3 className="text-lg font-semibold">Profile Visibility</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <Select
              label="Who can see your profile"
              selectedKeys={[privacySettings.profile_visibility]}
              onSelectionChange={(keys) => updateSetting('profile_visibility', Array.from(keys)[0])}
            >
              {visibilityOptions.map(option => (
                <SelectItem key={option.key} description={option.description}>
                  {option.label}
                </SelectItem>
              ))}
            </Select>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show in search results</div>
                  <div className="text-sm text-default-600">Allow others to find you</div>
                </div>
                <Switch
                  isSelected={privacySettings.allow_search}
                  onValueChange={(value) => updateSetting('allow_search', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Allow contact</div>
                  <div className="text-sm text-default-600">Let others message you</div>
                </div>
                <Switch
                  isSelected={privacySettings.allow_contact}
                  onValueChange={(value) => updateSetting('allow_contact', value)}
                />
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Contact Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">📞</span>
              <h3 className="text-lg font-semibold">Contact Information</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show email address</div>
                  <div className="text-sm text-default-600">Display your email publicly</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_email}
                  onValueChange={(value) => updateSetting('show_email', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show phone number</div>
                  <div className="text-sm text-default-600">Display your phone publicly</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_phone}
                  onValueChange={(value) => updateSetting('show_phone', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show location</div>
                  <div className="text-sm text-default-600">Display your city/country</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_location}
                  onValueChange={(value) => updateSetting('show_location', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show online status</div>
                  <div className="text-sm text-default-600">Let others see when you're online</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_online_status}
                  onValueChange={(value) => updateSetting('show_online_status', value)}
                />
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Profile Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">📋</span>
              <h3 className="text-lg font-semibold">Profile Content</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show portfolio</div>
                  <div className="text-sm text-default-600">Display your work samples</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_portfolio}
                  onValueChange={(value) => updateSetting('show_portfolio', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show skills</div>
                  <div className="text-sm text-default-600">Display your skills and expertise</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_skills}
                  onValueChange={(value) => updateSetting('show_skills', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show experience</div>
                  <div className="text-sm text-default-600">Display work history</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_experience}
                  onValueChange={(value) => updateSetting('show_experience', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Show reviews</div>
                  <div className="text-sm text-default-600">Display client reviews</div>
                </div>
                <Switch
                  isSelected={privacySettings.show_reviews}
                  onValueChange={(value) => updateSetting('show_reviews', value)}
                />
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Activity & Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">⏰</span>
              <h3 className="text-lg font-semibold">Activity & Status</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Show last active</div>
                <div className="text-sm text-default-600">Let others see when you were last online</div>
              </div>
              <Switch
                isSelected={privacySettings.show_last_active}
                onValueChange={(value) => updateSetting('show_last_active', value)}
              />
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Data & Communications */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">📧</span>
              <h3 className="text-lg font-semibold">Data & Communications</h3>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Marketing emails</div>
                  <div className="text-sm text-default-600">Receive promotional content</div>
                </div>
                <Switch
                  isSelected={privacySettings.marketing_emails}
                  onValueChange={(value) => updateSetting('marketing_emails', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Project notifications</div>
                  <div className="text-sm text-default-600">Get notified about projects</div>
                </div>
                <Switch
                  isSelected={privacySettings.project_notifications}
                  onValueChange={(value) => updateSetting('project_notifications', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Data sharing</div>
                  <div className="text-sm text-default-600">Share anonymized data for platform improvement</div>
                </div>
                <Switch
                  isSelected={privacySettings.data_sharing}
                  onValueChange={(value) => updateSetting('data_sharing', value)}
                />
              </div>
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Save Button */}
      <div className="flex justify-center">
        <Button
          color="primary"
          size="lg"
          onPress={savePrivacySettings}
          isLoading={saving}
          className="px-8"
        >
          Save All Privacy Settings
        </Button>
      </div>
    </div>
  );
};

export default PrivacySettings;
