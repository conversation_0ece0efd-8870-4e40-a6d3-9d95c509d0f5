import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Use hardcoded credentials
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function applyProductionFix() {
  console.log('🔧 Applying production database fixes...\n');

  try {
    // Read the SQL fix file
    const sqlPath = join(__dirname, 'fix-production-errors.sql');
    const sqlContent = readFileSync(sqlPath, 'utf8');

    console.log('📄 Loaded SQL fix file');
    console.log('📝 SQL content length:', sqlContent.length, 'characters');

    // Split SQL into individual statements (basic approach)
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && !stmt.startsWith('/*'));

    console.log('📋 Found', statements.length, 'SQL statements to execute\n');

    let successCount = 0;
    let errorCount = 0;

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.startsWith('/*') || statement.trim().length === 0) {
        continue;
      }

      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
      
      try {
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });

        if (error) {
          // Try alternative method for DDL statements
          console.log('   Trying alternative execution method...');
          
          // For CREATE TABLE and ALTER TABLE statements, we need to use a different approach
          if (statement.includes('CREATE TABLE') || statement.includes('ALTER TABLE') || statement.includes('CREATE INDEX')) {
            console.log('   ⚠️  DDL statement detected - may need manual execution');
            console.log('   Statement:', statement.substring(0, 100) + '...');
            errorCount++;
          } else {
            throw error;
          }
        } else {
          console.log('   ✅ Success');
          successCount++;
        }
      } catch (err) {
        console.log('   ❌ Error:', err.message);
        errorCount++;
        
        // Continue with other statements
        continue;
      }
    }

    console.log('\n📊 Execution Summary:');
    console.log(`✅ Successful: ${successCount}`);
    console.log(`❌ Errors: ${errorCount}`);
    console.log(`📋 Total: ${statements.length}`);

    if (errorCount > 0) {
      console.log('\n⚠️  Some statements failed. This is expected for DDL operations.');
      console.log('   You may need to run the SQL manually in Supabase dashboard.');
    }

    // Test the fixes
    console.log('\n🧪 Testing fixes...');
    await testFixes();

  } catch (error) {
    console.error('❌ Error applying fixes:', error.message);
    process.exit(1);
  }
}

async function testFixes() {
  console.log('🔍 Testing user_activity table...');
  try {
    const { data, error } = await supabase
      .from('user_activity')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('❌ user_activity test failed:', error.message);
    } else {
      console.log('✅ user_activity table accessible');
    }
  } catch (err) {
    console.log('❌ user_activity test error:', err.message);
  }

  console.log('🔍 Testing team_members status column...');
  try {
    const { data, error } = await supabase
      .from('team_members')
      .select('status')
      .limit(1);
    
    if (error) {
      console.log('❌ team_members status test failed:', error.message);
    } else {
      console.log('✅ team_members status column accessible');
    }
  } catch (err) {
    console.log('❌ team_members status test error:', err.message);
  }

  console.log('🔍 Testing activity_feeds table...');
  try {
    const { data, error } = await supabase
      .from('activity_feeds')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('❌ activity_feeds test failed:', error.message);
    } else {
      console.log('✅ activity_feeds table accessible');
    }
  } catch (err) {
    console.log('❌ activity_feeds test error:', err.message);
  }
}

applyProductionFix();
