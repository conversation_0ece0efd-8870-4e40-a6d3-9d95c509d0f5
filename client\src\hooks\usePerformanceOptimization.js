// Performance Optimization Hook
// Integration & Services Agent: React performance optimization utilities

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { trackComponentRender, trackCustomMetric } from '../utils/performance/PerformanceMonitor';
import { memoryManager } from '../utils/performance/BundleOptimizer';

// Main performance optimization hook
export const usePerformanceOptimization = (componentName, options = {}) => {
  const {
    trackRenders = true,
    trackMemory = true,
    enableProfiling = process.env.NODE_ENV === 'development',
    memoryThreshold = 10 * 1024 * 1024 // 10MB
  } = options;

  const renderStartTime = useRef(Date.now());
  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());

  // Track component renders
  useEffect(() => {
    if (trackRenders) {
      const renderTime = Date.now() - renderStartTime.current;
      renderCount.current++;
      
      trackComponentRender(componentName, renderTime);
      
      if (enableProfiling) {
        console.log(`🎭 ${componentName} render #${renderCount.current}: ${renderTime}ms`);
      }
    }
  });

  // Track memory usage
  useEffect(() => {
    if (trackMemory && 'memory' in performance) {
      const checkMemory = () => {
        const memory = performance.memory;
        if (memory.usedJSHeapSize > memoryThreshold) {
          trackCustomMetric('component-memory-warning', memory.usedJSHeapSize, {
            component: componentName,
            threshold: memoryThreshold
          });
        }
      };

      const interval = setInterval(checkMemory, 30000); // Check every 30 seconds
      return () => clearInterval(interval);
    }
  }, [trackMemory, memoryThreshold, componentName]);

  // Component lifecycle tracking
  useEffect(() => {
    const mountDuration = Date.now() - mountTime.current;
    trackCustomMetric('component-mount', mountDuration, { component: componentName });

    return () => {
      const lifetimeDuration = Date.now() - mountTime.current;
      trackCustomMetric('component-unmount', lifetimeDuration, { 
        component: componentName,
        renderCount: renderCount.current
      });
    };
  }, [componentName]);

  // Update render start time
  renderStartTime.current = Date.now();

  return {
    renderCount: renderCount.current,
    mountTime: mountTime.current
  };
};

// Debounced state hook for performance
export const useDebouncedState = (initialValue, delay = 300) => {
  const [value, setValue] = useState(initialValue);
  const [debouncedValue, setDebouncedValue] = useState(initialValue);
  const timeoutRef = useRef(null);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, delay]);

  return [debouncedValue, setValue, value];
};

// Throttled callback hook
export const useThrottledCallback = (callback, delay = 100) => {
  const lastRun = useRef(Date.now());

  return useCallback((...args) => {
    const now = Date.now();
    if (now - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = now;
    }
  }, [callback, delay]);
};

// Memoized expensive computation hook
export const useMemoizedComputation = (computeFn, dependencies, options = {}) => {
  const { 
    cacheSize = 10,
    enableLogging = false 
  } = options;

  const cache = useRef(new Map());
  const cacheOrder = useRef([]);

  return useMemo(() => {
    const key = JSON.stringify(dependencies);
    
    // Check cache first
    if (cache.current.has(key)) {
      if (enableLogging) {
        console.log('🎯 Cache hit for computation');
      }
      return cache.current.get(key);
    }

    // Compute new value
    const startTime = Date.now();
    const result = computeFn();
    const computeTime = Date.now() - startTime;

    if (enableLogging) {
      console.log(`🧮 Computation took ${computeTime}ms`);
    }

    // Add to cache
    cache.current.set(key, result);
    cacheOrder.current.push(key);

    // Maintain cache size
    if (cacheOrder.current.length > cacheSize) {
      const oldestKey = cacheOrder.current.shift();
      cache.current.delete(oldestKey);
    }

    return result;
  }, dependencies);
};

// Virtual scrolling hook for large lists
export const useVirtualScrolling = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerRef, setContainerRef] = useState(null);

  const visibleItems = useMemo(() => {
    if (!items.length || !itemHeight || !containerHeight) {
      return { startIndex: 0, endIndex: 0, visibleItems: [] };
    }

    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length - 1
    );

    const visibleItems = items.slice(startIndex, endIndex + 1);

    return { startIndex, endIndex, visibleItems };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useThrottledCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, 16); // ~60fps

  useEffect(() => {
    if (containerRef) {
      containerRef.addEventListener('scroll', handleScroll);
      return () => containerRef.removeEventListener('scroll', handleScroll);
    }
  }, [containerRef, handleScroll]);

  return {
    ...visibleItems,
    containerRef: setContainerRef,
    totalHeight: items.length * itemHeight,
    offsetY: visibleItems.startIndex * itemHeight
  };
};

// Intersection observer hook for lazy loading
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [element, setElement] = useState(null);
  const observerRef = useRef(null);

  const {
    threshold = 0.1,
    rootMargin = '0px',
    triggerOnce = true
  } = options;

  useEffect(() => {
    if (!element) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        const isElementIntersecting = entry.isIntersecting;
        setIsIntersecting(isElementIntersecting);

        if (isElementIntersecting && triggerOnce) {
          observerRef.current?.unobserve(element);
        }
      },
      { threshold, rootMargin }
    );

    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [element, threshold, rootMargin, triggerOnce]);

  return [setElement, isIntersecting];
};

// Optimized event listener hook
export const useOptimizedEventListener = (eventName, handler, element = window, options = {}) => {
  const { 
    passive = true,
    capture = false,
    throttle = 0,
    debounce = 0
  } = options;

  const savedHandler = useRef(handler);
  const throttledHandler = useRef(null);
  const debouncedHandler = useRef(null);

  // Update handler ref when handler changes
  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  // Create optimized handler
  useEffect(() => {
    if (throttle > 0) {
      let lastRun = 0;
      throttledHandler.current = (...args) => {
        const now = Date.now();
        if (now - lastRun >= throttle) {
          savedHandler.current(...args);
          lastRun = now;
        }
      };
    } else if (debounce > 0) {
      let timeoutId;
      debouncedHandler.current = (...args) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => savedHandler.current(...args), debounce);
      };
    }
  }, [throttle, debounce]);

  useEffect(() => {
    if (!element?.addEventListener) return;

    const eventHandler = throttledHandler.current || 
                         debouncedHandler.current || 
                         ((...args) => savedHandler.current(...args));

    const eventOptions = { passive, capture };
    element.addEventListener(eventName, eventHandler, eventOptions);

    return () => {
      element.removeEventListener(eventName, eventHandler, eventOptions);
    };
  }, [eventName, element, passive, capture]);
};

// Memory cleanup hook
export const useMemoryCleanup = (cleanupFn) => {
  useEffect(() => {
    const unregister = memoryManager.registerCleanup(cleanupFn);
    return unregister;
  }, [cleanupFn]);
};

// Performance profiler hook
export const usePerformanceProfiler = (name, enabled = process.env.NODE_ENV === 'development') => {
  const startTime = useRef(Date.now());
  const measurements = useRef([]);

  const mark = useCallback((label) => {
    if (!enabled) return;
    
    const now = Date.now();
    measurements.current.push({
      label,
      timestamp: now,
      elapsed: now - startTime.current
    });
  }, [enabled]);

  const getProfile = useCallback(() => {
    return {
      name,
      totalTime: Date.now() - startTime.current,
      measurements: [...measurements.current]
    };
  }, [name]);

  const reset = useCallback(() => {
    startTime.current = Date.now();
    measurements.current = [];
  }, []);

  useEffect(() => {
    return () => {
      if (enabled && measurements.current.length > 0) {
        const profile = getProfile();
        console.log(`📊 Performance Profile [${name}]:`, profile);
      }
    };
  }, [enabled, name, getProfile]);

  return { mark, getProfile, reset };
};

// Batch state updates hook
export const useBatchedUpdates = () => {
  const [updates, setUpdates] = useState([]);
  const timeoutRef = useRef(null);

  const batchUpdate = useCallback((updateFn) => {
    setUpdates(prev => [...prev, updateFn]);

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setUpdates(currentUpdates => {
        currentUpdates.forEach(fn => fn());
        return [];
      });
    }, 0);
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return batchUpdate;
};

export default {
  usePerformanceOptimization,
  useDebouncedState,
  useThrottledCallback,
  useMemoizedComputation,
  useVirtualScrolling,
  useIntersectionObserver,
  useOptimizedEventListener,
  useMemoryCleanup,
  usePerformanceProfiler,
  useBatchedUpdates
};
