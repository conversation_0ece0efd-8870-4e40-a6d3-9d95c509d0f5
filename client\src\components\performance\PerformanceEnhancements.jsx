import React, { Suspense, lazy, memo, useMemo, useCallback, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Spinner } from '@heroui/react';

/**
 * Performance Enhancement Components
 * 
 * Provides comprehensive performance optimizations:
 * - Enhanced lazy loading
 * - Virtual scrolling
 * - Image optimization
 * - Memory management
 * - Bundle optimization
 */

// Enhanced Lazy Loading with Preloading
export const LazyComponent = ({ 
  importFunc, 
  fallback = <Spinner size="lg" />,
  preload = false,
  errorFallback = null 
}) => {
  const LazyLoadedComponent = useMemo(() => lazy(importFunc), [importFunc]);
  
  useEffect(() => {
    if (preload) {
      // Preload the component
      importFunc();
    }
  }, [importFunc, preload]);
  
  return (
    <Suspense fallback={fallback}>
      <LazyLoadedComponent />
    </Suspense>
  );
};

// Virtual Scrolling Component
export const VirtualScrollList = memo(({ 
  items, 
  itemHeight = 50, 
  containerHeight = 400,
  renderItem,
  overscan = 5,
  className = ""
}) => {
  const [scrollTop, setScrollTop] = React.useState(0);
  const containerRef = useRef(null);
  
  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight),
    items.length - 1
  );
  
  const startIndex = Math.max(0, visibleStart - overscan);
  const endIndex = Math.min(items.length - 1, visibleEnd + overscan);
  
  const visibleItems = items.slice(startIndex, endIndex + 1);
  
  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);
  
  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => (
          <div
            key={startIndex + index}
            style={{
              position: 'absolute',
              top: (startIndex + index) * itemHeight,
              height: itemHeight,
              width: '100%'
            }}
          >
            {renderItem(item, startIndex + index)}
          </div>
        ))}
      </div>
    </div>
  );
});

// Optimized Image Component with Progressive Loading
export const OptimizedImage = memo(({ 
  src, 
  alt, 
  width, 
  height,
  className = "",
  lazy = true,
  quality = 75,
  placeholder = "blur",
  onLoad,
  ...props 
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isInView, setIsInView] = React.useState(!lazy);
  const imgRef = useRef(null);
  
  useEffect(() => {
    if (!lazy || isInView) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.unobserve(entry.target);
          }
        });
      },
      { rootMargin: '50px' }
    );
    
    if (imgRef.current) {
      observer.observe(imgRef.current);
    }
    
    return () => observer.disconnect();
  }, [lazy, isInView]);
  
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);
  
  const optimizedSrc = useMemo(() => {
    if (!src) return '';
    
    // Add optimization parameters if supported
    const url = new URL(src, window.location.origin);
    if (width) url.searchParams.set('w', width);
    if (height) url.searchParams.set('h', height);
    if (quality) url.searchParams.set('q', quality);
    
    return url.toString();
  }, [src, width, height, quality]);
  
  return (
    <div ref={imgRef} className={`relative overflow-hidden ${className}`}>
      {/* Placeholder */}
      {!isLoaded && placeholder === "blur" && (
        <div 
          className="absolute inset-0 bg-default-200 animate-pulse"
          style={{ width, height }}
        />
      )}
      
      {/* Actual Image */}
      {isInView && (
        <motion.img
          src={optimizedSrc}
          alt={alt}
          width={width}
          height={height}
          loading={lazy ? "lazy" : "eager"}
          onLoad={handleLoad}
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          {...props}
        />
      )}
    </div>
  );
});

// Memory-Efficient Component Wrapper
export const MemoryOptimized = memo(({ 
  children, 
  cleanupOnUnmount = true,
  memoryThreshold = 50 * 1024 * 1024 // 50MB
}) => {
  const componentRef = useRef(null);
  
  useEffect(() => {
    if (!cleanupOnUnmount) return;
    
    const checkMemoryUsage = () => {
      if (performance.memory && performance.memory.usedJSHeapSize > memoryThreshold) {
        console.warn('Memory usage high, consider optimizing components');
      }
    };
    
    const interval = setInterval(checkMemoryUsage, 10000); // Check every 10 seconds
    
    return () => {
      clearInterval(interval);
      // Force garbage collection if available (Chrome DevTools)
      if (window.gc) {
        window.gc();
      }
    };
  }, [cleanupOnUnmount, memoryThreshold]);
  
  return <div ref={componentRef}>{children}</div>;
});

// Performance Monitor Hook
export const usePerformanceMonitor = (componentName) => {
  const renderCount = useRef(0);
  const renderTimes = useRef([]);
  
  useEffect(() => {
    const startTime = performance.now();
    renderCount.current++;
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      renderTimes.current.push(renderTime);
      
      // Keep only last 10 render times
      if (renderTimes.current.length > 10) {
        renderTimes.current.shift();
      }
      
      if (import.meta.env.DEV) {
        const avgRenderTime = renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length;
        console.log(`🎭 ${componentName} - Render #${renderCount.current}: ${renderTime.toFixed(2)}ms (avg: ${avgRenderTime.toFixed(2)}ms)`);
      }
    };
  });
  
  return {
    renderCount: renderCount.current,
    averageRenderTime: renderTimes.current.length > 0 
      ? renderTimes.current.reduce((a, b) => a + b, 0) / renderTimes.current.length 
      : 0
  };
};

// Debounced Input Hook
export const useDebouncedValue = (value, delay = 300) => {
  const [debouncedValue, setDebouncedValue] = React.useState(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
};

// Intersection Observer Hook
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const [entry, setEntry] = React.useState(null);
  const elementRef = useRef(null);
  
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        setEntry(entry);
      },
      {
        threshold: 0.1,
        rootMargin: '0px',
        ...options
      }
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [options]);
  
  return { elementRef, isIntersecting, entry };
};

// Bundle Splitting Helper
export const createAsyncComponent = (importFunc, fallback) => {
  const AsyncComponent = lazy(importFunc);
  
  return memo((props) => (
    <Suspense fallback={fallback || <Spinner size="lg" />}>
      <AsyncComponent {...props} />
    </Suspense>
  ));
};

export default {
  LazyComponent,
  VirtualScrollList,
  OptimizedImage,
  MemoryOptimized,
  usePerformanceMonitor,
  useDebouncedValue,
  useIntersectionObserver,
  createAsyncComponent
};
