import React, { useState, useContext } from 'react';
import { Card, CardBody, Chip, Button, Avatar, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { Draggable } from 'react-beautiful-dnd';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Play, 
  Pause, 
  Clock, 
  User, 
  Flag,
  Calendar,
  Tag
} from 'lucide-react';

/**
 * Enhanced Task Card Component
 * 
 * Features:
 * - Drag and drop functionality
 * - Integrated per-task time tracking
 * - Priority and assignee display
 * - Time logged display
 * - Start/stop timer buttons
 */
const TaskCard = ({ 
  task, 
  index, 
  onStartTimer, 
  onStopTimer, 
  activeTimerTaskId = null,
  className = "" 
}) => {
  const { currentUser } = useContext(UserContext);
  const [isUpdating, setIsUpdating] = useState(false);

  const isTimerActive = activeTimerTaskId === task.id;
  const canStartTimer = !activeTimerTaskId || activeTimerTaskId === task.id;

  const handleTimerToggle = async () => {
    if (isTimerActive) {
      await onStopTimer(task.id);
    } else {
      await onStartTimer(task.id);
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getPriorityLabel = (priority) => {
    switch (priority) {
      case 'high': return 'High';
      case 'medium': return 'Medium';
      case 'low': return 'Low';
      default: return 'Normal';
    }
  };

  const formatTimeLogged = (minutes) => {
    if (!minutes || minutes === 0) return '0m';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 7) return `${diffDays} days`;
    return date.toLocaleDateString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'todo': return 'default';
      case 'in_progress': return 'primary';
      case 'review': return 'warning';
      case 'done': return 'success';
      case 'blocked': return 'danger';
      default: return 'default';
    }
  };

  return (
    <Draggable draggableId={task.id.toString()} index={index}>
      {(provided, snapshot) => (
        <motion.div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
          whileHover={{ scale: 1.02 }}
          className={className}
        >
          <Card 
            className={`
              bg-gradient-to-br from-gray-800/50 to-gray-900/50 
              border border-white/10 hover:border-white/20 
              transition-all duration-200
              ${snapshot.isDragging ? 'rotate-2 shadow-2xl' : ''}
              ${isTimerActive ? 'ring-2 ring-green-500/50' : ''}
            `}
          >
            <CardBody className="p-4">
              {/* Header with Priority and Timer */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Chip 
                    size="sm" 
                    color={getPriorityColor(task.priority)} 
                    variant="flat"
                    startContent={<Flag className="w-3 h-3" />}
                  >
                    {getPriorityLabel(task.priority)}
                  </Chip>
                  {task.due_date && (
                    <Chip size="sm" variant="flat" className="bg-blue-500/20 text-blue-200">
                      <Calendar className="w-3 h-3 mr-1" />
                      {formatDate(task.due_date)}
                    </Chip>
                  )}
                </div>

                <Button
                  isIconOnly
                  size="sm"
                  color={isTimerActive ? 'danger' : 'success'}
                  variant="flat"
                  onClick={handleTimerToggle}
                  disabled={!canStartTimer && !isTimerActive}
                  className="min-w-8 h-8"
                >
                  {isTimerActive ? (
                    <Pause className="w-3 h-3" />
                  ) : (
                    <Play className="w-3 h-3" />
                  )}
                </Button>
              </div>

              {/* Task Title */}
              <h4 className="font-semibold text-white mb-2 line-clamp-2">
                {task.title}
              </h4>

              {/* Task Description */}
              {task.description && (
                <p className="text-sm text-white/70 mb-3 line-clamp-2">
                  {task.description}
                </p>
              )}

              {/* Tags */}
              {task.tags && task.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {task.tags.slice(0, 3).map((tag, index) => (
                    <Chip key={index} size="sm" variant="flat" className="bg-purple-500/20 text-purple-200">
                      <Tag className="w-2 h-2 mr-1" />
                      {tag}
                    </Chip>
                  ))}
                  {task.tags.length > 3 && (
                    <Chip size="sm" variant="flat" className="bg-gray-500/20 text-gray-300">
                      +{task.tags.length - 3}
                    </Chip>
                  )}
                </div>
              )}

              {/* Footer with Assignee and Time */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {task.assignee_avatar ? (
                    <Avatar 
                      src={task.assignee_avatar} 
                      size="sm" 
                      className="w-6 h-6"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-gray-500/20 flex items-center justify-center">
                      <User className="w-3 h-3 text-white/60" />
                    </div>
                  )}
                  <span className="text-xs text-white/60">
                    {task.assignee_name || 'Unassigned'}
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <Clock className="w-3 h-3 text-white/40" />
                  <span className="text-xs text-white/60">
                    {formatTimeLogged(task.time_logged || 0)}
                  </span>
                  {isTimerActive && (
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  )}
                </div>
              </div>

              {/* Progress Bar for Estimated vs Logged Time */}
              {task.estimated_hours && task.estimated_hours > 0 && (
                <div className="mt-3">
                  <Progress
                    value={Math.min(((task.time_logged || 0) / 60) / task.estimated_hours * 100, 100)}
                    color={
                      ((task.time_logged || 0) / 60) / task.estimated_hours > 1 
                        ? 'danger' 
                        : ((task.time_logged || 0) / 60) / task.estimated_hours > 0.8 
                          ? 'warning' 
                          : 'success'
                    }
                    size="sm"
                    className="mt-2"
                  />
                  <div className="flex justify-between text-xs text-white/50 mt-1">
                    <span>{formatTimeLogged(task.time_logged || 0)} logged</span>
                    <span>{task.estimated_hours}h estimated</span>
                  </div>
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      )}
    </Draggable>
  );
};

export default TaskCard;
