# Cleanup Migrations Script
# This script removes duplicate and unnecessary migration files

Write-Host "=== Cleaning up Supabase Migrations ===" -ForegroundColor Cyan

# Define the migrations directory
$migrationsDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations"
$backupDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations_backup"

# Ensure the backup directory exists
if (-not (Test-Path $backupDir)) {
    New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
}

# List of duplicate or unnecessary migrations to remove
$migrationsToRemove = @(
    # Duplicate notifications table migrations
    "20240601000000_create_notifications_table_fixed.sql",
    
    # Duplicate task table migrations
    "20240501000018_create_tasks_table.sql",
    
    # Duplicate project thumbnails migration
    "20240501000020_ensure_project_thumbnails.sql",
    
    # Duplicate latest feature column migration
    "20240515000001_add_latest_feature_column.sql"
)

# Process each migration file
foreach ($migrationFile in $migrationsToRemove) {
    $filePath = Join-Path -Path $migrationsDir -ChildPath $migrationFile
    $backupPath = Join-Path -Path $backupDir -ChildPath $migrationFile
    
    if (Test-Path $filePath) {
        Write-Host "Moving $migrationFile to backup directory..." -ForegroundColor Yellow
        
        # If a file with the same name already exists in the backup directory, append a timestamp
        if (Test-Path $backupPath) {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $backupPath = Join-Path -Path $backupDir -ChildPath "$([System.IO.Path]::GetFileNameWithoutExtension($migrationFile))_$timestamp$([System.IO.Path]::GetExtension($migrationFile))"
        }
        
        # Move the file to the backup directory
        Move-Item -Path $filePath -Destination $backupPath -Force
    } else {
        Write-Host "File not found: $migrationFile" -ForegroundColor Gray
    }
}

# List remaining migration files
Write-Host "`nRemaining migration files:" -ForegroundColor Green
Get-ChildItem -Path $migrationsDir -File | ForEach-Object {
    Write-Host "- $($_.Name)" -ForegroundColor Gray
}

Write-Host "`n=== Migration Cleanup Complete ===" -ForegroundColor Cyan
