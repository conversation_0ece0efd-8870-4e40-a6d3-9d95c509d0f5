import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Tooltip } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  MessageCircle, 
  CheckSquare, 
  Users, 
  Settings,
  Plus,
  Download,
  Archive,
  BarChart3,
  Search,
  User,
  FolderOpen
} from 'lucide-react';

/**
 * Vertical Helper Navigation System
 * 
 * Implements the documented two vertical helper buttons:
 * - Left Sidebar: Static/Persistent helpers (60px width)
 * - Right Sidebar: Contextual actions (60px width)
 */
const VerticalHelperNavigation = ({ children, currentUser }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [notifications, setNotifications] = useState(0);
  const [messages, setMessages] = useState(0);

  // Static left sidebar actions (always available)
  const leftSidebarActions = [
    {
      id: 'notifications',
      icon: Bell,
      label: 'Notifications',
      path: '/notifications',
      badge: notifications,
      color: 'primary'
    },
    {
      id: 'messages',
      icon: MessageCircle,
      label: 'Messages',
      path: '/messages',
      badge: messages,
      color: 'secondary'
    },
    {
      id: 'tasks',
      icon: CheckSquare,
      label: 'Tasks',
      path: '/missions',
      color: 'success'
    },
    {
      id: 'social',
      icon: Users,
      label: 'Social',
      path: '/teams',
      color: 'warning'
    },
    {
      id: 'settings',
      icon: Settings,
      label: 'Settings',
      path: '/settings',
      color: 'default'
    }
  ];

  // Get contextual actions based on current page
  const getContextualActions = () => {
    const path = location.pathname;

    if (path.startsWith('/projects')) {
      return [
        { id: 'add-project', icon: Plus, label: 'Add Project', action: () => navigate('/project/create') },
        { id: 'import-export', icon: Download, label: 'Import/Export', action: () => {} },
        { id: 'archive', icon: Archive, label: 'Archive', action: () => {} },
        { id: 'reports', icon: BarChart3, label: 'Reports', action: () => navigate('/analytics') }
      ];
    }

    if (path.startsWith('/missions')) {
      return [
        { id: 'quick-add', icon: Plus, label: 'Quick Add', action: () => navigate('/missions/create') },
        { id: 'analytics', icon: BarChart3, label: 'Analytics', action: () => navigate('/analytics') },
        { id: 'search', icon: Search, label: 'Search', action: () => {} }
      ];
    }

    if (path.startsWith('/studios')) {
      return [
        { id: 'add-studio', icon: Plus, label: 'Add Studio', action: () => navigate('/studios/create') },
        { id: 'analytics', icon: BarChart3, label: 'Analytics', action: () => navigate('/analytics') }
      ];
    }

    // Default contextual actions
    return [
      { id: 'profile', icon: User, label: 'Profile', action: () => navigate('/profile') },
      { id: 'projects', icon: FolderOpen, label: 'All Projects', action: () => navigate('/projects') },
      { id: 'analytics', icon: BarChart3, label: 'Analytics', action: () => navigate('/analytics') }
    ];
  };

  const contextualActions = getContextualActions();

  const handleLeftAction = (action) => {
    if (action.path) {
      navigate(action.path);
    }
  };

  const handleContextualAction = (action) => {
    if (action.action) {
      action.action();
    }
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      {/* Left Sidebar - Static Helpers */}
      <motion.div
        initial={{ x: -60, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="w-15 bg-black/20 backdrop-blur-md border-r border-white/10 flex flex-col items-center py-6 space-y-4"
      >
        {leftSidebarActions.map((action, index) => (
          <motion.div
            key={action.id}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="relative"
          >
            <Tooltip content={action.label} placement="right">
              <Button
                isIconOnly
                variant="light"
                color={action.color}
                onClick={() => handleLeftAction(action)}
                className="w-12 h-12 text-white hover:bg-white/20 transition-all duration-200"
              >
                <action.icon size={20} />
                {action.badge > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {action.badge > 9 ? '9+' : action.badge}
                  </span>
                )}
              </Button>
            </Tooltip>
          </motion.div>
        ))}
      </motion.div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        {children}
      </div>

      {/* Right Sidebar - Contextual Actions */}
      <motion.div
        initial={{ x: 60, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="w-15 bg-black/20 backdrop-blur-md border-l border-white/10 flex flex-col items-center py-6 space-y-4"
      >
        <AnimatePresence mode="wait">
          {contextualActions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -20, opacity: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Tooltip content={action.label} placement="left">
                <Button
                  isIconOnly
                  variant="light"
                  onClick={() => handleContextualAction(action)}
                  className="w-12 h-12 text-white hover:bg-white/20 transition-all duration-200"
                >
                  <action.icon size={20} />
                </Button>
              </Tooltip>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>
    </div>
  );
};

export default VerticalHelperNavigation;
