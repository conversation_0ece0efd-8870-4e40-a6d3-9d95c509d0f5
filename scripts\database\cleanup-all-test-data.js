// Script to clean up all test data
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

console.log('Using Supabase URL:', supabaseUrl);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to clean up all notifications
async function cleanupNotifications() {
  try {
    console.log('\n=== Cleaning up all notifications ===');
    
    // Delete all notifications
    const { error: notificationsError } = await supabase
      .from('notifications')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Dummy condition to delete all
    
    if (notificationsError) {
      console.error('Error deleting notifications:', notificationsError);
    } else {
      console.log('Deleted all notifications successfully');
    }
  } catch (error) {
    console.error('Error in cleanupNotifications:', error);
  }
}

// Function to list all test projects
async function listTestProjects() {
  try {
    console.log('\n=== Listing all test projects ===');
    
    // Get all projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      return [];
    }
    
    if (!projects || projects.length === 0) {
      console.log('No projects found');
      return [];
    }
    
    // Filter test projects
    const testProjects = projects.filter(project => 
      project.name?.toLowerCase().includes('test') || 
      project.description?.toLowerCase().includes('test') ||
      project.name?.toLowerCase().includes('verification') ||
      project.name?.toLowerCase().includes('final') ||
      project.name?.toLowerCase().includes('clean') ||
      project.name?.toLowerCase().includes('simple') ||
      project.name?.toLowerCase().includes('permission') ||
      project.name?.toLowerCase().includes('ui')
    );
    
    console.log(`Found ${testProjects.length} test projects out of ${projects.length} total projects:`);
    testProjects.forEach((project, index) => {
      console.log(`${index + 1}. ID: ${project.id}, Name: ${project.name}, Created: ${project.created_at}`);
    });
    
    return testProjects;
  } catch (error) {
    console.error('Error in listTestProjects:', error);
    return [];
  }
}

// Function to delete project contributors
async function deleteProjectContributors(projectId) {
  try {
    console.log(`\nDeleting contributors for project ${projectId}...`);
    
    // Delete project contributors
    const { data, error } = await supabase
      .from('project_contributors')
      .delete()
      .eq('project_id', projectId)
      .select();
    
    if (error) {
      console.error('Error deleting project contributors:', error);
    } else {
      console.log(`Deleted ${data ? data.length : 0} contributors for project ${projectId}`);
    }
  } catch (error) {
    console.error('Error in deleteProjectContributors:', error);
  }
}

// Function to delete a test project
async function deleteTestProject(projectId) {
  try {
    console.log(`\nDeleting project ${projectId}...`);
    
    // First delete all contributors
    await deleteProjectContributors(projectId);
    
    // Then delete the project
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);
    
    if (error) {
      console.error('Error deleting project:', error);
    } else {
      console.log(`Deleted project ${projectId} successfully`);
    }
  } catch (error) {
    console.error('Error in deleteTestProject:', error);
  }
}

// Function to clean up all test projects
async function cleanupTestProjects() {
  try {
    console.log('\n=== Cleaning up all test projects ===');
    
    // Get all test projects
    const testProjects = await listTestProjects();
    
    if (testProjects.length === 0) {
      console.log('No test projects to clean up');
      return;
    }
    
    // Delete each test project
    for (const project of testProjects) {
      await deleteTestProject(project.id);
    }
    
    // Verify deletion
    const remainingTestProjects = await listTestProjects();
    
    if (remainingTestProjects.length > 0) {
      console.log(`WARNING: ${remainingTestProjects.length} test projects still remain after deletion`);
    } else {
      console.log('All test projects successfully deleted');
    }
  } catch (error) {
    console.error('Error in cleanupTestProjects:', error);
  }
}

// Function to create a proper RLS policy for project_contributors
async function createProperRLSPolicy() {
  try {
    console.log('\n=== Creating proper RLS policy for project_contributors ===');
    
    // First, enable RLS on the table
    const enableRLSSQL = `
      ALTER TABLE public.project_contributors ENABLE ROW LEVEL SECURITY;
    `;
    
    // Execute the SQL directly
    const { data: enableData, error: enableError } = await supabase.rpc('exec_sql', { sql: enableRLSSQL });
    
    if (enableError) {
      if (enableError.code === 'PGRST202') {
        console.log('Could not find exec_sql function. This is expected if it does not exist.');
        console.log('Continuing with direct SQL execution...');
      } else {
        console.error('Error enabling RLS:', enableError);
      }
    } else {
      console.log('RLS enabled successfully');
    }
    
    // Create a policy for users to update their own invitations
    console.log('\nCreating policy for users to update their own invitations...');
    
    // Try to drop any existing policy first
    const dropPolicySQL = `
      DROP POLICY IF EXISTS "Users can update their own invitations" ON public.project_contributors;
    `;
    
    // Execute the SQL directly
    const { data: dropData, error: dropError } = await supabase.rpc('exec_sql', { sql: dropPolicySQL });
    
    if (dropError && dropError.code !== 'PGRST202') {
      console.error('Error dropping policy:', dropError);
    }
    
    // Create the policy
    const createPolicySQL = `
      CREATE POLICY "Users can update their own invitations"
      ON public.project_contributors
      FOR UPDATE
      USING (auth.uid() = user_id)
      WITH CHECK (auth.uid() = user_id);
    `;
    
    // Execute the SQL directly
    const { data: createData, error: createError } = await supabase.rpc('exec_sql', { sql: createPolicySQL });
    
    if (createError && createError.code !== 'PGRST202') {
      console.error('Error creating policy:', createError);
    }
    
    console.log('Policy creation attempted. Check Supabase dashboard to verify.');
    
    // Since we can't execute SQL directly through the API, we'll use the Supabase client to update a test invitation
    console.log('\nTesting permission by updating a test invitation...');
    
    // Create a test project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        name: 'Permission Test Project',
        description: 'This is a test project to verify permissions',
        created_by: '93cbbbed-2772-4922-b7d7-d07fdc1aa62b', // Owner's user ID
        date_created: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
        is_public: true,
        estimated_duration: 3,
        start_date: new Date().toISOString().split('T')[0]
      })
      .select()
      .single();
    
    if (projectError) {
      console.error('Error creating test project:', projectError);
      return;
    }
    
    console.log('Created test project:', project.id);
    
    // Create a test invitation
    const { data: invitation, error: invitationError } = await supabase
      .from('project_contributors')
      .insert({
        project_id: project.id,
        user_id: '2a033231-d173-4292-aa36-90f4d735bcf3', // Gynell's user ID
        email: '<EMAIL>',
        display_name: 'Gynell Journigan',
        role: 'Contributor',
        permission_level: 'Contributor',
        is_admin: false,
        status: 'pending',
        invitation_sent_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (invitationError) {
      console.error('Error creating test invitation:', invitationError);
      return;
    }
    
    console.log('Created test invitation:', invitation.id);
    
    // Update the invitation status
    const { data: updatedInvitation, error: updateError } = await supabase
      .from('project_contributors')
      .update({
        status: 'active',
        updated_at: new Date().toISOString(),
        joined_at: new Date().toISOString()
      })
      .eq('id', invitation.id)
      .select();
    
    if (updateError) {
      console.error('Error updating invitation:', updateError);
    } else {
      console.log('Updated invitation successfully:', updatedInvitation);
    }
    
    // Clean up the test project
    await deleteTestProject(project.id);
    
    console.log('\nPermission setup complete. Please check Supabase dashboard to verify RLS policies.');
  } catch (error) {
    console.error('Error in createProperRLSPolicy:', error);
  }
}

// Main function
async function main() {
  try {
    console.log('=== Cleaning Up All Test Data ===');
    
    // Clean up existing notifications
    await cleanupNotifications();
    
    // Clean up existing test projects
    await cleanupTestProjects();
    
    // Create proper RLS policy
    await createProperRLSPolicy();
    
    console.log('\n=== Cleanup Complete ===');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
