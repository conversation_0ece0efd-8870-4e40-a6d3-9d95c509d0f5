// Studio Management API
// Backend Specialist: Core studio CRUD operations (formerly Alliance Management)
const { createClient } = require('@supabase/supabase-js');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Helper function to get user from request
const getUserFromRequest = (event) => {
  const authHeader = event.headers.authorization;
  if (!authHeader) return null;
  
  // Extract user ID from JWT token (simplified)
  // In production, properly verify JWT
  try {
    const token = authHeader.replace('Bearer ', '');
    const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
    return payload.sub;
  } catch {
    return null;
  }
};

// Get all studios for a user
const getStudios = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get studios where user is a member
    const { data: userStudios, error } = await supabase
      .from('teams')
      .select(`
        *,
        team_members!inner(
          id,
          user_id,
          role,
          status,
          joined_at,
          collaboration_type,
          engagement_duration
        )
      `)
      .eq('team_members.user_id', userId)
      .eq('team_members.status', 'active');

    if (error) throw error;

    // Add user role to each studio
    const studiosWithRole = userStudios.map(studio => ({
      ...studio,
      userRole: studio.team_members[0]?.role,
      collaborationType: studio.team_members[0]?.collaboration_type,
      engagementDuration: studio.team_members[0]?.engagement_duration
    }));

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        success: true, 
        data: studiosWithRole,
        count: studiosWithRole.length
      })
    };

  } catch (error) {
    console.error('Error fetching studios:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch studios' })
    };
  }
};

// Get single studio details
const getStudio = async (event) => {
  try {
    const studioId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Get studio with member details
    const { data: studio, error: studioError } = await supabase
      .from('teams')
      .select(`
        *,
        team_members(
          id,
          user_id,
          role,
          status,
          joined_at,
          collaboration_type,
          engagement_duration,
          specialization,
          users(
            id,
            display_name,
            email,
            avatar_url
          )
        )
      `)
      .eq('id', studioId)
      .single();

    if (studioError) throw studioError;

    // Check if user has access (is member of studio)
    const userMembership = studio.team_members.find(m => m.user_id === userId);
    if (!userMembership) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Access denied' })
      };
    }

    // Get studio projects
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        description,
        project_type,
        status,
        created_at,
        updated_at
      `)
      .eq('studio_id', studioId);

    if (projectsError) {
      console.warn('Error fetching studio projects:', projectsError);
    }

    // Get studio preferences
    const { data: preferences, error: preferencesError } = await supabase
      .from('studio_preferences')
      .select('*')
      .eq('studio_id', studioId)
      .single();

    if (preferencesError) {
      console.warn('Error fetching studio preferences:', preferencesError);
    }

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        success: true,
        data: {
          ...studio,
          projects: projects || [],
          preferences: preferences || null,
          userRole: userMembership.role,
          collaborationType: userMembership.collaboration_type,
          engagementDuration: userMembership.engagement_duration
        }
      })
    };

  } catch (error) {
    console.error('Error fetching studio:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to fetch studio' })
    };
  }
};

// Create new studio
const createStudio = async (event) => {
  try {
    const userId = getUserFromRequest(event);
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    const studioData = JSON.parse(event.body);
    
    // Validate required fields
    if (!studioData.name) {
      return {
        statusCode: 400,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Studio name is required' })
      };
    }

    // Create studio
    const { data: studio, error: studioError } = await supabase
      .from('teams')
      .insert([{
        name: studioData.name,
        description: studioData.description,
        studio_type: studioData.studio_type || 'emerging',
        industry: studioData.industry,
        business_model: studioData.business_model || {},
        is_public: studioData.is_public !== false,
        max_members: studioData.max_members || 10,
        created_by: userId
      }])
      .select()
      .single();

    if (studioError) throw studioError;

    // Add creator as founder
    const { error: memberError } = await supabase
      .from('team_members')
      .insert([{
        team_id: studio.id,
        user_id: userId,
        role: 'founder',
        status: 'active',
        collaboration_type: 'studio_member',
        engagement_duration: 'permanent',
        is_admin: true
      }]);

    if (memberError) throw memberError;

    return {
      statusCode: 201,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        success: true,
        data: studio,
        message: 'Studio created successfully'
      })
    };

  } catch (error) {
    console.error('Error creating studio:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to create studio' })
    };
  }
};

// Update studio
const updateStudio = async (event) => {
  try {
    const studioId = event.path.split('/').pop();
    const userId = getUserFromRequest(event);
    
    if (!userId) {
      return {
        statusCode: 401,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Unauthorized' })
      };
    }

    // Check if user has admin access
    const { data: membership, error: membershipError } = await supabase
      .from('team_members')
      .select('role')
      .eq('team_id', studioId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (membershipError || !membership || !['founder', 'owner', 'admin'].includes(membership.role)) {
      return {
        statusCode: 403,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ error: 'Insufficient permissions' })
      };
    }

    const updateData = JSON.parse(event.body);
    
    // Update studio
    const { data: studio, error: updateError } = await supabase
      .from('teams')
      .update({
        name: updateData.name,
        description: updateData.description,
        studio_type: updateData.studio_type,
        industry: updateData.industry,
        business_model: updateData.business_model,
        is_public: updateData.is_public,
        max_members: updateData.max_members,
        updated_at: new Date()
      })
      .eq('id', studioId)
      .select()
      .single();

    if (updateError) throw updateError;

    return {
      statusCode: 200,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        success: true,
        data: studio,
        message: 'Studio updated successfully'
      })
    };

  } catch (error) {
    console.error('Error updating studio:', error);
    return {
      statusCode: 500,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ error: 'Failed to update studio' })
    };
  }
};

// Main handler
exports.handler = async (event, context) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const path = event.path.replace('/api/studios', '').replace('/.netlify/functions/studios', '');
    
    let response;

    if (event.httpMethod === 'GET') {
      if (path === '' || path === '/') {
        response = await getStudios(event);
      } else {
        response = await getStudio(event);
      }
    } else if (event.httpMethod === 'POST') {
      response = await createStudio(event);
    } else if (event.httpMethod === 'PUT') {
      response = await updateStudio(event);
    } else {
      response = {
        statusCode: 405,
        body: JSON.stringify({ error: 'Method not allowed' })
      };
    }

    return {
      ...response,
      headers: { ...headers, ...response.headers }
    };

  } catch (error) {
    console.error('Studio API Error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Internal server error' })
    };
  }
};
