import React, { useState, useEffect, useContext } from 'react';
import { useParams, Navigate, Link } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import RevenueTracker from '../../components/revenue/RevenueTracker';
import RevenueDebugger from '../../components/revenue/RevenueDebugger';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import { Button, Card, CardBody, CardHeader, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { Calculator, ArrowLeft, DollarSign, Image } from 'lucide-react';

const RevenuePage = () => {
  const { projectId } = useParams();
  const { currentUser } = useContext(UserContext);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  // Fetch project and check access
  useEffect(() => {
    const fetchProjectAndCheckAccess = async () => {
      if (!projectId || !currentUser) return;

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;

        setProject(projectData);

        // Check if user has access to this project
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .eq('status', 'active')
          .single();

        if (contributorError && contributorError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which is expected if user is not a contributor
          throw contributorError;
        }

        // User has access if they are the project creator or a contributor
        const isCreator = projectData.created_by === currentUser.id;
        const isContributor = !!contributorData;

        setHasAccess(isCreator || isContributor);
      } catch (error) {
        console.error('Error fetching project:', error);
        toast.error('Failed to load project data');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectAndCheckAccess();
  }, [projectId, currentUser]);

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (loading) {
    return <LoadingAnimation />;
  }

  if (!project) {
    return (
      <div className="error-container">
        <h2>Project Not Found</h2>
        <p>The project you're looking for doesn't exist or has been deleted.</p>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <div className="error-container">
        <h2>Access Denied</h2>
        <p>You don't have permission to access this project's revenue data.</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardBody className="p-8">
              <div className="flex flex-col lg:flex-row items-start gap-8">
                {/* Project Info */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-4">
                    <DollarSign className="text-green-500" size={32} />
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                      {project.name} Revenue
                    </h1>
                  </div>

                  {project.description && (
                    <p className="text-lg text-default-600 mb-6 leading-relaxed">
                      {project.description}
                    </p>
                  )}

                  {hasAccess && (
                    <div className="flex flex-wrap gap-3">
                      <Button
                        as={Link}
                        to={`/project/${projectId}/royalty-calculator`}
                        color="primary"
                        size="lg"
                        className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-semibold"
                        startContent={<Calculator size={20} />}
                      >
                        Royalty Calculator
                      </Button>
                      <Button
                        as={Link}
                        to={`/project/${projectId}`}
                        variant="bordered"
                        size="lg"
                        className="border-2 border-default-300 hover:bg-default-100 font-semibold"
                        startContent={<ArrowLeft size={20} />}
                      >
                        Back to Project
                      </Button>
                    </div>
                  )}
                </div>

                {/* Project Thumbnail */}
                <div className="w-full lg:w-64 h-48 lg:h-64">
                  <Card className="h-full bg-gradient-to-br from-purple-100 to-blue-100 dark:from-purple-900/20 dark:to-blue-900/20">
                    <CardBody className="p-0 flex items-center justify-center overflow-hidden">
                      {project.thumbnail_url ? (
                        <img
                          src={project.thumbnail_url}
                          alt={project.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Image size={48} className="text-default-400" />
                      )}
                    </CardBody>
                  </Card>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Revenue Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <RevenueTracker projectId={projectId} />
        </motion.div>

        {/* Debug section - only visible to project creator */}
        {project.created_by === currentUser.id && (
          <motion.div
            className="mt-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-2 border-dashed border-yellow-300 dark:border-yellow-700">
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">🔧</span>
                  <h2 className="text-xl font-bold text-yellow-800 dark:text-yellow-200">
                    Debug Tools
                  </h2>
                  <Chip size="sm" color="warning" variant="flat">
                    Developer Only
                  </Chip>
                </div>
              </CardHeader>
              <CardBody>
                <RevenueDebugger projectId={projectId} />
              </CardBody>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default RevenuePage;
