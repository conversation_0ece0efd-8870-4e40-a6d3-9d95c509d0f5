#!/usr/bin/env node

/**
 * Comprehensive Test Runner for Agreement Generator V2
 * 
 * This script runs all tests and generates a comprehensive report
 * to validate the system is ready for production deployment.
 */

import { AgreementGeneratorV2 } from '../AgreementGeneratorV2.js';
import fs from 'fs';
import path from 'path';

class ComprehensiveTestRunner {
  constructor() {
    this.generator = new AgreementGeneratorV2();
    this.results = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      accuracyScores: [],
      testResults: [],
      startTime: Date.now(),
      endTime: null
    };
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Agreement Generator V2 Tests');
    console.log('=====================================================');

    try {
      // Test scenarios
      await this.runBasicFunctionalityTests();
      await this.runAccuracyValidationTests();
      await this.runProjectTypeTests();
      await this.runEdgeCaseTests();
      await this.runPerformanceTests();
      await this.runErrorHandlingTests();

      this.results.endTime = Date.now();
      await this.generateReport();

    } catch (error) {
      console.error('❌ Test runner failed:', error);
      process.exit(1);
    }
  }

  async runBasicFunctionalityTests() {
    console.log('\n📋 Running Basic Functionality Tests...');
    
    const testCases = [
      {
        name: 'Standard Software Project',
        data: {
          company: {
            name: 'TechCorp Solutions Inc.',
            address: '123 Innovation Drive, Orlando, FL 32801',
            state: 'Florida',
            city: 'Orlando',
            signerName: 'John Smith',
            signerTitle: 'CEO',
            billingEmail: '<EMAIL>'
          },
          project: {
            name: 'AI Analytics Platform',
            description: 'Advanced AI-powered analytics platform for enterprise data insights',
            projectType: 'software'
          },
          contributor: {
            name: 'Sarah Johnson',
            email: '<EMAIL>',
            address: '456 Developer Lane, Orlando, FL 32802'
          }
        }
      },
      {
        name: 'Game Development Project',
        data: {
          company: {
            name: 'GameStudio Collective LLC',
            address: '789 Creative Blvd, Los Angeles, CA 90210',
            state: 'California',
            city: 'Los Angeles',
            signerName: 'Alex Thompson',
            signerTitle: 'Creative Director',
            billingEmail: '<EMAIL>'
          },
          project: {
            name: 'Mystic Realms RPG',
            description: 'Fantasy role-playing game with immersive storytelling and strategic combat',
            projectType: 'game'
          },
          contributor: {
            name: 'Jordan Williams',
            email: '<EMAIL>',
            address: '321 Artist Ave, Los Angeles, CA 90211'
          }
        }
      },
      {
        name: 'Music Production Project',
        data: {
          company: {
            name: 'Harmony Productions Inc.',
            address: '555 Music Row, Nashville, TN 37203',
            state: 'Tennessee',
            city: 'Nashville',
            signerName: 'Maya Rodriguez',
            signerTitle: 'Producer',
            billingEmail: '<EMAIL>'
          },
          project: {
            name: 'Urban Beats Album',
            description: 'Contemporary urban music album with diverse artists',
            projectType: 'music'
          },
          contributor: {
            name: 'Chris Martinez',
            email: '<EMAIL>',
            address: '777 Studio Lane, Nashville, TN 37204'
          }
        }
      }
    ];

    for (const testCase of testCases) {
      await this.runSingleTest(testCase.name, testCase.data);
    }
  }

  async runAccuracyValidationTests() {
    console.log('\n🎯 Running Accuracy Validation Tests...');
    
    const testData = {
      company: {
        name: 'Accuracy Test Corp.',
        address: '100 Validation Street, Test City, TX 75001',
        state: 'Texas',
        city: 'Test City',
        signerName: 'Test Signer',
        signerTitle: 'Test Title',
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'Accuracy Validation Project',
        description: 'Project designed to test accuracy validation systems',
        projectType: 'software'
      },
      contributor: {
        name: 'Test Contributor',
        email: '<EMAIL>',
        address: '200 Contributor Ave, Test City, TX 75002'
      }
    };

    await this.runSingleTest('Accuracy Validation Test', testData, {
      validateAccuracy: true,
      minAccuracyScore: 95
    });
  }

  async runProjectTypeTests() {
    console.log('\n🎮 Running Project Type Specific Tests...');
    
    const projectTypes = ['software', 'game', 'music', 'film', 'art'];
    
    for (const projectType of projectTypes) {
      const testData = {
        company: {
          name: `${projectType.charAt(0).toUpperCase() + projectType.slice(1)} Company Inc.`,
          address: '123 Project Type Ave, Austin, TX 78701',
          state: 'Texas',
          city: 'Austin',
          signerName: 'Project Manager',
          signerTitle: 'CEO',
          billingEmail: `billing@${projectType}company.com`
        },
        project: {
          name: `${projectType.charAt(0).toUpperCase() + projectType.slice(1)} Project`,
          description: `A comprehensive ${projectType} development project`,
          projectType: projectType
        },
        contributor: {
          name: `${projectType.charAt(0).toUpperCase() + projectType.slice(1)} Contributor`,
          email: `contributor@${projectType}company.com`,
          address: '456 Contributor St, Austin, TX 78702'
        }
      };

      await this.runSingleTest(`${projectType.toUpperCase()} Project Type Test`, testData);
    }
  }

  async runEdgeCaseTests() {
    console.log('\n⚠️  Running Edge Case Tests...');
    
    // Test with maximum length fields
    const maxLengthData = {
      company: {
        name: 'A'.repeat(200), // Maximum allowed
        address: 'B'.repeat(500), // Maximum allowed
        state: 'California',
        city: 'C'.repeat(100), // Maximum allowed
        signerName: 'D'.repeat(100), // Maximum allowed
        signerTitle: 'E'.repeat(100), // Maximum allowed
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'F'.repeat(200), // Maximum allowed
        description: 'G'.repeat(1000), // Maximum allowed
        projectType: 'software'
      },
      contributor: {
        name: 'H'.repeat(100), // Maximum allowed
        email: '<EMAIL>',
        address: 'I'.repeat(500) // Maximum allowed
      }
    };

    await this.runSingleTest('Maximum Length Fields Test', maxLengthData);

    // Test with special characters
    const specialCharData = {
      company: {
        name: 'Tech & Innovation Corp.',
        address: '123 O\'Connor St., Suite #456, Austin, TX 78701',
        state: 'Texas',
        city: 'Austin',
        signerName: 'María José García-Smith',
        signerTitle: 'CEO & Founder',
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'AI/ML Analytics Platform (v2.0)',
        description: 'Advanced AI/ML platform with real-time analytics & reporting capabilities',
        projectType: 'software'
      },
      contributor: {
        name: 'Jean-Pierre O\'Connor',
        email: '<EMAIL>',
        address: '789 Saint-Laurent Blvd., Apt. #123, Austin, TX 78702'
      }
    };

    await this.runSingleTest('Special Characters Test', specialCharData);
  }

  async runPerformanceTests() {
    console.log('\n⚡ Running Performance Tests...');
    
    const testData = {
      company: {
        name: 'Performance Test Corp.',
        address: '100 Speed Lane, Fast City, FL 33101',
        state: 'Florida',
        city: 'Fast City',
        signerName: 'Speed Tester',
        signerTitle: 'Performance Manager',
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'Performance Validation Project',
        description: 'Project designed to test system performance and speed',
        projectType: 'software'
      },
      contributor: {
        name: 'Performance Contributor',
        email: '<EMAIL>',
        address: '200 Fast Ave, Fast City, FL 33102'
      }
    };

    await this.runSingleTest('Performance Test', testData, {
      validatePerformance: true,
      maxGenerationTime: 2000 // 2 seconds
    });
  }

  async runErrorHandlingTests() {
    console.log('\n🚨 Running Error Handling Tests...');
    
    // Test with missing required fields
    const incompleteData = {
      company: {
        name: 'Incomplete Corp.'
        // Missing required fields
      },
      project: {
        name: 'Incomplete Project'
        // Missing required fields
      },
      contributor: {
        name: 'Incomplete Contributor'
        // Missing required fields
      }
    };

    await this.runSingleTest('Missing Fields Error Test', incompleteData, {
      expectError: true,
      expectedErrorType: 'ValidationError'
    });
  }

  async runSingleTest(testName, testData, options = {}) {
    this.results.totalTests++;
    const startTime = Date.now();
    
    try {
      console.log(`  🧪 ${testName}...`);
      
      const result = await this.generator.generateAgreement('standard', testData);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Check if we expected an error
      if (options.expectError) {
        if (result.success) {
          throw new Error('Expected error but generation succeeded');
        }
        if (options.expectedErrorType && result.errorType !== options.expectedErrorType) {
          throw new Error(`Expected ${options.expectedErrorType} but got ${result.errorType}`);
        }
        console.log(`    ✅ Error handling test passed (${result.errorType})`);
        this.results.passedTests++;
        return;
      }

      // Check for success
      if (!result.success) {
        throw new Error(`Generation failed: ${result.error}`);
      }

      // Validate accuracy if required
      if (options.validateAccuracy) {
        const accuracyScore = result.metadata.accuracyScore;
        this.results.accuracyScores.push(accuracyScore);
        
        if (accuracyScore < (options.minAccuracyScore || 95)) {
          throw new Error(`Accuracy score ${accuracyScore}% below required ${options.minAccuracyScore || 95}%`);
        }
      }

      // Validate performance if required
      if (options.validatePerformance) {
        if (duration > (options.maxGenerationTime || 2000)) {
          throw new Error(`Generation took ${duration}ms, exceeding limit of ${options.maxGenerationTime || 2000}ms`);
        }
      }

      // Basic validation checks
      this.validateBasicRequirements(result.agreement, testData);

      console.log(`    ✅ Passed (${duration}ms, ${result.metadata.accuracyScore || 'N/A'}% accuracy)`);
      this.results.passedTests++;

      // Store test result
      this.results.testResults.push({
        name: testName,
        passed: true,
        duration: duration,
        accuracyScore: result.metadata.accuracyScore,
        agreementLength: result.agreement.length
      });

    } catch (error) {
      console.log(`    ❌ Failed: ${error.message}`);
      this.results.failedTests++;
      
      this.results.testResults.push({
        name: testName,
        passed: false,
        error: error.message,
        duration: Date.now() - startTime
      });
    }
  }

  validateBasicRequirements(agreement, testData) {
    // Check for required sections
    const requiredSections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      'SCHEDULE A',
      'SCHEDULE B'
    ];

    requiredSections.forEach(section => {
      if (!agreement.includes(section)) {
        throw new Error(`Missing required section: ${section}`);
      }
    });

    // Check for user data integration
    if (!agreement.includes(testData.company.name)) {
      throw new Error('Company name not found in agreement');
    }
    if (!agreement.includes(testData.project.name)) {
      throw new Error('Project name not found in agreement');
    }
    if (!agreement.includes(testData.contributor.name)) {
      throw new Error('Contributor name not found in agreement');
    }

    // Check for no unreplaced placeholders
    const placeholderPatterns = [
      /\{\{[A-Z_]+\}\}/g,
      /\[[A-Z][^\]]*\]/g
    ];

    placeholderPatterns.forEach(pattern => {
      const matches = agreement.match(pattern);
      if (matches) {
        throw new Error(`Unreplaced placeholders found: ${matches.join(', ')}`);
      }
    });

    // Check for no hardcoded content
    const forbiddenContent = [
      'City of Gamers Inc.',
      'Village of The Ages',
      'Gynell Journigan'
    ];

    forbiddenContent.forEach(content => {
      if (agreement.includes(content)) {
        throw new Error(`Hardcoded content found: ${content}`);
      }
    });
  }

  async generateReport() {
    const duration = this.results.endTime - this.results.startTime;
    const passRate = (this.results.passedTests / this.results.totalTests) * 100;
    const avgAccuracy = this.results.accuracyScores.length > 0 
      ? this.results.accuracyScores.reduce((a, b) => a + b, 0) / this.results.accuracyScores.length 
      : 0;

    console.log('\n📊 COMPREHENSIVE TEST RESULTS');
    console.log('==============================');
    console.log(`Total Tests: ${this.results.totalTests}`);
    console.log(`Passed: ${this.results.passedTests}`);
    console.log(`Failed: ${this.results.failedTests}`);
    console.log(`Pass Rate: ${passRate.toFixed(1)}%`);
    console.log(`Average Accuracy: ${avgAccuracy.toFixed(1)}%`);
    console.log(`Total Duration: ${duration}ms`);

    // System readiness assessment
    const isReady = this.results.failedTests === 0 && avgAccuracy >= 95;
    console.log(`\n🎯 SYSTEM READINESS: ${isReady ? '✅ READY FOR PRODUCTION' : '❌ NOT READY'}`);

    if (!isReady) {
      console.log('\n⚠️  ISSUES TO RESOLVE:');
      if (this.results.failedTests > 0) {
        console.log(`- ${this.results.failedTests} test(s) failed`);
      }
      if (avgAccuracy < 95) {
        console.log(`- Average accuracy ${avgAccuracy.toFixed(1)}% below 95% threshold`);
      }
    }

    // Save detailed report
    const reportPath = path.join(process.cwd(), 'agreement-v2-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Detailed report saved: ${reportPath}`);

    // Exit with appropriate code
    process.exit(isReady ? 0 : 1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const runner = new ComprehensiveTestRunner();
  runner.runAllTests().catch(console.error);
}
