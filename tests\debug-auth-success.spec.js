import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Debug Authentication Success', () => {
  test('Debug what happens after successful authentication', async ({ page }) => {
    console.log('🔍 Starting authentication debug...');
    
    // Go to production URL
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Step 1: Click "Sign In" to go to login page
    console.log('🔘 Clicking Sign In button...');
    await page.click('text="Sign In"');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Step 2: Click "LOGIN" to reveal the login form
    console.log('🔘 Clicking LOGIN button...');
    await page.click('text="LOGIN"');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);

    // Step 3: Fill and submit the login form
    console.log('📝 Filling login form...');
    const emailInput = page.locator('input[placeholder*="@"]').first();
    const passwordInput = page.locator('input[placeholder*="password"]').first();
    
    await emailInput.fill(TEST_CREDENTIALS.email);
    await passwordInput.fill(TEST_CREDENTIALS.password);

    console.log('🔘 Clicking submit button...');
    const submitButton = page.locator('button[type="submit"]').first();
    await submitButton.click();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);

    // Debug what happens after authentication
    const finalUrl = page.url();
    console.log(`🌐 Final URL after authentication: ${finalUrl}`);
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/debug-auth-success.png', fullPage: true });
    console.log('📸 Screenshot saved');
    
    // Check for various possible dashboard indicators
    const dashboardIndicators = [
      'text="Welcome back"',
      'text="Dashboard"', 
      'text="New Project"',
      'text="Browse Projects"',
      'text="Track Contribution"',
      'text="View Analytics"',
      '[data-testid="dashboard"]',
      '[data-testid="bento-dashboard"]',
      '.dashboard',
      '.bento-dashboard'
    ];
    
    console.log('🔍 Checking for dashboard indicators...');
    for (const indicator of dashboardIndicators) {
      const isVisible = await page.locator(indicator).isVisible().catch(() => false);
      console.log(`  ${indicator}: ${isVisible}`);
    }
    
    // Get page title
    const pageTitle = await page.title();
    console.log(`📄 Page title: "${pageTitle}"`);
    
    // Get all visible text on the page
    const bodyText = await page.locator('body').textContent();
    const visibleText = bodyText.substring(0, 500); // First 500 chars
    console.log(`📝 First 500 chars of page text: "${visibleText}"`);
    
    // Check for error messages
    const errorIndicators = [
      'text="Invalid"',
      'text="Error"',
      'text="Failed"',
      'text="Incorrect"',
      '.error',
      '[role="alert"]'
    ];
    
    console.log('❌ Checking for error messages...');
    for (const indicator of errorIndicators) {
      const isVisible = await page.locator(indicator).isVisible().catch(() => false);
      if (isVisible) {
        const errorText = await page.locator(indicator).textContent().catch(() => 'N/A');
        console.log(`  ERROR FOUND - ${indicator}: "${errorText}"`);
      }
    }
    
    // Check if we're still on login page
    const isStillOnLogin = finalUrl.includes('/login');
    console.log(`🔐 Still on login page: ${isStillOnLogin}`);
    
    // Check for authentication success indicators
    const authSuccessIndicators = [
      'text="Logout"',
      'text="Sign Out"', 
      'text="Profile"',
      'text="Settings"',
      '[data-testid="user-menu"]',
      '[data-testid="profile"]'
    ];
    
    console.log('✅ Checking for authentication success indicators...');
    for (const indicator of authSuccessIndicators) {
      const isVisible = await page.locator(indicator).isVisible().catch(() => false);
      console.log(`  ${indicator}: ${isVisible}`);
    }
    
    console.log('✅ Debug complete');
  });
});
