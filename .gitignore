# Environment files
.env
.env.local
.env.development
.env.production
.env.test

# Dependencies
node_modules
/client/node_modules

# Build outputs
/client/dist
/client/build
*.tsbuildinfo

# Credentials and secrets
credentials.json
*.key
*.pem
*.p12
*.pfx

# Local Netlify folder
.netlify

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache/
/client/.parcel-cache

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Temporary folders
tmp/
temp/

# Security and deployment
deployment-readiness-report.json
security-scan-results.json
