// Global Authentication Teardown for Playwright Tests
const fs = require('fs');
const path = require('path');

async function globalTeardown() {
  console.log('🧹 Cleaning up authentication artifacts...');
  
  const testResultsDir = path.join(process.cwd(), 'test-results');
  const authFiles = [
    'auth-state.json',
    'admin-auth-state.json'
  ];

  // Clean up auth state files (optional - you might want to keep them for debugging)
  for (const file of authFiles) {
    const filePath = path.join(testResultsDir, file);
    if (fs.existsSync(filePath)) {
      try {
        // Uncomment the next line if you want to delete auth files after tests
        // fs.unlinkSync(filePath);
        console.log(`📁 Auth state file preserved: ${file}`);
      } catch (error) {
        console.error(`⚠️  Could not clean up ${file}:`, error.message);
      }
    }
  }

  console.log('✅ Authentication teardown complete!');
}

module.exports = globalTeardown;
