import React from 'react';
import { Button } from '../heroui';

/**
 * ModernButton Component
 *
 * A modern button component that uses shadcn/ui's Button component.
 * This component is designed to be a drop-in replacement for Bootstrap buttons.
 *
 * @param {Object} props - Component props
 * @param {string} [props.variant] - Button variant (primary, secondary, success, danger, warning, info, light, dark)
 * @param {string} [props.size] - Button size (sm, lg)
 * @param {boolean} [props.outline] - Whether the button should have an outline style
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Button content
 * @returns {React.ReactElement} - ModernButton component
 */
const ModernButton = ({
  variant = 'primary',
  size,
  outline = false,
  className = '',
  children,
  ...props
}) => {

  // Map Bootstrap variants to shadcn/ui variants
  const variantMap = {
    primary: 'primary',
    secondary: 'secondary',
    success: 'success',
    danger: 'destructive',
    warning: 'warning',
    info: 'info',
    light: 'outline',
    dark: 'default',
  };

  // Map Bootstrap sizes to shadcn/ui sizes
  const sizeMap = {
    sm: 'sm',
    lg: 'lg',
  };

  // Determine the variant
  let buttonVariant = variantMap[variant] || 'default';
  if (outline) {
    buttonVariant = 'outline';
  }

  // Determine the size
  const buttonSize = sizeMap[size] || 'default';

  return (
    <Button
      variant={buttonVariant}
      size={buttonSize}
      className={className}
      {...props}
    >
      {children}
    </Button>
  );
};

export default ModernButton;
