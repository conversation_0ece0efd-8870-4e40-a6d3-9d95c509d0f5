import React, { useState } from 'react';
import { Card, CardBody, CardHeader, Button, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Input, Select, SelectItem } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Quick Actions Widget - 2x1 Bento Grid Component
 * 
 * Features:
 * - Quick access to common analytics actions
 * - Custom report generation
 * - Data export functionality
 * - Alert and notification setup
 * - Settings and configuration access
 */
const QuickActions = ({ onExport, onCreateReport, onSetAlert, className = "" }) => {
  const [showReportModal, setShowReportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showAlertModal, setShowAlertModal] = useState(false);

  // Quick action items
  const quickActions = [
    {
      id: 'custom-report',
      icon: '📊',
      label: 'Custom Report',
      description: 'Create detailed analytics report',
      color: 'primary',
      action: () => setShowReportModal(true)
    },
    {
      id: 'forecasting',
      icon: '📈',
      label: 'Forecasting',
      description: 'AI-powered predictions',
      color: 'secondary',
      action: () => onCreateReport?.('forecasting')
    },
    {
      id: 'export-data',
      icon: '📤',
      label: 'Export Data',
      description: 'Download analytics data',
      color: 'success',
      action: () => setShowExportModal(true)
    },
    {
      id: 'settings',
      icon: '⚙️',
      label: 'Settings',
      description: 'Configure analytics',
      color: 'default',
      action: () => onCreateReport?.('settings')
    },
    {
      id: 'alerts',
      icon: '🔔',
      label: 'Set Alerts',
      description: 'Performance notifications',
      color: 'warning',
      action: () => setShowAlertModal(true)
    },
    {
      id: 'schedule',
      icon: '📅',
      label: 'Schedule',
      description: 'Automated reports',
      color: 'secondary',
      action: () => onCreateReport?.('schedule')
    }
  ];

  return (
    <div className={`quick-actions ${className}`}>
      <Card className="bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-cyan-900/20 dark:to-blue-800/20 border-2 border-cyan-200 dark:border-cyan-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">⚡</span>
              <h3 className="text-lg font-semibold">Quick Actions</h3>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Action Grid */}
          <div className="grid grid-cols-2 gap-3">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Button
                  variant="flat"
                  color={action.color}
                  className="h-auto p-3 flex flex-col items-center gap-2 w-full"
                  onClick={action.action}
                >
                  <span className="text-2xl">{action.icon}</span>
                  <div className="text-center">
                    <div className="text-sm font-medium">{action.label}</div>
                    <div className="text-xs opacity-70">{action.description}</div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>

          {/* Quick Stats */}
          <div className="mt-4 p-3 bg-cyan-50 dark:bg-cyan-900/20 rounded-lg">
            <h5 className="text-sm font-semibold mb-2">Quick Stats</h5>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span>Reports Generated:</span>
                <span className="font-medium">23</span>
              </div>
              <div className="flex justify-between">
                <span>Data Exports:</span>
                <span className="font-medium">8</span>
              </div>
              <div className="flex justify-between">
                <span>Active Alerts:</span>
                <span className="font-medium">5</span>
              </div>
              <div className="flex justify-between">
                <span>Scheduled:</span>
                <span className="font-medium">3</span>
              </div>
            </div>
          </div>

          {/* View All Button */}
          <Button
            variant="bordered"
            color="primary"
            size="sm"
            className="w-full mt-3"
            onClick={() => onCreateReport?.('view-all')}
          >
            View All Actions
          </Button>
        </CardBody>
      </Card>

      {/* Custom Report Modal */}
      <Modal isOpen={showReportModal} onClose={() => setShowReportModal(false)} size="md">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">📊 Create Custom Report</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Report Name"
                placeholder="Monthly Performance Review"
              />
              <Select label="Report Type" placeholder="Select report type">
                <SelectItem key="performance">Performance Analysis</SelectItem>
                <SelectItem key="revenue">Revenue Report</SelectItem>
                <SelectItem key="user-engagement">User Engagement</SelectItem>
                <SelectItem key="custom">Custom Dashboard</SelectItem>
              </Select>
              <Select label="Time Period" placeholder="Select time period">
                <SelectItem key="7d">Last 7 Days</SelectItem>
                <SelectItem key="30d">Last 30 Days</SelectItem>
                <SelectItem key="90d">Last 90 Days</SelectItem>
                <SelectItem key="custom">Custom Range</SelectItem>
              </Select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onClick={() => setShowReportModal(false)}>
              Cancel
            </Button>
            <Button color="primary" onClick={() => {
              onCreateReport?.('custom');
              setShowReportModal(false);
            }}>
              Create Report
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Export Data Modal */}
      <Modal isOpen={showExportModal} onClose={() => setShowExportModal(false)} size="md">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">📤 Export Analytics Data</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Select label="Export Format" placeholder="Select format">
                <SelectItem key="pdf">PDF Report</SelectItem>
                <SelectItem key="excel">Excel Spreadsheet</SelectItem>
                <SelectItem key="csv">CSV Data</SelectItem>
                <SelectItem key="json">JSON Data</SelectItem>
              </Select>
              <Select label="Data Range" placeholder="Select data range">
                <SelectItem key="current">Current Dashboard</SelectItem>
                <SelectItem key="all">All Analytics Data</SelectItem>
                <SelectItem key="revenue">Revenue Data Only</SelectItem>
                <SelectItem key="performance">Performance Metrics</SelectItem>
              </Select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onClick={() => setShowExportModal(false)}>
              Cancel
            </Button>
            <Button color="success" onClick={() => {
              onExport?.('excel');
              setShowExportModal(false);
            }}>
              Export Data
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Set Alert Modal */}
      <Modal isOpen={showAlertModal} onClose={() => setShowAlertModal(false)} size="md">
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">🔔 Set Performance Alert</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Select label="Alert Type" placeholder="Select alert type">
                <SelectItem key="revenue">Revenue Threshold</SelectItem>
                <SelectItem key="performance">Performance Drop</SelectItem>
                <SelectItem key="completion">Completion Rate</SelectItem>
                <SelectItem key="custom">Custom Metric</SelectItem>
              </Select>
              <Input
                label="Threshold Value"
                placeholder="e.g., 85% or $10,000"
              />
              <Select label="Notification Method" placeholder="How to notify">
                <SelectItem key="email">Email</SelectItem>
                <SelectItem key="dashboard">Dashboard Alert</SelectItem>
                <SelectItem key="both">Email + Dashboard</SelectItem>
              </Select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onClick={() => setShowAlertModal(false)}>
              Cancel
            </Button>
            <Button color="warning" onClick={() => {
              onSetAlert?.();
              setShowAlertModal(false);
            }}>
              Set Alert
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default QuickActions;
