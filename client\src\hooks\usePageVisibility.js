import { useState, useEffect } from 'react';
import { isPageVisible, cleanupVisibilityHandler } from '../utils/visibility-handler';

/**
 * Hook that tracks whether the page is currently visible or hidden
 * Uses the enhanced visibility handler for more reliable behavior
 * @returns {boolean} True if the page is visible, false if hidden
 */
const usePageVisibility = () => {
  // Initialize with the current visibility state
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    // Set up an interval to check visibility state
    // This ensures the component stays in sync with the global visibility state
    const checkVisibility = () => {
      const currentVisibility = isPageVisible();
      if (visible !== currentVisibility) {
        setVisible(currentVisibility);
      }
    };

    // Initial check
    checkVisibility();

    // Set up interval to check regularly
    const intervalId = setInterval(checkVisibility, 1000);

    // Clean up on unmount
    return () => {
      clearInterval(intervalId);
    };
  }, [visible]);

  return visible;
};

export default usePageVisibility;
