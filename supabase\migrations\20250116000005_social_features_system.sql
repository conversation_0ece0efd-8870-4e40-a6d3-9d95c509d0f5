-- Social Features System Migration
-- Comprehensive social networking, messaging, and collaboration infrastructure

-- Create enum types for social features
CREATE TYPE IF NOT EXISTS public.connection_status AS ENUM ('pending', 'accepted', 'declined', 'blocked');
CREATE TYPE IF NOT EXISTS public.message_type AS ENUM ('text', 'file', 'image', 'video', 'audio', 'project_invite', 'collaboration_request', 'system');
CREATE TYPE IF NOT EXISTS public.activity_type AS ENUM ('achievement', 'project_completion', 'skill_earned', 'alliance_joined', 'venture_created', 'collaboration', 'milestone', 'learning', 'social_update');
CREATE TYPE IF NOT EXISTS public.call_status AS ENUM ('initiated', 'ringing', 'active', 'ended', 'missed', 'declined');

-- User connections/allies table
CREATE TABLE IF NOT EXISTS public.user_allies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    ally_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status connection_status DEFAULT 'pending',
    connection_strength INTEGER DEFAULT 1 CHECK (connection_strength BETWEEN 1 AND 10),
    connection_reason VARCHAR(100), -- 'collaboration', 'skill_learning', 'networking', 'industry'
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    last_interaction_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by UUID NOT NULL REFERENCES auth.users(id),
    request_message TEXT,
    mutual_connections_count INTEGER DEFAULT 0,
    collaboration_history JSONB DEFAULT '[]'::jsonb,
    shared_projects JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Prevent duplicate connections
    UNIQUE(user_id, ally_id),
    -- Prevent self-connections
    CHECK (user_id != ally_id)
);

-- Messages table for direct messaging
CREATE TABLE IF NOT EXISTS public.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    from_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    to_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- NULL for group messages
    group_id UUID, -- For group/team conversations
    content TEXT NOT NULL,
    message_type message_type DEFAULT 'text',
    thread_id UUID, -- For grouping related messages
    reply_to_id UUID REFERENCES public.messages(id), -- For threaded conversations
    read_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    edited_at TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    is_pinned BOOLEAN DEFAULT false,
    
    -- File attachments
    attachment_url TEXT,
    attachment_type VARCHAR(50),
    attachment_size INTEGER,
    attachment_name TEXT,
    
    -- Rich content
    mentions JSONB DEFAULT '[]'::jsonb, -- Array of mentioned user IDs
    reactions JSONB DEFAULT '{}'::jsonb, -- Emoji reactions with user counts
    metadata JSONB DEFAULT '{}'::jsonb, -- Additional message data
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Group conversations table
CREATE TABLE IF NOT EXISTS public.conversation_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    group_type VARCHAR(50) DEFAULT 'direct', -- 'direct', 'team', 'project', 'alliance', 'public'
    created_by UUID NOT NULL REFERENCES auth.users(id),
    team_id UUID REFERENCES public.teams(id), -- Link to alliance/team
    project_id UUID REFERENCES public.projects(id), -- Link to venture/project
    
    -- Group settings
    is_private BOOLEAN DEFAULT true,
    allow_file_sharing BOOLEAN DEFAULT true,
    allow_voice_calls BOOLEAN DEFAULT true,
    allow_video_calls BOOLEAN DEFAULT true,
    max_members INTEGER DEFAULT 50,
    
    -- Group metadata
    avatar_url TEXT,
    settings JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Group membership table
CREATE TABLE IF NOT EXISTS public.conversation_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.conversation_groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member', -- 'admin', 'moderator', 'member'
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    last_read_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_muted BOOLEAN DEFAULT false,
    notification_settings JSONB DEFAULT '{}'::jsonb,
    
    UNIQUE(group_id, user_id)
);

-- Activity feed table
CREATE TABLE IF NOT EXISTS public.activity_feed (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type activity_type NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    
    -- Related entities
    related_user_id UUID REFERENCES auth.users(id),
    related_project_id UUID REFERENCES public.projects(id),
    related_team_id UUID REFERENCES public.teams(id),
    related_entity_type VARCHAR(50), -- 'skill', 'achievement', 'milestone', etc.
    related_entity_id UUID,
    
    -- Activity data
    activity_data JSONB DEFAULT '{}'::jsonb,
    visibility VARCHAR(20) DEFAULT 'public', -- 'public', 'allies', 'team', 'private'
    
    -- Engagement
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Activity engagement table (likes, comments, shares)
CREATE TABLE IF NOT EXISTS public.activity_engagement (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    activity_id UUID NOT NULL REFERENCES public.activity_feed(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    engagement_type VARCHAR(20) NOT NULL, -- 'like', 'comment', 'share'
    content TEXT, -- For comments
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    UNIQUE(activity_id, user_id, engagement_type)
);

-- Video/voice calls table
CREATE TABLE IF NOT EXISTS public.calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    call_id VARCHAR(100) UNIQUE NOT NULL, -- External call service ID
    initiated_by UUID NOT NULL REFERENCES auth.users(id),
    call_type VARCHAR(20) NOT NULL, -- 'voice', 'video', 'screen_share'
    status call_status DEFAULT 'initiated',
    
    -- Call participants
    participants JSONB NOT NULL DEFAULT '[]'::jsonb, -- Array of user IDs
    max_participants INTEGER DEFAULT 10,
    
    -- Call metadata
    group_id UUID REFERENCES public.conversation_groups(id),
    project_id UUID REFERENCES public.projects(id),
    team_id UUID REFERENCES public.teams(id),
    
    -- Call timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    
    -- Call settings
    is_recorded BOOLEAN DEFAULT false,
    recording_url TEXT,
    meeting_notes TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Call participants table
CREATE TABLE IF NOT EXISTS public.call_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    call_id UUID NOT NULL REFERENCES public.calls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    left_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    
    -- Participant state
    is_muted BOOLEAN DEFAULT false,
    camera_enabled BOOLEAN DEFAULT true,
    screen_sharing BOOLEAN DEFAULT false,
    
    UNIQUE(call_id, user_id)
);

-- Enhanced notifications for social features
ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS priority VARCHAR(20) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
ADD COLUMN IF NOT EXISTS category VARCHAR(50) DEFAULT 'general', -- 'message', 'call', 'social', 'project', 'system'
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS interaction_data JSONB DEFAULT '{}'::jsonb;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_allies_user_id ON public.user_allies(user_id);
CREATE INDEX IF NOT EXISTS idx_user_allies_ally_id ON public.user_allies(ally_id);
CREATE INDEX IF NOT EXISTS idx_user_allies_status ON public.user_allies(status);
CREATE INDEX IF NOT EXISTS idx_messages_from_user ON public.messages(from_user_id);
CREATE INDEX IF NOT EXISTS idx_messages_to_user ON public.messages(to_user_id);
CREATE INDEX IF NOT EXISTS idx_messages_group_id ON public.messages(group_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON public.messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feed_user_id ON public.activity_feed(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_feed_created_at ON public.activity_feed(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feed_visibility ON public.activity_feed(visibility);
CREATE INDEX IF NOT EXISTS idx_calls_status ON public.calls(status);
CREATE INDEX IF NOT EXISTS idx_calls_started_at ON public.calls(started_at DESC);

-- Enable Row Level Security
ALTER TABLE public.user_allies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_feed ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_engagement ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.calls ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.call_participants ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_allies
CREATE POLICY "Users can view their own connections" ON public.user_allies
    FOR SELECT USING (auth.uid() = user_id OR auth.uid() = ally_id);

CREATE POLICY "Users can create connection requests" ON public.user_allies
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own connections" ON public.user_allies
    FOR UPDATE USING (auth.uid() = user_id OR auth.uid() = ally_id);

-- RLS Policies for messages
CREATE POLICY "Users can view their own messages" ON public.messages
    FOR SELECT USING (
        auth.uid() = from_user_id OR
        auth.uid() = to_user_id OR
        (group_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM public.conversation_participants
            WHERE group_id = messages.group_id AND user_id = auth.uid()
        ))
    );

CREATE POLICY "Users can send messages" ON public.messages
    FOR INSERT WITH CHECK (auth.uid() = from_user_id);

CREATE POLICY "Users can update their own messages" ON public.messages
    FOR UPDATE USING (auth.uid() = from_user_id);

-- RLS Policies for conversation_groups
CREATE POLICY "Users can view groups they participate in" ON public.conversation_groups
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.conversation_participants
            WHERE group_id = conversation_groups.id AND user_id = auth.uid()
        ) OR created_by = auth.uid()
    );

CREATE POLICY "Users can create conversation groups" ON public.conversation_groups
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Group creators can update groups" ON public.conversation_groups
    FOR UPDATE USING (auth.uid() = created_by);

-- RLS Policies for conversation_participants
CREATE POLICY "Users can view group participants" ON public.conversation_participants
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM public.conversation_participants cp2
            WHERE cp2.group_id = conversation_participants.group_id AND cp2.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can join groups" ON public.conversation_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own participation" ON public.conversation_participants
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for activity_feed
CREATE POLICY "Users can view public and ally activities" ON public.activity_feed
    FOR SELECT USING (
        visibility = 'public' OR
        auth.uid() = user_id OR
        (visibility = 'allies' AND EXISTS (
            SELECT 1 FROM public.user_allies
            WHERE (user_id = auth.uid() AND ally_id = activity_feed.user_id AND status = 'accepted') OR
                  (ally_id = auth.uid() AND user_id = activity_feed.user_id AND status = 'accepted')
        )) OR
        (visibility = 'team' AND related_team_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM public.teams
            WHERE id = activity_feed.related_team_id AND auth.uid() = ANY(member_ids)
        ))
    );

CREATE POLICY "Users can create their own activities" ON public.activity_feed
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own activities" ON public.activity_feed
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for activity_engagement
CREATE POLICY "Users can view engagement on visible activities" ON public.activity_engagement
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.activity_feed
            WHERE id = activity_engagement.activity_id AND (
                visibility = 'public' OR
                auth.uid() = user_id OR
                (visibility = 'allies' AND EXISTS (
                    SELECT 1 FROM public.user_allies
                    WHERE (user_id = auth.uid() AND ally_id = activity_feed.user_id AND status = 'accepted') OR
                          (ally_id = auth.uid() AND user_id = activity_feed.user_id AND status = 'accepted')
                ))
            )
        )
    );

CREATE POLICY "Users can engage with activities" ON public.activity_engagement
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own engagement" ON public.activity_engagement
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for calls
CREATE POLICY "Users can view calls they participate in" ON public.calls
    FOR SELECT USING (
        auth.uid() = initiated_by OR
        auth.uid()::text = ANY(SELECT jsonb_array_elements_text(participants))
    );

CREATE POLICY "Users can initiate calls" ON public.calls
    FOR INSERT WITH CHECK (auth.uid() = initiated_by);

CREATE POLICY "Call initiators can update calls" ON public.calls
    FOR UPDATE USING (auth.uid() = initiated_by);

-- RLS Policies for call_participants
CREATE POLICY "Users can view call participants" ON public.call_participants
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM public.calls
            WHERE id = call_participants.call_id AND (
                initiated_by = auth.uid() OR
                auth.uid()::text = ANY(SELECT jsonb_array_elements_text(participants))
            )
        )
    );

CREATE POLICY "Users can join calls" ON public.call_participants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their call participation" ON public.call_participants
    FOR UPDATE USING (auth.uid() = user_id);

-- Database functions for social features

-- Function to send friend request
CREATE OR REPLACE FUNCTION public.send_friend_request(
    target_user_id UUID,
    request_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    request_id UUID;
    existing_connection UUID;
BEGIN
    -- Check if connection already exists
    SELECT id INTO existing_connection
    FROM public.user_allies
    WHERE (user_id = auth.uid() AND ally_id = target_user_id) OR
          (user_id = target_user_id AND ally_id = auth.uid());

    IF existing_connection IS NOT NULL THEN
        RAISE EXCEPTION 'Connection already exists';
    END IF;

    -- Create friend request
    INSERT INTO public.user_allies (
        user_id, ally_id, status, created_by, request_message
    ) VALUES (
        auth.uid(), target_user_id, 'pending', auth.uid(), request_message
    ) RETURNING id INTO request_id;

    -- Create notification for target user
    INSERT INTO public.notifications (
        user_id, type, title, message, related_id, category, priority
    ) VALUES (
        target_user_id,
        'friend_request',
        'New Connection Request',
        COALESCE(request_message, 'Someone wants to connect with you'),
        request_id,
        'social',
        'normal'
    );

    RETURN request_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to respond to friend request
CREATE OR REPLACE FUNCTION public.respond_to_friend_request(
    request_id UUID,
    response TEXT -- 'accept', 'decline'
)
RETURNS BOOLEAN AS $$
DECLARE
    request_record RECORD;
BEGIN
    -- Get the request
    SELECT * INTO request_record
    FROM public.user_allies
    WHERE id = request_id AND ally_id = auth.uid() AND status = 'pending';

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Friend request not found or not authorized';
    END IF;

    -- Update the request
    UPDATE public.user_allies
    SET
        status = response::connection_status,
        accepted_at = CASE WHEN response = 'accept' THEN now() ELSE NULL END,
        updated_at = now()
    WHERE id = request_id;

    -- Create notification for requester
    INSERT INTO public.notifications (
        user_id, type, title, message, related_id, category
    ) VALUES (
        request_record.user_id,
        'friend_request_response',
        CASE WHEN response = 'accept' THEN 'Connection Accepted' ELSE 'Connection Declined' END,
        CASE WHEN response = 'accept' THEN 'Your connection request was accepted!' ELSE 'Your connection request was declined.' END,
        request_id,
        'social'
    );

    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to send message
CREATE OR REPLACE FUNCTION public.send_message(
    recipient_user_id UUID DEFAULT NULL,
    group_id UUID DEFAULT NULL,
    content TEXT,
    message_type message_type DEFAULT 'text',
    reply_to_id UUID DEFAULT NULL,
    attachment_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    message_id UUID;
    thread_id UUID;
BEGIN
    -- Validate input
    IF recipient_user_id IS NULL AND group_id IS NULL THEN
        RAISE EXCEPTION 'Must specify either recipient_user_id or group_id';
    END IF;

    -- Generate thread_id for direct messages
    IF recipient_user_id IS NOT NULL AND group_id IS NULL THEN
        -- Create consistent thread_id for direct messages
        SELECT CASE
            WHEN auth.uid() < recipient_user_id THEN
                auth.uid()::text || '_' || recipient_user_id::text
            ELSE
                recipient_user_id::text || '_' || auth.uid()::text
        END INTO thread_id;
    ELSE
        thread_id := group_id;
    END IF;

    -- Insert message
    INSERT INTO public.messages (
        from_user_id, to_user_id, group_id, content, message_type,
        thread_id, reply_to_id, attachment_url, attachment_type, attachment_name
    ) VALUES (
        auth.uid(),
        recipient_user_id,
        group_id,
        content,
        message_type,
        thread_id::uuid,
        reply_to_id,
        attachment_data->>'url',
        attachment_data->>'type',
        attachment_data->>'name'
    ) RETURNING id INTO message_id;

    -- Create notification for recipient (direct message only)
    IF recipient_user_id IS NOT NULL THEN
        INSERT INTO public.notifications (
            user_id, type, title, message, related_id, category
        ) VALUES (
            recipient_user_id,
            'message',
            'New Message',
            LEFT(content, 100),
            message_id,
            'message'
        );
    END IF;

    RETURN message_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create activity feed entry
CREATE OR REPLACE FUNCTION public.create_activity(
    activity_type activity_type,
    title TEXT,
    description TEXT DEFAULT NULL,
    activity_data JSONB DEFAULT '{}',
    visibility VARCHAR(20) DEFAULT 'public',
    related_entity_type VARCHAR(50) DEFAULT NULL,
    related_entity_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO public.activity_feed (
        user_id, activity_type, title, description, activity_data,
        visibility, related_entity_type, related_entity_id
    ) VALUES (
        auth.uid(), activity_type, title, description, activity_data,
        visibility, related_entity_type, related_entity_id
    ) RETURNING id INTO activity_id;

    RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to initiate call
CREATE OR REPLACE FUNCTION public.initiate_call(
    participants UUID[],
    call_type VARCHAR(20) DEFAULT 'video',
    group_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    call_id UUID;
    call_external_id VARCHAR(100);
    participant_id UUID;
BEGIN
    -- Generate external call ID
    call_external_id := 'call_' || extract(epoch from now())::bigint || '_' || substr(md5(random()::text), 1, 8);

    -- Create call record
    INSERT INTO public.calls (
        call_id, initiated_by, call_type, participants, group_id
    ) VALUES (
        call_external_id, auth.uid(), call_type, to_jsonb(participants), group_id
    ) RETURNING id INTO call_id;

    -- Add call participants
    FOREACH participant_id IN ARRAY participants
    LOOP
        INSERT INTO public.call_participants (call_id, user_id)
        VALUES (call_id, participant_id);

        -- Create notification for each participant (except initiator)
        IF participant_id != auth.uid() THEN
            INSERT INTO public.notifications (
                user_id, type, title, message, related_id, category, priority
            ) VALUES (
                participant_id,
                'call_invitation',
                'Incoming Call',
                'You have an incoming ' || call_type || ' call',
                call_id,
                'call',
                'high'
            );
        END IF;
    END LOOP;

    RETURN call_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's social feed
CREATE OR REPLACE FUNCTION public.get_social_feed(
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    activity_type activity_type,
    title TEXT,
    description TEXT,
    activity_data JSONB,
    visibility VARCHAR(20),
    likes_count INTEGER,
    comments_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    user_name TEXT,
    user_avatar TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        af.id,
        af.user_id,
        af.activity_type,
        af.title,
        af.description,
        af.activity_data,
        af.visibility,
        af.likes_count,
        af.comments_count,
        af.created_at,
        COALESCE(u.display_name, u.email) as user_name,
        u.avatar_url as user_avatar
    FROM public.activity_feed af
    LEFT JOIN auth.users u ON af.user_id = u.id
    WHERE (
        af.visibility = 'public' OR
        af.user_id = auth.uid() OR
        (af.visibility = 'allies' AND EXISTS (
            SELECT 1 FROM public.user_allies ua
            WHERE (ua.user_id = auth.uid() AND ua.ally_id = af.user_id AND ua.status = 'accepted') OR
                  (ua.ally_id = auth.uid() AND ua.user_id = af.user_id AND ua.status = 'accepted')
        ))
    )
    ORDER BY af.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON TABLE public.user_allies IS 'User connections and friend relationships with status tracking';
COMMENT ON TABLE public.messages IS 'Direct and group messaging system with rich media support';
COMMENT ON TABLE public.conversation_groups IS 'Group conversations for teams, projects, and social groups';
COMMENT ON TABLE public.activity_feed IS 'Social activity feed for achievements, updates, and collaboration';
COMMENT ON TABLE public.calls IS 'Video and voice call management with participant tracking';
COMMENT ON FUNCTION public.send_friend_request IS 'Send a friend/connection request to another user';
COMMENT ON FUNCTION public.send_message IS 'Send a message to a user or group with notification';
COMMENT ON FUNCTION public.create_activity IS 'Create an activity feed entry for social sharing';
COMMENT ON FUNCTION public.initiate_call IS 'Initiate a video or voice call with multiple participants';
