import React from 'react';
import { useVirtualScrolling } from '../../utils/performance/componentOptimization';

const VirtualScrollList = ({ 
  items, 
  itemHeight = 50, 
  containerHeight = 400,
  renderItem,
  className = ''
}) => {
  const { visibleItems, handleScroll, totalHeight, offsetY } = useVirtualScrolling(
    items, 
    itemHeight, 
    containerHeight
  );

  return (
    <div 
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.items.map((item, index) => (
            <div key={visibleItems.startIndex + index} style={{ height: itemHeight }}>
              {renderItem(item, visibleItems.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VirtualScrollList;