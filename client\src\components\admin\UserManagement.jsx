import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Textarea, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * User Management Component - Comprehensive User Account Oversight
 * 
 * Features:
 * - User account search and filtering
 * - Role and permission management
 * - Account actions (suspend, ban, activate)
 * - User profile viewing and editing
 * - Communication tools and support
 */
const UserManagement = ({ currentUser, className = "" }) => {
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');

  // Load users data
  const loadUsers = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Mock users data for development
      const mockUsers = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          display_name: 'John Doe',
          status: 'active',
          role: 'user',
          level: 12,
          joinedDate: '2024-01-15',
          lastSeen: '2 hours ago',
          studios: 2,
          projects: 5,
          reputation: 4.8,
          verified: true
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          display_name: 'Sarah Smith',
          status: 'active',
          role: 'premium',
          level: 8,
          joinedDate: '2024-03-20',
          lastSeen: '1 day ago',
          studios: 1,
          projects: 3,
          reputation: 4.6,
          verified: true
        },
        {
          id: 'user-3',
          email: '<EMAIL>',
          display_name: 'Mike Johnson',
          status: 'suspended',
          role: 'user',
          level: 5,
          joinedDate: '2024-12-01',
          lastSeen: '7 days ago',
          studios: 0,
          projects: 1,
          reputation: 3.2,
          verified: false,
          suspensionReason: 'Policy violation - inappropriate content'
        },
        {
          id: 'user-4',
          email: '<EMAIL>',
          display_name: 'Emma Wilson',
          status: 'active',
          role: 'moderator',
          level: 15,
          joinedDate: '2023-11-10',
          lastSeen: '30 minutes ago',
          studios: 3,
          projects: 12,
          reputation: 4.9,
          verified: true
        },
        {
          id: 'user-5',
          email: '<EMAIL>',
          display_name: 'Alex Brown',
          status: 'banned',
          role: 'user',
          level: 3,
          joinedDate: '2024-11-25',
          lastSeen: '2 weeks ago',
          studios: 0,
          projects: 0,
          reputation: 2.1,
          verified: false,
          banReason: 'Multiple policy violations and harassment'
        }
      ];

      setUsers(mockUsers);
      
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  // Filter users based on search and filters
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    return matchesSearch && matchesStatus && matchesRole;
  });

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'active': 'success',
      'suspended': 'warning',
      'banned': 'danger',
      'pending': 'default'
    };
    return colors[status] || 'default';
  };

  // Get role color
  const getRoleColor = (role) => {
    const colors = {
      'admin': 'danger',
      'moderator': 'warning',
      'premium': 'primary',
      'user': 'default'
    };
    return colors[role] || 'default';
  };

  // Handle user action
  const handleUserAction = async (action, user) => {
    setSelectedUser(user);
    setActionType(action);
    setActionReason('');
    setShowActionModal(true);
  };

  // Execute user action
  const executeUserAction = async () => {
    try {
      if (!actionReason.trim() && actionType !== 'activate') {
        toast.error('Please provide a reason for this action');
        return;
      }

      // Mock API call - in production this would call the admin API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update user status locally
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === selectedUser.id 
            ? { 
                ...user, 
                status: actionType === 'activate' ? 'active' : actionType,
                [`${actionType}Reason`]: actionReason
              }
            : user
        )
      );

      toast.success(`User ${actionType}d successfully`);
      setShowActionModal(false);
      setSelectedUser(null);
      setActionType('');
      setActionReason('');
      
    } catch (error) {
      console.error('Error executing user action:', error);
      toast.error('Failed to execute action');
    }
  };

  // Handle send message
  const handleSendMessage = (user) => {
    toast.info(`Message interface for ${user.display_name} coming soon`);
  };

  // Handle view profile
  const handleViewProfile = (user) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  useEffect(() => {
    loadUsers();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">👥</span>
              <div>
                <h2 className="text-2xl font-bold">User Management</h2>
                <p className="text-default-600">Manage user accounts, roles, and permissions</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">{users.length}</div>
              <div className="text-sm text-default-600">Total Users</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startContent={<span>🔍</span>}
              className="flex-1"
            />
            
            <Select
              label="Status"
              selectedKeys={[statusFilter]}
              onSelectionChange={(keys) => setStatusFilter(Array.from(keys)[0])}
              className="w-40"
            >
              <SelectItem key="all">All Status</SelectItem>
              <SelectItem key="active">Active</SelectItem>
              <SelectItem key="suspended">Suspended</SelectItem>
              <SelectItem key="banned">Banned</SelectItem>
              <SelectItem key="pending">Pending</SelectItem>
            </Select>
            
            <Select
              label="Role"
              selectedKeys={[roleFilter]}
              onSelectionChange={(keys) => setRoleFilter(Array.from(keys)[0])}
              className="w-40"
            >
              <SelectItem key="all">All Roles</SelectItem>
              <SelectItem key="admin">Admin</SelectItem>
              <SelectItem key="moderator">Moderator</SelectItem>
              <SelectItem key="premium">Premium</SelectItem>
              <SelectItem key="user">User</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Users ({filteredUsers.length})</h3>
        </CardHeader>
        <CardBody>
          <Table aria-label="Users table">
            <TableHeader>
              <TableColumn>USER</TableColumn>
              <TableColumn>STATUS</TableColumn>
              <TableColumn>ROLE</TableColumn>
              <TableColumn>ACTIVITY</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {user.display_name.charAt(0)}
                      </div>
                      <div>
                        <div className="font-semibold">{user.display_name}</div>
                        <div className="text-sm text-default-600">{user.email}</div>
                        <div className="text-xs text-default-500">
                          Level {user.level} • Joined {user.joinedDate}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Chip
                        color={getStatusColor(user.status)}
                        size="sm"
                        variant="flat"
                      >
                        {user.status}
                      </Chip>
                      {user.verified && (
                        <div className="text-xs text-success">✓ Verified</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Chip
                      color={getRoleColor(user.role)}
                      size="sm"
                      variant="flat"
                    >
                      {user.role}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm space-y-1">
                      <div>Last seen: {user.lastSeen}</div>
                      <div>Studios: {user.studios} • Projects: {user.projects}</div>
                      <div>Rating: {user.reputation}⭐</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="flat"
                        onClick={() => handleViewProfile(user)}
                      >
                        View
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        onClick={() => handleSendMessage(user)}
                      >
                        Message
                      </Button>
                      
                      <Dropdown>
                        <DropdownTrigger>
                          <Button size="sm" variant="flat" color="warning">
                            Actions
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu>
                          {user.status === 'active' && (
                            <>
                              <DropdownItem
                                key="suspend"
                                onClick={() => handleUserAction('suspended', user)}
                              >
                                Suspend User
                              </DropdownItem>
                              <DropdownItem
                                key="ban"
                                className="text-danger"
                                onClick={() => handleUserAction('banned', user)}
                              >
                                Ban User
                              </DropdownItem>
                            </>
                          )}
                          {(user.status === 'suspended' || user.status === 'banned') && (
                            <DropdownItem
                              key="activate"
                              className="text-success"
                              onClick={() => handleUserAction('activate', user)}
                            >
                              Activate User
                            </DropdownItem>
                          )}
                          <DropdownItem key="reset-password">
                            Reset Password
                          </DropdownItem>
                          <DropdownItem key="view-logs">
                            View Activity Logs
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* User Details Modal */}
      <Modal 
        isOpen={showUserModal} 
        onClose={() => setShowUserModal(false)}
        size="2xl"
      >
        <ModalContent>
          <ModalHeader>
            <h3>User Profile: {selectedUser?.display_name}</h3>
          </ModalHeader>
          <ModalBody>
            {selectedUser && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Email</label>
                    <div className="text-sm text-default-600">{selectedUser.email}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <div>
                      <Chip color={getStatusColor(selectedUser.status)} size="sm">
                        {selectedUser.status}
                      </Chip>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Role</label>
                    <div>
                      <Chip color={getRoleColor(selectedUser.role)} size="sm">
                        {selectedUser.role}
                      </Chip>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Level</label>
                    <div className="text-sm text-default-600">{selectedUser.level}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Joined</label>
                    <div className="text-sm text-default-600">{selectedUser.joinedDate}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Last Seen</label>
                    <div className="text-sm text-default-600">{selectedUser.lastSeen}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Reputation</label>
                    <div className="text-sm text-default-600">{selectedUser.reputation}⭐</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Verified</label>
                    <div className="text-sm text-default-600">
                      {selectedUser.verified ? '✓ Yes' : '✗ No'}
                    </div>
                  </div>
                </div>
                
                {(selectedUser.suspensionReason || selectedUser.banReason) && (
                  <div className="p-3 bg-warning-50 rounded-lg">
                    <label className="text-sm font-medium">Reason</label>
                    <div className="text-sm text-default-600">
                      {selectedUser.suspensionReason || selectedUser.banReason}
                    </div>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={() => setShowUserModal(false)}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Action Confirmation Modal */}
      <Modal 
        isOpen={showActionModal} 
        onClose={() => setShowActionModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Confirm Action: {actionType} User</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <p>Are you sure you want to {actionType} <strong>{selectedUser?.display_name}</strong>?</p>
              </div>
              
              {actionType !== 'activate' && (
                <Textarea
                  label="Reason (required)"
                  placeholder="Please provide a reason for this action..."
                  value={actionReason}
                  onChange={(e) => setActionReason(e.target.value)}
                  minRows={3}
                />
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={() => setShowActionModal(false)}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={executeUserAction}
              disabled={actionType !== 'activate' && !actionReason.trim()}
            >
              Confirm {actionType}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default UserManagement;
