-- Update revenue tables to match the new schema while preserving existing data

-- Check if the revenue table exists
DO $$
DECLARE
    revenue_table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'revenue'
    ) INTO revenue_table_exists;

    IF revenue_table_exists THEN
        RAISE NOTICE 'Revenue table exists, will update schema';
    ELSE
        RAISE NOTICE 'Revenue table does not exist, will create new tables';
    END IF;
END $$;

-- Create revenue_sources table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.revenue_sources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create revenue_entries table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.revenue_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    source_id UUID REFERENCES public.revenue_sources(id) ON DELETE SET NULL,
    amount DECIMAL(12, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    date_received DATE NOT NULL,
    description TEXT,
    reference_number TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'verified', 'distributed', 'cancelled')),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    verified_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    verified_at TIMESTAMP WITH TIME ZONE,
    has_attachments BOOLEAN DEFAULT false,
    attachments_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Create revenue_attachments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.revenue_attachments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    revenue_id UUID NOT NULL REFERENCES public.revenue_entries(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_type TEXT,
    file_size INTEGER,
    file_path TEXT NOT NULL,
    uploaded_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create royalty_distributions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.royalty_distributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    revenue_id UUID REFERENCES public.revenue_entries(id) ON DELETE SET NULL,
    distribution_date DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'calculated', 'approved', 'distributed', 'cancelled')),
    calculation_method TEXT NOT NULL,
    calculation_data JSONB DEFAULT '{}'::jsonb,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    approved_at TIMESTAMP WITH TIME ZONE,
    notes TEXT
);

-- Create royalty_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.royalty_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    distribution_id UUID NOT NULL REFERENCES public.royalty_distributions(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(12, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'failed')),
    payment_method TEXT,
    payment_reference TEXT,
    payment_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    notes TEXT
);

-- Migrate data from revenue table to revenue_entries if the revenue table exists
DO $$
DECLARE
    revenue_table_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'revenue'
    ) INTO revenue_table_exists;

    IF revenue_table_exists THEN
        -- Check if there's data to migrate
        IF EXISTS (SELECT 1 FROM public.revenue LIMIT 1) THEN
            -- Create default revenue sources if they don't exist
            INSERT INTO public.revenue_sources (name, description, category, is_active)
            VALUES
            ('Steam', 'Revenue from Steam digital store', 'Digital Store', true),
            ('Epic Games Store', 'Revenue from Epic Games Store', 'Digital Store', true),
            ('App Store', 'Revenue from Apple App Store', 'Mobile Store', true),
            ('Google Play', 'Revenue from Google Play Store', 'Mobile Store', true),
            ('Direct Sales', 'Revenue from direct sales on own website', 'Direct', true),
            ('Licensing', 'Revenue from licensing agreements', 'Licensing', true),
            ('Merchandise', 'Revenue from merchandise sales', 'Physical', true),
            ('Crowdfunding', 'Revenue from crowdfunding campaigns', 'Crowdfunding', true),
            ('Subscriptions', 'Revenue from subscription services', 'Subscription', true),
            ('Advertising', 'Revenue from in-game or in-app advertising', 'Advertising', true),
            ('Other', 'Other revenue sources', 'Other', true)
            ON CONFLICT (name) DO NOTHING;

            -- Get the 'Other' source ID for fallback
            DECLARE
                other_source_id UUID;
            BEGIN
                SELECT id INTO other_source_id FROM public.revenue_sources WHERE name = 'Other';

                -- Migrate data from revenue to revenue_entries
                INSERT INTO public.revenue_entries (
                    id,
                    project_id,
                    source_id,
                    amount,
                    currency,
                    date_received,
                    description,
                    reference_number,
                    status,
                    created_by,
                    created_at,
                    updated_at,
                    metadata
                )
                SELECT
                    r.id,
                    r.project_id,
                    COALESCE(
                        (SELECT id FROM public.revenue_sources WHERE name = r.source LIMIT 1),
                        other_source_id
                    ),
                    r.amount,
                    r.currency,
                    r.date_received,
                    r.description,
                    r.reference_number,
                    CASE
                        WHEN r.status = 'approved' THEN 'verified'
                        WHEN r.status = 'pending' THEN 'pending'
                        WHEN r.status = 'cancelled' THEN 'cancelled'
                        ELSE 'pending'
                    END,
                    r.created_by,
                    r.created_at,
                    r.updated_at,
                    jsonb_build_object(
                        'original_source', r.source,
                        'original_category', r.category,
                        'original_distribution_status', r.distribution_status,
                        'original_distributed_at', r.distributed_at
                    )
                FROM public.revenue r
                ON CONFLICT (id) DO NOTHING;

                -- Migrate receipt data if it exists
                IF EXISTS (
                    SELECT 1
                    FROM information_schema.columns
                    WHERE table_schema = 'public'
                    AND table_name = 'revenue'
                    AND column_name = 'has_receipt'
                ) THEN
                    UPDATE public.revenue_entries re
                    SET
                        has_attachments = r.has_receipt,
                        attachments_count = CASE WHEN r.has_receipt THEN 1 ELSE 0 END
                    FROM public.revenue r
                    WHERE re.id = r.id AND r.has_receipt = true;

                    -- Create attachment records for receipts
                    INSERT INTO public.revenue_attachments (
                        revenue_id,
                        file_name,
                        file_type,
                        file_size,
                        file_path,
                        uploaded_by,
                        created_at,
                        updated_at
                    )
                    SELECT
                        r.id,
                        'receipt.pdf',
                        'application/pdf',
                        0,
                        COALESCE(r.receipt_url, 'receipts/' || r.id || '/receipt.pdf'),
                        r.created_by,
                        r.created_at,
                        r.updated_at
                    FROM public.revenue r
                    WHERE r.has_receipt = true
                    ON CONFLICT DO NOTHING;
                END IF;

                -- Migrate distribution data if it exists
                IF EXISTS (
                    SELECT 1
                    FROM information_schema.columns
                    WHERE table_schema = 'public'
                    AND table_name = 'revenue'
                    AND column_name = 'distribution_status'
                ) THEN
                    INSERT INTO public.royalty_distributions (
                        project_id,
                        revenue_id,
                        distribution_date,
                        total_amount,
                        currency,
                        status,
                        calculation_method,
                        created_by,
                        created_at,
                        updated_at
                    )
                    SELECT
                        r.project_id,
                        r.id,
                        COALESCE(r.distributed_at, r.date_received),
                        r.amount,
                        r.currency,
                        CASE
                            WHEN r.distribution_status = 'completed' THEN 'distributed'
                            WHEN r.distribution_status = 'in_progress' THEN 'calculated'
                            ELSE 'pending'
                        END,
                        'CoG',
                        r.created_by,
                        r.created_at,
                        r.updated_at
                    FROM public.revenue r
                    WHERE r.distribution_status IN ('completed', 'in_progress')
                    ON CONFLICT DO NOTHING;
                END IF;
            END;

            RAISE NOTICE 'Data migration completed';
        ELSE
            RAISE NOTICE 'No data to migrate from revenue table';
        END IF;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_revenue_entries_project_id ON public.revenue_entries(project_id);
CREATE INDEX IF NOT EXISTS idx_revenue_entries_source_id ON public.revenue_entries(source_id);
CREATE INDEX IF NOT EXISTS idx_revenue_entries_date_received ON public.revenue_entries(date_received);
CREATE INDEX IF NOT EXISTS idx_revenue_entries_status ON public.revenue_entries(status);
CREATE INDEX IF NOT EXISTS idx_revenue_attachments_revenue_id ON public.revenue_attachments(revenue_id);
CREATE INDEX IF NOT EXISTS idx_royalty_distributions_project_id ON public.royalty_distributions(project_id);
CREATE INDEX IF NOT EXISTS idx_royalty_distributions_revenue_id ON public.royalty_distributions(revenue_id);
CREATE INDEX IF NOT EXISTS idx_royalty_distributions_status ON public.royalty_distributions(status);
CREATE INDEX IF NOT EXISTS idx_royalty_payments_distribution_id ON public.royalty_payments(distribution_id);
CREATE INDEX IF NOT EXISTS idx_royalty_payments_recipient_id ON public.royalty_payments(recipient_id);
CREATE INDEX IF NOT EXISTS idx_royalty_payments_status ON public.royalty_payments(status);

-- Set up Row Level Security (RLS)
-- Revenue Sources
ALTER TABLE public.revenue_sources ENABLE ROW LEVEL SECURITY;

-- Anyone can view active revenue sources
DROP POLICY IF EXISTS "Anyone can view active revenue sources" ON public.revenue_sources;
CREATE POLICY "Anyone can view active revenue sources"
ON public.revenue_sources FOR SELECT
USING (is_active = true);

-- Only admins can create, update, or delete revenue sources
DROP POLICY IF EXISTS "Admins can manage revenue sources" ON public.revenue_sources;
CREATE POLICY "Admins can manage revenue sources"
ON public.revenue_sources FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE users.id = auth.uid()
        AND users.is_admin = true
    )
);

-- Revenue Entries
ALTER TABLE public.revenue_entries ENABLE ROW LEVEL SECURITY;

-- Project members can view revenue entries
DROP POLICY IF EXISTS "Project members can view revenue entries" ON public.revenue_entries;
CREATE POLICY "Project members can view revenue entries"
ON public.revenue_entries FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = revenue_entries.project_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
    )
);

-- Project admins can create, update, or delete revenue entries
DROP POLICY IF EXISTS "Project admins can manage revenue entries" ON public.revenue_entries;
CREATE POLICY "Project admins can manage revenue entries"
ON public.revenue_entries FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = revenue_entries.project_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Revenue Attachments
ALTER TABLE public.revenue_attachments ENABLE ROW LEVEL SECURITY;

-- Project members can view revenue attachments
DROP POLICY IF EXISTS "Project members can view revenue attachments" ON public.revenue_attachments;
CREATE POLICY "Project members can view revenue attachments"
ON public.revenue_attachments FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.revenue_entries
        JOIN public.project_contributors ON project_contributors.project_id = revenue_entries.project_id
        WHERE revenue_entries.id = revenue_attachments.revenue_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
    )
);

-- Project admins can create, update, or delete revenue attachments
DROP POLICY IF EXISTS "Project admins can manage revenue attachments" ON public.revenue_attachments;
CREATE POLICY "Project admins can manage revenue attachments"
ON public.revenue_attachments FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.revenue_entries
        JOIN public.project_contributors ON project_contributors.project_id = revenue_entries.project_id
        WHERE revenue_entries.id = revenue_attachments.revenue_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Royalty Distributions
ALTER TABLE public.royalty_distributions ENABLE ROW LEVEL SECURITY;

-- Project members can view royalty distributions
DROP POLICY IF EXISTS "Project members can view royalty distributions" ON public.royalty_distributions;
CREATE POLICY "Project members can view royalty distributions"
ON public.royalty_distributions FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = royalty_distributions.project_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
    )
);

-- Project admins can create, update, or delete royalty distributions
DROP POLICY IF EXISTS "Project admins can manage royalty distributions" ON public.royalty_distributions;
CREATE POLICY "Project admins can manage royalty distributions"
ON public.royalty_distributions FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = royalty_distributions.project_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Royalty Payments
ALTER TABLE public.royalty_payments ENABLE ROW LEVEL SECURITY;

-- Users can view their own royalty payments
DROP POLICY IF EXISTS "Users can view their own royalty payments" ON public.royalty_payments;
CREATE POLICY "Users can view their own royalty payments"
ON public.royalty_payments FOR SELECT
USING (
    recipient_id = auth.uid()
);

-- Project admins can view all royalty payments for their projects
DROP POLICY IF EXISTS "Project admins can view all royalty payments" ON public.royalty_payments;
CREATE POLICY "Project admins can view all royalty payments"
ON public.royalty_payments FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.royalty_distributions
        JOIN public.project_contributors ON project_contributors.project_id = royalty_distributions.project_id
        WHERE royalty_distributions.id = royalty_payments.distribution_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Project admins can create, update, or delete royalty payments
DROP POLICY IF EXISTS "Project admins can manage royalty payments" ON public.royalty_payments;
CREATE POLICY "Project admins can manage royalty payments"
ON public.royalty_payments FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.royalty_distributions
        JOIN public.project_contributors ON project_contributors.project_id = royalty_distributions.project_id
        WHERE royalty_distributions.id = royalty_payments.distribution_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to update the updated_at column
DROP TRIGGER IF EXISTS update_revenue_sources_updated_at ON public.revenue_sources;
CREATE TRIGGER update_revenue_sources_updated_at
BEFORE UPDATE ON public.revenue_sources
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_revenue_entries_updated_at ON public.revenue_entries;
CREATE TRIGGER update_revenue_entries_updated_at
BEFORE UPDATE ON public.revenue_entries
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_revenue_attachments_updated_at ON public.revenue_attachments;
CREATE TRIGGER update_revenue_attachments_updated_at
BEFORE UPDATE ON public.revenue_attachments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_royalty_distributions_updated_at ON public.royalty_distributions;
CREATE TRIGGER update_royalty_distributions_updated_at
BEFORE UPDATE ON public.royalty_distributions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_royalty_payments_updated_at ON public.royalty_payments;
CREATE TRIGGER update_royalty_payments_updated_at
BEFORE UPDATE ON public.royalty_payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to update the attachments_count in revenue_entries
CREATE OR REPLACE FUNCTION update_revenue_attachments_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.revenue_entries
        SET
            attachments_count = attachments_count + 1,
            has_attachments = true
        WHERE id = NEW.revenue_id;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.revenue_entries
        SET
            attachments_count = attachments_count - 1,
            has_attachments = (
                SELECT COUNT(*) > 0
                FROM public.revenue_attachments
                WHERE revenue_id = OLD.revenue_id
            )
        WHERE id = OLD.revenue_id;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create triggers to update the attachments_count
DROP TRIGGER IF EXISTS update_revenue_attachments_count_insert ON public.revenue_attachments;
CREATE TRIGGER update_revenue_attachments_count_insert
AFTER INSERT ON public.revenue_attachments
FOR EACH ROW
EXECUTE FUNCTION update_revenue_attachments_count();

DROP TRIGGER IF EXISTS update_revenue_attachments_count_delete ON public.revenue_attachments;
CREATE TRIGGER update_revenue_attachments_count_delete
AFTER DELETE ON public.revenue_attachments
FOR EACH ROW
EXECUTE FUNCTION update_revenue_attachments_count();

-- Create a function to log revenue activities
CREATE OR REPLACE FUNCTION log_revenue_activity()
RETURNS TRIGGER AS $$
DECLARE
    project_id_val UUID;
    activity_type_val TEXT;
    activity_data_val JSONB;
BEGIN
    IF TG_TABLE_NAME = 'revenue_entries' THEN
        project_id_val := NEW.project_id;

        IF TG_OP = 'INSERT' THEN
            activity_type_val := 'revenue_added';
            activity_data_val := jsonb_build_object(
                'revenue_id', NEW.id,
                'amount', NEW.amount,
                'currency', NEW.currency,
                'source_id', NEW.source_id,
                'date_received', NEW.date_received
            );
        ELSIF TG_OP = 'UPDATE' THEN
            activity_type_val := 'revenue_updated';
            activity_data_val := jsonb_build_object(
                'revenue_id', NEW.id,
                'amount', NEW.amount,
                'currency', NEW.currency,
                'source_id', NEW.source_id,
                'date_received', NEW.date_received,
                'status', NEW.status
            );
        END IF;
    ELSIF TG_TABLE_NAME = 'royalty_distributions' THEN
        project_id_val := NEW.project_id;

        IF TG_OP = 'INSERT' THEN
            activity_type_val := 'royalty_distribution_created';
            activity_data_val := jsonb_build_object(
                'distribution_id', NEW.id,
                'total_amount', NEW.total_amount,
                'currency', NEW.currency,
                'distribution_date', NEW.distribution_date,
                'calculation_method', NEW.calculation_method
            );
        ELSIF TG_OP = 'UPDATE' THEN
            activity_type_val := 'royalty_distribution_updated';
            activity_data_val := jsonb_build_object(
                'distribution_id', NEW.id,
                'total_amount', NEW.total_amount,
                'currency', NEW.currency,
                'distribution_date', NEW.distribution_date,
                'status', NEW.status
            );
        END IF;
    ELSIF TG_TABLE_NAME = 'royalty_payments' THEN
        -- Get project_id from the distribution
        SELECT d.project_id INTO project_id_val
        FROM public.royalty_distributions d
        WHERE d.id = NEW.distribution_id;

        IF TG_OP = 'INSERT' THEN
            activity_type_val := 'royalty_payment_created';
            activity_data_val := jsonb_build_object(
                'payment_id', NEW.id,
                'distribution_id', NEW.distribution_id,
                'recipient_id', NEW.recipient_id,
                'amount', NEW.amount,
                'currency', NEW.currency
            );
        ELSIF TG_OP = 'UPDATE' AND NEW.status != OLD.status THEN
            activity_type_val := 'royalty_payment_status_changed';
            activity_data_val := jsonb_build_object(
                'payment_id', NEW.id,
                'distribution_id', NEW.distribution_id,
                'recipient_id', NEW.recipient_id,
                'amount', NEW.amount,
                'currency', NEW.currency,
                'old_status', OLD.status,
                'new_status', NEW.status
            );
        END IF;
    END IF;

    -- Log the activity if we have a valid activity type
    IF activity_type_val IS NOT NULL AND project_id_val IS NOT NULL THEN
        INSERT INTO public.project_activities (
            project_id,
            user_id,
            activity_type,
            activity_data
        ) VALUES (
            project_id_val,
            COALESCE(NEW.created_by, auth.uid()),
            activity_type_val,
            activity_data_val
        );
    END IF;

    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create triggers to log revenue activities
DROP TRIGGER IF EXISTS log_revenue_entries_activity ON public.revenue_entries;
CREATE TRIGGER log_revenue_entries_activity
AFTER INSERT OR UPDATE ON public.revenue_entries
FOR EACH ROW
EXECUTE FUNCTION log_revenue_activity();

DROP TRIGGER IF EXISTS log_royalty_distributions_activity ON public.royalty_distributions;
CREATE TRIGGER log_royalty_distributions_activity
AFTER INSERT OR UPDATE ON public.royalty_distributions
FOR EACH ROW
EXECUTE FUNCTION log_revenue_activity();

DROP TRIGGER IF EXISTS log_royalty_payments_activity ON public.royalty_payments;
CREATE TRIGGER log_royalty_payments_activity
AFTER INSERT OR UPDATE ON public.royalty_payments
FOR EACH ROW
EXECUTE FUNCTION log_revenue_activity();

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.revenue_sources TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.revenue_entries TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.revenue_attachments TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.royalty_distributions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.royalty_payments TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
