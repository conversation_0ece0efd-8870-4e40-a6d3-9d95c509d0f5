import React, { useState, useContext, useEffect } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Navigate } from 'react-router-dom';
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import { toast } from 'react-hot-toast';

const ProfileMigration = () => {
  const { currentUser, isLoading } = useContext(UserContext);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState(false);
  const [results, setResults] = useState(null);

  // Check if user is admin
  useEffect(() => {
    const checkAdmin = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        const { data, error } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (error) {
          console.error('Error checking admin status:', error);
          toast.error('Error checking admin status');
          setIsAdmin(false);
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        toast.error('Error checking admin status');
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    if (!isLoading) {
      checkAdmin();
    }
  }, [currentUser, isLoading]);

  // Execute migration
  const executeMigration = async () => {
    if (!currentUser || !isAdmin) return;

    setExecuting(true);
    setResults(null);

    try {
      // Get the JWT token
      const { data: { session } } = await supabase.auth.getSession();
      const token = session?.access_token;

      if (!token) {
        toast.error('Authentication error');
        return;
      }

      // Call the Netlify function
      const response = await fetch('/.netlify/functions/execute-profile-migration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to execute migration');
      }

      setResults(data);
      
      if (data.success) {
        toast.success('Migration executed successfully');
      } else {
        toast.error('Migration completed with errors');
      }
    } catch (error) {
      console.error('Error executing migration:', error);
      toast.error(`Error executing migration: ${error.message}`);
    } finally {
      setExecuting(false);
    }
  };

  if (isLoading || loading) {
    return <LoadingAnimation />;
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  if (!isAdmin) {
    return (
      <div className="container mt-5">
        <div className="alert alert-danger">
          <h4>Access Denied</h4>
          <p>You do not have permission to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-5 profile-migration-page">
      <div className="card">
        <div className="card-header">
          <h2>Profile System Migration</h2>
        </div>
        <div className="card-body">
          <p>
            This page allows you to execute the database migration for the Retro Profile system.
            This will add the necessary columns and tables to the database.
          </p>
          
          <div className="alert alert-warning">
            <strong>Warning:</strong> This operation will modify the database schema. Make sure you have a backup before proceeding.
          </div>
          
          <button 
            className="btn btn-primary"
            onClick={executeMigration}
            disabled={executing}
          >
            {executing ? 'Executing Migration...' : 'Execute Migration'}
          </button>
          
          {results && (
            <div className="migration-results mt-4">
              <h4>Migration Results</h4>
              <div className="alert alert-info">
                <strong>Status:</strong> {results.success ? 'Success' : 'Completed with errors'}
              </div>
              
              <h5>Steps:</h5>
              <ul className="list-group">
                {results.steps.map((step, index) => (
                  <li 
                    key={index} 
                    className={`list-group-item ${
                      step.status === 'success' ? 'list-group-item-success' : 
                      step.status === 'error' ? 'list-group-item-danger' : 
                      'list-group-item-warning'
                    }`}
                  >
                    <strong>{step.step}</strong>: {step.status}
                    {step.details && (
                      <div className="mt-2 small">
                        <pre>{step.details}</pre>
                      </div>
                    )}
                  </li>
                ))}
              </ul>
              
              {results.error && (
                <div className="alert alert-danger mt-3">
                  <strong>Error:</strong> {results.error}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileMigration;
