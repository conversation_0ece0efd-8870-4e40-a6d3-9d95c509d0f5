import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Progress, Chip, Button } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * SkillMatchAnalysis Component
 * 
 * Analyzes user skills against mission requirements
 * Provides compatibility scoring and skill gap analysis
 */
const SkillMatchAnalysis = ({ 
  userSkills = [], 
  missionSkills = [], 
  mission,
  onSkillAssessment,
  className = ""
}) => {
  const [analysis, setAnalysis] = useState(null);
  const [loading, setLoading] = useState(false);

  // Perform skill matching analysis
  useEffect(() => {
    if (userSkills.length > 0 && missionSkills.length > 0) {
      performSkillAnalysis();
    }
  }, [userSkills, missionSkills]);

  const performSkillAnalysis = () => {
    setLoading(true);
    
    // Simulate analysis delay
    setTimeout(() => {
      const analysis = analyzeSkillMatch(userSkills, missionSkills);
      setAnalysis(analysis);
      setLoading(false);
    }, 500);
  };

  // Core skill matching algorithm
  const analyzeSkillMatch = (userSkills, requiredSkills) => {
    const matchedSkills = [];
    const missingSkills = [];
    const partialMatches = [];
    
    // Normalize skills for comparison
    const normalizeSkill = (skill) => skill.toLowerCase().trim();
    const userSkillsNormalized = userSkills.map(skill => ({
      original: skill,
      normalized: normalizeSkill(skill.name || skill),
      level: skill.level || 'intermediate'
    }));
    
    // Check each required skill
    requiredSkills.forEach(requiredSkill => {
      const requiredNormalized = normalizeSkill(requiredSkill);
      
      // Exact match
      const exactMatch = userSkillsNormalized.find(userSkill => 
        userSkill.normalized === requiredNormalized
      );
      
      if (exactMatch) {
        matchedSkills.push({
          skill: requiredSkill,
          userLevel: exactMatch.level,
          match: 'exact',
          confidence: 100
        });
      } else {
        // Partial match (similar skills)
        const partialMatch = findPartialMatch(requiredNormalized, userSkillsNormalized);
        
        if (partialMatch) {
          partialMatches.push({
            skill: requiredSkill,
            userSkill: partialMatch.original,
            userLevel: partialMatch.level,
            match: 'partial',
            confidence: partialMatch.confidence
          });
        } else {
          missingSkills.push({
            skill: requiredSkill,
            match: 'missing',
            confidence: 0
          });
        }
      }
    });
    
    // Calculate overall score
    const totalSkills = requiredSkills.length;
    const exactMatches = matchedSkills.length;
    const partialScore = partialMatches.reduce((sum, match) => sum + (match.confidence / 100), 0);
    const overallScore = Math.round(((exactMatches + partialScore) / totalSkills) * 100);
    
    // Determine recommendation
    const recommendation = getRecommendation(overallScore, missingSkills.length);
    
    return {
      overallScore,
      totalSkills,
      matchedSkills,
      partialMatches,
      missingSkills,
      recommendation,
      timeCommitment: calculateTimeCommitment(mission, overallScore),
      learningOpportunities: identifyLearningOpportunities(missingSkills)
    };
  };

  // Find partial skill matches
  const findPartialMatch = (requiredSkill, userSkills) => {
    const skillSynonyms = {
      'javascript': ['js', 'ecmascript', 'node.js', 'nodejs'],
      'typescript': ['ts'],
      'react': ['reactjs', 'react.js'],
      'python': ['py'],
      'css': ['styling', 'sass', 'scss', 'less'],
      'html': ['markup', 'html5'],
      'ui': ['user interface', 'interface design'],
      'ux': ['user experience', 'usability'],
      'design': ['ui', 'ux', 'visual design', 'graphic design']
    };
    
    // Check synonyms
    for (const [baseSkill, synonyms] of Object.entries(skillSynonyms)) {
      if (requiredSkill.includes(baseSkill) || synonyms.some(syn => requiredSkill.includes(syn))) {
        const match = userSkills.find(userSkill => 
          userSkill.normalized.includes(baseSkill) || 
          synonyms.some(syn => userSkill.normalized.includes(syn))
        );
        
        if (match) {
          return { ...match, confidence: 75 };
        }
      }
    }
    
    // Check substring matches
    const substringMatch = userSkills.find(userSkill => 
      userSkill.normalized.includes(requiredSkill) || 
      requiredSkill.includes(userSkill.normalized)
    );
    
    if (substringMatch) {
      return { ...substringMatch, confidence: 60 };
    }
    
    return null;
  };

  // Get recommendation based on score
  const getRecommendation = (score, missingCount) => {
    if (score >= 85) {
      return {
        level: 'excellent',
        message: 'Excellent fit! You have all the skills needed.',
        color: 'success',
        icon: '🎯'
      };
    } else if (score >= 70) {
      return {
        level: 'good',
        message: 'Good match! You can handle this mission well.',
        color: 'primary',
        icon: '👍'
      };
    } else if (score >= 50) {
      return {
        level: 'moderate',
        message: 'Moderate fit. Some learning may be required.',
        color: 'warning',
        icon: '📚'
      };
    } else {
      return {
        level: 'challenging',
        message: 'This mission would be quite challenging for your current skill level.',
        color: 'danger',
        icon: '⚠️'
      };
    }
  };

  // Calculate time commitment adjustment
  const calculateTimeCommitment = (mission, skillScore) => {
    const baseHours = mission?.estimatedDuration || 8;
    const difficultyMultiplier = skillScore >= 80 ? 1 : skillScore >= 60 ? 1.2 : 1.5;
    const adjustedHours = Math.round(baseHours * difficultyMultiplier);
    
    return {
      estimated: adjustedHours,
      confidence: skillScore >= 70 ? 'high' : skillScore >= 50 ? 'medium' : 'low'
    };
  };

  // Identify learning opportunities
  const identifyLearningOpportunities = (missingSkills) => {
    return missingSkills.map(missing => ({
      skill: missing.skill,
      difficulty: 'beginner', // Could be enhanced with skill difficulty data
      resources: [] // Could be enhanced with learning resource recommendations
    }));
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-4 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-default-600">Analyzing skill match...</p>
        </CardBody>
      </Card>
    );
  }

  if (!analysis) {
    return (
      <Card className={className}>
        <CardBody className="p-4 text-center">
          <p className="text-sm text-default-600">No skill analysis available</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">🎯 Skill Match Analysis</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          {/* Overall Score */}
          <div className="text-center">
            <div className="text-3xl font-bold mb-2" style={{ color: `var(--nextui-colors-${analysis.recommendation.color})` }}>
              {analysis.overallScore}%
            </div>
            <Progress 
              value={analysis.overallScore} 
              color={analysis.recommendation.color}
              className="mb-2"
            />
            <div className="flex items-center justify-center gap-2">
              <span>{analysis.recommendation.icon}</span>
              <span className="text-sm font-medium">{analysis.recommendation.message}</span>
            </div>
          </div>

          {/* Skill Breakdown */}
          <div className="space-y-3">
            {/* Matched Skills */}
            {analysis.matchedSkills.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-success mb-2">✅ Skills You Have</h4>
                <div className="flex flex-wrap gap-1">
                  {analysis.matchedSkills.map((skill, idx) => (
                    <Chip key={idx} color="success" variant="flat" size="sm">
                      {skill.skill} ({skill.userLevel})
                    </Chip>
                  ))}
                </div>
              </div>
            )}

            {/* Partial Matches */}
            {analysis.partialMatches.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-warning mb-2">⚠️ Similar Skills</h4>
                <div className="flex flex-wrap gap-1">
                  {analysis.partialMatches.map((skill, idx) => (
                    <Chip key={idx} color="warning" variant="flat" size="sm">
                      {skill.skill} (~{skill.userSkill})
                    </Chip>
                  ))}
                </div>
              </div>
            )}

            {/* Missing Skills */}
            {analysis.missingSkills.length > 0 && (
              <div>
                <h4 className="text-sm font-semibold text-danger mb-2">📚 Skills to Learn</h4>
                <div className="flex flex-wrap gap-1">
                  {analysis.missingSkills.map((skill, idx) => (
                    <Chip key={idx} color="danger" variant="flat" size="sm">
                      {skill.skill}
                    </Chip>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Time Commitment */}
          <div className="bg-default-50 rounded-lg p-3">
            <h4 className="text-sm font-semibold mb-2">⏰ Estimated Time Commitment</h4>
            <div className="flex justify-between items-center">
              <span className="text-sm">
                {analysis.timeCommitment.estimated} hours
              </span>
              <Chip 
                size="sm" 
                color={analysis.timeCommitment.confidence === 'high' ? 'success' : 
                       analysis.timeCommitment.confidence === 'medium' ? 'warning' : 'danger'}
                variant="flat"
              >
                {analysis.timeCommitment.confidence} confidence
              </Chip>
            </div>
          </div>

          {/* Action Button */}
          {analysis.learningOpportunities.length > 0 && onSkillAssessment && (
            <Button
              size="sm"
              variant="flat"
              onPress={() => onSkillAssessment(analysis.learningOpportunities)}
              className="w-full"
            >
              📈 Take Skill Assessment
            </Button>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default SkillMatchAnalysis;
