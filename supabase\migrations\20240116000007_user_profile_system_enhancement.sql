-- Migration: User Profile System Enhancement
-- Description: Enhanced user profiles with skills, portfolio, and professional features
-- Created: 2024-01-16
-- Integration & Services Agent

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enhanced user profiles table (extends existing users table)
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  professional_title VARCHAR(255),
  bio TEXT,
  location VARCHAR(255),
  website_url VARCHAR(500),
  linkedin_url VARCHAR(500),
  github_url VARCHAR(500),
  twitter_url VARCHAR(500),
  portfolio_url VARCHAR(500),
  profile_photo_url VARCHAR(500),
  cover_image_url VARCHAR(500),
  availability_status VARCHAR(20) DEFAULT 'available', -- 'available', 'busy', 'unavailable'
  profile_visibility VARCHAR(20) DEFAULT 'public', -- 'public', 'allies', 'private'
  years_experience INTEGER DEFAULT 0,
  hourly_rate DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'USD',
  timezone VARCHAR(50),
  languages TEXT[],
  education JSONB DEFAULT '[]'::jsonb,
  work_experience JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- User skills table
CREATE TABLE user_skills (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  skill_name VARCHAR(255) NOT NULL,
  skill_category VARCHAR(100), -- 'programming', 'design', 'marketing', 'management', etc.
  skill_level VARCHAR(20) DEFAULT 'beginner', -- 'beginner', 'intermediate', 'advanced', 'expert'
  years_experience INTEGER DEFAULT 0,
  is_verified BOOLEAN DEFAULT false,
  verification_source VARCHAR(100), -- 'platform_test', 'peer_review', 'certification', 'project'
  last_used_date DATE,
  proficiency_score INTEGER CHECK (proficiency_score >= 0 AND proficiency_score <= 100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, skill_name)
);

-- Skill endorsements table
CREATE TABLE skill_endorsements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  endorser_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  endorsed_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  skill_name VARCHAR(255) NOT NULL,
  endorsement_level VARCHAR(20) NOT NULL, -- 'beginner', 'intermediate', 'advanced', 'expert'
  message TEXT,
  relationship_context VARCHAR(100), -- 'colleague', 'client', 'mentor', 'collaborator'
  project_context VARCHAR(255), -- Optional project where skill was observed
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(endorser_id, endorsed_id, skill_name)
);

-- Portfolio items table
CREATE TABLE portfolio_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  project_url VARCHAR(500),
  repository_url VARCHAR(500),
  demo_url VARCHAR(500),
  image_url VARCHAR(500),
  images JSONB DEFAULT '[]'::jsonb, -- Array of image URLs
  technologies JSONB DEFAULT '[]'::jsonb, -- Array of technologies used
  skills_demonstrated JSONB DEFAULT '[]'::jsonb, -- Array of skills showcased
  project_type VARCHAR(50), -- 'web_app', 'mobile_app', 'design', 'api', 'game', etc.
  project_status VARCHAR(20) DEFAULT 'completed', -- 'completed', 'in_progress', 'concept'
  collaboration_type VARCHAR(20), -- 'solo', 'team', 'client_work'
  team_size INTEGER,
  role_in_project VARCHAR(100),
  start_date DATE,
  completion_date DATE,
  client_name VARCHAR(255),
  is_featured BOOLEAN DEFAULT false,
  is_public BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  like_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User achievements table
CREATE TABLE user_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  achievement_type VARCHAR(50) NOT NULL, -- 'badge', 'certification', 'milestone', 'award'
  achievement_name VARCHAR(255) NOT NULL,
  achievement_description TEXT,
  achievement_icon VARCHAR(255),
  achievement_color VARCHAR(20),
  issuer VARCHAR(255), -- Who issued the achievement
  verification_url VARCHAR(500),
  earned_date DATE NOT NULL,
  expiry_date DATE,
  is_featured BOOLEAN DEFAULT false,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User settings table (enhanced)
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  theme VARCHAR(20) DEFAULT 'system', -- 'light', 'dark', 'system'
  language VARCHAR(10) DEFAULT 'en',
  timezone VARCHAR(50),
  currency VARCHAR(3) DEFAULT 'USD',
  date_format VARCHAR(20) DEFAULT 'MM/DD/YYYY',
  time_format VARCHAR(10) DEFAULT '12h',
  email_notifications JSONB DEFAULT '{
    "project_updates": true,
    "messages": true,
    "endorsements": true,
    "achievements": true,
    "marketing": false
  }'::jsonb,
  push_notifications JSONB DEFAULT '{
    "project_updates": true,
    "messages": true,
    "endorsements": true,
    "achievements": true
  }'::jsonb,
  privacy_settings JSONB DEFAULT '{
    "profile_visibility": "public",
    "show_email": false,
    "show_location": true,
    "show_experience": true,
    "show_hourly_rate": false,
    "show_availability": true,
    "allow_endorsements": true,
    "allow_contact": true
  }'::jsonb,
  interface_preferences JSONB DEFAULT '{
    "dashboard_layout": "default",
    "sidebar_collapsed": false,
    "show_tooltips": true,
    "animation_speed": "normal"
  }'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Profile analytics table
CREATE TABLE profile_analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  metric_date DATE NOT NULL,
  profile_views INTEGER DEFAULT 0,
  portfolio_views INTEGER DEFAULT 0,
  skill_endorsements_received INTEGER DEFAULT 0,
  connection_requests INTEGER DEFAULT 0,
  project_inquiries INTEGER DEFAULT 0,
  search_appearances INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(user_id, metric_date)
);

-- Professional references table
CREATE TABLE professional_references (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reference_name VARCHAR(255) NOT NULL,
  reference_title VARCHAR(255),
  reference_company VARCHAR(255),
  reference_email VARCHAR(255),
  reference_phone VARCHAR(50),
  relationship VARCHAR(100), -- 'supervisor', 'colleague', 'client', 'mentor'
  project_context VARCHAR(255),
  reference_text TEXT,
  permission_granted BOOLEAN DEFAULT false,
  contact_allowed BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX idx_user_profiles_availability ON user_profiles(availability_status);
CREATE INDEX idx_user_profiles_visibility ON user_profiles(profile_visibility);

CREATE INDEX idx_user_skills_user_id ON user_skills(user_id);
CREATE INDEX idx_user_skills_category ON user_skills(skill_category);
CREATE INDEX idx_user_skills_level ON user_skills(skill_level);
CREATE INDEX idx_user_skills_verified ON user_skills(is_verified);

CREATE INDEX idx_skill_endorsements_endorsed_id ON skill_endorsements(endorsed_id);
CREATE INDEX idx_skill_endorsements_endorser_id ON skill_endorsements(endorser_id);
CREATE INDEX idx_skill_endorsements_skill ON skill_endorsements(skill_name);

CREATE INDEX idx_portfolio_items_user_id ON portfolio_items(user_id);
CREATE INDEX idx_portfolio_items_featured ON portfolio_items(is_featured);
CREATE INDEX idx_portfolio_items_public ON portfolio_items(is_public);
CREATE INDEX idx_portfolio_items_type ON portfolio_items(project_type);

CREATE INDEX idx_user_achievements_user_id ON user_achievements(user_id);
CREATE INDEX idx_user_achievements_type ON user_achievements(achievement_type);
CREATE INDEX idx_user_achievements_featured ON user_achievements(is_featured);

CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);

CREATE INDEX idx_profile_analytics_user_id ON profile_analytics(user_id);
CREATE INDEX idx_profile_analytics_date ON profile_analytics(metric_date);

CREATE INDEX idx_professional_references_user_id ON professional_references(user_id);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_skills_updated_at BEFORE UPDATE ON user_skills FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_portfolio_items_updated_at BEFORE UPDATE ON portfolio_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_settings_updated_at BEFORE UPDATE ON user_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_professional_references_updated_at BEFORE UPDATE ON professional_references FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_skills ENABLE ROW LEVEL SECURITY;
ALTER TABLE skill_endorsements ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolio_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE profile_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE professional_references ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can manage their own profile" ON user_profiles FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Public profiles are viewable by all" ON user_profiles FOR SELECT USING (profile_visibility = 'public' OR auth.uid() = user_id);

CREATE POLICY "Users can manage their own skills" ON user_skills FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Skills are viewable based on profile visibility" ON user_skills FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles up 
    WHERE up.user_id = user_skills.user_id 
    AND (up.profile_visibility = 'public' OR auth.uid() = user_skills.user_id)
  )
);

CREATE POLICY "Users can endorse others and view endorsements" ON skill_endorsements FOR SELECT USING (true);
CREATE POLICY "Users can create endorsements" ON skill_endorsements FOR INSERT WITH CHECK (auth.uid() = endorser_id);
CREATE POLICY "Users can update their own endorsements" ON skill_endorsements FOR UPDATE USING (auth.uid() = endorser_id);

CREATE POLICY "Users can manage their own portfolio" ON portfolio_items FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Public portfolio items are viewable by all" ON portfolio_items FOR SELECT USING (is_public = true OR auth.uid() = user_id);

CREATE POLICY "Users can manage their own achievements" ON user_achievements FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Achievements are viewable based on profile visibility" ON user_achievements FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM user_profiles up 
    WHERE up.user_id = user_achievements.user_id 
    AND (up.profile_visibility = 'public' OR auth.uid() = user_achievements.user_id)
  )
);

CREATE POLICY "Users can manage their own settings" ON user_settings FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own analytics" ON profile_analytics FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own references" ON professional_references FOR ALL USING (auth.uid() = user_id);

-- Functions for profile management
CREATE OR REPLACE FUNCTION update_profile_view_count(profile_user_id UUID)
RETURNS VOID AS $$
BEGIN
  INSERT INTO profile_analytics (user_id, metric_date, profile_views)
  VALUES (profile_user_id, CURRENT_DATE, 1)
  ON CONFLICT (user_id, metric_date)
  DO UPDATE SET profile_views = profile_analytics.profile_views + 1;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_user_skill_summary(target_user_id UUID)
RETURNS TABLE (
  total_skills INTEGER,
  verified_skills INTEGER,
  expert_skills INTEGER,
  total_endorsements INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_skills,
    COUNT(CASE WHEN is_verified THEN 1 END)::INTEGER as verified_skills,
    COUNT(CASE WHEN skill_level = 'expert' THEN 1 END)::INTEGER as expert_skills,
    (SELECT COUNT(*)::INTEGER FROM skill_endorsements WHERE endorsed_id = target_user_id) as total_endorsements
  FROM user_skills 
  WHERE user_id = target_user_id;
END;
$$ LANGUAGE plpgsql;

-- Table comments
COMMENT ON TABLE user_profiles IS 'Enhanced user profile information and professional details';
COMMENT ON TABLE user_skills IS 'User skills with levels, verification, and proficiency tracking';
COMMENT ON TABLE skill_endorsements IS 'Peer endorsements for user skills';
COMMENT ON TABLE portfolio_items IS 'User portfolio projects and work samples';
COMMENT ON TABLE user_achievements IS 'User achievements, badges, and certifications';
COMMENT ON TABLE user_settings IS 'User preferences, privacy settings, and customization';
COMMENT ON TABLE profile_analytics IS 'Profile view and engagement analytics';
COMMENT ON TABLE professional_references IS 'Professional references and recommendations';
