import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import KanbanTask from './KanbanTask';

const KanbanColumn = ({ column, tasks, onEditTask }) => {
  const { setNodeRef, isOver } = useDroppable({
    id: column.id,
  });

  return (
    <div className="bg-gray-800/40 backdrop-blur-md rounded-xl border border-white/20 p-3 h-full flex flex-col shadow-lg w-full min-w-[280px]">
      <h3 className="text-sm font-semibold text-white mb-3 flex items-center justify-between flex-shrink-0">
        <span className="flex items-center gap-2 text-sm">
          {column.title}
          <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-400 to-purple-400"></div>
        </span>
        <span className="bg-gradient-to-r from-white/20 to-white/10 text-white/80 text-xs px-2 py-1 rounded-full border border-white/20 font-medium">
          {tasks.length}
        </span>
      </h3>
      <div
        ref={setNodeRef}
        className={`flex-1 min-h-0 space-y-2 transition-all duration-300 rounded-lg p-1 overflow-y-auto ${
          isOver
            ? 'bg-gradient-to-b from-blue-500/10 to-purple-500/10 border-2 border-dashed border-blue-400/30'
            : ''
        }`}
      >
        {tasks.map((task, index) => (
          <KanbanTask
            key={task.id}
            task={task}
            index={index}
            onEdit={() => onEditTask(task.id)}
          />
        ))}
        {tasks.length === 0 && (
          <div className="flex flex-col items-center justify-center h-24 text-white/40 text-xs">
            <div className="w-8 h-8 rounded-full bg-white/5 border-2 border-dashed border-white/20 flex items-center justify-center mb-2">
              <span className="text-xs">+</span>
            </div>
            <p className="text-xs">No tasks yet</p>
            <p className="text-xs text-white/30 mt-1">Drag tasks here</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default KanbanColumn;
