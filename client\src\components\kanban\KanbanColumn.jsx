import React from 'react';
import { Droppable } from 'react-beautiful-dnd';
import KanbanTask from './KanbanTask';

const KanbanColumn = ({ column, tasks, onEditTask }) => {
  return (
    <div className="bg-gray-800/40 backdrop-blur-md rounded-xl border border-white/20 p-4 min-h-[600px] flex flex-col shadow-lg w-full max-w-none">
      <h3 className="text-base font-semibold text-white mb-4 flex items-center justify-between">
        <span className="flex items-center gap-2 text-sm">
          {column.title}
          <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-400 to-purple-400"></div>
        </span>
        <span className="bg-gradient-to-r from-white/20 to-white/10 text-white/80 text-xs px-2 py-1 rounded-full border border-white/20 font-medium">
          {tasks.length}
        </span>
      </h3>
      <Droppable droppableId={column.id}>
        {(provided, snapshot) => (
          <div
            className={`flex-1 space-y-3 transition-all duration-300 rounded-lg p-1 ${
              snapshot.isDraggingOver
                ? 'bg-gradient-to-b from-blue-500/10 to-purple-500/10 border-2 border-dashed border-blue-400/30'
                : ''
            }`}
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {tasks.map((task, index) => (
              <KanbanTask
                key={task.id}
                task={task}
                index={index}
                onEdit={() => onEditTask(task.id)}
              />
            ))}
            {provided.placeholder}
            {tasks.length === 0 && (
              <div className="flex flex-col items-center justify-center h-32 text-white/40 text-sm">
                <div className="w-10 h-10 rounded-full bg-white/5 border-2 border-dashed border-white/20 flex items-center justify-center mb-2">
                  <span className="text-sm">+</span>
                </div>
                <p className="text-xs">No tasks yet</p>
                <p className="text-xs text-white/30 mt-1">Drag tasks here</p>
              </div>
            )}
          </div>
        )}
      </Droppable>
    </div>
  );
};

export default KanbanColumn;
