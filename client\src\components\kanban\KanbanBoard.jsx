import React, { useState, useEffect, useMemo } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { supabase } from '../../utils/supabase/supabase.utils';
import KanbanColumn from './KanbanColumn';
import KanbanTaskModal from './KanbanTaskModal';
import KanbanToolbar from './KanbanToolbar';

// Debug helper function
const logDebug = (message, data) => {
  console.log(`[KanbanBoard] ${message}`, data);
};

const KanbanBoard = ({ projectId }) => {
  const [tasks, setTasks] = useState([]);
  const [allTasks, setAllTasks] = useState({});
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    assignee: '',
    taskType: '',
    difficulty: '',
    showCompleted: true
  });
  const [contributors, setContributors] = useState([]);
  const [taskTypes, setTaskTypes] = useState([]);
  const [difficultyLevels, setDifficultyLevels] = useState([]);
  const [columns, setColumns] = useState({
    todo: {
      id: 'todo',
      title: 'To Do',
      taskIds: []
    },
    in_progress: {
      id: 'in_progress',
      title: 'In Progress',
      taskIds: []
    },
    review: {
      id: 'review',
      title: 'Review',
      taskIds: []
    },
    done: {
      id: 'done',
      title: 'Done',
      taskIds: []
    },
    blocked: {
      id: 'blocked',
      title: 'Blocked',
      taskIds: []
    }
  });
  const [columnOrder, setColumnOrder] = useState(['todo', 'in_progress', 'review', 'done', 'blocked']);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [activeId, setActiveId] = useState(null);

  // Set up sensors for drag and drop with better configuration
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before drag starts
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Fetch tasks, contributors, task types, and difficulty levels from Supabase
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        // Fetch project configuration for task types and difficulty levels
        const { data: configData, error: configError } = await supabase
          .from('contribution_tracking_config')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (configError && configError.code !== 'PGRST116') {
          console.error('Error fetching project configuration:', configError);
        }

        if (configData) {
          // Set task types
          if (configData.task_types && Array.isArray(configData.task_types)) {
            setTaskTypes(configData.task_types);
          }

          // Set difficulty levels
          if (configData.difficulty_levels && Array.isArray(configData.difficulty_levels)) {
            setDifficultyLevels(configData.difficulty_levels);
          }
        } else {
          // Set default values if no configuration found
          setTaskTypes([
            { name: 'Feature', value: 'feature' },
            { name: 'Bug', value: 'bug' },
            { name: 'Documentation', value: 'documentation' },
            { name: 'Design', value: 'design' },
            { name: 'Testing', value: 'testing' }
          ]);

          setDifficultyLevels([
            { name: 'Easy', value: 'easy', multiplier: 1 },
            { name: 'Medium', value: 'medium', multiplier: 2 },
            { name: 'Hard', value: 'hard', multiplier: 3 },
            { name: 'Expert', value: 'expert', multiplier: 5 }
          ]);
        }

        // Fetch project contributors
        const { data: contributorsData, error: contributorsError } = await supabase
          .from('project_contributors')
          .select('user_id, is_admin, role')
          .eq('project_id', projectId);

        if (contributorsError) {
          console.error('Error fetching project contributors:', contributorsError);
        }

        if (contributorsData && contributorsData.length > 0) {
          // Get user details for contributors
          const userIds = contributorsData.map(c => c.user_id);

          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('id, display_name, email, avatar_url')
            .in('id', userIds);

          if (usersError) {
            console.error('Error fetching user details:', usersError);
          }

          if (usersData) {
            // Combine contributor data with user details
            const contributorsWithDetails = contributorsData.map(contributor => {
              const user = usersData.find(u => u.id === contributor.user_id);
              return {
                ...contributor,
                id: contributor.user_id, // Use user_id as the id for the select dropdown
                display_name: user?.display_name || user?.email || 'Unknown User',
                email: user?.email,
                avatar_url: user?.avatar_url
              };
            });

            setContributors(contributorsWithDetails);
          }
        }
      } catch (error) {
        console.error('Error fetching project data:', error);
      }
    };

    const fetchTasks = async () => {
      try {
        setLoading(true);
        setError(null); // Reset error state
        let data;
        let error;

        logDebug('Fetching tasks for project', projectId);
        logDebug('Current Supabase URL', supabase.supabaseUrl);

        try {
          // First, check if the tasks table exists
          logDebug('Checking if tasks table exists', null);
          const { error: tableCheckError } = await supabase
            .from('tasks')
            .select('count(*)', { count: 'exact', head: true });

          if (tableCheckError) {
            logDebug('Error checking tasks table', tableCheckError);
            // Check if the error is due to authentication
            if (tableCheckError.code === '401' || tableCheckError.message?.includes('JWT')) {
              logDebug('Authentication error detected', tableCheckError.message);
              // Try to refresh the session
              const { error: refreshError } = await supabase.auth.refreshSession();
              if (refreshError) {
                logDebug('Failed to refresh session', refreshError);
                setError('Authentication error. Please try logging in again.');
                setLoading(false);
                return;
              }
              logDebug('Session refreshed successfully', null);
            }

            if (tableCheckError.code === '42P01' ||
                tableCheckError.message?.includes('relation "tasks" does not exist')) {
              logDebug('Tasks table does not exist yet', null);
              setTasks({});
              setAllTasks({});
              setLoading(false);
              return;
            }
          }

          logDebug('Tasks table exists, fetching tasks', null);

          // Try to fetch tasks with assignee information
          const result = await supabase
            .from('tasks')
            .select('*')
            .eq('project_id', projectId);

          data = result.data || [];
          error = result.error;

          logDebug('Fetched tasks', data);
          logDebug('Fetch error', error);

          // If we have tasks and they have assignee_ids, fetch the users separately
          if (data && data.length > 0 && data.some(task => task.assignee_id)) {
            const assigneeIds = data
              .filter(task => task.assignee_id)
              .map(task => task.assignee_id);

            if (assigneeIds.length > 0) {
              logDebug('Fetching assignees', assigneeIds);

              const { data: usersData, error: usersError } = await supabase
                .from('users')
                .select('id, display_name, avatar_url')
                .in('id', assigneeIds);

              logDebug('Fetched users', usersData);
              logDebug('Users fetch error', usersError);

              if (usersError) {
                logDebug('Error fetching users', usersError);
                // Continue without user data rather than failing completely
              }

              // Add assignee information to tasks
              if (usersData) {
                data = data.map(task => {
                  if (task.assignee_id) {
                    const assignee = usersData.find(user => user.id === task.assignee_id);
                    if (assignee) {
                      return { ...task, assignee };
                    }
                  }
                  return task;
                });
              }
            }
          }
        } catch (e) {
          logDebug('Error in task fetching process', e);
          setError('An unexpected error occurred while fetching tasks. Please try again later.');
          setLoading(false);
          return;
        }

        if (error) {
          // Check if the error is because the table doesn't exist
          if (error.code === 'PGRST200' || error.message?.includes('relation "tasks" does not exist')) {
            logDebug('Tasks table does not exist yet. Creating empty task list.', null);
            setTasks({});
            setAllTasks({});
            setLoading(false);
            return;
          }

          // Check for authentication errors
          if (error.code === '401' || error.message?.includes('JWT')) {
            logDebug('Authentication error in tasks fetch', error);
            setError('Authentication error. Please try logging in again.');
            setLoading(false);
            return;
          }

          logDebug('Unhandled error in tasks fetch', error);
          throw error;
        }

        // If no data or empty array, set empty tasks
        if (!data || data.length === 0) {
          setTasks({});
          setLoading(false);
          return;
        }

        // Organize tasks by status
        const taskMap = {};
        const columnMap = { ...columns };

        // Reset taskIds arrays
        Object.keys(columnMap).forEach(columnId => {
          columnMap[columnId].taskIds = [];
        });

        // Populate taskMap and columnMap
        data.forEach(task => {
          taskMap[task.id] = task;
          const status = task.status || 'todo';
          if (columnMap[status]) {
            columnMap[status].taskIds.push(task.id);
          } else {
            // If status doesn't match any column, put in todo
            columnMap.todo.taskIds.push(task.id);
          }
        });

        // Store all tasks for filtering
        setAllTasks(taskMap);
        setTasks(taskMap);
        setColumns(columnMap);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching tasks:', error);
        setError('Failed to load tasks. Please try again later.');
        setLoading(false);
      }
    };

    if (projectId) {
      fetchProjectData();
      fetchTasks();
    }
  }, [projectId]);

  // Filter and search tasks
  useEffect(() => {
    if (Object.keys(allTasks).length === 0) return;

    // Apply filters and search
    const filteredTasks = {};

    Object.entries(allTasks).forEach(([taskId, task]) => {
      // Skip completed tasks if showCompleted is false
      if (!filters.showCompleted && task.status === 'done') {
        return;
      }

      // Filter by assignee
      if (filters.assignee && filters.assignee !== 'unassigned') {
        if (task.assignee_id !== filters.assignee) {
          return;
        }
      } else if (filters.assignee === 'unassigned') {
        if (task.assignee_id) {
          return;
        }
      }

      // Filter by task type
      if (filters.taskType && task.task_type !== filters.taskType) {
        return;
      }

      // Filter by difficulty
      if (filters.difficulty && task.difficulty_level !== filters.difficulty) {
        return;
      }

      // Search by title or description
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const titleMatch = task.title?.toLowerCase().includes(searchLower);
        const descMatch = task.description?.toLowerCase().includes(searchLower);

        if (!titleMatch && !descMatch) {
          return;
        }
      }

      // If it passes all filters, include it
      filteredTasks[taskId] = task;
    });

    // Update tasks state with filtered tasks
    setTasks(filteredTasks);

    // Update columns to only include filtered tasks
    const updatedColumns = {};

    Object.entries(columns).forEach(([columnId, column]) => {
      const filteredTaskIds = column.taskIds.filter(taskId => filteredTasks[taskId]);
      updatedColumns[columnId] = {
        ...column,
        taskIds: filteredTaskIds
      };
    });

    setColumns(updatedColumns);
  }, [allTasks, searchTerm, filters]);

  // Handle search term change
  const handleSearchChange = (term) => {
    setSearchTerm(term);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  // Handle drag start
  const handleDragStart = (event) => {
    setActiveId(event.active.id);
  };

  // Handle drag cancel
  const handleDragCancel = () => {
    setActiveId(null);
  };

  // Handle drag end
  const handleDragEnd = async (event) => {
    const { active, over } = event;

    // Clear active drag state
    setActiveId(null);

    // If no destination or dropped in the same place, do nothing
    if (!over || active.id === over.id) {
      return;
    }

    const activeId = active.id;
    const overId = over.id;

    // Find which columns contain the active and over items
    const activeColumn = Object.values(columns).find(column =>
      column.taskIds.includes(activeId)
    );
    const overColumn = Object.values(columns).find(column =>
      column.taskIds.includes(overId) || column.id === overId
    );

    if (!activeColumn || !overColumn) {
      return;
    }

    // If dropping on a column header, move to that column
    const targetColumn = overColumn.id === overId ? overColumn :
      Object.values(columns).find(column => column.taskIds.includes(overId));

    if (!targetColumn) {
      return;
    }

    // If moving within the same column
    if (activeColumn === targetColumn) {
      const activeIndex = activeColumn.taskIds.indexOf(activeId);
      const overIndex = targetColumn.taskIds.indexOf(overId);

      if (activeIndex !== overIndex) {
        const newTaskIds = arrayMove(activeColumn.taskIds, activeIndex, overIndex);

        setColumns({
          ...columns,
          [activeColumn.id]: {
            ...activeColumn,
            taskIds: newTaskIds
          }
        });
      }
    } else {
      // Moving from one column to another
      const sourceTaskIds = activeColumn.taskIds.filter(id => id !== activeId);
      const destinationTaskIds = [...targetColumn.taskIds, activeId];

      setColumns({
        ...columns,
        [activeColumn.id]: {
          ...activeColumn,
          taskIds: sourceTaskIds
        },
        [targetColumn.id]: {
          ...targetColumn,
          taskIds: destinationTaskIds
        }
      });

      // Update task status in Supabase
      try {
        const { error } = await supabase
          .from('tasks')
          .update({ status: targetColumn.id })
          .eq('id', activeId);

        if (error) throw error;

        // Update local task data
        setTasks({
          ...tasks,
          [activeId]: {
            ...tasks[activeId],
            status: targetColumn.id
          }
        });
      } catch (error) {
        console.error('Error updating task status:', error);
        // Revert the UI change if the API call fails
        setColumns({
          ...columns,
          [activeColumn.id]: activeColumn,
          [targetColumn.id]: targetColumn
        });
      }
    }
  };

  // Open modal to create a new task
  const handleAddTask = () => {
    setCurrentTask(null);
    setIsModalOpen(true);
  };

  // Open modal to edit an existing task
  const handleEditTask = (taskId) => {
    setCurrentTask(tasks[taskId]);
    setIsModalOpen(true);
  };

  // Handle task creation or update
  const handleSaveTask = async (taskData) => {
    try {
      if (taskData.id) {
        // Update existing task
        const { error } = await supabase
          .from('tasks')
          .update({
            title: taskData.title,
            description: taskData.description,
            assignee_id: taskData.assignee_id,
            task_type: taskData.task_type,
            difficulty_level: taskData.difficulty_level,
            difficulty_points: taskData.difficulty_points,
            estimated_hours: taskData.estimated_hours,
            logged_hours: taskData.logged_hours,
            status: taskData.status
          })
          .eq('id', taskData.id);

        if (error) throw error;

        // Update local state
        const updatedTask = {
          ...tasks[taskData.id],
          ...taskData
        };

        setAllTasks({
          ...allTasks,
          [taskData.id]: updatedTask
        });

        setTasks({
          ...tasks,
          [taskData.id]: updatedTask
        });

        // Update columns if status changed
        if (tasks[taskData.id].status !== taskData.status) {
          const oldStatus = tasks[taskData.id].status;
          const newStatus = taskData.status;

          const oldColumn = columns[oldStatus];
          const newColumn = columns[newStatus];

          const oldTaskIds = oldColumn.taskIds.filter(id => id !== taskData.id);
          const newTaskIds = [...newColumn.taskIds, taskData.id];

          setColumns({
            ...columns,
            [oldStatus]: {
              ...oldColumn,
              taskIds: oldTaskIds
            },
            [newStatus]: {
              ...newColumn,
              taskIds: newTaskIds
            }
          });
        }
      } else {
        // Create new task
        const { data, error } = await supabase
          .from('tasks')
          .insert({
            project_id: projectId,
            title: taskData.title,
            description: taskData.description,
            status: taskData.status || 'todo',
            assignee_id: taskData.assignee_id,
            task_type: taskData.task_type,
            difficulty_level: taskData.difficulty_level,
            difficulty_points: taskData.difficulty_points,
            estimated_hours: taskData.estimated_hours,
            logged_hours: taskData.logged_hours,
            source_tool: 'internal'
          })
          .select();

        if (error) throw error;

        const newTask = data[0];

        // Update local state
        setAllTasks({
          ...allTasks,
          [newTask.id]: newTask
        });

        setTasks({
          ...tasks,
          [newTask.id]: newTask
        });

        // Add to appropriate column
        const status = newTask.status || 'todo';
        const column = columns[status];

        setColumns({
          ...columns,
          [status]: {
            ...column,
            taskIds: [...column.taskIds, newTask.id]
          }
        });
      }

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error saving task:', error);
      alert('Failed to save task. Please try again.');
    }
  };

  // Handle task deletion
  const handleDeleteTask = async (taskId) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      // Remove from local state
      const task = tasks[taskId];
      const status = task.status;
      const column = columns[status];
      const newTaskIds = column.taskIds.filter(id => id !== taskId);

      // Create a new tasks object without the deleted task
      const newTasks = { ...tasks };
      delete newTasks[taskId];

      // Also remove from allTasks
      const newAllTasks = { ...allTasks };
      delete newAllTasks[taskId];

      setAllTasks(newAllTasks);
      setTasks(newTasks);
      setColumns({
        ...columns,
        [status]: {
          ...column,
          taskIds: newTaskIds
        }
      });

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error deleting task:', error);
      alert('Failed to delete task. Please try again.');
    }
  };

  // Always calculate task statistics first - before any conditional rendering
  // This ensures hooks are called in the same order every render
  const taskStats = useMemo(() => {
    try {
      // Ensure allTasks is an object and not null/undefined
      if (!allTasks || typeof allTasks !== 'object') {
        return {
          total: 0,
          completed: 0,
          inProgress: 0,
          review: 0,
          blocked: 0,
          completionPercentage: 0
        };
      }

      const allTasksArray = Object.values(allTasks);
      const totalTasks = allTasksArray.length;
      const completedTasks = allTasksArray.filter(task => task && task.status === 'done').length;
      const inProgressTasks = allTasksArray.filter(task => task && task.status === 'in_progress').length;
      const reviewTasks = allTasksArray.filter(task => task && task.status === 'review').length;
      const blockedTasks = allTasksArray.filter(task => task && task.status === 'blocked').length;

      return {
        total: totalTasks,
        completed: completedTasks,
        inProgress: inProgressTasks,
        review: reviewTasks,
        blocked: blockedTasks,
        completionPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
      };
    } catch (e) {
      logDebug('Error calculating task stats', e);
      return {
        total: 0,
        completed: 0,
        inProgress: 0,
        review: 0,
        blocked: 0,
        completionPercentage: 0
      };
    }
  }, [allTasks]);

  // Check if there are any tasks after all hooks are called
  const hasTasks = Object.values(tasks).length > 0;

  // Render different content based on state
  let content;

  if (loading) {
    content = <div className="kanban-loading">Loading tasks...</div>;
  } else if (error) {
    content = (
      <div className="kanban-error">
        <div className="alert alert-danger">
          <h4 className="alert-heading"><i className="bi bi-exclamation-triangle me-2"></i>Error Loading Tasks</h4>
          <p>{error}</p>
          <hr />
          <p className="mb-0">Please try again later or contact support if the problem persists.</p>
        </div>
      </div>
    );
  } else if (!hasTasks) {
    content = (
      <div className="p-6 text-center flex-1 flex items-center justify-center">
        <div className="max-w-md mx-auto">
          <div className="w-12 h-12 mx-auto mb-4 bg-blue-500/20 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">No Tasks Yet</h3>
          <p className="text-white/70 mb-4 text-sm">
            This project doesn't have any tasks yet. Get started by adding your first task!
          </p>
          <button
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 flex items-center gap-2 mx-auto text-sm"
            onClick={handleAddTask}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Add Your First Task
          </button>
        </div>
      </div>
    );
  } else {
    content = (
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 p-3 flex-1 min-h-0">
          {columnOrder.map(columnId => {
            const column = columns[columnId];
            const columnTasks = column.taskIds.map(taskId => tasks[taskId]).filter(Boolean);

            return (
              <SortableContext
                key={column.id}
                items={column.taskIds}
                strategy={verticalListSortingStrategy}
              >
                <KanbanColumn
                  column={column}
                  tasks={columnTasks}
                  onEditTask={handleEditTask}
                />
              </SortableContext>
            );
          })}
        </div>

        <DragOverlay
          adjustScale={false}
          dropAnimation={{
            duration: 200,
            easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
          }}
          style={{
            cursor: 'grabbing',
          }}
        >
          {activeId ? (
            <div
              className="bg-gray-800/95 backdrop-blur-md rounded-lg border border-blue-400/50 p-4 shadow-2xl shadow-black/40 cursor-grabbing"
              style={{
                width: '280px',
                minHeight: '120px',
                transform: 'rotate(2deg) scale(1.05)',
                transformOrigin: 'top left',
                pointerEvents: 'none'
              }}
            >
              {/* Subtle gradient overlay for depth */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none rounded-lg"></div>

              <div className="relative z-10 h-full flex flex-col">
                <div className="flex items-start justify-between mb-3">
                  <h4 className="text-white font-semibold text-base leading-tight flex-1 pr-2 line-clamp-2">
                    {tasks[activeId]?.title || 'Task'}
                  </h4>
                  <div className="text-xs text-white/60 font-mono bg-white/10 px-2 py-1 rounded text-[10px] shrink-0">
                    #{tasks[activeId]?.id?.substring(0, 6) || '000000'}
                  </div>
                </div>

                {tasks[activeId]?.description && (
                  <div className="text-white/80 text-sm mb-3 leading-relaxed line-clamp-3 flex-1">
                    {tasks[activeId].description.length > 120
                      ? `${tasks[activeId].description.substring(0, 120)}...`
                      : tasks[activeId].description}
                  </div>
                )}

                <div className="mt-auto space-y-3">
                  <div className="flex flex-wrap gap-1.5">
                    {tasks[activeId]?.task_type && (
                      <div className="bg-blue-500/30 text-blue-300 text-xs px-2 py-1 rounded border border-blue-400/40 font-medium">
                        {tasks[activeId].task_type}
                      </div>
                    )}
                    {tasks[activeId]?.difficulty_level && (
                      <div className="bg-orange-500/20 text-orange-400 border-orange-500/30 text-xs px-2 py-1 rounded border font-medium">
                        {tasks[activeId].difficulty_level}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    );
  }

  // Always return the same component structure
  return (
    <div className="w-full h-full bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 flex flex-col">
      <div className="p-3 border-b border-white/10 flex-shrink-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <h2 className="text-xl font-bold text-white">Project Tasks</h2>
          <div className="flex flex-wrap gap-4 text-xs">
            <span className="flex items-center gap-3 text-white/80 font-medium" title="Total Tasks">
              <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
              Total: <span className="text-white font-semibold">{taskStats.total}</span>
            </span>
            <span className="flex items-center gap-3 text-white/80 font-medium" title="Completed Tasks">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              Completed: <span className="text-white font-semibold">{taskStats.completed}</span>
            </span>
            <span className="flex items-center gap-3 text-white/80 font-medium" title="Completion Percentage">
              <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
              Progress: <span className="text-white font-semibold">{taskStats.completionPercentage}%</span>
            </span>
          </div>
        </div>
      </div>

      <div className="flex-shrink-0">
        <KanbanToolbar
          onAddTask={handleAddTask}
          onFilterChange={handleFilterChange}
          onSearchChange={handleSearchChange}
          contributors={contributors}
          taskTypes={taskTypes}
          difficultyLevels={difficultyLevels}
        />
      </div>

      <div className="flex-1 min-h-0">
        {content}
      </div>

      {isModalOpen && (
        <KanbanTaskModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveTask}
          onDelete={handleDeleteTask}
          task={currentTask}
          projectId={projectId}
        />
      )}
    </div>
  );
};

export default KanbanBoard;
