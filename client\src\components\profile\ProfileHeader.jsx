import React from 'react';
import { Link } from 'react-router-dom';

const ProfileHeader = ({ 
  profile, 
  isOwnProfile = false, 
  onEditProfile = null,
  onEditStatus = null
}) => {
  const {
    id,
    display_name,
    headline,
    avatar_url,
    cover_image_url,
    status_message,
    availability_status,
    profile_views,
    is_premium
  } = profile || {};

  // Default avatar based on premium status
  const defaultAvatar = is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png';
  
  // Default cover image
  const defaultCover = '/default-cover.jpg';

  // Availability status options
  const availabilityOptions = {
    'available': { label: 'Available for Work', color: '#10b981' },
    'busy': { label: 'Currently Busy', color: '#f59e0b' },
    'not-available': { label: 'Not Available', color: '#ef4444' },
    'open-to-collab': { label: 'Open to Collaborations', color: '#3b82f6' }
  };

  // Get availability status details
  const availabilityDetails = availability_status && availabilityOptions[availability_status] 
    ? availabilityOptions[availability_status] 
    : { label: 'Status Not Set', color: '#6b7280' };

  return (
    <div className="profile-header-container">
      {/* Cover Image */}
      <div 
        className="profile-cover-image" 
        style={{ backgroundImage: `url(${cover_image_url || defaultCover})` }}
      >
        {isOwnProfile && onEditProfile && (
          <button 
            className="edit-cover-btn"
            onClick={() => onEditProfile('cover')}
            title="Change Cover Image"
          >
            <i className="bi bi-camera"></i>
          </button>
        )}
      </div>

      <div className="profile-header-content">
        {/* Avatar */}
        <div className="profile-avatar-container">
          <img 
            src={avatar_url || defaultAvatar} 
            alt={`${display_name}'s avatar`} 
            className="profile-avatar"
          />
          {isOwnProfile && onEditProfile && (
            <button 
              className="edit-avatar-btn"
              onClick={() => onEditProfile('avatar')}
              title="Change Profile Picture"
            >
              <i className="bi bi-camera"></i>
            </button>
          )}
        </div>

        {/* User Info */}
        <div className="profile-user-info">
          <h1 className="profile-name">
            {display_name}
            {is_premium && (
              <span className="premium-badge" title="Premium Member">
                <i className="bi bi-star-fill"></i>
              </span>
            )}
          </h1>
          
          {headline && (
            <p className="profile-headline">{headline}</p>
          )}

          <div className="profile-meta">
            <span className="profile-views">
              <i className="bi bi-eye"></i> {profile_views || 0} views
            </span>

            <span className="profile-availability" style={{ backgroundColor: availabilityDetails.color }}>
              {availabilityDetails.label}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="profile-actions">
          {isOwnProfile && onEditProfile ? (
            <button 
              className="btn btn-outline-primary edit-profile-btn"
              onClick={() => onEditProfile('profile')}
            >
              <i className="bi bi-pencil"></i> Edit Profile
            </button>
          ) : (
            <Link to={`/messages/new?recipient=${id}`} className="btn btn-outline-primary message-btn">
              <i className="bi bi-chat"></i> Message
            </Link>
          )}
        </div>
      </div>

      {/* Status Message */}
      {(status_message || (isOwnProfile && onEditStatus)) && (
        <div className="profile-status-container">
          <div className="profile-status">
            {status_message ? (
              <>
                <i className="bi bi-chat-quote status-icon"></i>
                <p className="status-text">{status_message}</p>
              </>
            ) : (
              <p className="status-text empty-status">Set a status message...</p>
            )}
          </div>
          
          {isOwnProfile && onEditStatus && (
            <button 
              className="edit-status-btn"
              onClick={onEditStatus}
              title="Update Status"
            >
              <i className="bi bi-pencil"></i>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ProfileHeader;
