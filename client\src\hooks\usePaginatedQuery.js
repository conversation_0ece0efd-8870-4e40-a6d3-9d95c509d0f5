import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '../utils/supabase/supabase.utils';
import { useDataCache } from './useDataCache';

/**
 * Paginated Query Hook
 *
 * Optimizes database queries with pagination, caching, and performance enhancements
 * for large result sets in contributions, projects, and earnings data.
 */

export const usePaginatedQuery = (options = {}) => {
  const {
    table,
    select = '*',
    filters = {},
    orderBy = { column: 'created_at', ascending: false },
    pageSize = 20,
    enabled = true,
    cacheKey,
    cacheTTL = 300000, // 5 minutes
    enableRealtime = false
  } = options;

  const { getCache, setCache } = useDataCache();

  const [state, setState] = useState({
    data: [],
    loading: false,
    error: null,
    hasNextPage: true,
    hasPreviousPage: false,
    currentPage: 1,
    totalCount: null,
    totalPages: null
  });

  const abortControllerRef = useRef();
  const realtimeSubscriptionRef = useRef();

  // Generate cache key
  const getCacheKey = useCallback((page) => {
    const filterString = JSON.stringify(filters);
    const orderString = `${orderBy.column}_${orderBy.ascending}`;
    return cacheKey || `${table}_${filterString}_${orderString}_page_${page}_size_${pageSize}`;
  }, [table, filters, orderBy, pageSize, cacheKey]);

  // Build query with filters
  const buildQuery = useCallback((page = 1) => {
    let query = supabase
      .from(table)
      .select(select, { count: 'exact' });

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (typeof value === 'object' && value.operator) {
          // Advanced filter with operator
          switch (value.operator) {
            case 'gte':
              query = query.gte(key, value.value);
              break;
            case 'lte':
              query = query.lte(key, value.value);
              break;
            case 'like':
              query = query.ilike(key, `%${value.value}%`);
              break;
            case 'in':
              query = query.in(key, value.value);
              break;
            case 'neq':
              query = query.neq(key, value.value);
              break;
            default:
              query = query.eq(key, value.value);
          }
        } else {
          // Simple equality filter
          query = query.eq(key, value);
        }
      }
    });

    // Apply ordering
    query = query.order(orderBy.column, { ascending: orderBy.ascending });

    // Apply pagination
    const from = (page - 1) * pageSize;
    const to = from + pageSize - 1;
    query = query.range(from, to);

    return query;
  }, [table, select, filters, orderBy, pageSize]);

  // Execute query
  const executeQuery = useCallback(async (page = 1, useCache = true) => {
    if (!enabled) return;

    // Check cache first
    const cacheKeyForPage = getCacheKey(page);
    if (useCache) {
      const cachedData = getCache(cacheKeyForPage);
      if (cachedData) {
        setState(prev => ({
          ...prev,
          ...cachedData,
          currentPage: page
        }));
        return cachedData;
      }
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const query = buildQuery(page);
      const { data, error, count } = await query;

      if (error) throw error;

      const totalPages = Math.ceil(count / pageSize);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      const result = {
        data: data || [],
        hasNextPage,
        hasPreviousPage,
        totalCount: count,
        totalPages,
        loading: false,
        error: null
      };

      setState(prev => ({
        ...prev,
        ...result,
        currentPage: page
      }));

      // Cache the result
      setCache(cacheKeyForPage, result, cacheTTL);

      return result;
    } catch (error) {
      if (error.name !== 'AbortError') {
        setState(prev => ({
          ...prev,
          loading: false,
          error
        }));
      }
    }
  }, [enabled, getCacheKey, getCache, setCache, cacheTTL, buildQuery, pageSize]);

  // Navigation functions
  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= state.totalPages) {
      executeQuery(page);
    }
  }, [executeQuery, state.totalPages]);

  const nextPage = useCallback(() => {
    if (state.hasNextPage) {
      goToPage(state.currentPage + 1);
    }
  }, [goToPage, state.hasNextPage, state.currentPage]);

  const previousPage = useCallback(() => {
    if (state.hasPreviousPage) {
      goToPage(state.currentPage - 1);
    }
  }, [goToPage, state.hasPreviousPage, state.currentPage]);

  const firstPage = useCallback(() => {
    goToPage(1);
  }, [goToPage]);

  const lastPage = useCallback(() => {
    if (state.totalPages) {
      goToPage(state.totalPages);
    }
  }, [goToPage, state.totalPages]);

  // Refresh current page
  const refresh = useCallback(() => {
    executeQuery(state.currentPage, false);
  }, [executeQuery, state.currentPage]);

  // Setup realtime subscription with error handling
  useEffect(() => {
    if (!enableRealtime || !table) return;

    try {
      const subscription = supabase
        .channel(`${table}_changes`)
        .on('postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table
          },
          (payload) => {
            console.log('Realtime change detected:', payload);
            // Refresh current page when data changes
            refresh();
          }
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log(`✅ Realtime subscription active for ${table}`);
          } else if (status === 'CHANNEL_ERROR') {
            console.warn(`⚠️ Realtime subscription error for ${table}`);
          } else if (status === 'TIMED_OUT') {
            console.warn(`⏰ Realtime subscription timed out for ${table}`);
          } else if (status === 'CLOSED') {
            console.log(`🔒 Realtime subscription closed for ${table}`);
          }
        });

      realtimeSubscriptionRef.current = subscription;

      return () => {
        if (realtimeSubscriptionRef.current) {
          try {
            supabase.removeChannel(realtimeSubscriptionRef.current);
          } catch (error) {
            console.warn('Error removing realtime subscription:', error);
          }
        }
      };
    } catch (error) {
      console.warn(`Failed to setup realtime subscription for ${table}:`, error);
    }
  }, [enableRealtime, table, refresh]);

  // Initial load
  useEffect(() => {
    executeQuery(1);
  }, [executeQuery]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (realtimeSubscriptionRef.current) {
        supabase.removeChannel(realtimeSubscriptionRef.current);
      }
    };
  }, []);

  return {
    ...state,
    goToPage,
    nextPage,
    previousPage,
    firstPage,
    lastPage,
    refresh,
    pageSize
  };
};

// Optimized contributions query
export const useContributionsQuery = (userId, options = {}) => {
  return usePaginatedQuery({
    table: 'contributions',
    select: `
      *,
      projects (
        id,
        name,
        project_type
      )
    `,
    filters: {
      user_id: userId,
      ...options.filters
    },
    orderBy: options.orderBy || { column: 'created_at', ascending: false },
    pageSize: options.pageSize || 20,
    enabled: !!userId,
    cacheKey: `contributions_${userId}`,
    enableRealtime: options.enableRealtime || false
  });
};

// Optimized projects query with fallback for missing project_contributors table
export const useProjectsQuery = (userId, options = {}) => {
  return usePaginatedQuery({
    table: 'projects',
    select: `
      *,
      project_contributors (
        user_id,
        role,
        status
      )
    `,
    filters: {
      created_by: userId,
      ...options.filters
    },
    orderBy: options.orderBy || { column: 'updated_at', ascending: false },
    pageSize: options.pageSize || 10,
    enabled: !!userId,
    cacheKey: `projects_${userId}`,
    enableRealtime: options.enableRealtime || false
  });
};

// Optimized earnings query
export const useEarningsQuery = (userId, options = {}) => {
  return usePaginatedQuery({
    table: 'payments',
    select: '*',
    filters: {
      user_id: userId,
      ...options.filters
    },
    orderBy: options.orderBy || { column: 'payment_date', ascending: false },
    pageSize: options.pageSize || 20,
    enabled: !!userId,
    cacheKey: `earnings_${userId}`,
    enableRealtime: options.enableRealtime || false
  });
};

// Infinite scroll hook for continuous loading
export const useInfiniteQuery = (options = {}) => {
  const [allData, setAllData] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);

  const {
    table,
    select = '*',
    filters = {},
    orderBy = { column: 'created_at', ascending: false },
    pageSize = 20,
    enabled = true
  } = options;

  const loadMore = useCallback(async () => {
    if (loading || !hasMore || !enabled) return;

    try {
      setLoading(true);
      setError(null);

      let query = supabase
        .from(table)
        .select(select);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          query = query.eq(key, value);
        }
      });

      // Apply ordering and pagination
      query = query
        .order(orderBy.column, { ascending: orderBy.ascending })
        .range((page - 1) * pageSize, page * pageSize - 1);

      const { data, error: queryError } = await query;

      if (queryError) throw queryError;

      if (data && data.length > 0) {
        setAllData(prev => [...prev, ...data]);
        setPage(prev => prev + 1);

        // Check if we have more data
        if (data.length < pageSize) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [table, select, filters, orderBy, pageSize, page, loading, hasMore, enabled]);

  const reset = useCallback(() => {
    setAllData([]);
    setPage(1);
    setHasMore(true);
    setError(null);
  }, []);

  // Initial load
  useEffect(() => {
    if (enabled) {
      loadMore();
    }
  }, [enabled]); // Only run on enabled change

  return {
    data: allData,
    loading,
    error,
    hasMore,
    loadMore,
    reset
  };
};

export default usePaginatedQuery;
