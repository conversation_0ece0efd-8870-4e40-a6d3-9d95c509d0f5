# Asset Processing Instructions for Dev Team
**How to Process Design Team Assets**

## 🎯 **Overview**

The design team works in `docs/design-team-assets/` and creates assets there. The dev team (AI agents) must process these assets and integrate them into the platform at `client/src/assets/design-system/`.

---

## 🔄 **Asset Processing Workflow**

### **When Design Team Updates Assets**

1. **Monitor Changes** in `docs/design-team-assets/`
2. **Process Assets** according to specifications below
3. **Copy to Implementation Location** at `client/src/assets/design-system/`
4. **Update Component Imports** to use new assets
5. **Validate Integration** in the platform

---

## 📁 **Asset Mapping**

### **Icons Processing**

#### **From Design Team Location → Implementation Location**
```
docs/design-team-assets/icons/system/alliance-icon.svg
→ client/src/assets/design-system/icons/system/alliance-24-default.svg

docs/design-team-assets/icons/system/venture-icon.svg  
→ client/src/assets/design-system/icons/system/venture-24-default.svg

docs/design-team-assets/icons/system/mission-icon.svg
→ client/src/assets/design-system/icons/system/mission-24-default.svg

docs/design-team-assets/icons/system/orb-currency-icon.svg
→ client/src/assets/design-system/icons/system/orb-24-default.svg

docs/design-team-assets/icons/navigation/spatial-grid-icon.svg
→ client/src/assets/design-system/icons/navigation/dashboard-24-default.svg

docs/design-team-assets/icons/navigation/world-view-icon.svg
→ client/src/assets/design-system/icons/navigation/world-view-24-default.svg

docs/design-team-assets/icons/social/ally-connection-icon.svg
→ client/src/assets/design-system/icons/social/ally-24-default.svg
```

### **Processing Steps for Icons**

1. **Copy Asset** from design team location to implementation location
2. **Rename File** to follow naming convention: `[name]-24-default.svg`
3. **Optimize SVG** - Remove unnecessary elements, optimize paths
4. **Validate Accessibility** - Ensure title and desc elements exist
5. **Update Icon Component** - Add to icon mapping in `client/src/components/ui/Icon.jsx`

---

## 🔧 **Icon Component Integration**

### **Auto-Update Icon Component**

When new icons are processed, update the Icon component:

```javascript
// client/src/components/ui/Icon.jsx

import { ReactComponent as AllianceIcon } from '../../assets/design-system/icons/system/alliance-24-default.svg';
import { ReactComponent as VentureIcon } from '../../assets/design-system/icons/system/venture-24-default.svg';
import { ReactComponent as MissionIcon } from '../../assets/design-system/icons/system/mission-24-default.svg';
import { ReactComponent as OrbIcon } from '../../assets/design-system/icons/system/orb-24-default.svg';
import { ReactComponent as DashboardIcon } from '../../assets/design-system/icons/navigation/dashboard-24-default.svg';
import { ReactComponent as WorldViewIcon } from '../../assets/design-system/icons/navigation/world-view-24-default.svg';
import { ReactComponent as AllyIcon } from '../../assets/design-system/icons/social/ally-24-default.svg';

const iconMap = {
  alliance: AllianceIcon,
  venture: VentureIcon,
  mission: MissionIcon,
  orb: OrbIcon,
  dashboard: DashboardIcon,
  'world-view': WorldViewIcon,
  ally: AllyIcon,
  // Add new icons here as they're processed
};

const Icon = ({ name, size = 24, className = '', ...props }) => {
  const IconComponent = iconMap[name];
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in design system`);
    return null;
  }
  
  return (
    <IconComponent 
      width={size} 
      height={size} 
      className={className}
      {...props} 
    />
  );
};

export default Icon;
```

---

## 📐 **Future Asset Types**

### **Wireframes** (When Available)
```
docs/design-team-assets/wireframes/alliance-dashboard.png
→ client/src/assets/wireframes/exports/alliance-dashboard.png
```

### **Brand Assets** (When Available)
```
docs/design-team-assets/brand/royaltea-logo.svg
→ client/src/assets/brand/logos/royaltea-logo.svg
```

### **Mockups** (When Available)
```
docs/design-team-assets/mockups/desktop-dashboard.png
→ client/src/assets/mockups/desktop/dashboard.png
```

---

## ✅ **Asset Quality Validation**

### **Before Processing Assets**
- [ ] SVG is properly formatted and optimized
- [ ] Uses `currentColor` for theme compatibility
- [ ] Includes accessibility attributes (`title`, `desc`)
- [ ] Follows 24x24px viewBox standard
- [ ] Paths are clean and optimized

### **After Processing Assets**
- [ ] Asset copied to correct implementation location
- [ ] File renamed following naming convention
- [ ] Icon component updated with new import
- [ ] Icon renders correctly in UI
- [ ] Icon works with light/dark themes
- [ ] No console errors or warnings

---

## 🚀 **Automation Opportunities**

### **Future Improvements**
1. **Automated Asset Processing** - Script to automatically copy and rename assets
2. **Icon Component Generation** - Auto-generate icon imports and mappings
3. **Asset Optimization Pipeline** - Automatic SVG optimization and validation
4. **Integration Testing** - Automated tests to ensure assets work correctly

---

## 📞 **Process Questions**

### **When Assets Are Updated**
1. Check `docs/design-team-assets/` for changes
2. Process according to mapping above
3. Test integration in development environment
4. Validate accessibility and theme compatibility
5. Deploy to staging for design team review

### **When New Asset Types Are Added**
1. Create corresponding directory structure in `client/src/assets/`
2. Update this processing guide
3. Create integration instructions for new asset type
4. Test complete workflow

---

**🎯 Goal**: Seamless integration of design team assets into the platform while maintaining quality and consistency standards.**
