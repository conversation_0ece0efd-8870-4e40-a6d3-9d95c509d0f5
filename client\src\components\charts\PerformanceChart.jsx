import React from 'react';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Tooltip,
  Legend
} from 'recharts';
import { Card, CardBody, CardHeader, Chip, Tabs, Tab } from '@heroui/react';

/**
 * Performance Chart Component - Multi-dimensional Performance Visualization
 * 
 * Features:
 * - Radar chart for skill/performance metrics
 * - Pie chart for distribution analysis
 * - Tabbed interface for different views
 * - Interactive tooltips
 * - Responsive design
 */
const PerformanceChart = ({ 
  data = {}, 
  height = 300, 
  className = "",
  title = "Performance Metrics"
}) => {
  const [activeTab, setActiveTab] = React.useState('radar');

  // Sample performance data
  const defaultRadarData = [
    { metric: 'Quality', value: 85, fullMark: 100 },
    { metric: 'Speed', value: 78, fullMark: 100 },
    { metric: 'Communication', value: 92, fullMark: 100 },
    { metric: 'Innovation', value: 74, fullMark: 100 },
    { metric: 'Reliability', value: 88, fullMark: 100 },
    { metric: 'Teamwork', value: 90, fullMark: 100 }
  ];

  const defaultPieData = [
    { name: 'Completed', value: 65, color: '#10b981' },
    { name: 'In Progress', value: 25, color: '#3b82f6' },
    { name: 'Pending', value: 7, color: '#f59e0b' },
    { name: 'Cancelled', value: 3, color: '#ef4444' }
  ];

  const radarData = data.radar || defaultRadarData;
  const pieData = data.pie || defaultPieData;

  // Custom tooltip for radar chart
  const RadarTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-slate-800 p-3 border border-default-200 rounded-lg shadow-lg">
          <p className="font-semibold text-default-900 dark:text-default-100">{label}</p>
          <p style={{ color: payload[0].color }} className="text-sm">
            Score: {payload[0].value}/100
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom tooltip for pie chart
  const PieTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      return (
        <div className="bg-white dark:bg-slate-800 p-3 border border-default-200 rounded-lg shadow-lg">
          <p className="font-semibold text-default-900 dark:text-default-100">{data.name}</p>
          <p style={{ color: data.payload.color }} className="text-sm">
            {data.value}% ({data.payload.count || Math.round(data.value * 1.5)} items)
          </p>
        </div>
      );
    }
    return null;
  };

  // Custom label for pie chart
  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-2xl">🎯</span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <Chip color="secondary" variant="flat" size="sm">
            Performance
          </Chip>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <Tabs 
          selectedKey={activeTab} 
          onSelectionChange={setActiveTab}
          size="sm"
          className="mb-4"
        >
          <Tab key="radar" title="Skills" />
          <Tab key="pie" title="Distribution" />
        </Tabs>

        <div style={{ width: '100%', height: height - 60 }}>
          <ResponsiveContainer>
            {activeTab === 'radar' ? (
              <RadarChart data={radarData}>
                <PolarGrid stroke="#e2e8f0" />
                <PolarAngleAxis 
                  dataKey="metric" 
                  tick={{ fontSize: 12, fill: '#64748b' }}
                />
                <PolarRadiusAxis 
                  angle={90} 
                  domain={[0, 100]}
                  tick={{ fontSize: 10, fill: '#64748b' }}
                />
                <Radar
                  name="Performance"
                  dataKey="value"
                  stroke="#8b5cf6"
                  fill="#8b5cf6"
                  fillOpacity={0.3}
                  strokeWidth={2}
                  dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 4 }}
                />
                <Tooltip content={<RadarTooltip />} />
              </RadarChart>
            ) : (
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<PieTooltip />} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  formatter={(value, entry) => (
                    <span style={{ color: entry.color }}>{value}</span>
                  )}
                />
              </PieChart>
            )}
          </ResponsiveContainer>
        </div>
      </CardBody>
    </Card>
  );
};

export default PerformanceChart;
