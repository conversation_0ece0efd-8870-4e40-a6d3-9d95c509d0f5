import { supabase } from '../supabase/supabase.utils';

/**
 * Default permissions for each role
 */
export const defaultRolePermissions = {
  owner: {
    // Owners have all permissions by default
    create_projects: true,
    edit_projects: true,
    delete_projects: true,
    manage_project_members: true,
    invite_members: true,
    remove_members: true,
    change_member_roles: true,
    generate_agreements: true,
    sign_agreements: true,
    view_all_agreements: true,
    view_finances: true,
    manage_payments: true,
    edit_revenue_settings: true,
  },
  admin: {
    // Admins have most permissions except critical ones
    create_projects: true,
    edit_projects: true,
    delete_projects: false,
    manage_project_members: true,
    invite_members: true,
    remove_members: true,
    change_member_roles: false,
    generate_agreements: true,
    sign_agreements: true,
    view_all_agreements: true,
    view_finances: true,
    manage_payments: false,
    edit_revenue_settings: false,
  },
  member: {
    // Regular members have limited permissions
    create_projects: false,
    edit_projects: false,
    delete_projects: false,
    manage_project_members: false,
    invite_members: false,
    remove_members: false,
    change_member_roles: false,
    generate_agreements: false,
    sign_agreements: false,
    view_all_agreements: false,
    view_finances: false,
    manage_payments: false,
    edit_revenue_settings: false,
  }
};

/**
 * Get team permissions from the database
 * @param {string} teamId - The team ID
 * @returns {Promise<Object>} - The team permissions
 */
export const getTeamPermissions = async (teamId) => {
  try {
    // Get team permissions from the database
    const { data, error } = await supabase
      .from('team_permissions')
      .select('*')
      .eq('team_id', teamId)
      .single();

    if (error) {
      // If no permissions exist yet, return default permissions
      if (error.code === 'PGRST116') {
        return {
          admin: { ...defaultRolePermissions.admin },
          member: { ...defaultRolePermissions.member }
        };
      }
      throw error;
    }

    // Return existing permissions or fall back to defaults
    return {
      admin: data.admin_permissions || { ...defaultRolePermissions.admin },
      member: data.member_permissions || { ...defaultRolePermissions.member }
    };
  } catch (error) {
    console.error('Error fetching team permissions:', error);
    // Return default permissions in case of error
    return {
      admin: { ...defaultRolePermissions.admin },
      member: { ...defaultRolePermissions.member }
    };
  }
};

/**
 * Check if a user has a specific permission in a team
 * @param {string} userId - The user ID
 * @param {string} teamId - The team ID
 * @param {string} permission - The permission to check
 * @returns {Promise<boolean>} - Whether the user has the permission
 */
export const checkTeamPermission = async (userId, teamId, permission) => {
  try {
    // Get user's role in the team
    const { data: memberData, error: memberError } = await supabase
      .from('team_members')
      .select('role, is_admin')
      .eq('team_id', teamId)
      .eq('user_id', userId)
      .single();

    if (memberError) {
      // If user is not a member, they have no permissions
      return false;
    }

    // If user is the owner, they have all permissions
    if (memberData.role === 'owner') {
      return true;
    }

    // Get team permissions
    const permissions = await getTeamPermissions(teamId);

    // Check if user has the permission based on their role
    if (memberData.is_admin) {
      return permissions.admin[permission] || false;
    } else {
      return permissions.member[permission] || false;
    }
  } catch (error) {
    console.error('Error checking team permission:', error);
    return false;
  }
};

/**
 * Get all permissions for a user in a team
 * @param {string} userId - The user ID
 * @param {string} teamId - The team ID
 * @returns {Promise<Object>} - The user's permissions
 */
export const getUserTeamPermissions = async (userId, teamId) => {
  try {
    // Get user's role in the team
    const { data: memberData, error: memberError } = await supabase
      .from('team_members')
      .select('role, is_admin')
      .eq('team_id', teamId)
      .eq('user_id', userId)
      .single();

    if (memberError) {
      // If user is not a member, they have no permissions
      return {};
    }

    // If user is the owner, they have all permissions
    if (memberData.role === 'owner') {
      return { ...defaultRolePermissions.owner };
    }

    // Get team permissions
    const permissions = await getTeamPermissions(teamId);

    // Return permissions based on user's role
    if (memberData.is_admin) {
      return permissions.admin;
    } else {
      return permissions.member;
    }
  } catch (error) {
    console.error('Error getting user team permissions:', error);
    return {};
  }
};

/**
 * React hook to check team permissions
 * @param {string} userId - The user ID
 * @param {string} teamId - The team ID
 * @returns {Object} - The permissions checking functions
 */
export const useTeamPermissions = (userId, teamId) => {
  /**
   * Check if the user has a specific permission
   * @param {string} permission - The permission to check
   * @returns {Promise<boolean>} - Whether the user has the permission
   */
  const hasPermission = async (permission) => {
    if (!userId || !teamId) return false;
    return await checkTeamPermission(userId, teamId, permission);
  };

  /**
   * Get all permissions for the user
   * @returns {Promise<Object>} - The user's permissions
   */
  const getAllPermissions = async () => {
    if (!userId || !teamId) return {};
    return await getUserTeamPermissions(userId, teamId);
  };

  return {
    hasPermission,
    getAllPermissions
  };
};
