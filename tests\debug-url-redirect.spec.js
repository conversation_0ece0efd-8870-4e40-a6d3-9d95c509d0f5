import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';

// Authentication function (copied from working test)
async function authenticateUser(page) {
  console.log('🔐 Starting authentication...');

  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);

  // Multi-step authentication flow
  console.log('🔘 Clicking Sign In button...');
  const signInButton = page.locator('text="Sign In"').first();
  await signInButton.click();
  await page.waitForTimeout(1000);

  console.log('🔘 Clicking LOGIN button...');
  const loginButton = page.locator('text="LOGIN"').first();
  await loginButton.click();
  await page.waitForTimeout(2000);

  // Find form inputs with more specific selectors
  const emailInput = page.locator('input[placeholder*="@"], input[type="email"]').first();
  const passwordInput = page.locator('input[placeholder*="password"], input[type="password"]').first();

  console.log('📧 Found email input with placeholder containing "@"');
  console.log('🔒 Found password input with placeholder containing "password"');

  console.log('📝 Filling login form...');
  await emailInput.fill('<EMAIL>');
  await passwordInput.fill('TestPassword123!');

  console.log('🔘 Clicking submit button...');
  const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  await submitButton.click();

  // Wait for navigation with longer timeout
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);

  const finalUrl = page.url();

  // Check multiple dashboard indicators (same as working test)
  const newProjectCard = page.locator('text="New Project"').isVisible().catch(() => false);
  const browseProjectsCard = page.locator('text="Browse Projects"').isVisible().catch(() => false);
  const trackContributionCard = page.locator('text="Track Contribution"').isVisible().catch(() => false);
  const viewAnalyticsCard = page.locator('text="View Analytics"').isVisible().catch(() => false);

  // Dashboard is considered loaded if we have the action cards
  const hasDashboard = (await newProjectCard) && (await browseProjectsCard) && (await trackContributionCard) && (await viewAnalyticsCard);

  console.log(`🔐 Authentication result: ${finalUrl}`);
  console.log(`📊 Dashboard visible: ${hasDashboard}`);
  console.log(`📊 Action cards - New Project: ${await newProjectCard}, Browse: ${await browseProjectsCard}, Track: ${await trackContributionCard}, Analytics: ${await viewAnalyticsCard}`);

  return { success: hasDashboard, url: finalUrl };
}

test.describe('Debug URL Redirect Issues', () => {
  test('Debug URL behavior after authentication', async ({ page }) => {
    // Set up console error tracking
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
        console.log(`❌ Browser console error: ${msg.text()}`);
      }
    });
    
    // Authenticate user
    const authResult = await authenticateUser(page);
    expect(authResult.success).toBe(true);
    
    console.log(`📍 URL immediately after authentication: ${authResult.url}`);
    
    // Wait a bit more to see if URL changes
    await page.waitForTimeout(3000);
    
    const urlAfterWait = page.url();
    console.log(`📍 URL after 3 second wait: ${urlAfterWait}`);
    
    // Check if we're on dashboard or home
    if (urlAfterWait.includes('/dashboard')) {
      console.log('✅ User is on dashboard URL');
    } else if (urlAfterWait === 'https://royalty.technology/') {
      console.log('⚠️ User is on home page URL but should be on dashboard');
    }
    
    // Try to navigate to dashboard explicitly
    console.log('🔄 Trying to navigate to /dashboard explicitly...');
    await page.goto('https://royalty.technology/dashboard');
    await page.waitForTimeout(2000);
    
    const dashboardUrl = page.url();
    console.log(`📍 URL after explicit dashboard navigation: ${dashboardUrl}`);
    
    // Check if it redirects back to home
    if (dashboardUrl !== 'https://royalty.technology/dashboard') {
      console.log('❌ Dashboard URL redirects to different URL');
    } else {
      console.log('✅ Dashboard URL stays on dashboard');
    }
    
    // Now test clicking Track Contribution from dashboard URL
    if (dashboardUrl === 'https://royalty.technology/dashboard') {
      console.log('🧪 Testing Track Contribution from dashboard URL...');
      
      // Verify action cards are still visible
      const trackCard = page.locator('text="Track Contribution"');
      const isVisible = await trackCard.isVisible();
      console.log(`📊 Track Contribution card visible on dashboard URL: ${isVisible}`);
      
      if (isVisible) {
        console.log('🔘 Clicking Track Contribution from dashboard URL...');
        await trackCard.click();
        await page.waitForTimeout(2000);
        
        const trackUrl = page.url();
        console.log(`📍 URL after Track Contribution click from dashboard: ${trackUrl}`);
        
        if (trackUrl.includes('/track')) {
          console.log('✅ Track Contribution works from dashboard URL');
        } else {
          console.log('❌ Track Contribution failed from dashboard URL');
        }
      } else {
        console.log('❌ Track Contribution button not found on dashboard URL');
      }
    }
    
    // Test from home page URL too
    console.log('🔄 Testing from home page URL...');
    await page.goto('https://royalty.technology/');
    await page.waitForTimeout(2000);
    
    const homeUrl = page.url();
    console.log(`📍 URL after going to home: ${homeUrl}`);
    
    // Check if action cards are visible on home page
    const trackCardHome = page.locator('text="Track Contribution"');
    const isVisibleHome = await trackCardHome.isVisible();
    console.log(`📊 Track Contribution card visible on home URL: ${isVisibleHome}`);
    
    if (isVisibleHome) {
      console.log('🔘 Clicking Track Contribution from home URL...');
      await trackCardHome.click();
      await page.waitForTimeout(2000);
      
      const trackUrlHome = page.url();
      console.log(`📍 URL after Track Contribution click from home: ${trackUrlHome}`);
      
      if (trackUrlHome.includes('/track')) {
        console.log('✅ Track Contribution works from home URL');
      } else {
        console.log('❌ Track Contribution failed from home URL');
      }
    }
  });
});
