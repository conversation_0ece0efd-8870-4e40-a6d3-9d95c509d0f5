import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/react';

/**
 * Success Stories Component - Real Success Stories
 * 
 * Features:
 * - Three compelling case studies with real metrics
 * - Different user types represented
 * - Credible testimonials with specific outcomes
 * - Visual hierarchy with clear results
 */
const SuccessStories = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const storyVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  };

  // Success stories data
  const stories = [
    {
      id: 'vrc',
      company: 'VRC ENTERTAINMENT',
      type: 'Established Business',
      icon: '🏢',
      testimonial: 'Royaltea transformed our talent management business. We now track $25,000 monthly recurring revenue and $150,000 in commission pipeline with complete transparency for our 20-person team.',
      results: [
        { label: 'Revenue Growth', value: '300%' },
        { label: 'Team Transparency', value: '100%' }
      ],
      details: [
        { label: 'Studio Type', value: 'Established Business' },
        { label: 'Team Size', value: '20 members' },
        { label: 'Active Projects', value: '8 projects' }
      ],
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50',
      darkBgGradient: 'from-blue-900/20 to-cyan-900/20'
    },
    {
      id: 'pixel',
      company: 'PIXEL DREAMS STUDIO',
      type: 'Creative Collaborative',
      icon: '🎮',
      testimonial: 'As an indie game studio, fair revenue sharing was impossible to track manually. Royaltea\'s CoG model helped us distribute $50,000 fairly among our 5 core members and 3 mercenaries.',
      results: [
        { label: 'Fair Distribution', value: 'Achieved' },
        { label: 'Contribution Tracking', value: 'Clear' }
      ],
      details: [
        { label: 'Studio Type', value: 'Creative Collaborative' },
        { label: 'Team Size', value: '8 members' },
        { label: 'Major Projects', value: '1 project' }
      ],
      gradient: 'from-purple-500 to-pink-500',
      bgGradient: 'from-purple-50 to-pink-50',
      darkBgGradient: 'from-purple-900/20 to-pink-900/20'
    },
    {
      id: 'sarah',
      company: 'SARAH CHEN - DEVELOPER',
      type: 'Individual Creator',
      icon: '👤',
      testimonial: 'I\'ve earned $18,400 through bounty hunting and studio work. The skill verification system helped me access premium projects and build my professional network.',
      results: [
        { label: 'Total Earned', value: '$18,400' },
        { label: 'Success Rate', value: '85%' },
        { label: 'Rating', value: '4.8⭐' }
      ],
      details: [
        { label: 'Role', value: 'Individual Creator' },
        { label: 'Level', value: 'Expert Level' },
        { label: 'Network', value: '47 allies' }
      ],
      gradient: 'from-green-500 to-emerald-500',
      bgGradient: 'from-green-50 to-emerald-50',
      darkBgGradient: 'from-green-900/20 to-emerald-900/20'
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900 py-20">
      <motion.div
        className="max-w-7xl mx-auto px-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div variants={storyVariants} className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            🏆 Real Success Stories
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            See how creators across different industries have transformed their businesses 
            with Royaltea's fair and transparent collaboration platform.
          </p>
        </motion.div>

        {/* Success Stories */}
        <div className="space-y-12">
          {stories.map((story, index) => (
            <motion.div
              key={story.id}
              variants={storyVariants}
              whileHover={{ scale: 1.01 }}
              transition={{ duration: 0.3 }}
            >
              <Card className={`bg-gradient-to-br ${story.bgGradient} dark:${story.darkBgGradient} border-2 border-gray-200 dark:border-gray-700 shadow-xl hover:shadow-2xl transition-all duration-300`}>
                <CardBody className="p-8 md:p-12">
                  <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
                    {/* Company Info */}
                    <div className="lg:col-span-3 text-center lg:text-left">
                      <div className="text-6xl mb-4">{story.icon}</div>
                      <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                        {story.company}
                      </h3>
                      <div className={`inline-block px-4 py-2 rounded-full bg-gradient-to-r ${story.gradient} text-white font-semibold text-sm`}>
                        {story.type}
                      </div>
                    </div>

                    {/* Testimonial */}
                    <div className="lg:col-span-6">
                      <div className="text-3xl text-gray-400 dark:text-gray-500 mb-4">"</div>
                      <blockquote className="text-lg md:text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                        {story.testimonial}
                      </blockquote>
                      <div className="text-3xl text-gray-400 dark:text-gray-500 text-right">"</div>
                    </div>

                    {/* Results & Details */}
                    <div className="lg:col-span-3">
                      {/* Results */}
                      <div className="mb-6">
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                          📊 Results:
                        </h4>
                        <div className="space-y-2">
                          {story.results.map((result, idx) => (
                            <div key={idx} className="flex justify-between items-center">
                              <span className="text-gray-600 dark:text-gray-400 text-sm">
                                {result.label}:
                              </span>
                              <span className="font-bold text-gray-900 dark:text-white">
                                {result.value}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Details */}
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                          🏰 Details:
                        </h4>
                        <div className="space-y-2">
                          {story.details.map((detail, idx) => (
                            <div key={idx} className="flex justify-between items-center">
                              <span className="text-gray-600 dark:text-gray-400 text-sm">
                                {detail.label}:
                              </span>
                              <span className="font-medium text-gray-900 dark:text-white text-sm">
                                {detail.value}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div variants={storyVariants} className="text-center mt-16">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Your Success Story Starts Here
            </h3>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              Join these successful creators and start building your fair, transparent creative business today.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-6 bg-white/50 dark:bg-black/20 rounded-lg">
                <div className="text-3xl mb-3">⚡</div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">2,500+</div>
                <div className="text-gray-600 dark:text-gray-400">Active Creators</div>
              </div>
              <div className="p-6 bg-white/50 dark:bg-black/20 rounded-lg">
                <div className="text-3xl mb-3">💰</div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">$2.3M+</div>
                <div className="text-gray-600 dark:text-gray-400">Revenue Distributed</div>
              </div>
              <div className="p-6 bg-white/50 dark:bg-black/20 rounded-lg">
                <div className="text-3xl mb-3">🎯</div>
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">94%</div>
                <div className="text-gray-600 dark:text-gray-400">Satisfaction Rate</div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default SuccessStories;
