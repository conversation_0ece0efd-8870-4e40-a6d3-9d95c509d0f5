// Social Service - Frontend integration for social features
import { supabase } from '../utils/supabase/supabase.utils';

class SocialService {
  constructor() {
    this.baseUrl = '/.netlify/functions/social';
    this.listeners = new Set();
    this.realtimeSubscriptions = new Map();
    this.currentUser = null;
  }

  // Get auth headers for API calls
  async getAuthHeaders() {
    const { data: { session } } = await supabase.auth.getSession();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session?.access_token}`
    };
  }

  // Initialize social service with user context
  async initialize(user) {
    this.currentUser = user;
    if (user) {
      await this.setupRealtimeSubscriptions();
    }
  }

  // Setup real-time subscriptions for social features
  async setupRealtimeSubscriptions() {
    if (!this.currentUser) return;

    // Subscribe to notifications for real-time messaging
    const notificationSubscription = supabase
      .channel('social-notifications')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${this.currentUser.id}`
      }, (payload) => {
        this.handleRealtimeNotification(payload.new);
      })
      .subscribe();

    this.realtimeSubscriptions.set('notifications', notificationSubscription);

    this.notifyListeners('realtime_connected', { user_id: this.currentUser.id });
  }

  // Handle real-time notifications
  handleRealtimeNotification(notification) {
    const { type, metadata } = notification;

    switch (type) {
      case 'message':
        this.notifyListeners('new_message', {
          from_user_id: metadata?.from_user_id,
          from_user_name: metadata?.from_user_name,
          content: metadata?.full_content,
          notification_id: notification.id
        });
        break;
      case 'friend_request':
        this.notifyListeners('friend_request', {
          from_user_id: metadata?.from_user_id,
          from_user_name: metadata?.from_user_name,
          message: metadata?.request_message,
          request_id: notification.id
        });
        break;
      case 'friend_request_response':
        this.notifyListeners('friend_request_response', {
          response_user_id: metadata?.response_user_id,
          response: metadata?.response,
          notification_id: notification.id
        });
        break;
      default:
        this.notifyListeners('notification', notification);
    }
  }

  // Send friend request
  async sendFriendRequest(targetUserId, message = '') {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/friend-request`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          target_user_id: targetUserId,
          message
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('friend_request_sent', { target_user_id: targetUserId, request_id: result.request_id });
      
      return result;
    } catch (error) {
      console.error('Error sending friend request:', error);
      throw error;
    }
  }

  // Respond to friend request
  async respondToFriendRequest(requestId, response) {
    try {
      const headers = await this.getAuthHeaders();
      
      const apiResponse = await fetch(`${this.baseUrl}/friend-request/respond`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          request_id: requestId,
          response
        })
      });

      const result = await apiResponse.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('friend_request_responded', { request_id: requestId, response });
      
      return result;
    } catch (error) {
      console.error('Error responding to friend request:', error);
      throw error;
    }
  }

  // Send message
  async sendMessage(recipientId, content, messageType = 'text') {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/message`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          recipient_id: recipientId,
          content,
          message_type: messageType
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('message_sent', { 
        recipient_id: recipientId, 
        content, 
        message_id: result.message_id 
      });
      
      return result;
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Get conversations
  async getConversations() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/conversations`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('conversations_loaded', result.conversations);
      
      return result.conversations;
    } catch (error) {
      console.error('Error getting conversations:', error);
      throw error;
    }
  }

  // Create activity
  async createActivity(activityType, title, description = '', visibility = 'public') {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/activity`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          activity_type: activityType,
          title,
          description,
          visibility
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('activity_created', { 
        activity_type: activityType, 
        title, 
        activity_id: result.activity_id 
      });
      
      return result;
    } catch (error) {
      console.error('Error creating activity:', error);
      throw error;
    }
  }

  // Get activity feed
  async getActivityFeed() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/activity-feed`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('activity_feed_loaded', result.activities);
      
      return result.activities;
    } catch (error) {
      console.error('Error getting activity feed:', error);
      throw error;
    }
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId) {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;

      this.notifyListeners('notification_read', { notification_id: notificationId });
      
      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Get unread notifications count
  async getUnreadNotificationsCount() {
    try {
      if (!this.currentUser) return 0;

      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', this.currentUser.id)
        .eq('is_read', false);

      if (error) throw error;

      return count || 0;
    } catch (error) {
      console.error('Error getting unread notifications count:', error);
      return 0;
    }
  }

  // Event listener management
  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  notifyListeners(event, data) {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in social service listener:', error);
      }
    });
  }

  // Cleanup
  cleanup() {
    // Remove all real-time subscriptions
    this.realtimeSubscriptions.forEach((subscription, key) => {
      supabase.removeChannel(subscription);
    });
    this.realtimeSubscriptions.clear();
    
    // Clear listeners
    this.listeners.clear();
    
    this.currentUser = null;
  }

  // Utility methods
  formatLastSeen(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return time.toLocaleDateString();
  }

  formatMessagePreview(content, maxLength = 50) {
    if (!content) return '';
    return content.length > maxLength 
      ? content.substring(0, maxLength) + '...' 
      : content;
  }

  // Connection status helpers
  isUserOnline(userId) {
    // This would integrate with presence system in the future
    return false; // Placeholder
  }

  getUserStatus(userId) {
    // This would return user's current status
    return 'offline'; // Placeholder
  }
}

// Create singleton instance
const socialService = new SocialService();

export default socialService;
