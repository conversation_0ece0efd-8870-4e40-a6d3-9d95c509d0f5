import React from 'react';
import { Helmet } from 'react-helmet-async';

/**
 * SEO Meta Component - Comprehensive Meta Tag Management
 * 
 * This component provides comprehensive SEO meta tag management
 * for improved search engine optimization and social sharing.
 */
const SEOMeta = ({
  title = 'RoyalTea - Collaborative Project Platform',
  description = 'Join <PERSON>ea, the premier platform for collaborative projects, skill verification, and revenue sharing. Connect with professionals and build amazing projects together.',
  keywords = 'collaboration, projects, skills, verification, revenue sharing, freelance, teamwork',
  image = '/og-image.png',
  url = typeof window !== 'undefined' ? window.location.href : '',
  type = 'website',
  siteName = 'RoyalTea',
  author = 'RoyalTea Team',
  locale = 'en_US',
  twitterCard = 'summary_large_image',
  twitterSite = '@royaltea_app',
  canonical = url,
  noindex = false,
  nofollow = false,
  structuredData = null
}) => {
  // Clean and format title
  const cleanTitle = title.length > 60 ? title.substring(0, 57) + '...' : title;
  const fullTitle = title.includes('RoyalTea') ? title : `${title} | RoyalTea`;
  
  // Clean and format description
  const cleanDescription = description.length > 160 ? description.substring(0, 157) + '...' : description;
  
  // Ensure absolute URL for image
  const absoluteImage = image.startsWith('http') ? image : `https://royaltea.app${image}`;
  const absoluteUrl = url.startsWith('http') ? url : `https://royaltea.app${url}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={cleanDescription} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      <meta name="robots" content={`${noindex ? 'noindex' : 'index'},${nofollow ? 'nofollow' : 'follow'}`} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={cleanTitle} />
      <meta property="og:description" content={cleanDescription} />
      <meta property="og:image" content={absoluteImage} />
      <meta property="og:url" content={absoluteUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content={twitterSite} />
      <meta name="twitter:title" content={cleanTitle} />
      <meta name="twitter:description" content={cleanDescription} />
      <meta name="twitter:image" content={absoluteImage} />
      
      {/* Additional SEO Meta Tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="revisit-after" content="7 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOMeta;