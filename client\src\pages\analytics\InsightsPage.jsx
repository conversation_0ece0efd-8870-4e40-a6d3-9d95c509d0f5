import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Tabs, Tab, Progress, Chip } from '@heroui/react';

/**
 * AI Insights Page Component
 * 
 * Provides AI-powered analytics, predictions, recommendations, and optimization suggestions.
 * Uses advanced data visualization and machine learning insights.
 */
const InsightsPage = () => {
  const [activeTab, setActiveTab] = useState('predictions');

  // Mock AI insights data
  const predictions = [
    {
      title: 'Revenue Forecast',
      prediction: 'Your earnings are projected to increase by 35% next month',
      confidence: 87,
      trend: 'up',
      details: 'Based on current contribution patterns and project momentum'
    },
    {
      title: 'Optimal Work Schedule',
      prediction: 'You\'re most productive between 9-11 AM and 2-4 PM',
      confidence: 92,
      trend: 'neutral',
      details: 'Analysis of 3 months of time tracking data'
    },
    {
      title: 'Skill Development',
      prediction: 'Focus on UI/UX skills for maximum royalty impact',
      confidence: 78,
      trend: 'up',
      details: 'Market demand analysis and your current skill set'
    }
  ];

  const recommendations = [
    {
      type: 'productivity',
      title: 'Increase Task Granularity',
      description: 'Break down large tasks into smaller 2-4 hour chunks for better tracking accuracy',
      impact: 'High',
      effort: 'Low'
    },
    {
      type: 'collaboration',
      title: 'Join Design Reviews',
      description: 'Participate in weekly design reviews to increase your project influence',
      impact: 'Medium',
      effort: 'Medium'
    },
    {
      type: 'skills',
      title: 'Learn React Native',
      description: 'Mobile development skills are in high demand for your current projects',
      impact: 'High',
      effort: 'High'
    }
  ];

  const optimizations = [
    {
      area: 'Time Allocation',
      current: 'Frontend: 60%, Backend: 40%',
      suggested: 'Frontend: 70%, Backend: 30%',
      reason: 'Frontend tasks have 25% higher royalty multiplier',
      savings: '+$450/month'
    },
    {
      area: 'Project Selection',
      current: '2 active projects',
      suggested: '3 active projects',
      reason: 'Diversification reduces risk and increases opportunities',
      savings: '+$200/month'
    }
  ];

  const getTrendIcon = (trend) => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      case 'neutral': return '➡️';
      default: return '📊';
    }
  };

  const getImpactColor = (impact) => {
    switch (impact.toLowerCase()) {
      case 'high': return 'success';
      case 'medium': return 'warning';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-violet-900">
      {/* Header */}
      <motion.div
        className="relative z-10 pt-8 pb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 10, -10, 0]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              🤖
            </motion.div>
            <h1 className="text-4xl font-bold text-white mb-2">
              AI Insights
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Powered by machine learning to optimize your contributions and maximize earnings.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Navigation Tabs */}
          <Card className="mb-8 bg-white/5 border border-white/10">
            <CardBody className="p-6">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={setActiveTab}
                variant="underlined"
                classNames={{
                  tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-gradient-to-r from-purple-500 to-violet-500",
                  tab: "max-w-fit px-0 h-12",
                  tabContent: "group-data-[selected=true]:text-white text-white/70"
                }}
              >
                <Tab key="predictions" title="🔮 Predictions" />
                <Tab key="recommendations" title="💡 Recommendations" />
                <Tab key="optimization" title="⚡ Optimization" />
                <Tab key="forecasting" title="📊 Forecasting" />
              </Tabs>
            </CardBody>
          </Card>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'predictions' && (
              <div className="space-y-6">
                {predictions.map((prediction, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="bg-white/5 border border-white/10">
                      <CardBody className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="text-3xl">{getTrendIcon(prediction.trend)}</div>
                            <div>
                              <h3 className="text-xl font-semibold text-white">{prediction.title}</h3>
                              <p className="text-white/70">{prediction.details}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-white/70 mb-1">Confidence</div>
                            <div className="text-2xl font-bold text-purple-400">{prediction.confidence}%</div>
                          </div>
                        </div>
                        
                        <div className="bg-white/5 rounded-lg p-4 mb-4">
                          <p className="text-white text-lg">{prediction.prediction}</p>
                        </div>
                        
                        <Progress
                          value={prediction.confidence}
                          color="secondary"
                          className="max-w-full"
                          label="AI Confidence Level"
                        />
                      </CardBody>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'recommendations' && (
              <div className="space-y-6">
                {recommendations.map((rec, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="bg-white/5 border border-white/10">
                      <CardBody className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-semibold text-white mb-2">{rec.title}</h3>
                            <p className="text-white/80">{rec.description}</p>
                          </div>
                          <div className="flex gap-2">
                            <Chip size="sm" color={getImpactColor(rec.impact)} variant="flat">
                              {rec.impact} Impact
                            </Chip>
                            <Chip size="sm" variant="flat" className="bg-white/10 text-white">
                              {rec.effort} Effort
                            </Chip>
                          </div>
                        </div>
                        <Button className="bg-purple-500 hover:bg-purple-600 text-white">
                          Implement Suggestion
                        </Button>
                      </CardBody>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'optimization' && (
              <div className="space-y-6">
                {optimizations.map((opt, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card className="bg-white/5 border border-white/10">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-center w-full">
                          <h3 className="text-xl font-semibold text-white">{opt.area}</h3>
                          <Chip color="success" variant="flat" className="text-green-400">
                            {opt.savings}
                          </Chip>
                        </div>
                      </CardHeader>
                      <CardBody className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-sm text-white/70 mb-1">Current</div>
                            <div className="text-white">{opt.current}</div>
                          </div>
                          <div>
                            <div className="text-sm text-white/70 mb-1">Suggested</div>
                            <div className="text-green-400 font-medium">{opt.suggested}</div>
                          </div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3">
                          <div className="text-sm text-white/70 mb-1">Reasoning</div>
                          <p className="text-white">{opt.reason}</p>
                        </div>
                        <Button className="bg-green-500 hover:bg-green-600 text-white">
                          Apply Optimization
                        </Button>
                      </CardBody>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'forecasting' && (
              <Card className="bg-white/5 border border-white/10">
                <CardBody className="p-6">
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">📊</div>
                    <h3 className="text-xl font-semibold text-white mb-2">Advanced Forecasting</h3>
                    <p className="text-white/70 mb-6">
                      Detailed revenue and contribution forecasting models coming soon
                    </p>
                    <Button className="bg-purple-500 hover:bg-purple-600 text-white">
                      Request Beta Access
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default InsightsPage;
