import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@heroui/react';
import ImmersiveLogin from './ImmersiveLogin';
import ImmersiveSignup from './ImmersiveSignup';
import ImmersivePasswordReset from './ImmersivePasswordReset';

/**
 * AuthLandingPage Component
 * 
 * Entry point for authentication following wireframe specifications
 * Provides choice between login and signup with immersive transitions
 */
const AuthLandingPage = ({ 
  onCancel,
  redirectTo 
}) => {
  const [currentView, setCurrentView] = useState('landing'); // 'landing', 'login', 'signup', 'reset'

  // Handle view transitions
  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  // Animation variants for view transitions
  const pageVariants = {
    hidden: { opacity: 0, scale: 0.95 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    },
    exit: {
      opacity: 0,
      scale: 1.05,
      transition: {
        duration: 0.3,
        ease: "easeIn"
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Render current view
  const renderCurrentView = () => {
    switch (currentView) {
      case 'login':
        return (
          <ImmersiveLogin
            onSwitchToSignup={() => handleViewChange('signup')}
            onSwitchToReset={() => handleViewChange('reset')}
            onCancel={() => handleViewChange('landing')}
            redirectTo={redirectTo}
          />
        );
      
      case 'signup':
        return (
          <ImmersiveSignup
            onSwitchToLogin={() => handleViewChange('login')}
            onCancel={() => handleViewChange('landing')}
            redirectTo={redirectTo}
          />
        );
      
      case 'reset':
        return (
          <ImmersivePasswordReset
            onBackToLogin={() => handleViewChange('login')}
            onCancel={() => handleViewChange('landing')}
          />
        );
      
      default:
        return renderLandingPage();
    }
  };

  // Render landing page
  const renderLandingPage = () => (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-primary-50 to-secondary-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Exit button */}
      {onCancel && (
        <motion.div
          className="absolute top-6 right-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </motion.div>
      )}

      <div className="max-w-2xl mx-auto text-center">
        {/* Logo and Title */}
        <motion.div variants={itemVariants} className="mb-12">
          <h1 className="text-6xl font-bold text-foreground mb-4">
            ROYALTEA
          </h1>
          <p className="text-2xl text-primary font-semibold mb-2">
            Fighting for the Tea
          </p>
          <p className="text-lg text-default-600">
            Fair compensation for creative professionals
          </p>
        </motion.div>

        {/* Main Action Buttons */}
        <motion.div variants={itemVariants} className="mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
            <Button
              size="lg"
              onPress={() => handleViewChange('login')}
              className="bg-primary text-white font-semibold py-6 text-lg"
            >
              LOGIN
            </Button>
            
            <Button
              size="lg"
              onPress={() => handleViewChange('signup')}
              className="bg-secondary text-white font-semibold py-6 text-lg"
            >
              SIGN UP
            </Button>
            
            <Button
              size="lg"
              variant="bordered"
              onPress={() => {
                // Navigate to learn more page or show info
                console.log('Learn more clicked');
              }}
              className="border-2 border-primary text-primary font-semibold py-6 text-lg hover:bg-primary hover:text-white"
            >
              LEARN MORE
            </Button>
          </div>
        </motion.div>

        {/* Social Login Options */}
        <motion.div variants={itemVariants} className="mb-12">
          <p className="text-default-600 mb-4">Or continue with:</p>
          <div className="flex justify-center space-x-4">
            <Button
              variant="bordered"
              size="lg"
              className="px-8 py-4"
              onPress={() => {
                // Handle GitHub social login
                console.log('GitHub login clicked');
              }}
            >
              <i className="bi bi-github text-xl mr-2"></i>
              GitHub
            </Button>
            
            <Button
              variant="bordered"
              size="lg"
              className="px-8 py-4"
              onPress={() => {
                // Handle Google social login
                console.log('Google login clicked');
              }}
            >
              <i className="bi bi-google text-xl mr-2"></i>
              Google
            </Button>
          </div>
        </motion.div>

        {/* Value Propositions */}
        <motion.div variants={itemVariants}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="bi bi-graph-up text-2xl text-primary"></i>
              </div>
              <h3 className="font-semibold text-foreground mb-2">
                Transparent Revenue Sharing
              </h3>
              <p className="text-sm text-default-600">
                See exactly how your contributions translate to earnings
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="bi bi-people text-2xl text-secondary"></i>
              </div>
              <h3 className="font-semibold text-foreground mb-2">
                Fair Contribution Tracking
              </h3>
              <p className="text-sm text-default-600">
                Every task, hour, and difficulty level is properly valued
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="bi bi-tools text-2xl text-success"></i>
              </div>
              <h3 className="font-semibold text-foreground mb-2">
                Professional Collaboration Tools
              </h3>
              <p className="text-sm text-default-600">
                Everything you need to work together effectively
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={currentView}
        variants={pageVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        {renderCurrentView()}
      </motion.div>
    </AnimatePresence>
  );
};

export default AuthLandingPage;
