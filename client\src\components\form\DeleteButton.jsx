import axios from "axios";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import _ from "lodash";
import { getAuth } from "firebase/auth";
import { signOut } from "firebase/auth";
import { Button } from "../ui/shadcn/button";

const DeleteButton = (props) => {
  const { id, item, user } = props;
  const navigate = useNavigate();
  const auth = getAuth(); // Initialize Firebase Auth

  let currentUser = null;

  // Function to get Firebase auth token
  const getToken = async () => {
    currentUser = auth.currentUser;
    return currentUser ? await currentUser.getIdToken() : null;
  };

  // Generic delete function
  const handleDelete = async () => {
    const confirmMessage =
      item === "user"
        ? `Are you sure you want to delete the user, ${user.displayName}? This will also remove all of ${user.displayName}'s contributions. This action cannot be undone.`
        : `Are you sure you want to delete this ${item}? This action cannot be undone.`;

    if (!confirm(confirmMessage)) return;

    const token = await getToken();
    if (!token) {
      toast.error("Authentication error. Please log in again.");
      return;
    }

    try {
      await axios.delete(`/${item}/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      toast.success(_.capitalize(item) + " Deleted");
      // If the deleted item is the current user, log them out
      if (item === "user" && user.email === currentUser.email) {
        // Log out from Firebase
        await signOut(auth);
        toast.success("You have been logged out");

        // Redirect to login or home page
        navigate("/login"); // or navigate("/") for homepage
      } else {
        // Navigate to index page of the item
        navigate(`/${item}/index`);
      }
    } catch (error) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
      console.error(`Error deleting ${item}:`, error);
    }
  };

  return (
    <div className="text-center mt-5">
      <Button type="button" onClick={handleDelete} variant="destructive">
        Delete {_.capitalize(item)}
      </Button>
    </div>
  );
};

export default DeleteButton;
