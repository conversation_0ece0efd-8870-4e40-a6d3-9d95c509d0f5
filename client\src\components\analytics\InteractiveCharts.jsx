import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Tabs, <PERSON><PERSON>, <PERSON><PERSON>, Chip } from '@heroui/react';

/**
 * Interactive Charts Component
 * 
 * Provides data visualizations for contribution trends and earnings projections.
 * Uses CSS and SVG for lightweight, responsive charts without external dependencies.
 */

const InteractiveCharts = ({ 
  contributionData = [], 
  earningsData = [], 
  className = "" 
}) => {
  const [activeChart, setActiveChart] = useState('contributions');
  const [timeRange, setTimeRange] = useState('7d'); // '7d', '30d', '90d'

  // Process contribution data for visualization
  const processedContributionData = useMemo(() => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const data = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayContributions = contributionData.filter(c => 
        c.created_at && c.created_at.startsWith(dateStr)
      );
      
      data.push({
        date: dateStr,
        count: dayContributions.length,
        hours: dayContributions.reduce((sum, c) => sum + (c.hours_tracked || 0), 0),
        difficulty: dayContributions.length > 0 
          ? dayContributions.reduce((sum, c) => sum + (c.difficulty_rating || 3), 0) / dayContributions.length 
          : 0
      });
    }
    
    return data;
  }, [contributionData, timeRange]);

  // Process earnings data for visualization
  const processedEarningsData = useMemo(() => {
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const data = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayEarnings = earningsData.filter(e => 
        e.payment_date && e.payment_date.startsWith(dateStr)
      );
      
      data.push({
        date: dateStr,
        amount: dayEarnings.reduce((sum, e) => sum + (e.amount || 0), 0),
        count: dayEarnings.length
      });
    }
    
    return data;
  }, [earningsData, timeRange]);

  // Chart dimensions
  const chartWidth = 400;
  const chartHeight = 200;
  const padding = 40;

  // Contribution Trend Chart
  const ContributionChart = () => {
    const maxCount = Math.max(...processedContributionData.map(d => d.count), 1);
    const maxHours = Math.max(...processedContributionData.map(d => d.hours), 1);

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Contribution Trends</h4>
          <div className="flex gap-2">
            {['7d', '30d', '90d'].map((range) => (
              <Button
                key={range}
                size="sm"
                variant={timeRange === range ? 'solid' : 'flat'}
                onClick={() => setTimeRange(range)}
              >
                {range}
              </Button>
            ))}
          </div>
        </div>

        <div className="bg-default-50 rounded-lg p-4">
          <svg width={chartWidth} height={chartHeight} className="w-full h-auto">
            {/* Grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
              <line
                key={ratio}
                x1={padding}
                y1={padding + (chartHeight - 2 * padding) * ratio}
                x2={chartWidth - padding}
                y2={padding + (chartHeight - 2 * padding) * ratio}
                stroke="#e5e7eb"
                strokeWidth="1"
              />
            ))}

            {/* Contribution count line */}
            <polyline
              fill="none"
              stroke="#3b82f6"
              strokeWidth="2"
              points={processedContributionData.map((d, i) => {
                const x = padding + (i / (processedContributionData.length - 1)) * (chartWidth - 2 * padding);
                const y = chartHeight - padding - (d.count / maxCount) * (chartHeight - 2 * padding);
                return `${x},${y}`;
              }).join(' ')}
            />

            {/* Hours line */}
            <polyline
              fill="none"
              stroke="#10b981"
              strokeWidth="2"
              strokeDasharray="5,5"
              points={processedContributionData.map((d, i) => {
                const x = padding + (i / (processedContributionData.length - 1)) * (chartWidth - 2 * padding);
                const y = chartHeight - padding - (d.hours / maxHours) * (chartHeight - 2 * padding);
                return `${x},${y}`;
              }).join(' ')}
            />

            {/* Data points */}
            {processedContributionData.map((d, i) => {
              const x = padding + (i / (processedContributionData.length - 1)) * (chartWidth - 2 * padding);
              const yCount = chartHeight - padding - (d.count / maxCount) * (chartHeight - 2 * padding);
              const yHours = chartHeight - padding - (d.hours / maxHours) * (chartHeight - 2 * padding);
              
              return (
                <g key={i}>
                  <circle cx={x} cy={yCount} r="4" fill="#3b82f6" />
                  <circle cx={x} cy={yHours} r="4" fill="#10b981" />
                </g>
              );
            })}
          </svg>

          <div className="flex items-center justify-center gap-6 mt-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-0.5 bg-blue-500"></div>
              <span className="text-sm text-muted-foreground">Contributions</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-0.5 bg-green-500 border-dashed border-t-2"></div>
              <span className="text-sm text-muted-foreground">Hours</span>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {processedContributionData.reduce((sum, d) => sum + d.count, 0)}
            </p>
            <p className="text-sm text-muted-foreground">Total Contributions</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {processedContributionData.reduce((sum, d) => sum + d.hours, 0).toFixed(1)}
            </p>
            <p className="text-sm text-muted-foreground">Total Hours</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {(processedContributionData.reduce((sum, d) => sum + d.difficulty, 0) / processedContributionData.length || 0).toFixed(1)}
            </p>
            <p className="text-sm text-muted-foreground">Avg Difficulty</p>
          </div>
        </div>
      </div>
    );
  };

  // Earnings Projection Chart
  const EarningsChart = () => {
    const maxAmount = Math.max(...processedEarningsData.map(d => d.amount), 1);
    const totalEarnings = processedEarningsData.reduce((sum, d) => sum + d.amount, 0);

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium">Earnings Overview</h4>
          <Chip color="success" variant="flat">
            ${totalEarnings.toFixed(2)}
          </Chip>
        </div>

        <div className="bg-default-50 rounded-lg p-4">
          <svg width={chartWidth} height={chartHeight} className="w-full h-auto">
            {/* Grid lines */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
              <line
                key={ratio}
                x1={padding}
                y1={padding + (chartHeight - 2 * padding) * ratio}
                x2={chartWidth - padding}
                y2={padding + (chartHeight - 2 * padding) * ratio}
                stroke="#e5e7eb"
                strokeWidth="1"
              />
            ))}

            {/* Earnings bars */}
            {processedEarningsData.map((d, i) => {
              const x = padding + (i / processedEarningsData.length) * (chartWidth - 2 * padding);
              const barWidth = (chartWidth - 2 * padding) / processedEarningsData.length * 0.8;
              const barHeight = (d.amount / maxAmount) * (chartHeight - 2 * padding);
              const y = chartHeight - padding - barHeight;
              
              return (
                <rect
                  key={i}
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill="#10b981"
                  opacity="0.8"
                  rx="2"
                />
              );
            })}
          </svg>
        </div>

        {/* Earnings Breakdown */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              ${totalEarnings.toFixed(2)}
            </p>
            <p className="text-sm text-muted-foreground">Total Earnings</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {processedEarningsData.reduce((sum, d) => sum + d.count, 0)}
            </p>
            <p className="text-sm text-muted-foreground">Payments</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <Tabs
            selectedKey={activeChart}
            onSelectionChange={setActiveChart}
            variant="underlined"
          >
            <Tab key="contributions" title="📊 Contributions" />
            <Tab key="earnings" title="💰 Earnings" />
          </Tabs>
        </CardHeader>
        <CardBody>
          <motion.div
            key={activeChart}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeChart === 'contributions' ? <ContributionChart /> : <EarningsChart />}
          </motion.div>
        </CardBody>
      </Card>
    </div>
  );
};

export default InteractiveCharts;
