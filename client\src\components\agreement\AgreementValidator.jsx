import React, { useState, useEffect } from 'react';
import { validateAgreement, getImprovementSuggestions } from '../../utils/agreement/agreementValidator';
import { Card, CardBody, CardHeader, Button, Progress, Chip as Badge, Accordion, AccordionItem } from '../ui/heroui';

/**
 * Component for validating and displaying agreement validation results
 * @param {Object} props - Component props
 * @param {string} props.agreementText - The agreement text to validate
 * @param {string} props.projectType - The project type (game, software, music, film)
 * @param {Function} props.onValidationComplete - Callback when validation is complete
 * @returns {JSX.Element} - Agreement validator component
 */
const AgreementValidator = ({
  agreementText,
  projectType = 'game',
  onValidationComplete
}) => {
  const [validationResults, setValidationResults] = useState(null);
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Run validation when agreement text changes
  useEffect(() => {
    if (!agreementText) return;

    validateAgreementText();
  }, [agreementText, projectType]);

  // Validate the agreement text
  const validateAgreementText = () => {
    try {
      setLoading(true);
      setError(null);

      // Validate the agreement
      const results = validateAgreement(agreementText, projectType);
      setValidationResults(results);

      // Get improvement suggestions
      const improvementSuggestions = getImprovementSuggestions(results);
      setSuggestions(improvementSuggestions);

      // Call the callback if provided
      if (onValidationComplete) {
        onValidationComplete(results);
      }
    } catch (err) {
      console.error('Error validating agreement:', err);
      setError('Failed to validate agreement. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Get score color based on percentage
  const getScoreColor = (percentage) => {
    if (percentage >= 90) return 'success';
    if (percentage >= 70) return 'info';
    if (percentage >= 50) return 'warning';
    return 'danger';
  };

  // Get progress bar color class - let HeroUI handle colors
  const getProgressColor = (percentage) => {
    if (percentage >= 90) return 'bg-success';
    if (percentage >= 70) return 'bg-primary';
    if (percentage >= 50) return 'bg-warning';
    return 'bg-danger';
  };

  // Render loading state
  if (loading) {
    return (
      <Card className="mb-4">
        <CardHeader>
          <h3 className="text-lg font-semibold">Agreement Validation</h3>
        </CardHeader>
        <CardBody className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" role="status">
            <span className="sr-only">Loading...</span>
          </div>
          <p className="mt-3 text-muted-foreground">Validating agreement...</p>
        </CardBody>
      </Card>
    );
  }

  // Render error state
  if (error) {
    return (
      <Card className="mb-4">
        <CardHeader>
          <h3 className="text-lg font-semibold">Agreement Validation</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-start">
              <i className="bi bi-exclamation-triangle-fill text-red-500 mr-2 mt-0.5"></i>
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
          <Button onPress={validateAgreementText}>
            Try Again
          </Button>
        </CardBody>
      </Card>
    );
  }

  // Render no results state
  if (!validationResults) {
    return (
      <Card className="mb-4">
        <CardHeader>
          <h3 className="text-lg font-semibold">Agreement Validation</h3>
        </CardHeader>
        <CardBody className="text-center py-8 space-y-4">
          <p className="text-muted-foreground">No validation results available.</p>
          <Button onPress={validateAgreementText}>
            Validate Agreement
          </Button>
        </CardBody>
      </Card>
    );
  }

  // Render validation results
  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Agreement Validation</h3>
          <Button
            variant="bordered"
            size="sm"
            onPress={validateAgreementText}
          >
            Re-validate
          </Button>
        </div>
      </CardHeader>
      <CardBody className="space-y-6">
        {/* Overall Score */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Overall Score</h3>
            <Badge color={getScoreColor(validationResults.scorePercentage)}>
              {validationResults.scorePercentage}%
            </Badge>
          </div>
          <div className="space-y-2">
            <Progress
              value={validationResults.scorePercentage}
              className="h-3"
            />
            <p className="text-sm text-muted-foreground">
              {validationResults.score} / {validationResults.maxScore} points
            </p>
          </div>
        </div>

        {/* Validation Status */}
        {validationResults.isValid ? (
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-start">
              <i className="bi bi-check-circle-fill text-green-500 mr-2 mt-0.5"></i>
              <p className="text-sm text-green-700">
                Agreement passes all required validations.
              </p>
            </div>
          </div>
        ) : (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-start">
              <i className="bi bi-exclamation-triangle-fill text-yellow-500 mr-2 mt-0.5"></i>
              <p className="text-sm text-yellow-700">
                Agreement is missing required elements.
              </p>
            </div>
          </div>
        )}

        {/* Improvement Suggestions */}
        {suggestions.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Improvement Suggestions</h3>
            <div className="border rounded-md divide-y">
              {suggestions.map((suggestion, index) => (
                <div key={index} className="p-3 flex items-start">
                  <i className="bi bi-lightbulb-fill text-yellow-500 mr-2 mt-0.5 flex-shrink-0"></i>
                  <p className="text-sm">{suggestion}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Detailed Results */}
        <Accordion className="space-y-2">
          {/* Missing Sections */}
          {validationResults.missingSections.length > 0 && (
            <AccordionItem
              key="missing-sections"
              aria-label="Missing Sections"
              title={
                <div className="flex items-center text-red-600">
                  <i className="bi bi-exclamation-circle mr-2"></i>
                  Missing Sections ({validationResults.missingSections.length})
                </div>
              }
            >
                <div className="space-y-2">
                  {validationResults.missingSections.map((section, index) => (
                    <div key={index} className="p-3 border rounded-md">
                      <p className="text-sm">
                        <span className="font-medium">{section.name}:</span> {section.description}
                      </p>
                    </div>
                  ))}
                </div>
            </AccordionItem>
          )}

          {/* Missing Clauses */}
          {validationResults.missingClauses.length > 0 && (
            <AccordionItem
              key="missing-clauses"
              aria-label="Missing Legal Clauses"
              title={
                <div className="flex items-center text-red-600">
                  <i className="bi bi-exclamation-circle mr-2"></i>
                  Missing Legal Clauses ({validationResults.missingClauses.length})
                </div>
              }
            >
                <div className="space-y-2">
                  {validationResults.missingClauses.map((clause, index) => (
                    <div key={index} className="p-3 border rounded-md">
                      <p className="text-sm">
                        <span className="font-medium">{clause.name}:</span> {clause.description}
                      </p>
                    </div>
                  ))}
                </div>
            </AccordionItem>
          )}

          {/* Formatting Issues */}
          {validationResults.formattingIssues.length > 0 && (
            <AccordionItem
              key="formatting-issues"
              aria-label="Formatting Issues"
              title={
                <div className="flex items-center text-yellow-600">
                  <i className="bi bi-exclamation-triangle mr-2"></i>
                  Formatting Issues ({validationResults.formattingIssues.length})
                </div>
              }
            >
                <div className="space-y-2">
                  {validationResults.formattingIssues.map((issue, index) => (
                    <div key={index} className="p-3 border rounded-md">
                      <p className="text-sm">
                        <span className="font-medium">{issue.name}:</span> {issue.description}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Found {issue.found}, need at least {issue.required}
                      </p>
                    </div>
                  ))}
                </div>
            </AccordionItem>
          )}

          {/* Project Type Issues */}
          {validationResults.projectTypeIssues.length > 0 && (
            <AccordionItem
              key="project-type-issues"
              aria-label="Project-Specific Issues"
              title={
                <div className="flex items-center text-yellow-600">
                  <i className="bi bi-exclamation-triangle mr-2"></i>
                  Project-Specific Issues ({validationResults.projectTypeIssues.length})
                </div>
              }
            >
                <div className="space-y-2">
                  {validationResults.projectTypeIssues.map((issue, index) => (
                    <div key={index} className="p-3 border rounded-md">
                      <p className="text-sm">
                        <span className="font-medium">{issue.name}:</span> {issue.description}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        Missing terms: {issue.missing.join(', ')}
                      </p>
                    </div>
                  ))}
                </div>
            </AccordionItem>
          )}

          {/* Warnings */}
          {validationResults.warnings.length > 0 && (
            <AccordionItem
              key="warnings"
              aria-label="Warnings"
              title={
                <div className="flex items-center text-blue-600">
                  <i className="bi bi-info-circle mr-2"></i>
                  Warnings ({validationResults.warnings.length})
                </div>
              }
            >
                <div className="space-y-2">
                  {validationResults.warnings.map((warning, index) => (
                    <div key={index} className="p-3 border rounded-md">
                      <p className="text-sm">
                        <span className="font-medium">{warning.name}:</span> {warning.description}
                      </p>
                      {warning.placeholders && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Placeholders: {warning.placeholders.join(', ')}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
            </AccordionItem>
          )}
        </Accordion>
      </CardBody>
    </Card>
  );
};

export default AgreementValidator;
