-- Fix PostgreSQL policy syntax errors
-- PostgreSQL doesn't support "CREATE POLICY IF NOT EXISTS"
-- We need to use DO blocks with conditional logic instead

-- Drop existing policies if they exist and recreate them
-- This is safer than trying to check if they exist

-- Projects policies (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'projects') THEN
        DROP POLICY IF EXISTS "Users can view projects they created or contribute to" ON public.projects;
        CREATE POLICY "Users can view projects they created or contribute to"
        ON public.projects FOR SELECT
        USING (
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_id = projects.id
                AND user_id = auth.uid()
                AND status = 'active'
            )
        );

        DROP POLICY IF EXISTS "Users can insert their own projects" ON public.projects;
        CREATE POLICY "Users can insert their own projects"
        ON public.projects FOR INSERT
        WITH CHECK (created_by = auth.uid());

        DROP POLICY IF EXISTS "Project owners can update their projects" ON public.projects;
        CREATE POLICY "Project owners can update their projects"
        ON public.projects FOR UPDATE
        USING (
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_id = projects.id
                AND user_id = auth.uid()
                AND status = 'active'
                AND role IN ('owner', 'admin')
            )
        );
    END IF;
END $$;

-- Project contributors policies (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'project_contributors') THEN
        DROP POLICY IF EXISTS "Users can view project contributors for their projects" ON public.project_contributors;
        CREATE POLICY "Users can view project contributors for their projects"
        ON public.project_contributors FOR SELECT
        USING (
            user_id = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.projects
                WHERE id = project_contributors.project_id
                AND created_by = auth.uid()
            )
        );

        DROP POLICY IF EXISTS "Project owners can manage contributors" ON public.project_contributors;
        CREATE POLICY "Project owners can manage contributors"
        ON public.project_contributors FOR ALL
        USING (
            EXISTS (
                SELECT 1 FROM public.projects
                WHERE id = project_contributors.project_id
                AND created_by = auth.uid()
            )
        );
    END IF;
END $$;

-- Active timers policies (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'active_timers') THEN
        DROP POLICY IF EXISTS "Users can manage their own timers" ON public.active_timers;
        CREATE POLICY "Users can manage their own timers"
        ON public.active_timers FOR ALL
        USING (user_id = auth.uid());
    END IF;
END $$;

-- Tasks policies (only if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
        DROP POLICY IF EXISTS "Users can view tasks for their projects" ON public.tasks;
        CREATE POLICY "Users can view tasks for their projects"
        ON public.tasks FOR SELECT
        USING (
            assignee_id = auth.uid() OR
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_id = tasks.project_id
                AND user_id = auth.uid()
                AND status = 'active'
            )
        );

        DROP POLICY IF EXISTS "Users can create tasks in their projects" ON public.tasks;
        CREATE POLICY "Users can create tasks in their projects"
        ON public.tasks FOR INSERT
        WITH CHECK (
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_id = tasks.project_id
                AND user_id = auth.uid()
                AND status = 'active'
            )
        );

        DROP POLICY IF EXISTS "Users can update tasks they created or are assigned to" ON public.tasks;
        CREATE POLICY "Users can update tasks they created or are assigned to"
        ON public.tasks FOR UPDATE
        USING (
            assignee_id = auth.uid() OR
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM public.project_contributors
                WHERE project_id = tasks.project_id
                AND user_id = auth.uid()
                AND status = 'active'
                AND role IN ('owner', 'admin')
            )
        );
    END IF;
END $$;

-- Enable RLS on tables that exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'projects') THEN
        ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
        GRANT SELECT, INSERT, UPDATE, DELETE ON public.projects TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'project_contributors') THEN
        ALTER TABLE public.project_contributors ENABLE ROW LEVEL SECURITY;
        GRANT SELECT, INSERT, UPDATE, DELETE ON public.project_contributors TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'active_timers') THEN
        ALTER TABLE public.active_timers ENABLE ROW LEVEL SECURITY;
        GRANT SELECT, INSERT, UPDATE, DELETE ON public.active_timers TO authenticated;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'tasks') THEN
        ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
        GRANT SELECT, INSERT, UPDATE, DELETE ON public.tasks TO authenticated;
    END IF;
END $$;

-- Ensure sequences are accessible
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

COMMIT;
