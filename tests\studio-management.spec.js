// Studio Management E2E Tests
// Day 2 - Testing business compliance features

import { test, expect } from '@playwright/test';

const SITE_URL = 'https://royalty.technology';

// Test user credentials (using existing test account)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Studio Management System', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the site
    await page.goto(SITE_URL);
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if we need to login
    const loginButton = page.locator('text=Login').first();
    if (await loginButton.isVisible()) {
      await loginButton.click();
      
      // Fill in login credentials
      await page.fill('input[type="email"]', TEST_USER.email);
      await page.fill('input[type="password"]', TEST_USER.password);
      
      // Submit login
      await page.click('button[type="submit"]');
      
      // Wait for login to complete
      await page.waitForLoadState('networkidle');
    }
  });

  test('should navigate to studio management', async ({ page }) => {
    // Try to navigate to studios section
    // First check if we can find studios in navigation
    const studiosLink = page.locator('text=Studios').first();

    if (await studiosLink.isVisible()) {
      await studiosLink.click();
      await page.waitForLoadState('networkidle');

      // Check if we're on studios page
      await expect(page).toHaveURL(/.*studios.*/);
    } else {
      // Try direct navigation to studios
      await page.goto(`${SITE_URL}/studios`);
      await page.waitForLoadState('networkidle');
    }

    // Look for studio management elements
    const pageContent = await page.textContent('body');
    console.log('Studios page content preview:', pageContent.substring(0, 500));
  });

  test('should access studio management through direct URL', async ({ page }) => {
    // Try to navigate directly to studios
    await page.goto(`${SITE_URL}/studios`);
    await page.waitForLoadState('networkidle');

    // Check if page loads without errors
    const pageTitle = await page.title();
    console.log('Studios page title:', pageTitle);

    // Look for studio-related content
    const hasStudioContent = await page.locator('text=Studio').first().isVisible();
    console.log('Has studio content:', hasStudioContent);

    // Check for studio list
    const hasStudioList = await page.locator('[class*="studio"], [class*="team"]').first().isVisible();
    console.log('Has studio list:', hasStudioList);
  });

  test('should test company registration flow', async ({ page }) => {
    // Navigate to studios
    await page.goto(`${SITE_URL}/studios`);
    await page.waitForLoadState('networkidle');

    // Look for create studio button
    const createButton = page.locator('text=Create').first();
    if (await createButton.isVisible()) {
      await createButton.click();
      await page.waitForLoadState('networkidle');
    }
    
    // Check if we can find business registration elements
    const businessElements = [
      'text=Business Entity',
      'text=Company',
      'text=Register',
      'text=Legal Name',
      'text=Tax ID'
    ];
    
    for (const element of businessElements) {
      const isVisible = await page.locator(element).first().isVisible();
      console.log(`${element} visible:`, isVisible);
    }
  });

  test('should test financial transaction API', async ({ page }) => {
    // Test the financial transactions API endpoint
    const response = await page.request.get(`${SITE_URL}/.netlify/functions/financial-transactions`);
    
    console.log('Financial transactions API status:', response.status());
    
    if (response.status() === 401) {
      console.log('API requires authentication (expected)');
    } else if (response.status() === 200) {
      const data = await response.json();
      console.log('Financial transactions response:', data);
    } else {
      console.log('Unexpected API response status:', response.status());
    }
  });

  test('should test companies API', async ({ page }) => {
    // Test the companies API endpoint
    const response = await page.request.get(`${SITE_URL}/.netlify/functions/companies`);
    
    console.log('Companies API status:', response.status());
    
    if (response.status() === 401) {
      console.log('Companies API requires authentication (expected)');
    } else if (response.status() === 200) {
      const data = await response.json();
      console.log('Companies response:', data);
    } else {
      console.log('Unexpected API response status:', response.status());
    }
  });

  test('should check for alliance management components', async ({ page }) => {
    // Navigate to teams and look for any existing team to manage
    await page.goto(`${SITE_URL}/teams`);
    await page.waitForLoadState('networkidle');
    
    // Look for team cards or list items
    const teamElements = await page.locator('[class*="team"], [class*="card"], [class*="item"]').all();
    console.log('Found team elements:', teamElements.length);
    
    if (teamElements.length > 0) {
      // Try to click on the first team
      await teamElements[0].click();
      await page.waitForLoadState('networkidle');
      
      // Look for manage button
      const manageButton = page.locator('text=Manage').first();
      if (await manageButton.isVisible()) {
        await manageButton.click();
        await page.waitForLoadState('networkidle');
        
        // Check if we're on alliance management page
        const currentUrl = page.url();
        console.log('Alliance management URL:', currentUrl);
        
        // Look for business entity section
        const businessSection = await page.locator('text=Business Entity').first().isVisible();
        console.log('Business entity section visible:', businessSection);
      }
    }
  });

  test('should verify CSS and styling loaded', async ({ page }) => {
    await page.goto(`${SITE_URL}/alliances`);
    await page.waitForLoadState('networkidle');
    
    // Check if our alliance styles are loaded
    const stylesheets = await page.evaluate(() => {
      return Array.from(document.styleSheets).map(sheet => {
        try {
          return sheet.href || 'inline';
        } catch (e) {
          return 'blocked';
        }
      });
    });
    
    console.log('Loaded stylesheets:', stylesheets);
    
    // Check for specific alliance styling
    const hasAllianceStyles = await page.evaluate(() => {
      const styles = Array.from(document.styleSheets);
      for (const sheet of styles) {
        try {
          const rules = Array.from(sheet.cssRules || sheet.rules || []);
          for (const rule of rules) {
            if (rule.selectorText && rule.selectorText.includes('alliance')) {
              return true;
            }
          }
        } catch (e) {
          // Cross-origin or other access issues
        }
      }
      return false;
    });
    
    console.log('Has alliance-specific styles:', hasAllianceStyles);
  });

  test('should test navigation integration', async ({ page }) => {
    // Test that alliance routes work with experimental navigation
    await page.goto(`${SITE_URL}`);
    await page.waitForLoadState('networkidle');
    
    // Try keyboard navigation (if experimental nav is active)
    await page.keyboard.press('ArrowDown'); // Should go to overworld view
    await page.waitForTimeout(1000);
    
    await page.keyboard.press('ArrowDown'); // Should go to grid view
    await page.waitForTimeout(1000);
    
    // Look for teams/alliance card in grid view
    const teamsCard = page.locator('text=Teams').first();
    if (await teamsCard.isVisible()) {
      await teamsCard.click();
      await page.waitForLoadState('networkidle');
      
      console.log('Successfully navigated to teams via experimental navigation');
    }
  });

  test('should check database connectivity', async ({ page }) => {
    // Test if we can access Supabase data
    await page.goto(`${SITE_URL}`);
    await page.waitForLoadState('networkidle');
    
    // Check browser console for any database errors
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });
    
    // Wait a bit for any async operations
    await page.waitForTimeout(3000);
    
    // Check for Supabase-related errors
    const supabaseErrors = logs.filter(log => 
      log.includes('supabase') || 
      log.includes('database') || 
      log.includes('companies') ||
      log.includes('financial_transactions')
    );
    
    console.log('Database-related errors:', supabaseErrors);
    
    if (supabaseErrors.length === 0) {
      console.log('No database connectivity issues detected');
    }
  });
});

test.describe('API Endpoint Tests', () => {
  test('should test all new API endpoints', async ({ page }) => {
    const endpoints = [
      '/.netlify/functions/companies',
      '/.netlify/functions/financial-transactions',
      '/.netlify/functions/hello', // Test basic function
    ];
    
    for (const endpoint of endpoints) {
      const response = await page.request.get(`${SITE_URL}${endpoint}`);
      console.log(`${endpoint} status:`, response.status());
      
      // Log response for debugging
      if (response.status() !== 404) {
        try {
          const text = await response.text();
          console.log(`${endpoint} response:`, text.substring(0, 200));
        } catch (e) {
          console.log(`${endpoint} response could not be read`);
        }
      }
    }
  });
});
