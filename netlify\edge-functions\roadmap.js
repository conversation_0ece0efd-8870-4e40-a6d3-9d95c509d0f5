// Roadmap Edge Function - Static implementation
export default async (request, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Hardcoded roadmap data for testing
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          }
        ]
      },
      {
        id: 4,
        title: "Enhanced Track Page - Project Management Hub",
        timeframe: "Phase 2 - Current",
        expanded: true,
        sections: [
          {
            id: "4.1",
            title: "Core Track Page Infrastructure",
            tasks: [
              { id: "4.1.1", text: "Analyze current Track page structure", completed: true },
              { id: "4.1.2", text: "Create vertical navigation system (60px width)", completed: true },
              { id: "4.1.3", text: "Restore and enhance kanban board integration", completed: true },
              { id: "4.1.4", text: "Build project overview cards grid", completed: true }
            ]
          },
          {
            id: "4.2",
            title: "Time Tracking & Task Management",
            tasks: [
              { id: "4.2.1", text: "Transform global timer to per-task timers", completed: true },
              { id: "4.2.2", text: "Create elegant task creation modal", completed: true },
              { id: "4.2.3", text: "Implement quick actions panel", completed: true },
              { id: "4.2.4", text: "Add mission board integration with styling", completed: true }
            ]
          },
          {
            id: "4.3",
            title: "Advanced Features & Polish",
            tasks: [
              { id: "4.3.1", text: "Add real-time collaboration features", completed: true },
              { id: "4.3.2", text: "Implement responsive design for mobile", completed: false },
              { id: "4.3.3", text: "Add integration status monitoring", completed: false },
              { id: "4.3.4", text: "Comprehensive testing and polish", completed: false }
            ]
          }
        ]
      }
    ];

    // Calculate stats
    const stats = calculateStats(roadmapData);

    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'edge-function'
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in roadmap edge function:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers
      }
    );
  }
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}
