import { useContext } from 'react';
import { UserContext } from '../contexts/supabase-auth.context';
import { supabase } from '../utils/supabase/supabase.utils';

/**
 * Custom hook to access Supabase client and user context
 * Provides a unified interface for Supabase operations
 */
export const useSupabase = () => {
  const { currentUser: user, isLoading } = useContext(UserContext);

  return {
    supabase,
    user,
    isLoading
  };
};

export default useSupabase;
