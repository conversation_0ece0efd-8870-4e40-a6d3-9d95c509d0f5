// DataExportManager - Advanced data export and reporting functionality
// Implements data export following system specifications
import React, { useState, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Badge, Select, SelectItem, Checkbox, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure, Progress } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { 
  Download, 
  FileText, 
  Table, 
  BarChart3,
  Calendar,
  Filter,
  Settings,
  CheckCircle,
  Clock,
  AlertCircle,
  FileSpreadsheet,
  FileImage,
  Mail
} from 'lucide-react';

const DataExportManager = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [selectedDataTypes, setSelectedDataTypes] = useState([]);
  const [selectedFormat, setSelectedFormat] = useState('csv');
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [includeCharts, setIncludeCharts] = useState(false);
  const [emailReport, setEmailReport] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportHistory, setExportHistory] = useState([
    {
      id: 1,
      name: 'Financial Report Q2 2025',
      format: 'PDF',
      status: 'completed',
      createdAt: '2025-01-15T10:30:00Z',
      downloadUrl: '#',
      size: '2.4 MB'
    },
    {
      id: 2,
      name: 'Project Analytics Export',
      format: 'CSV',
      status: 'completed',
      createdAt: '2025-01-14T15:45:00Z',
      downloadUrl: '#',
      size: '856 KB'
    },
    {
      id: 3,
      name: 'Studio Performance Report',
      format: 'Excel',
      status: 'processing',
      createdAt: '2025-01-16T09:15:00Z',
      downloadUrl: null,
      size: null
    }
  ]);

  const { isOpen, onOpen, onClose } = useDisclosure();

  const dataTypes = [
    { key: 'financial', label: 'Financial Data', description: 'Revenue, expenses, profit margins' },
    { key: 'projects', label: 'Project Analytics', description: 'Project performance, timelines, ratings' },
    { key: 'studios', label: 'Studio Data', description: 'Member data, collaboration metrics' },
    { key: 'social', label: 'Social Analytics', description: 'Network growth, engagement metrics' },
    { key: 'performance', label: 'Performance Metrics', description: 'KPIs, success rates, trends' },
    { key: 'users', label: 'User Data', description: 'User profiles, activity, preferences' }
  ];

  const exportFormats = [
    { key: 'csv', label: 'CSV', icon: <Table size={16} />, description: 'Comma-separated values for spreadsheets' },
    { key: 'excel', label: 'Excel', icon: <FileSpreadsheet size={16} />, description: 'Microsoft Excel workbook with charts' },
    { key: 'pdf', label: 'PDF', icon: <FileText size={16} />, description: 'Formatted report with visualizations' },
    { key: 'json', label: 'JSON', icon: <BarChart3 size={16} />, description: 'Raw data for API integration' }
  ];

  const timePeriods = [
    { key: '7d', label: 'Last 7 Days' },
    { key: '30d', label: 'Last 30 Days' },
    { key: '90d', label: 'Last 90 Days' },
    { key: '6m', label: 'Last 6 Months' },
    { key: '1y', label: 'Last Year' },
    { key: 'all', label: 'All Time' }
  ];

  const handleDataTypeToggle = (dataType) => {
    setSelectedDataTypes(prev => 
      prev.includes(dataType) 
        ? prev.filter(type => type !== dataType)
        : [...prev, dataType]
    );
  };

  const handleExport = async () => {
    if (selectedDataTypes.length === 0) {
      alert('Please select at least one data type to export');
      return;
    }

    try {
      setIsExporting(true);
      
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Create new export record
      const newExport = {
        id: Date.now(),
        name: `Analytics Export ${new Date().toLocaleDateString()}`,
        format: selectedFormat.toUpperCase(),
        status: 'completed',
        createdAt: new Date().toISOString(),
        downloadUrl: '#',
        size: '1.2 MB'
      };
      
      setExportHistory(prev => [newExport, ...prev]);
      
      // Reset form
      setSelectedDataTypes([]);
      setIncludeCharts(false);
      setEmailReport(false);
      onClose();
      
      alert('Export completed successfully!');
      
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle className="text-green-500" size={16} />;
      case 'processing': return <Clock className="text-yellow-500" size={16} />;
      case 'failed': return <AlertCircle className="text-red-500" size={16} />;
      default: return <Clock className="text-gray-500" size={16} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'processing': return 'warning';
      case 'failed': return 'danger';
      default: return 'default';
    }
  };

  const getFormatIcon = (format) => {
    switch (format.toLowerCase()) {
      case 'pdf': return <FileText className="text-red-500" size={16} />;
      case 'csv': return <Table className="text-green-500" size={16} />;
      case 'excel': return <FileSpreadsheet className="text-blue-500" size={16} />;
      case 'json': return <BarChart3 className="text-purple-500" size={16} />;
      default: return <FileText className="text-gray-500" size={16} />;
    }
  };

  return (
    <div className={`data-export-manager space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Download className="text-blue-500" size={28} />
            Data Export Manager
          </h2>
          <p className="text-gray-600">Export analytics data in various formats</p>
        </div>
        <Button color="primary" size="lg" startContent={<Download size={20} />} onPress={onOpen}>
          Create Export
        </Button>
      </div>

      {/* Quick Export Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onPress={() => {
            setSelectedDataTypes(['financial']);
            setSelectedFormat('pdf');
            onOpen();
          }}>
            <CardBody className="p-6 text-center">
              <FileText className="mx-auto mb-3 text-red-500" size={32} />
              <h3 className="font-semibold mb-2">Financial Report</h3>
              <p className="text-sm text-gray-600">Comprehensive financial analytics in PDF format</p>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onPress={() => {
            setSelectedDataTypes(['projects', 'performance']);
            setSelectedFormat('excel');
            onOpen();
          }}>
            <CardBody className="p-6 text-center">
              <FileSpreadsheet className="mx-auto mb-3 text-green-500" size={32} />
              <h3 className="font-semibold mb-2">Project Analytics</h3>
              <p className="text-sm text-gray-600">Project performance data in Excel format</p>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
          <Card className="cursor-pointer hover:shadow-md transition-shadow" onPress={() => {
            setSelectedDataTypes(['studios', 'social']);
            setSelectedFormat('csv');
            onOpen();
          }}>
            <CardBody className="p-6 text-center">
              <Table className="mx-auto mb-3 text-blue-500" size={32} />
              <h3 className="font-semibold mb-2">Studio Data</h3>
              <p className="text-sm text-gray-600">Studio and social metrics in CSV format</p>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Export History */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <h3 className="text-lg font-semibold">Export History</h3>
            <Badge color="primary" variant="flat">{exportHistory.length} exports</Badge>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            {exportHistory.map((exportItem) => (
              <motion.div
                key={exportItem.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between p-4 border rounded-lg hover:shadow-md transition-shadow"
              >
                <div className="flex items-center gap-4">
                  {getFormatIcon(exportItem.format)}
                  <div>
                    <h4 className="font-semibold">{exportItem.name}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>{new Date(exportItem.createdAt).toLocaleDateString()}</span>
                      {exportItem.size && <span>{exportItem.size}</span>}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(exportItem.status)}
                    <Badge color={getStatusColor(exportItem.status)} size="sm">
                      {exportItem.status}
                    </Badge>
                  </div>
                  
                  {exportItem.status === 'completed' && (
                    <Button size="sm" variant="light" color="primary">
                      Download
                    </Button>
                  )}
                  
                  {exportItem.status === 'processing' && (
                    <div className="w-16">
                      <Progress size="sm" isIndeterminate />
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Export Configuration Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>
            <h3>Configure Data Export</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-6">
              {/* Data Types Selection */}
              <div>
                <h4 className="font-semibold mb-3">Select Data Types</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {dataTypes.map((dataType) => (
                    <div
                      key={dataType.key}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedDataTypes.includes(dataType.key)
                          ? 'border-primary bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleDataTypeToggle(dataType.key)}
                    >
                      <div className="flex items-start gap-3">
                        <Checkbox
                          isSelected={selectedDataTypes.includes(dataType.key)}
                          onChange={() => handleDataTypeToggle(dataType.key)}
                        />
                        <div>
                          <h5 className="font-medium">{dataType.label}</h5>
                          <p className="text-sm text-gray-600">{dataType.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Export Format */}
              <div>
                <h4 className="font-semibold mb-3">Export Format</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {exportFormats.map((format) => (
                    <div
                      key={format.key}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors text-center ${
                        selectedFormat === format.key
                          ? 'border-primary bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedFormat(format.key)}
                    >
                      <div className="flex flex-col items-center gap-2">
                        {format.icon}
                        <span className="font-medium">{format.label}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Time Period */}
              <div>
                <h4 className="font-semibold mb-3">Time Period</h4>
                <Select
                  selectedKeys={[selectedPeriod]}
                  onSelectionChange={(keys) => setSelectedPeriod(Array.from(keys)[0])}
                  className="w-full"
                >
                  {timePeriods.map(period => (
                    <SelectItem key={period.key}>{period.label}</SelectItem>
                  ))}
                </Select>
              </div>

              {/* Additional Options */}
              <div>
                <h4 className="font-semibold mb-3">Additional Options</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">Include Charts</span>
                      <p className="text-sm text-gray-600">Add visualizations to the export</p>
                    </div>
                    <Checkbox
                      isSelected={includeCharts}
                      onChange={setIncludeCharts}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">Email Report</span>
                      <p className="text-sm text-gray-600">Send export to your email when ready</p>
                    </div>
                    <Checkbox
                      isSelected={emailReport}
                      onChange={setEmailReport}
                    />
                  </div>
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleExport}
              isLoading={isExporting}
              isDisabled={selectedDataTypes.length === 0}
              startContent={!isExporting && <Download size={16} />}
            >
              {isExporting ? 'Exporting...' : 'Start Export'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default DataExportManager;
