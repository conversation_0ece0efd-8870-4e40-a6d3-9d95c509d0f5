[build]
  command = "cd client && npm run build"
  publish = "client/dist"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"
  VITE_SUPABASE_URL = "https://hqqlrrqvjcetoxbdjgzx.supabase.co"
  VITE_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ"

[dev]
  command = "npm run dev"
  port = 8888
  targetPort = 1234

# API endpoints - These must come BEFORE the SPA redirect
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
  force = true

# Specific API endpoints for roadmap data
[[redirects]]
  from = "/api/roadmap-static"
  to = "/.netlify/functions/roadmap-static"
  status = 200
  force = true

[[redirects]]
  from = "/api/basic-roadmap"
  to = "/.netlify/functions/basic-roadmap"
  status = 200
  force = true

[[redirects]]
  from = "/api/hello"
  to = "/.netlify/functions/hello"
  status = 200
  force = true

[[redirects]]
  from = "/api/test-function"
  to = "/.netlify/functions/test-function"
  status = 200
  force = true

# Agreement notifications endpoint
[[redirects]]
  from = "/api/agreement-notifications"
  to = "/.netlify/functions/agreement-notifications"
  status = 200
  force = true

# Profile migration endpoint
[[redirects]]
  from = "/api/profile-migration"
  to = "/.netlify/functions/execute-profile-migration"
  status = 200
  force = true

# JavaScript module MIME types
[[headers]]
  for = "*.js"
  [headers.values]
    Content-Type = "application/javascript"

[[headers]]
  for = "*.mjs"
  [headers.values]
    Content-Type = "application/javascript"

# CSS MIME types
[[headers]]
  for = "*.css"
  [headers.values]
    Content-Type = "text/css"

# CORS headers for all API endpoints
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type"

# Add CORS headers for the roadmap.json file
[[headers]]
  for = "/roadmap.json"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type"
    Content-Type = "application/json"

# SPA redirect - this must be LAST to avoid catching API requests
# Don't redirect requests for static assets
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false
