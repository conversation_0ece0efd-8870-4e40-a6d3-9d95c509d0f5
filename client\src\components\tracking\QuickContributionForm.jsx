import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button, Input, Textarea, Select, SelectItem, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Quick Contribution Form Component
 * 
 * A streamlined form for submitting contributions without time tracking.
 * Perfect for logging completed work, code commits, design assets, etc.
 */
const QuickContributionForm = ({ projectId = null, onSubmit, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  const [formData, setFormData] = useState({
    task_description: '',
    description: '',
    hours_tracked: '',
    difficulty_rating: '3',
    contribution_type: 'development',
    tags: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Contribution types
  const contributionTypes = [
    { key: 'development', label: '💻 Development', description: 'Code, programming, technical work' },
    { key: 'design', label: '🎨 Design', description: 'UI/UX, graphics, visual assets' },
    { key: 'content', label: '📝 Content', description: 'Writing, documentation, copy' },
    { key: 'testing', label: '🧪 Testing', description: 'QA, bug testing, user testing' },
    { key: 'research', label: '🔬 Research', description: 'Market research, user research' },
    { key: 'management', label: '📋 Management', description: 'Project management, coordination' },
    { key: 'marketing', label: '📢 Marketing', description: 'Promotion, social media, outreach' },
    { key: 'other', label: '🔧 Other', description: 'Other types of contributions' }
  ];

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!currentUser) {
      toast.error('You must be logged in to submit contributions');
      return;
    }

    if (!formData.task_description.trim()) {
      toast.error('Task description is required');
      return;
    }

    if (!formData.hours_tracked || parseFloat(formData.hours_tracked) <= 0) {
      toast.error('Please enter the time spent (hours)');
      return;
    }

    setIsSubmitting(true);

    try {
      const contributionData = {
        user_id: currentUser.id,
        project_id: projectId,
        task_description: formData.task_description.trim(),
        description: formData.description.trim() || formData.task_description.trim(),
        hours_tracked: parseFloat(formData.hours_tracked),
        difficulty_rating: parseInt(formData.difficulty_rating),
        contribution_type: formData.contribution_type,
        status: 'pending',
        tags: formData.tags.trim() ? formData.tags.split(',').map(tag => tag.trim()) : [],
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('contributions')
        .insert([contributionData])
        .select()
        .single();

      if (error) throw error;

      toast.success('Contribution submitted successfully!');
      
      // Reset form
      setFormData({
        task_description: '',
        description: '',
        hours_tracked: '',
        difficulty_rating: '3',
        contribution_type: 'development',
        tags: ''
      });

      if (onSubmit) {
        onSubmit(data);
      }
    } catch (error) {
      console.error('Error submitting contribution:', error);
      toast.error('Failed to submit contribution');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get difficulty color
  const getDifficultyColor = (diff) => {
    const colors = {
      1: 'success', 2: 'success', 3: 'warning', 4: 'danger', 5: 'danger'
    };
    return colors[diff] || 'default';
  };

  // Get contribution type info
  const getContributionTypeInfo = (type) => {
    return contributionTypes.find(ct => ct.key === type) || contributionTypes[0];
  };

  return (
    <Card className={`bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-white/10 ${className}`}>
      <CardBody className="p-6">
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-white mb-2">Submit Contribution</h3>
          <p className="text-white/70 text-sm">
            Log completed work and track your contributions to the project
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Task Description */}
          <Input
            label="What did you work on?"
            placeholder="e.g., Fixed user authentication bug"
            value={formData.task_description}
            onChange={(e) => setFormData({ ...formData, task_description: e.target.value })}
            required
            className="text-white"
          />

          {/* Additional Description */}
          <Textarea
            label="Additional details (optional)"
            placeholder="Provide more context about your contribution..."
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            rows={3}
          />

          {/* Time and Difficulty Row */}
          <div className="grid grid-cols-2 gap-4">
            <Input
              type="number"
              label="Time Spent (hours)"
              placeholder="2.5"
              step="0.25"
              min="0.25"
              max="24"
              value={formData.hours_tracked}
              onChange={(e) => setFormData({ ...formData, hours_tracked: e.target.value })}
              required
            />

            <Select
              label="Difficulty Level"
              selectedKeys={[formData.difficulty_rating]}
              onSelectionChange={(keys) => setFormData({ ...formData, difficulty_rating: Array.from(keys)[0] })}
            >
              <SelectItem key="1" value="1">1 - Very Easy</SelectItem>
              <SelectItem key="2" value="2">2 - Easy</SelectItem>
              <SelectItem key="3" value="3">3 - Medium</SelectItem>
              <SelectItem key="4" value="4">4 - Hard</SelectItem>
              <SelectItem key="5" value="5">5 - Very Hard</SelectItem>
            </Select>
          </div>

          {/* Contribution Type */}
          <Select
            label="Contribution Type"
            selectedKeys={[formData.contribution_type]}
            onSelectionChange={(keys) => setFormData({ ...formData, contribution_type: Array.from(keys)[0] })}
          >
            {contributionTypes.map((type) => (
              <SelectItem key={type.key} value={type.key}>
                {type.label}
              </SelectItem>
            ))}
          </Select>

          {/* Tags */}
          <Input
            label="Tags (optional)"
            placeholder="frontend, bug-fix, authentication"
            value={formData.tags}
            onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
            description="Separate tags with commas"
          />

          {/* Preview */}
          <motion.div
            className="p-4 bg-white/5 rounded-lg border border-white/10"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <h4 className="text-sm font-medium text-white mb-3">Preview</h4>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-white/70 text-sm">Type:</span>
                <span className="text-white text-sm">
                  {getContributionTypeInfo(formData.contribution_type).label}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-white/70 text-sm">Time:</span>
                <span className="text-white text-sm">
                  {formData.hours_tracked || '0'} hours
                </span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-white/70 text-sm">Difficulty:</span>
                <Chip
                  size="sm"
                  color={getDifficultyColor(formData.difficulty_rating)}
                  variant="flat"
                >
                  Level {formData.difficulty_rating}
                </Chip>
              </div>
              {formData.tags && (
                <div className="flex items-center gap-2 flex-wrap">
                  <span className="text-white/70 text-sm">Tags:</span>
                  {formData.tags.split(',').map((tag, index) => (
                    <Chip key={index} size="sm" variant="flat" className="bg-blue-500/20 text-blue-200">
                      {tag.trim()}
                    </Chip>
                  ))}
                </div>
              )}
            </div>
          </motion.div>

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white"
            size="lg"
            isLoading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : '📤 Submit Contribution'}
          </Button>
        </form>
      </CardBody>
    </Card>
  );
};

export default QuickContributionForm;
