import { supabase } from '../utils/supabase/supabase.utils';

class LinkedInLearningService {
  constructor() {
    this.apiKey = process.env.REACT_APP_LINKEDIN_LEARNING_API_KEY;
    this.baseUrl = 'https://api.linkedin.com/v2/learningAssets';
    this.authUrl = 'https://www.linkedin.com/oauth/v2/authorization';
    this.tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
    this.clientId = process.env.REACT_APP_LINKEDIN_CLIENT_ID;
    this.clientSecret = process.env.REACT_APP_LINKEDIN_CLIENT_SECRET;
    this.redirectUri = process.env.REACT_APP_LINKEDIN_REDIRECT_URI;
  }

  /**
   * Initialize LinkedIn Learning OAuth flow
   */
  initiateOAuth(userId, state = null) {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientId,
      redirect_uri: this.redirectUri,
      scope: 'r_liteprofile r_emailaddress w_member_social learning_api',
      state: state || `user_${userId}_${Date.now()}`
    });

    const authUrl = `${this.authUrl}?${params.toString()}`;
    window.location.href = authUrl;
  }

  /**
   * Exchange authorization code for access token
   */
  async exchangeCodeForToken(code, state) {
    try {
      const response = await fetch(this.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'authorization_code',
          code: code,
          redirect_uri: this.redirectUri,
          client_id: this.clientId,
          client_secret: this.clientSecret
        })
      });

      if (!response.ok) {
        throw new Error('Failed to exchange code for token');
      }

      const tokenData = await response.json();
      
      // Store token in user preferences or secure storage
      await this.storeUserToken(state, tokenData);
      
      return tokenData;
    } catch (error) {
      console.error('Error exchanging code for token:', error);
      throw error;
    }
  }

  /**
   * Store LinkedIn Learning access token
   */
  async storeUserToken(state, tokenData) {
    try {
      const userId = this.extractUserIdFromState(state);
      
      const { error } = await supabase
        .from('user_integrations')
        .upsert({
          user_id: userId,
          integration_type: 'linkedin_learning',
          access_token: tokenData.access_token,
          refresh_token: tokenData.refresh_token,
          expires_at: new Date(Date.now() + tokenData.expires_in * 1000).toISOString(),
          integration_data: tokenData,
          is_active: true
        }, { onConflict: 'user_id,integration_type' });

      if (error) throw error;
    } catch (error) {
      console.error('Error storing user token:', error);
      throw error;
    }
  }

  /**
   * Get user's LinkedIn Learning access token
   */
  async getUserToken(userId) {
    try {
      const { data, error } = await supabase
        .from('user_integrations')
        .select('*')
        .eq('user_id', userId)
        .eq('integration_type', 'linkedin_learning')
        .eq('is_active', true)
        .single();

      if (error) throw error;

      // Check if token is expired and refresh if needed
      if (data && new Date(data.expires_at) <= new Date()) {
        return await this.refreshToken(data);
      }

      return data;
    } catch (error) {
      console.error('Error getting user token:', error);
      return null;
    }
  }

  /**
   * Refresh expired access token
   */
  async refreshToken(tokenData) {
    try {
      const response = await fetch(this.tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: tokenData.refresh_token,
          client_id: this.clientId,
          client_secret: this.clientSecret
        })
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const newTokenData = await response.json();
      
      // Update stored token
      const { data, error } = await supabase
        .from('user_integrations')
        .update({
          access_token: newTokenData.access_token,
          refresh_token: newTokenData.refresh_token || tokenData.refresh_token,
          expires_at: new Date(Date.now() + newTokenData.expires_in * 1000).toISOString(),
          integration_data: { ...tokenData.integration_data, ...newTokenData }
        })
        .eq('id', tokenData.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  /**
   * Search LinkedIn Learning courses
   */
  async searchCourses(query, filters = {}) {
    try {
      const params = new URLSearchParams({
        q: 'search',
        keywords: query,
        count: filters.count || 25,
        start: filters.start || 0
      });

      // Add additional filters
      if (filters.difficulty) params.append('difficulty', filters.difficulty);
      if (filters.duration) params.append('duration', filters.duration);
      if (filters.language) params.append('language', filters.language);

      const response = await fetch(`${this.baseUrl}?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to search courses');
      }

      const data = await response.json();
      return this.formatCourseData(data.elements || []);
    } catch (error) {
      console.error('Error searching courses:', error);
      throw error;
    }
  }

  /**
   * Get course details by ID
   */
  async getCourseDetails(courseId) {
    try {
      const response = await fetch(`${this.baseUrl}/${courseId}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get course details');
      }

      const data = await response.json();
      return this.formatCourseData([data])[0];
    } catch (error) {
      console.error('Error getting course details:', error);
      throw error;
    }
  }

  /**
   * Get user's learning progress
   */
  async getUserProgress(userId, courseId = null) {
    try {
      const tokenData = await this.getUserToken(userId);
      if (!tokenData) {
        throw new Error('User not connected to LinkedIn Learning');
      }

      let url = 'https://api.linkedin.com/v2/learningProgress';
      if (courseId) {
        url += `?q=learningAsset&learningAsset=${courseId}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${tokenData.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get user progress');
      }

      const data = await response.json();
      return data.elements || [];
    } catch (error) {
      console.error('Error getting user progress:', error);
      throw error;
    }
  }

  /**
   * Track course completion
   */
  async trackCompletion(userId, courseId, completionData) {
    try {
      // Store completion in our database
      const { data, error } = await supabase
        .from('learning_progress')
        .upsert({
          user_id: userId,
          course_id: courseId,
          course_provider: 'linkedin_learning',
          completion_percentage: completionData.percentage || 100,
          completed_at: completionData.completed_at || new Date().toISOString(),
          time_spent_minutes: completionData.time_spent_minutes || 0,
          progress_data: completionData
        }, { onConflict: 'user_id,course_id,course_provider' });

      if (error) throw error;

      // Update skill assessments if course is linked to skills
      await this.updateSkillProgress(userId, courseId, completionData);

      return data;
    } catch (error) {
      console.error('Error tracking completion:', error);
      throw error;
    }
  }

  /**
   * Update skill progress based on course completion
   */
  async updateSkillProgress(userId, courseId, completionData) {
    try {
      // Get course-skill mappings
      const { data: mappings, error } = await supabase
        .from('course_skill_mappings')
        .select('*')
        .eq('course_id', courseId);

      if (error) throw error;

      for (const mapping of mappings || []) {
        // Update or create skill assessment
        await supabase
          .from('skill_assessments')
          .upsert({
            user_id: userId,
            skill_name: mapping.skill_name,
            skill_category: mapping.skill_category,
            assessment_type: 'course_completion',
            score: completionData.percentage || 100,
            verification_level: 'self_reported',
            assessment_data: {
              course_id: courseId,
              course_provider: 'linkedin_learning',
              completion_data: completionData
            }
          }, { onConflict: 'user_id,skill_name,assessment_type' });
      }
    } catch (error) {
      console.error('Error updating skill progress:', error);
    }
  }

  /**
   * Get recommended courses for user
   */
  async getRecommendedCourses(userId, skillGaps = []) {
    try {
      // Get user's current skills and learning history
      const { data: userSkills, error: skillsError } = await supabase
        .from('skill_assessments')
        .select('skill_name, skill_category, score')
        .eq('user_id', userId);

      if (skillsError) throw skillsError;

      // Get completed courses
      const { data: completedCourses, error: coursesError } = await supabase
        .from('learning_progress')
        .select('course_id')
        .eq('user_id', userId)
        .eq('course_provider', 'linkedin_learning')
        .gte('completion_percentage', 80);

      if (coursesError) throw coursesError;

      const completedCourseIds = completedCourses?.map(c => c.course_id) || [];

      // Build recommendation query based on skill gaps
      const recommendations = [];
      
      for (const skillGap of skillGaps) {
        const courses = await this.searchCourses(skillGap.skill_name, {
          difficulty: skillGap.target_level || 'intermediate',
          count: 5
        });
        
        // Filter out completed courses
        const filteredCourses = courses.filter(course => 
          !completedCourseIds.includes(course.id)
        );
        
        recommendations.push(...filteredCourses);
      }

      // Remove duplicates and return top recommendations
      const uniqueRecommendations = recommendations.filter((course, index, self) =>
        index === self.findIndex(c => c.id === course.id)
      );

      return uniqueRecommendations.slice(0, 10);
    } catch (error) {
      console.error('Error getting recommended courses:', error);
      throw error;
    }
  }

  /**
   * Format course data from LinkedIn Learning API
   */
  formatCourseData(courses) {
    return courses.map(course => ({
      id: course.urn || course.id,
      title: course.title?.localized?.en_US || course.title,
      description: course.description?.localized?.en_US || course.description,
      duration: course.duration,
      difficulty: course.difficultyLevel,
      instructor: course.authors?.[0]?.name,
      thumbnail: course.images?.[0]?.url,
      url: course.url,
      skills: course.skills || [],
      language: course.locale?.language || 'en',
      publishedAt: course.publishedAt,
      updatedAt: course.lastModifiedAt,
      rating: course.rating,
      provider: 'linkedin_learning'
    }));
  }

  /**
   * Extract user ID from OAuth state parameter
   */
  extractUserIdFromState(state) {
    const match = state.match(/user_([^_]+)_/);
    return match ? match[1] : null;
  }

  /**
   * Check if user has LinkedIn Learning integration
   */
  async isUserConnected(userId) {
    try {
      const tokenData = await this.getUserToken(userId);
      return !!tokenData;
    } catch (error) {
      return false;
    }
  }

  /**
   * Disconnect user from LinkedIn Learning
   */
  async disconnectUser(userId) {
    try {
      const { error } = await supabase
        .from('user_integrations')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('integration_type', 'linkedin_learning');

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error disconnecting user:', error);
      throw error;
    }
  }
}

export const linkedInLearningService = new LinkedInLearningService();
export default linkedInLearningService;
