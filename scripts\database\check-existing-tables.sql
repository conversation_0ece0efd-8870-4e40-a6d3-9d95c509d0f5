-- Check what tables exist in the public schema
-- Run this first to see what tables are actually available

SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name IN (
        'projects', 
        'project_contributors', 
        'active_timers', 
        'tasks',
        'contribution_tracking_config',
        'users'
    )
ORDER BY table_name;

-- Also check what policies already exist
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
    AND tablename IN ('projects', 'project_contributors', 'active_timers', 'tasks')
ORDER BY tablename, policyname;
