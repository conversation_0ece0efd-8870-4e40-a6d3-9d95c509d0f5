/**
 * Comprehensive Test Suite for Agreement Generator V2
 * 
 * Tests all aspects of the new agreement generation system
 * including accuracy validation against lawyer-approved template.
 */

import { AgreementGeneratorV2 } from '../AgreementGeneratorV2.js';
import { DataValidator } from '../DataValidator.js';
import { TemplateLoader } from '../TemplateLoader.js';
import { ReplacementEngine } from '../ReplacementEngine.js';
import { OutputValidator } from '../OutputValidator.js';
import { ValidationError, ReplacementError, TemplateError } from '../errors/AgreementErrors.js';

describe('Agreement Generator V2 - Comprehensive Test Suite', () => {
  let generator;
  let testData;

  beforeEach(() => {
    generator = new AgreementGeneratorV2();
    
    // Standard test data
    testData = {
      company: {
        name: 'TechCorp Solutions Inc.',
        address: '123 Innovation Drive, Orlando, FL 32801',
        state: 'Florida',
        city: 'Orlando',
        signerName: '<PERSON>',
        signerTitle: 'CEO',
        billingEmail: '<EMAIL>'
      },
      project: {
        name: 'AI Analytics Platform',
        description: 'Advanced AI-powered analytics platform for enterprise data insights',
        projectType: 'software'
      },
      contributor: {
        name: '<PERSON>',
        email: '<EMAIL>',
        address: '456 Developer Lane, Orlando, FL 32802'
      }
    };
  });

  describe('System Integration Tests', () => {
    
    test('should generate agreement with 95%+ accuracy', async () => {
      const result = await generator.generateAgreement('standard', testData);
      
      expect(result.success).toBe(true);
      expect(result.metadata.accuracyScore).toBeGreaterThanOrEqual(95);
      expect(result.agreement).toBeDefined();
      expect(result.agreement.length).toBeGreaterThan(1000);
    });

    test('should contain all required sections', async () => {
      const result = await generator.generateAgreement('standard', testData);
      
      const requiredSections = [
        'CONTRIBUTOR AGREEMENT',
        'Recitals',
        '1. Definitions',
        '2. Treatment of Confidential Information',
        '3. Ownership of Work Product',
        'SCHEDULE A',
        'SCHEDULE B'
      ];

      requiredSections.forEach(section => {
        expect(result.agreement).toContain(section);
      });
    });

    test('should have no unreplaced placeholders', async () => {
      const result = await generator.generateAgreement('standard', testData);
      
      const placeholderPatterns = [
        /\{\{[A-Z_]+\}\}/g,
        /\[[A-Z][^\]]*\]/g,
        /\[_+\]/g
      ];

      placeholderPatterns.forEach(pattern => {
        const matches = result.agreement.match(pattern);
        expect(matches).toBeNull();
      });
    });

    test('should contain all user data', async () => {
      const result = await generator.generateAgreement('standard', testData);
      
      // Check company data
      expect(result.agreement).toContain(testData.company.name);
      expect(result.agreement).toContain(testData.company.address);
      expect(result.agreement).toContain(testData.company.signerName);
      expect(result.agreement).toContain(testData.company.signerTitle);
      expect(result.agreement).toContain(testData.company.billingEmail);
      
      // Check project data
      expect(result.agreement).toContain(testData.project.name);
      expect(result.agreement).toContain(testData.project.description);
      
      // Check contributor data
      expect(result.agreement).toContain(testData.contributor.name);
      expect(result.agreement).toContain(testData.contributor.email);
    });

    test('should have no hardcoded content', async () => {
      const result = await generator.generateAgreement('standard', testData);
      
      const forbiddenContent = [
        'City of Gamers Inc.',
        'Village of The Ages',
        'Gynell Journigan',
        '1205 43rd Street',
        'Orange County',
        '32839'
      ];

      forbiddenContent.forEach(content => {
        expect(result.agreement).not.toContain(content);
      });
    });
  });

  describe('Data Validation Tests', () => {
    
    test('should reject missing required fields', async () => {
      const invalidData = { ...testData };
      delete invalidData.company.name;
      
      const result = await generator.generateAgreement('standard', invalidData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('ValidationError');
    });

    test('should reject invalid email addresses', async () => {
      const invalidData = { ...testData };
      invalidData.company.billingEmail = 'invalid-email';
      
      const result = await generator.generateAgreement('standard', invalidData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('ValidationError');
    });

    test('should reject hardcoded company names', async () => {
      const invalidData = { ...testData };
      invalidData.company.name = 'City of Gamers Inc.';
      
      const result = await generator.generateAgreement('standard', invalidData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('ValidationError');
    });

    test('should reject generic project names', async () => {
      const invalidData = { ...testData };
      invalidData.project.name = 'Test Project';
      
      const result = await generator.generateAgreement('standard', invalidData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('ValidationError');
    });
  });

  describe('Project Type Specific Tests', () => {
    
    test('should generate game-specific content for game projects', async () => {
      const gameData = { ...testData };
      gameData.project.projectType = 'game';
      gameData.project.name = 'Mystic Realms RPG';
      gameData.project.description = 'Fantasy role-playing game with immersive storytelling';
      
      const result = await generator.generateAgreement('standard', gameData);
      
      expect(result.success).toBe(true);
      expect(result.agreement).toContain('Game Development Services');
      expect(result.agreement).toContain('game development work');
    });

    test('should generate software-specific content for software projects', async () => {
      const result = await generator.generateAgreement('standard', testData);
      
      expect(result.success).toBe(true);
      expect(result.agreement).toContain('Software Development Services');
      expect(result.agreement).toContain('software development work');
    });

    test('should generate music-specific content for music projects', async () => {
      const musicData = { ...testData };
      musicData.project.projectType = 'music';
      musicData.project.name = 'Urban Beats Album';
      musicData.project.description = 'Contemporary urban music album';
      
      const result = await generator.generateAgreement('standard', musicData);
      
      expect(result.success).toBe(true);
      expect(result.agreement).toContain('Music Production Services');
      expect(result.agreement).toContain('music production work');
    });
  });

  describe('Edge Case Tests', () => {
    
    test('should handle very long company names', async () => {
      const longNameData = { ...testData };
      longNameData.company.name = 'A'.repeat(200); // Maximum allowed length
      
      const result = await generator.generateAgreement('standard', longNameData);
      
      expect(result.success).toBe(true);
      expect(result.agreement).toContain(longNameData.company.name);
    });

    test('should reject company names that are too long', async () => {
      const tooLongData = { ...testData };
      tooLongData.company.name = 'A'.repeat(201); // Over maximum
      
      const result = await generator.generateAgreement('standard', tooLongData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('ValidationError');
    });

    test('should handle special characters in names', async () => {
      const specialCharData = { ...testData };
      specialCharData.company.name = 'Tech & Innovation Corp.';
      specialCharData.contributor.name = "O'Connor & Associates";
      
      const result = await generator.generateAgreement('standard', specialCharData);
      
      expect(result.success).toBe(true);
      expect(result.agreement).toContain(specialCharData.company.name);
      expect(result.agreement).toContain(specialCharData.contributor.name);
    });

    test('should handle different US states', async () => {
      const states = ['California', 'New York', 'Texas', 'Florida', 'CA', 'NY', 'TX', 'FL'];
      
      for (const state of states) {
        const stateData = { ...testData };
        stateData.company.state = state;
        
        const result = await generator.generateAgreement('standard', stateData);
        
        expect(result.success).toBe(true);
        expect(result.agreement).toContain(state);
      }
    });

    test('should reject invalid states', async () => {
      const invalidStateData = { ...testData };
      invalidStateData.company.state = 'Invalid State';
      
      const result = await generator.generateAgreement('standard', invalidStateData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('ValidationError');
    });
  });

  describe('Performance Tests', () => {
    
    test('should generate agreement in under 2 seconds', async () => {
      const startTime = Date.now();
      
      const result = await generator.generateAgreement('standard', testData);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(2000);
    });

    test('should handle multiple concurrent generations', async () => {
      const promises = [];
      
      for (let i = 0; i < 5; i++) {
        const data = { ...testData };
        data.company.name = `Company ${i}`;
        promises.push(generator.generateAgreement('standard', data));
      }
      
      const results = await Promise.all(promises);
      
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.agreement).toContain(`Company ${index}`);
      });
    });
  });

  describe('Error Handling Tests', () => {
    
    test('should provide detailed error information', async () => {
      const invalidData = {};
      
      const result = await generator.generateAgreement('standard', invalidData);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.details).toBeDefined();
      expect(result.metadata).toBeDefined();
    });

    test('should handle template loading errors gracefully', async () => {
      const result = await generator.generateAgreement('nonexistent', testData);
      
      expect(result.success).toBe(false);
      expect(result.errorType).toBe('TemplateError');
    });
  });

  describe('Utility Function Tests', () => {
    
    test('should validate data without generating agreement', async () => {
      const validation = await generator.validateAgreementData(testData);
      
      expect(validation.valid).toBe(true);
      expect(validation.data).toBeDefined();
      expect(validation.issues).toHaveLength(0);
    });

    test('should get available templates', async () => {
      const templates = await generator.getAvailableTemplates();
      
      expect(Array.isArray(templates)).toBe(true);
      expect(templates.length).toBeGreaterThan(0);
      expect(templates[0]).toHaveProperty('type');
      expect(templates[0]).toHaveProperty('name');
    });

    test('should provide system information', () => {
      const info = generator.getSystemInfo();
      
      expect(info).toHaveProperty('version');
      expect(info).toHaveProperty('config');
      expect(info).toHaveProperty('components');
      expect(info.version).toBe('2.0.0');
    });

    test('should generate test agreements', async () => {
      const result = await generator.testGeneration('standard', 'default');
      
      expect(result.success).toBe(true);
      expect(result.agreement).toBeDefined();
    });
  });

  describe('Accuracy Validation Tests', () => {

    test('should achieve 95%+ accuracy against lawyer template', async () => {
      const result = await generator.generateAgreement('standard', testData);

      expect(result.success).toBe(true);
      expect(result.metadata.accuracyScore).toBeGreaterThanOrEqual(95);

      // Additional accuracy checks
      const validation = result.metadata.validationResults;
      expect(validation.criticalIssues).toHaveLength(0);
      expect(validation.completenessScore).toBeGreaterThanOrEqual(95);
    });

    test('should pass all legal compliance checks', async () => {
      const result = await generator.generateAgreement('standard', testData);

      expect(result.success).toBe(true);

      const validation = result.metadata.validationResults;
      const legalCompliance = validation.legalCompliance;

      Object.values(legalCompliance).forEach(passed => {
        expect(passed).toBe(true);
      });
    });

    test('should have proper signature blocks', async () => {
      const result = await generator.generateAgreement('standard', testData);

      expect(result.success).toBe(true);
      expect(result.agreement).toContain('COMPANY:');
      expect(result.agreement).toContain('CONTRIBUTOR:');
      expect(result.agreement).toContain('By: _________________________________');
      expect(result.agreement).toContain('_________________________________');
    });

    test('should have proper schedules with content', async () => {
      const result = await generator.generateAgreement('standard', testData);

      expect(result.success).toBe(true);
      expect(result.agreement).toContain('SCHEDULE A');
      expect(result.agreement).toContain('Description of Services');
      expect(result.agreement).toContain('SCHEDULE B');
      expect(result.agreement).toContain('Description of Consideration');
    });
  });

  describe('Regression Tests', () => {

    test('should not contain any old system artifacts', async () => {
      const result = await generator.generateAgreement('standard', testData);

      expect(result.success).toBe(true);

      // Check for old replacement patterns
      expect(result.agreement).not.toMatch(/processed\s*=/);
      expect(result.agreement).not.toMatch(/replace\(/);
      expect(result.agreement).not.toMatch(/\$\{.*\}/);
    });

    test('should maintain consistent formatting', async () => {
      const result = await generator.generateAgreement('standard', testData);

      expect(result.success).toBe(true);

      // Check for proper markdown formatting
      expect(result.agreement).toMatch(/^# /m);
      expect(result.agreement).toMatch(/^## /m);

      // Check for proper section numbering
      expect(result.agreement).toMatch(/^\d+\. /m);
    });
  });
});
