// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * Royaltea Platform - Authenticated Playwright Test Configuration
 * 
 * Specialized configuration for testing authenticated features including
 * the bento grid navigation system with proper user authentication.
 */

module.exports = defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: false, // Disabled for auth tests to avoid conflicts
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 1, // Allow retries for auth flakiness
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : 2, // Limited workers for auth stability
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html', { outputFolder: 'test-results/auth-report' }],
    ['json', { outputFile: 'test-results/auth-results.json' }],
    ['junit', { outputFile: 'test-results/auth-results.xml' }],
    ['list'] // Console output for debugging
  ],
  /* Shared settings for all the projects below. */
  use: {
    /* Base URL - automatically detect local vs production */
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173',
    /* Collect trace when retrying the failed test */
    trace: 'on-first-retry',
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',
    /* Record video on failure */
    video: 'retain-on-failure',
    /* Global timeout for each action - increased for auth */
    actionTimeout: 15000,
    /* Global timeout for navigation - increased for auth */
    navigationTimeout: 45000,
    /* Extra HTTP headers */
    extraHTTPHeaders: {
      'Accept': 'application/json',
    },
    /* Ignore HTTPS errors for local development */
    ignoreHTTPSErrors: true,
  },

  /* Configure projects for major browsers */
  projects: [
    // Setup project for authentication
    {
      name: 'setup',
      testMatch: /.*\.setup\.js/,
      teardown: 'cleanup',
    },
    {
      name: 'cleanup',
      testMatch: /.*\.teardown\.js/,
    },

    // Main testing projects
    {
      name: 'chromium-auth',
      use: { 
        ...devices['Desktop Chrome'],
        // Use authenticated state
        storageState: 'test-results/auth-state.json',
      },
      dependencies: ['setup'],
    },

    {
      name: 'firefox-auth',
      use: { 
        ...devices['Desktop Firefox'],
        storageState: 'test-results/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Mobile testing with auth
    {
      name: 'mobile-chrome-auth',
      use: { 
        ...devices['Pixel 5'],
        storageState: 'test-results/auth-state.json',
      },
      dependencies: ['setup'],
    },

    // Admin testing project
    {
      name: 'admin-tests',
      use: { 
        ...devices['Desktop Chrome'],
        storageState: 'test-results/admin-auth-state.json',
      },
      dependencies: ['setup'],
      testMatch: /.*admin.*\.spec\.js/,
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'cd client && npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000, // 2 minutes
    stdout: 'ignore',
    stderr: 'pipe',
  },

  /* Global setup and teardown for authentication */
  globalSetup: require.resolve('./tests/auth-global-setup.js'),
  globalTeardown: require.resolve('./tests/auth-global-teardown.js'),

  /* Test timeout - increased for auth flows */
  timeout: 60 * 1000, // 60 seconds

  /* Expect timeout - increased for auth */
  expect: {
    timeout: 10 * 1000, // 10 seconds
  },

  /* Output directory for test artifacts */
  outputDir: 'test-results/auth-artifacts/',

  /* Test match patterns */
  testMatch: [
    '**/bento-grid-navigation.spec.js',
    '**/auth-*.spec.js',
    '**/authenticated-*.spec.js'
  ],

  /* Test ignore patterns */
  testIgnore: [
    '**/node_modules/**',
    '**/legacy/**',
    '**/archive/**'
  ],
});
