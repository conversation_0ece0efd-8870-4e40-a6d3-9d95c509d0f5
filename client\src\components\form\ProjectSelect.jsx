import { useState, useEffect } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import { getAuth } from "firebase/auth";

const ProjectSelect = (props) => {
  const { data, setData } = props;
  const [projects, setProjects] = useState([]); // State to store projects
  const [loading, setLoading] = useState(true);
  const auth = getAuth();

  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        const token = await auth.currentUser?.getIdToken();
        if (!token)
          throw new Error("Authentication error. Please log in again.");

        const response = await axios.get("/project/index", {
          headers: { Authorization: `Bearer ${token}` },
        }); // Fetch projects from project index
        if (response.status === 200) {
          setProjects(response.data); // Make sure to only set projects if response is OK
        } else {
          throw new Error("Failed to load projects.");
        }
      } catch (error) {
        console.error("Error fetching projects:", error);
        toast.error(error.response?.data?.error || "Failed to load projects.");
      } finally {
        setLoading(false);
      }
    };
    fetchProjects();
  }, []);

  return (
    <select
      id="project"
      type="text"
      className="form-control"
      value={data.project || ""} // Use the selected project's ID or default to an empty string
      onChange={(e) => setData({ ...data, project: e.target.value })} // Update the project when selection changes
      disabled={loading} // Disable dropdown while loading
    >
      {/* Blank/default option */}
      <option value="" disabled>
        {loading ? "Loading projects..." : "-- Select a Project --"}
      </option>

      {!loading &&
        projects.map((project) => (
          <option key={project._id} value={project._id}>
            {project.title} {/* Display the project's title in the dropdown */}
          </option>
        ))}
    </select>
  );
};

export default ProjectSelect;
