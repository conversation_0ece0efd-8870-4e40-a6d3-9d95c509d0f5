/**
 * Utility functions to help with routing issues
 */

/**
 * Ensures the URL is properly formatted for client-side routing
 * This helps with refresh and back button issues
 */
export const fixRouting = () => {
  // Log the current URL for debugging
  console.log('Current URL before fix:', window.location.href);

  // Check if we're on a route that needs fixing
  const pathname = window.location.pathname;

  // If we're on a route that should be handled by the SPA router
  if (pathname.startsWith('/profile/') ||
      pathname.startsWith('/retro-profile/') ||
      pathname.startsWith('/admin/') ||
      pathname.startsWith('/project/wizard') ||
      pathname.startsWith('/project/create') ||
      pathname.startsWith('/contribution/') ||
      pathname.startsWith('/validation/') ||
      pathname.startsWith('/analytics/') ||
      pathname.startsWith('/project/') && (pathname.includes('/contributions') || pathname.includes('/revenue') || pathname.includes('/royalty-calculator')) ||
      ['/profile', '/retro-profile', '/settings', '/start', '/track', '/earn', '/learn'].includes(pathname)) {

    // Log that we're fixing the route
    console.log('Fixing route:', pathname);

    // Use history API to ensure the route is properly handled
    if (window.history && window.history.replaceState) {
      // Keep the current URL but update the history state
      window.history.replaceState(
        { path: pathname },
        document.title,
        pathname
      );

      // Log the fixed URL
      console.log('URL after fix:', window.location.href);
    }
  }
};

/**
 * Handles navigation to ensure it works properly
 * @param {string} path - The path to navigate to
 */
export const navigateTo = (path) => {
  // Log the navigation
  console.log('Navigating to:', path);

  // Use history API to navigate
  if (window.history && window.history.pushState) {
    window.history.pushState(
      { path },
      document.title,
      path
    );

    // Dispatch a popstate event to trigger the router
    window.dispatchEvent(new PopStateEvent('popstate', { state: { path } }));
  } else {
    // Fallback for older browsers
    window.location.href = path;
  }
};

/**
 * Handles back button navigation
 */
export const goBack = () => {
  console.log('Going back');
  window.history.back();
};

/**
 * Handles forward button navigation
 */
export const goForward = () => {
  console.log('Going forward');
  window.history.forward();
};

export default {
  fixRouting,
  navigateTo,
  goBack,
  goForward
};
