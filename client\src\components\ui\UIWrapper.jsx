import React from 'react';
import { useFeatureFlags } from '../../contexts/feature-flags.context';

/**
 * UIWrapper Component
 * 
 * A wrapper component that conditionally renders either the new UI component
 * or the legacy component based on feature flags.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.legacy - The legacy component to render if feature flag is disabled
 * @param {React.ReactNode} props.modern - The modern component to render if feature flag is enabled
 * @param {string} props.featureFlag - The name of the feature flag to check (defaults to 'new-ui')
 * @param {Object} props.legacyProps - Props to pass to the legacy component
 * @param {Object} props.modernProps - Props to pass to the modern component
 * @returns {React.ReactNode} - Either the legacy or modern component
 */
const UIWrapper = ({
  legacy,
  modern,
  featureFlag = 'new-ui',
  legacyProps = {},
  modernProps = {},
  ...commonProps
}) => {
  const { isFeatureEnabled } = useFeatureFlags();
  
  // Check if the feature flag is enabled
  const useModernUI = isFeatureEnabled(featureFlag);
  
  // Render the appropriate component
  if (useModernUI && modern) {
    // Clone the modern component with combined props
    return React.cloneElement(modern, { ...commonProps, ...modernProps });
  }
  
  // Clone the legacy component with combined props
  return React.cloneElement(legacy, { ...commonProps, ...legacyProps });
};

export default UIWrapper;
