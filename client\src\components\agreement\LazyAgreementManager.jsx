import React, { lazy } from 'react';
import SafeLazyComponentWrapper from '../common/LazyComponentWrapper';

/**
 * Lazy-loaded Agreement Manager Component
 * 
 * This component lazy-loads the large AgreementManager component to improve
 * initial bundle size and page load performance.
 * 
 * Task: O3 Performance Optimization - Component Lazy Loading
 */

// Lazy load the AgreementManager component
const AgreementManager = lazy(() => import('./AgreementManager'));

const LazyAgreementManager = (props) => {
  return (
    <SafeLazyComponentWrapper
      className="w-full"
      minHeight="500px"
      showSpinner={true}
    >
      <AgreementManager {...props} />
    </SafeLazyComponentWrapper>
  );
};

export default LazyAgreementManager;
