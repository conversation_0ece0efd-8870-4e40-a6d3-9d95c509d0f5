// Contribution validation handler
import { supabase } from './supabase/supabase.utils';
import { logProjectActivity } from '../utils/activity-logger';

/**
 * Validate a contribution
 * @param {string} contributionId - The ID of the contribution to validate
 * @param {string} status - The validation status (approved, rejected, pending_changes)
 * @param {string} feedback - Optional feedback message
 * @returns {Promise} - The validation result
 */
export async function validateContribution(contributionId, status, feedback = '') {
  try {
    // First check if the contribution exists
    const { data: contribution, error: contributionError } = await supabase
      .from('contributions')
      .select('id, project_id, user_id')
      .eq('id', contributionId)
      .single();

    if (contributionError) {
      throw new Error(`Contribution not found: ${contributionError.message}`);
    }

    // Check if the user is a project admin
    const { data: user } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const { data: projectContributor, error: projectContributorError } = await supabase
      .from('project_contributors')
      .select('permission_level, is_admin')
      .eq('project_id', contribution.project_id)
      .eq('user_id', user.user.id)
      .single();

    if (projectContributorError) {
      throw new Error(`Not a project contributor: ${projectContributorError.message}`);
    }

    // Check if user is an admin or has owner/admin permission level
    const isAdmin = projectContributor.is_admin ||
                   ['Owner', 'Admin'].includes(projectContributor.permission_level);

    if (!isAdmin) {
      throw new Error('Only project admins can validate contributions');
    }

    // Update the contribution status directly
    const { data: updateData, error: updateError } = await supabase
      .from('contributions')
      .update({
        validation_status: status
      })
      .eq('id', contributionId);

    if (updateError) {
      throw new Error(`Failed to update contribution: ${updateError.message}`);
    }

    // Store the validation record in a separate table if it exists
    try {
      await supabase
        .from('contribution_validations')
        .insert({
          contribution_id: contributionId,
          validator_id: user.user.id,
          status,
          feedback
        });
    } catch (error) {
      console.warn('Could not store validation record, but contribution was updated:', error);
    }

    // Notifications are handled by database triggers

    // Log the validation activity
    try {
      await logProjectActivity(
        contribution.project_id,
        user.user.id,
        'contribution_validated',
        {
          contribution_id: contributionId,
          status,
          has_feedback: !!feedback.trim()
        }
      );
    } catch (logError) {
      console.warn('Failed to log validation activity:', logError);
    }

    return { success: true, message: `Contribution ${status}` };
  } catch (error) {
    console.error('Validation error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Validate multiple contributions at once
 * @param {Array} contributions - Array of contribution objects with id and feedback properties
 * @param {string} status - The validation status to set for all contributions
 * @param {string} projectId - The project ID that all contributions belong to
 * @returns {Promise} - The validation result
 */
export async function validateContributionsBulk(contributions, status, projectId) {
  try {
    if (!contributions || !Array.isArray(contributions) || contributions.length === 0) {
      throw new Error('No contributions provided for bulk validation');
    }

    // Check if the user is a project admin
    const { data: user } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Verify project admin status
    const { data: projectContributor, error: projectContributorError } = await supabase
      .from('project_contributors')
      .select('permission_level, is_admin')
      .eq('project_id', projectId)
      .eq('user_id', user.user.id)
      .single();

    if (projectContributorError) {
      throw new Error(`Not a project contributor: ${projectContributorError.message}`);
    }

    // Check if user is an admin or has owner/admin permission level
    const isAdmin = projectContributor.is_admin ||
                   ['Owner', 'Admin'].includes(projectContributor.permission_level);

    if (!isAdmin) {
      throw new Error('Only project admins can validate contributions');
    }

    // Get contribution IDs
    const contributionIds = contributions.map(c => c.id);

    // Update all contributions at once
    const { data: updateData, error: updateError } = await supabase
      .from('contributions')
      .update({
        validation_status: status
      })
      .in('id', contributionIds);

    if (updateError) {
      throw new Error(`Failed to update contributions: ${updateError.message}`);
    }

    // Store validation records for each contribution
    const validationRecords = contributions.map(contribution => {
      // Ensure feedback is provided for rejections and change requests
      let feedback = contribution.feedback || '';

      // If status requires feedback but none is provided, add a default message
      if ((status === 'rejected' || status === 'pending_changes') && !feedback.trim()) {
        feedback = status === 'rejected'
          ? 'Rejected in bulk validation.'
          : 'Changes requested in bulk validation.';
      }

      return {
        contribution_id: contribution.id,
        validator_id: user.user.id,
        status,
        feedback
      };
    });

    try {
      const { error: validationError } = await supabase
        .from('contribution_validations')
        .insert(validationRecords);

      if (validationError) {
        console.warn('Error storing validation records:', validationError);
      }
    } catch (error) {
      console.warn('Could not store validation records, but contributions were updated:', error);
    }

    // Notifications are handled by database triggers

    // Log the bulk validation activity
    try {
      await logProjectActivity(
        projectId,
        user.user.id,
        'bulk_contributions_validated',
        {
          count: contributions.length,
          status,
          contribution_ids: contributionIds
        }
      );
    } catch (logError) {
      console.warn('Failed to log bulk validation activity:', logError);
    }

    return {
      success: true,
      message: `${contributions.length} contributions ${status}`,
      count: contributions.length
    };
  } catch (error) {
    console.error('Bulk validation error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Get validations for a contribution
 * @param {string} contributionId - The ID of the contribution
 * @returns {Promise} - The validation history
 */
export async function getValidationHistory(contributionId) {
  try {
    // Try to get from the validations table first
    try {
      // First get the validation records
      const { data: validations, error: validationsError } = await supabase
        .from('contribution_validations')
        .select(`
          id,
          contribution_id,
          validator_id,
          status,
          feedback,
          created_at,
          updated_at
        `)
        .eq('contribution_id', contributionId)
        .order('created_at', { ascending: false });

      if (validationsError) {
        console.warn('Error fetching validations:', validationsError);
        throw validationsError;
      }

      // If we have validations, fetch the validator details separately
      if (validations && validations.length > 0) {
        // Get unique validator IDs
        const validatorIds = [...new Set(validations.map(v => v.validator_id))];

        // Fetch validator details
        const { data: validators, error: validatorsError } = await supabase
          .from('users')
          .select('id, email, display_name')
          .in('id', validatorIds);

        if (validatorsError) {
          console.warn('Error fetching validators:', validatorsError);
        }

        // Create a map of validator data
        const validatorMap = {};
        if (validators) {
          validators.forEach(user => {
            validatorMap[user.id] = user;
          });
        }

        // Combine the data
        const data = validations.map(validation => ({
          ...validation,
          validator: validatorMap[validation.validator_id] || null
        }));

        return { success: true, data };
      }
    } catch (error) {
      console.warn('Could not fetch from validation table:', error);
    }

    // Fall back to just getting the contribution status
    const { data: contribution, error: contributionError } = await supabase
      .from('contributions')
      .select('id, validation_status, updated_at')
      .eq('id', contributionId)
      .single();

    if (contributionError) {
      throw new Error(`Contribution not found: ${contributionError.message}`);
    }

    return {
      success: true,
      data: [{
        id: 'default',
        contribution_id: contributionId,
        status: contribution.validation_status || 'pending',
        created_at: contribution.updated_at,
        updated_at: contribution.updated_at
      }]
    };
  } catch (error) {
    console.error('Error fetching validation history:', error);
    return { success: false, error: error.message };
  }
}
