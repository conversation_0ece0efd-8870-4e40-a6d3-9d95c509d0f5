import React from 'react';

/**
 * AgreementButtons Component
 * 
 * A component for rendering the agreement action buttons with proper styling
 */
const AgreementButtons = ({ 
  onView, 
  onSign, 
  onRegenerate, 
  canSign = true, 
  regenerating = false 
}) => {
  return (
    <div className="agreement-buttons-container">
      <button
        className="agreement-button view-button"
        onClick={onView}
        type="button"
      >
        <i className="bi bi-file-earmark-text"></i>
        <span>View Agreement</span>
      </button>

      {canSign && (
        <button
          className="agreement-button sign-button"
          onClick={onSign}
          type="button"
        >
          <i className="bi bi-pen"></i>
          <span>Sign Agreement</span>
        </button>
      )}

      <button
        className="agreement-button regenerate-button"
        onClick={onRegenerate}
        disabled={regenerating}
        type="button"
      >
        <i className="bi bi-arrow-repeat"></i>
        <span>{regenerating ? 'Regenerating...' : 'Regenerate Agreement'}</span>
      </button>
    </div>
  );
};

export default AgreementButtons;
