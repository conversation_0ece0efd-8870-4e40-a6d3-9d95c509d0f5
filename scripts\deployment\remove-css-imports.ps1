# PowerShell script to remove all broken CSS imports from JSX files
Write-Host "=== Removing Broken CSS Imports ===" -ForegroundColor Cyan

# Get all JSX files with CSS imports
$jsxFiles = Get-ChildItem -Path "client\src" -Recurse -Include "*.jsx" | Where-Object {
    (Get-Content $_.FullName -Raw) -match "import.*\.css"
}

Write-Host "Found $($jsxFiles.Count) JSX files with CSS imports" -ForegroundColor Yellow

foreach ($file in $jsxFiles) {
    Write-Host "Processing: $($file.FullName)" -ForegroundColor Green

    # Read the file content
    $content = Get-Content $file.FullName -Raw

    # Remove CSS import lines (both single and double quotes)
    $updatedContent = $content -replace "import\s+['\`"].*\.css['\`"];\r?\n?", ""

    # Remove any extra blank lines that might be left
    $updatedContent = $updatedContent -replace "\n\n\n+", "`n`n"

    # Write the updated content back to the file
    Set-Content -Path $file.FullName -Value $updatedContent -NoNewline

    Write-Host "  ✅ Removed CSS imports from $($file.Name)" -ForegroundColor Green
}

Write-Host "=== CSS Import Cleanup Complete ===" -ForegroundColor Cyan
Write-Host "Processed $($jsxFiles.Count) files" -ForegroundColor Yellow
