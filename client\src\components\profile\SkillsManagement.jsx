import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Badge, Chip, Progress, Autocomplete, AutocompleteItem } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Skills Management Component
 * 
 * Comprehensive skills management interface providing:
 * - Skills addition and removal
 * - Skill level assessment and tracking
 * - Endorsements and validations
 * - Skills categorization and organization
 * - Progress tracking and analytics
 * - Skills marketplace integration
 */
const SkillsManagement = ({ userId, isOwnProfile = false }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [skills, setSkills] = useState([]);
  const [skillCategories, setSkillCategories] = useState([]);
  const [availableSkills, setAvailableSkills] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [newSkill, setNewSkill] = useState({
    skill_name: '',
    skill_level: 'beginner',
    category: '',
    years_experience: 0,
    is_primary: false
  });

  // Skill levels
  const skillLevels = [
    { key: 'beginner', label: 'Beginner', description: '0-1 years experience' },
    { key: 'intermediate', label: 'Intermediate', description: '1-3 years experience' },
    { key: 'advanced', label: 'Advanced', description: '3-5 years experience' },
    { key: 'expert', label: 'Expert', description: '5+ years experience' }
  ];

  // Skill categories
  const categories = [
    { key: 'programming', label: 'Programming Languages', icon: '💻' },
    { key: 'frameworks', label: 'Frameworks & Libraries', icon: '🔧' },
    { key: 'databases', label: 'Databases', icon: '🗄️' },
    { key: 'design', label: 'Design & UI/UX', icon: '🎨' },
    { key: 'marketing', label: 'Marketing & Sales', icon: '📈' },
    { key: 'business', label: 'Business & Strategy', icon: '💼' },
    { key: 'communication', label: 'Communication', icon: '💬' },
    { key: 'project-management', label: 'Project Management', icon: '📋' },
    { key: 'other', label: 'Other', icon: '🔗' }
  ];

  // Load skills data
  useEffect(() => {
    if (userId) {
      loadSkillsData();
    }
  }, [userId]);

  const loadSkillsData = async () => {
    try {
      setLoading(true);
      
      // Load user skills
      const { data: userSkills, error: skillsError } = await supabase
        .from('user_skills')
        .select('*')
        .eq('user_id', userId)
        .order('is_primary', { ascending: false })
        .order('skill_level', { ascending: false });

      if (skillsError) throw skillsError;

      // Load available skills for autocomplete
      const { data: availableSkillsData, error: availableError } = await supabase
        .from('skills_database')
        .select('skill_name, category')
        .order('skill_name');

      if (availableError) console.error('Error loading available skills:', availableError);

      // Group skills by category
      const categorizedSkills = categories.map(category => ({
        ...category,
        skills: (userSkills || []).filter(skill => skill.category === category.key)
      }));

      setSkills(userSkills || []);
      setSkillCategories(categorizedSkills);
      setAvailableSkills(availableSkillsData || []);
    } catch (error) {
      console.error('Error loading skills data:', error);
      toast.error('Failed to load skills data');
    } finally {
      setLoading(false);
    }
  };

  // Add new skill
  const handleAddSkill = async () => {
    if (!newSkill.skill_name.trim()) {
      toast.error('Please enter a skill name');
      return;
    }

    try {
      const skillData = {
        user_id: userId,
        skill_name: newSkill.skill_name.trim(),
        skill_level: newSkill.skill_level,
        category: newSkill.category || 'other',
        years_experience: parseInt(newSkill.years_experience) || 0,
        is_primary: newSkill.is_primary,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('user_skills')
        .insert(skillData)
        .select()
        .single();

      if (error) throw error;

      setSkills(prev => [...prev, data]);
      setShowAddModal(false);
      setNewSkill({
        skill_name: '',
        skill_level: 'beginner',
        category: '',
        years_experience: 0,
        is_primary: false
      });

      toast.success('Skill added successfully');
      loadSkillsData(); // Reload to update categories
    } catch (error) {
      console.error('Error adding skill:', error);
      toast.error('Failed to add skill');
    }
  };

  // Remove skill
  const handleRemoveSkill = async (skillId) => {
    try {
      const { error } = await supabase
        .from('user_skills')
        .delete()
        .eq('id', skillId);

      if (error) throw error;

      setSkills(prev => prev.filter(skill => skill.id !== skillId));
      toast.success('Skill removed successfully');
      loadSkillsData(); // Reload to update categories
    } catch (error) {
      console.error('Error removing skill:', error);
      toast.error('Failed to remove skill');
    }
  };

  // Update skill level
  const handleUpdateSkillLevel = async (skillId, newLevel) => {
    try {
      const { error } = await supabase
        .from('user_skills')
        .update({ skill_level: newLevel })
        .eq('id', skillId);

      if (error) throw error;

      setSkills(prev => prev.map(skill => 
        skill.id === skillId ? { ...skill, skill_level: newLevel } : skill
      ));

      toast.success('Skill level updated');
    } catch (error) {
      console.error('Error updating skill level:', error);
      toast.error('Failed to update skill level');
    }
  };

  // Get skill level color
  const getSkillLevelColor = (level) => {
    switch (level) {
      case 'expert': return 'danger';
      case 'advanced': return 'warning';
      case 'intermediate': return 'primary';
      case 'beginner': return 'default';
      default: return 'default';
    }
  };

  // Get skill level progress
  const getSkillLevelProgress = (level) => {
    switch (level) {
      case 'expert': return 100;
      case 'advanced': return 75;
      case 'intermediate': return 50;
      case 'beginner': return 25;
      default: return 0;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="skills-management space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div>
              <h2 className="text-xl font-bold">Skills & Expertise</h2>
              <p className="text-sm text-default-600">
                Showcase your professional skills and experience levels
              </p>
            </div>
            
            {isOwnProfile && (
              <Button
                color="primary"
                onPress={() => setShowAddModal(true)}
                startContent={<span>➕</span>}
              >
                Add Skill
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Skills Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-primary mb-1">
                {skills.length}
              </div>
              <div className="text-sm text-default-600">Total Skills</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-success mb-1">
                {skills.filter(s => s.skill_level === 'expert' || s.skill_level === 'advanced').length}
              </div>
              <div className="text-sm text-default-600">Advanced Skills</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-warning mb-1">
                {skills.filter(s => s.is_primary).length}
              </div>
              <div className="text-sm text-default-600">Primary Skills</div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Skills by Category */}
      <div className="space-y-4">
        {skillCategories.map((category, categoryIndex) => (
          category.skills.length > 0 && (
            <motion.div
              key={category.key}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: categoryIndex * 0.1 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    <span className="text-xl">{category.icon}</span>
                    <h3 className="text-lg font-semibold">{category.label}</h3>
                    <Badge color="primary" variant="flat" size="sm">
                      {category.skills.length}
                    </Badge>
                  </div>
                </CardHeader>
                <CardBody>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {category.skills.map((skill, skillIndex) => (
                      <motion.div
                        key={skill.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: skillIndex * 0.05 }}
                        className="border rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">{skill.skill_name}</h4>
                            {skill.is_primary && (
                              <Chip color="warning" size="sm" variant="flat">⭐</Chip>
                            )}
                          </div>
                          
                          {isOwnProfile && (
                            <Button
                              size="sm"
                              color="danger"
                              variant="light"
                              onClick={() => handleRemoveSkill(skill.id)}
                            >
                              ✕
                            </Button>
                          )}
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Badge
                              color={getSkillLevelColor(skill.skill_level)}
                              variant="flat"
                              size="sm"
                            >
                              {skill.skill_level}
                            </Badge>
                            {skill.years_experience > 0 && (
                              <span className="text-sm text-default-600">
                                {skill.years_experience}y exp
                              </span>
                            )}
                          </div>
                          
                          <Progress
                            value={getSkillLevelProgress(skill.skill_level)}
                            color={getSkillLevelColor(skill.skill_level)}
                            size="sm"
                          />
                          
                          {isOwnProfile && (
                            <Select
                              size="sm"
                              selectedKeys={[skill.skill_level]}
                              onSelectionChange={(keys) => 
                                handleUpdateSkillLevel(skill.id, Array.from(keys)[0])
                              }
                              className="mt-2"
                            >
                              {skillLevels.map(level => (
                                <SelectItem key={level.key} description={level.description}>
                                  {level.label}
                                </SelectItem>
                              ))}
                            </Select>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          )
        ))}
      </div>

      {/* Empty State */}
      {skills.length === 0 && (
        <Card>
          <CardBody className="text-center py-8">
            <span className="text-4xl mb-4 block">🛠️</span>
            <h3 className="text-lg font-semibold mb-2">No Skills Added Yet</h3>
            <p className="text-default-600 mb-4">
              Start building your professional profile by adding your skills and expertise
            </p>
            {isOwnProfile && (
              <Button
                color="primary"
                onPress={() => setShowAddModal(true)}
              >
                Add Your First Skill
              </Button>
            )}
          </CardBody>
        </Card>
      )}

      {/* Add Skill Modal */}
      <Modal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        size="2xl"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">🛠️</span>
              <span>Add New Skill</span>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Autocomplete
                label="Skill Name"
                placeholder="Search or enter skill name"
                value={newSkill.skill_name}
                onInputChange={(value) => setNewSkill(prev => ({ ...prev, skill_name: value }))}
              >
                {availableSkills.map((skill, index) => (
                  <AutocompleteItem key={index} value={skill.skill_name}>
                    {skill.skill_name}
                  </AutocompleteItem>
                ))}
              </Autocomplete>
              
              <div className="grid grid-cols-2 gap-4">
                <Select
                  label="Skill Level"
                  selectedKeys={[newSkill.skill_level]}
                  onSelectionChange={(keys) => setNewSkill(prev => ({
                    ...prev,
                    skill_level: Array.from(keys)[0]
                  }))}
                >
                  {skillLevels.map(level => (
                    <SelectItem key={level.key} description={level.description}>
                      {level.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  label="Category"
                  selectedKeys={[newSkill.category]}
                  onSelectionChange={(keys) => setNewSkill(prev => ({
                    ...prev,
                    category: Array.from(keys)[0]
                  }))}
                >
                  {categories.map(category => (
                    <SelectItem key={category.key} startContent={category.icon}>
                      {category.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
              
              <Input
                type="number"
                label="Years of Experience"
                placeholder="0"
                value={newSkill.years_experience.toString()}
                onChange={(e) => setNewSkill(prev => ({
                  ...prev,
                  years_experience: parseInt(e.target.value) || 0
                }))}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowAddModal(false)}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleAddSkill}
              isDisabled={!newSkill.skill_name.trim()}
            >
              Add Skill
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default SkillsManagement;
