import React, { useState, useEffect } from 'react';
import { getProfileThemes } from '../../utils/profile/profile.utils';
import { getPresetThemeSettings, generateColorPalettePreview } from '../../utils/profile/theme.utils';
import { Card, CardBody, CardHeader, Button } from '../ui/heroui';

const ThemeCustomizer = ({
  show,
  onHide,
  currentTheme,
  onSaveTheme,
  isPremium = false
}) => {
  const [themes, setThemes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('preset');
  const [selectedTheme, setSelectedTheme] = useState('default');
  const [customTheme, setCustomTheme] = useState({
    colors: {
      background: '#f8fafc',
      primary: '#1c1e21',
      secondary: '#f0f2f5',
      text: '#1c1e21',
      accent: '#6b7280',
      links: '#1c1e21',
      borders: '#e2e8f0'
    },
    fonts: {
      heading: 'Inter',
      body: 'Inter'
    },
    layout: 'standard'
  });
  const [customCss, setCustomCss] = useState('');

  // Load available themes
  useEffect(() => {
    const fetchThemes = async () => {
      try {
        setLoading(true);
        const data = await getProfileThemes(isPremium);
        setThemes(data);
      } catch (error) {
        console.error('Error fetching themes:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchThemes();
  }, [isPremium]);

  // Initialize with current theme
  useEffect(() => {
    if (currentTheme) {
      if (currentTheme.theme && currentTheme.theme !== 'custom') {
        setSelectedTheme(currentTheme.theme);
        setActiveTab('preset');
      } else {
        setCustomTheme({
          colors: { ...currentTheme.colors },
          fonts: { ...currentTheme.fonts },
          layout: currentTheme.layout || 'standard'
        });
        setCustomCss(currentTheme.customCss || '');
        setActiveTab('custom');
      }
    }
  }, [currentTheme]);

  // Handle preset theme selection
  const handlePresetThemeSelect = (themeName) => {
    setSelectedTheme(themeName);
  };

  // Handle color change
  const handleColorChange = (colorName, value) => {
    setCustomTheme({
      ...customTheme,
      colors: {
        ...customTheme.colors,
        [colorName]: value
      }
    });
  };

  // Handle font change
  const handleFontChange = (fontType, value) => {
    setCustomTheme({
      ...customTheme,
      fonts: {
        ...customTheme.fonts,
        [fontType]: value
      }
    });
  };

  // Handle layout change
  const handleLayoutChange = (layout) => {
    setCustomTheme({
      ...customTheme,
      layout
    });
  };

  // Handle save
  const handleSave = () => {
    if (activeTab === 'preset') {
      // Get the preset theme settings
      const themeSettings = getPresetThemeSettings(selectedTheme);
      onSaveTheme({
        ...themeSettings,
        customCss: ''
      });
    } else {
      // Save custom theme
      onSaveTheme({
        theme: 'custom',
        colors: customTheme.colors,
        fonts: customTheme.fonts,
        layout: customTheme.layout,
        customCss
      });
    }

    onHide();
  };

  // Available fonts
  const availableFonts = [
    'Inter',
    'Roboto',
    'Open Sans',
    'Montserrat',
    'Lato',
    'Poppins',
    'Raleway',
    'Oswald',
    'Orbitron',
    'Playfair Display',
    'Arial',
    'Verdana',
    'Georgia',
    'Times New Roman'
  ];

  // Available layouts
  const availableLayouts = [
    { value: 'standard', label: 'Standard Layout' },
    { value: 'projects-first', label: 'Projects First' },
    { value: 'skills-first', label: 'Skills First' }
  ];

  // Generate color palette preview
  const { html: colorPaletteHtml, css: colorPaletteCss } = generateColorPalettePreview(
    activeTab === 'preset'
      ? getPresetThemeSettings(selectedTheme).colors
      : customTheme.colors
  );

  return (
    <Modal show={show} onHide={onHide} size="lg" centered className="theme-customizer-modal">
      <Modal.Header closeButton>
        <Modal.Title>Customize Your Profile Theme</Modal.Title>
      </Modal.Header>

      <Modal.Body>
        <Tabs
          activeKey={activeTab}
          onSelect={(key) => setActiveTab(key)}
          className="mb-4"
        >
          <Tab eventKey="preset" title="Preset Themes">
            {loading ? (
              <div className="loading-themes">Loading themes...</div>
            ) : (
              <div className="preset-themes-grid">
                {themes.map((theme) => (
                  <div
                    key={theme.id}
                    className={`theme-card ${selectedTheme === theme.name ? 'selected' : ''} ${theme.is_premium && !isPremium ? 'premium-locked' : ''}`}
                    onClick={() => {
                      if (!theme.is_premium || isPremium) {
                        handlePresetThemeSelect(theme.name);
                      }
                    }}
                  >
                    <div
                      className="theme-thumbnail"
                      style={{ backgroundImage: `url(${theme.thumbnail_url || '/themes/default.jpg'})` }}
                    >
                      {theme.is_premium && !isPremium && (
                        <div className="premium-overlay">
                          <i className="bi bi-lock-fill"></i>
                          <span>Premium</span>
                        </div>
                      )}
                    </div>
                    <div className="theme-info">
                      <h4>{theme.name}</h4>
                      <p>{theme.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Tab>

          <Tab eventKey="custom" title="Custom Theme">
            <div className="custom-theme-container">
              <div className="theme-info-section">
                <h4>Theme Customization</h4>
                <div className="info-content">
                  <p><strong>Theme customization is disabled</strong> to showcase HeroUI's beautiful default styling.</p>
                  <p>HeroUI provides modern, accessible, and professionally designed themes that look amazing out of the box.</p>
                  <p>The current interface demonstrates HeroUI's clean, modern aesthetic without any custom color overrides.</p>
                </div>
              </div>
            </div>
          </Tab>
        </Tabs>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button variant="primary" onClick={handleSave}>
          Save Theme
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ThemeCustomizer;
