import React, { useState, useEffect, useContext } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import { formatCurrency } from '../../utils/royalty/royalty-calculator';
import DatePicker from 'react-datepicker';

const ESCROW_REASONS = [
  'Tranche requirements not met',
  'No eligible contributors',
  'Pending contributor validation',
  'Legal review required',
  'Dispute resolution',
  'Milestone completion pending',
  'Contract requirements',
  'Other'
];

const EscrowManager = ({ projectId, onEscrowUpdate }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [escrowRecords, setEscrowRecords] = useState([]);
  const [revenueEntries, setRevenueEntries] = useState([]);
  const [showAddEscrow, setShowAddEscrow] = useState(false);
  const [selectedRevenue, setSelectedRevenue] = useState(null);
  const [escrowReason, setEscrowReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [releaseCondition, setReleaseCondition] = useState('');
  const [releaseDate, setReleaseDate] = useState(null);
  const [notes, setNotes] = useState('');
  const [saving, setSaving] = useState(false);
  const [expandedEscrowId, setExpandedEscrowId] = useState(null);

  // Fetch escrow records and available revenue entries
  useEffect(() => {
    const fetchData = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch escrow records
        const { data: escrowData, error: escrowError } = await supabase
          .from('revenue_escrow')
          .select('*, revenue:revenue_entries(*)')
          .eq('project_id', projectId)
          .order('created_at', { ascending: false });

        if (escrowError) throw escrowError;

        setEscrowRecords(escrowData || []);

        // Fetch revenue entries that are not in escrow
        const { data: revenueData, error: revenueError } = await supabase
          .from('revenue_entries')
          .select('*')
          .eq('project_id', projectId)
          .eq('in_escrow', false)
          .eq('status', 'pending')
          .order('date_received', { ascending: false });

        if (revenueError) throw revenueError;

        setRevenueEntries(revenueData || []);
      } catch (error) {
        console.error('Error fetching escrow data:', error);
        toast.error('Failed to load escrow data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle adding revenue to escrow
  const handleAddToEscrow = async () => {
    if (!selectedRevenue) {
      toast.error('Please select a revenue entry');
      return;
    }

    if (!escrowReason && !customReason) {
      toast.error('Please provide a reason for escrow');
      return;
    }

    const reason = escrowReason === 'Other' ? customReason : escrowReason;

    try {
      setSaving(true);

      // Update revenue entry
      const { error: updateError } = await supabase
        .from('revenue_entries')
        .update({
          in_escrow: true,
          escrow_reason: reason,
          escrow_release_condition: releaseCondition,
          escrow_release_date: releaseDate ? releaseDate.toISOString().split('T')[0] : null,
          escrow_notes: notes
        })
        .eq('id', selectedRevenue.id);

      if (updateError) throw updateError;

      // Create escrow record
      const { data: escrowData, error: escrowError } = await supabase
        .from('revenue_escrow')
        .insert({
          revenue_id: selectedRevenue.id,
          project_id: projectId,
          amount: selectedRevenue.amount,
          currency: selectedRevenue.currency,
          escrow_date: new Date().toISOString().split('T')[0],
          release_date: releaseDate ? releaseDate.toISOString().split('T')[0] : null,
          status: 'active',
          reason: reason,
          release_condition: releaseCondition,
          created_by: currentUser.id,
          notes: notes
        })
        .select()
        .single();

      if (escrowError) throw escrowError;

      toast.success('Revenue added to escrow successfully');

      // Reset form
      setSelectedRevenue(null);
      setEscrowReason('');
      setCustomReason('');
      setReleaseCondition('');
      setReleaseDate(null);
      setNotes('');
      setShowAddEscrow(false);

      // Update local state
      setEscrowRecords([{ ...escrowData, revenue: selectedRevenue }, ...escrowRecords]);
      setRevenueEntries(revenueEntries.filter(r => r.id !== selectedRevenue.id));

      // Call callback if provided
      if (onEscrowUpdate) {
        onEscrowUpdate();
      }
    } catch (error) {
      console.error('Error adding to escrow:', error);
      toast.error('Failed to add revenue to escrow');
    } finally {
      setSaving(false);
    }
  };

  // Handle releasing from escrow
  const handleReleaseFromEscrow = async (escrowId, revenueId) => {
    try {
      setSaving(true);

      // Update escrow record
      const { error: escrowError } = await supabase
        .from('revenue_escrow')
        .update({
          status: 'released',
          release_date: new Date().toISOString().split('T')[0],
          released_by: currentUser.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', escrowId);

      if (escrowError) throw escrowError;

      // Update revenue entry
      const { error: updateError } = await supabase
        .from('revenue_entries')
        .update({
          in_escrow: false,
          status: 'approved' // Set to approved so it can be distributed
        })
        .eq('id', revenueId);

      if (updateError) throw updateError;

      toast.success('Revenue released from escrow');

      // Update local state
      setEscrowRecords(escrowRecords.map(record =>
        record.id === escrowId
          ? {
              ...record,
              status: 'released',
              release_date: new Date().toISOString().split('T')[0],
              released_by: currentUser.id
            }
          : record
      ));

      // Call callback if provided
      if (onEscrowUpdate) {
        onEscrowUpdate();
      }

      // Ask if user wants to distribute the revenue now
      const shouldDistribute = window.confirm('Would you like to distribute this revenue now?');

      if (shouldDistribute) {
        // Navigate to the revenue distribution page
        window.location.href = `/project/${projectId}/revenue?highlight=${revenueId}`;
      }
    } catch (error) {
      console.error('Error releasing from escrow:', error);
      toast.error('Failed to release from escrow');
    } finally {
      setSaving(false);
    }
  };

  // Toggle expanded escrow record
  const toggleExpanded = (escrowId) => {
    setExpandedEscrowId(expandedEscrowId === escrowId ? null : escrowId);
  };

  if (loading) {
    return <div className="loading-spinner">Loading escrow data...</div>;
  }

  return (
    <div className="escrow-manager">
      <div className="escrow-manager-header">
        <h3>Revenue Escrow Manager</h3>

        <button
          className="add-escrow-button"
          onClick={() => setShowAddEscrow(!showAddEscrow)}
        >
          {showAddEscrow ? 'Cancel' : 'Add Revenue to Escrow'}
        </button>
      </div>

      {showAddEscrow && (
        <div className="add-escrow-form">
          <h4>Add Revenue to Escrow</h4>

          <div className="form-group">
            <label htmlFor="revenue-select">Select Revenue Entry:</label>
            <select
              id="revenue-select"
              value={selectedRevenue ? selectedRevenue.id : ''}
              onChange={(e) => {
                const selected = revenueEntries.find(r => r.id === e.target.value);
                setSelectedRevenue(selected || null);
              }}
              className="form-control"
              required
            >
              <option value="">Select Revenue Entry</option>
              {revenueEntries.map(revenue => (
                <option key={revenue.id} value={revenue.id}>
                  {formatCurrency(revenue.amount, revenue.currency)} - {formatDate(revenue.date_received)} - {revenue.description || 'No description'}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="escrow-reason">Reason for Escrow:</label>
            <select
              id="escrow-reason"
              value={escrowReason}
              onChange={(e) => setEscrowReason(e.target.value)}
              className="form-control"
              required
            >
              <option value="">Select Reason</option>
              {ESCROW_REASONS.map(reason => (
                <option key={reason} value={reason}>{reason}</option>
              ))}
            </select>
          </div>

          {escrowReason === 'Other' && (
            <div className="form-group">
              <label htmlFor="custom-reason">Custom Reason:</label>
              <input
                type="text"
                id="custom-reason"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="form-control"
                placeholder="Enter custom reason"
                required
              />
            </div>
          )}

          <div className="form-group">
            <label htmlFor="release-condition">Release Condition:</label>
            <textarea
              id="release-condition"
              value={releaseCondition}
              onChange={(e) => setReleaseCondition(e.target.value)}
              className="form-control"
              rows="2"
              placeholder="Conditions that must be met for release"
            />
          </div>

          <div className="form-group">
            <label htmlFor="release-date">Expected Release Date (optional):</label>
            <DatePicker
              id="release-date"
              selected={releaseDate}
              onChange={setReleaseDate}
              className="form-control"
              dateFormat="MMMM d, yyyy"
              minDate={new Date()}
              placeholderText="Select expected release date"
            />
          </div>

          <div className="form-group">
            <label htmlFor="escrow-notes">Notes:</label>
            <textarea
              id="escrow-notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="form-control"
              rows="3"
              placeholder="Additional notes about this escrow"
            />
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="cancel-button"
              onClick={() => setShowAddEscrow(false)}
            >
              Cancel
            </button>

            <button
              type="button"
              className="add-button"
              onClick={handleAddToEscrow}
              disabled={saving || !selectedRevenue || (!escrowReason && !customReason)}
            >
              {saving ? 'Adding...' : 'Add to Escrow'}
            </button>
          </div>
        </div>
      )}

      <div className="escrow-records">
        <h4>Escrow Records</h4>

        {escrowRecords.length === 0 ? (
          <div className="no-records">
            <p>No revenue is currently held in escrow.</p>
          </div>
        ) : (
          <div className="escrow-list">
            {escrowRecords.map(record => (
              <div
                key={record.id}
                className={`escrow-record ${record.status} ${expandedEscrowId === record.id ? 'expanded' : ''}`}
              >
                <div
                  className="escrow-record-header"
                  onClick={() => toggleExpanded(record.id)}
                >
                  <div className="escrow-info">
                    <div className="escrow-amount">
                      {formatCurrency(record.amount, record.currency)}
                    </div>
                    <div className="escrow-dates">
                      <span className="escrow-date">Escrowed: {formatDate(record.escrow_date)}</span>
                      {record.release_date && (
                        <span className="release-date">Expected Release: {formatDate(record.release_date)}</span>
                      )}
                    </div>
                    <div className="escrow-reason-short">
                      {record.reason}
                    </div>
                    <div className="escrow-status">
                      <span className={`status-badge ${record.status}`}>
                        {record.status}
                      </span>
                    </div>
                  </div>
                  <div className="expand-toggle">
                    <i className={`bi ${expandedEscrowId === record.id ? 'bi-chevron-up' : 'bi-chevron-down'}`}></i>
                  </div>
                </div>

                {expandedEscrowId === record.id && (
                  <div className="escrow-record-details">
                    <div className="escrow-detail-row">
                      <div className="detail-label">Revenue Source:</div>
                      <div className="detail-value">
                        {record.revenue?.source_id || 'Unknown'} - {formatDate(record.revenue?.date_received)}
                      </div>
                    </div>

                    <div className="escrow-detail-row">
                      <div className="detail-label">Reason:</div>
                      <div className="detail-value">{record.reason}</div>
                    </div>

                    {record.release_condition && (
                      <div className="escrow-detail-row">
                        <div className="detail-label">Release Condition:</div>
                        <div className="detail-value">{record.release_condition}</div>
                      </div>
                    )}

                    {record.notes && (
                      <div className="escrow-detail-row">
                        <div className="detail-label">Notes:</div>
                        <div className="detail-value">{record.notes}</div>
                      </div>
                    )}

                    {record.status === 'active' && (
                      <div className="escrow-actions">
                        <button
                          className="release-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleReleaseFromEscrow(record.id, record.revenue_id);
                          }}
                          disabled={saving}
                        >
                          {saving ? 'Releasing...' : 'Release from Escrow'}
                        </button>
                      </div>
                    )}

                    {record.status === 'released' && (
                      <div className="escrow-detail-row">
                        <div className="detail-label">Released Date:</div>
                        <div className="detail-value">{formatDate(record.release_date)}</div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default EscrowManager;
