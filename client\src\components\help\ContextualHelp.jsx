import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Tooltip, Modal, Modal<PERSON>ontent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';

/**
 * Contextual Help Component
 * 
 * Provides contextual help tooltips and guided tours for complex features.
 * Adapts help content based on current location and user progress.
 */

const ContextualHelp = ({ 
  context = 'general', 
  showTour = false, 
  onTourComplete,
  className = "" 
}) => {
  const [currentTourStep, setCurrentTourStep] = useState(0);
  const [isTourActive, setIsTourActive] = useState(showTour);
  const [showHelpModal, setShowHelpModal] = useState(false);

  // Help content based on context
  const getHelpContent = (context) => {
    const helpData = {
      general: {
        title: 'Getting Started',
        description: 'Welcome to Royaltea! Here\'s how to get started with tracking your work and earning royalties.',
        tips: [
          'Use the Start journey to create your first project',
          'Track your contributions in the Track section',
          'Monitor your earnings in the Earn section',
          'Use keyboard shortcuts for faster navigation'
        ]
      },
      track: {
        title: 'Track Your Work',
        description: 'Learn how to effectively track your contributions and manage your work.',
        tips: [
          'Use the time tracker for accurate hour logging',
          'Submit contributions with detailed descriptions',
          'Set appropriate difficulty ratings for fair royalty calculations',
          'Review your progress regularly to stay motivated'
        ]
      },
      earn: {
        title: 'Understanding Earnings',
        description: 'Learn how royalties work and how to maximize your earnings.',
        tips: [
          'Earnings are calculated based on contribution hours and difficulty',
          'Funds are held in escrow until project milestones are met',
          'Higher difficulty contributions earn more royalties',
          'Consistent contributions build long-term value'
        ]
      },
      project: {
        title: 'Project Management',
        description: 'Manage your projects effectively with these features.',
        tips: [
          'Use milestones to track project progress',
          'Drag and drop tasks to update their status',
          'Invite collaborators to work together',
          'Monitor project analytics for insights'
        ]
      }
    };

    return helpData[context] || helpData.general;
  };

  // Tour steps based on context
  const getTourSteps = (context) => {
    const tourSteps = {
      track: [
        {
          target: '[data-tour="time-tracker"]',
          title: 'Time Tracker',
          content: 'Start here to track time as you work. Click the play button to begin timing your session.',
          position: 'bottom'
        },
        {
          target: '[data-tour="submit-form"]',
          title: 'Submit Contributions',
          content: 'Use this form to log completed work. Include detailed descriptions for better tracking.',
          position: 'left'
        },
        {
          target: '[data-tour="progress-view"]',
          title: 'Progress Overview',
          content: 'Monitor your contribution statistics and track your productivity over time.',
          position: 'top'
        },
        {
          target: '[data-tour="analytics"]',
          title: 'Analytics Dashboard',
          content: 'Analyze your work patterns and performance with interactive charts and insights.',
          position: 'top'
        }
      ],
      earn: [
        {
          target: '[data-tour="earnings-cards"]',
          title: 'Earnings Overview',
          content: 'These cards show your projected, escrow, released, and pending earnings.',
          position: 'bottom'
        },
        {
          target: '[data-tour="earnings-breakdown"]',
          title: 'Earnings Breakdown',
          content: 'See how your earnings are distributed across different categories.',
          position: 'right'
        },
        {
          target: '[data-tour="contribution-value"]',
          title: 'Contribution Value',
          content: 'Each contribution shows its calculated value based on hours and difficulty.',
          position: 'left'
        }
      ],
      project: [
        {
          target: '[data-tour="project-metrics"]',
          title: 'Project Metrics',
          content: 'Key project statistics including contributions, hours, and completion percentage.',
          position: 'bottom'
        },
        {
          target: '[data-tour="milestone-tracker"]',
          title: 'Milestone Tracking',
          content: 'Create and track project milestones to measure progress.',
          position: 'bottom'
        },
        {
          target: '[data-tour="task-board"]',
          title: 'Task Management',
          content: 'Drag and drop tasks between columns to update their status.',
          position: 'top'
        }
      ]
    };

    return tourSteps[context] || [];
  };

  const helpContent = getHelpContent(context);
  const tourSteps = getTourSteps(context);

  // Tour navigation
  const nextTourStep = () => {
    if (currentTourStep < tourSteps.length - 1) {
      setCurrentTourStep(currentTourStep + 1);
    } else {
      completeTour();
    }
  };

  const prevTourStep = () => {
    if (currentTourStep > 0) {
      setCurrentTourStep(currentTourStep - 1);
    }
  };

  const completeTour = () => {
    setIsTourActive(false);
    setCurrentTourStep(0);
    onTourComplete?.();
  };

  const skipTour = () => {
    setIsTourActive(false);
    setCurrentTourStep(0);
  };

  // Highlight tour target
  const highlightTourTarget = (target) => {
    const element = document.querySelector(target);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      element.classList.add('tour-highlight');
      
      setTimeout(() => {
        element.classList.remove('tour-highlight');
      }, 3000);
    }
  };

  // Effect to highlight current tour step
  useEffect(() => {
    if (isTourActive && tourSteps[currentTourStep]) {
      highlightTourTarget(tourSteps[currentTourStep].target);
    }
  }, [currentTourStep, isTourActive]);

  // Quick help tooltips
  const QuickHelpTooltip = ({ children, content, ...props }) => (
    <Tooltip
      content={content}
      placement="top"
      className="max-w-xs"
      {...props}
    >
      {children}
    </Tooltip>
  );

  return (
    <>
      {/* Help Button */}
      <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
        <QuickHelpTooltip content="Get help and start a guided tour">
          <Button
            isIconOnly
            color="primary"
            variant="shadow"
            onClick={() => setShowHelpModal(true)}
            className="w-12 h-12"
          >
            ❓
          </Button>
        </QuickHelpTooltip>
      </div>

      {/* Tour Overlay */}
      <AnimatePresence>
        {isTourActive && tourSteps[currentTourStep] && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-background border border-divider rounded-lg p-6 max-w-md mx-4 shadow-xl"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  {tourSteps[currentTourStep].title}
                </h3>
                <span className="text-sm text-muted-foreground">
                  {currentTourStep + 1} / {tourSteps.length}
                </span>
              </div>
              
              <p className="text-muted-foreground mb-6">
                {tourSteps[currentTourStep].content}
              </p>

              <div className="flex items-center justify-between">
                <Button
                  variant="light"
                  onClick={skipTour}
                  size="sm"
                >
                  Skip Tour
                </Button>

                <div className="flex gap-2">
                  <Button
                    variant="flat"
                    onClick={prevTourStep}
                    disabled={currentTourStep === 0}
                    size="sm"
                  >
                    Previous
                  </Button>
                  <Button
                    color="primary"
                    onClick={nextTourStep}
                    size="sm"
                  >
                    {currentTourStep === tourSteps.length - 1 ? 'Finish' : 'Next'}
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Help Modal */}
      <Modal isOpen={showHelpModal} onClose={() => setShowHelpModal(false)} size="lg">
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <span>❓</span>
              {helpContent.title}
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <p className="text-muted-foreground">
                {helpContent.description}
              </p>

              <div>
                <h4 className="font-medium mb-3">Tips & Tricks:</h4>
                <ul className="space-y-2">
                  {helpContent.tips.map((tip, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary mt-1">•</span>
                      <span className="text-sm">{tip}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {tourSteps.length > 0 && (
                <div className="border-t border-divider pt-4">
                  <h4 className="font-medium mb-2">Interactive Tour Available</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Take a guided tour to learn about the features on this page.
                  </p>
                  <Button
                    color="primary"
                    onClick={() => {
                      setShowHelpModal(false);
                      setIsTourActive(true);
                      setCurrentTourStep(0);
                    }}
                    size="sm"
                  >
                    Start Tour
                  </Button>
                </div>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onClick={() => setShowHelpModal(false)}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Tour highlight styles */}
      <style jsx global>{`
        .tour-highlight {
          position: relative;
          z-index: 1000;
        }
        
        .tour-highlight::before {
          content: '';
          position: absolute;
          inset: -4px;
          border: 2px solid #3b82f6;
          border-radius: 8px;
          background: rgba(59, 130, 246, 0.1);
          animation: pulse 2s infinite;
          pointer-events: none;
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </>
  );
};

export default ContextualHelp;
