import React from 'react';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';

const NotificationItem = ({ notification, onMarkAsRead }) => {
  const navigate = useNavigate();

  // Get icon based on notification type
  const getIcon = (type) => {
    switch (type) {
      case 'friend_request':
        return 'bi-person-plus';
      case 'friend_request_accepted':
        return 'bi-person-check';
      case 'friend_request_rejected':
        return 'bi-person-x';
      case 'project_invitation':
        return 'bi-envelope-plus';
      case 'project_invitation_accepted':
        return 'bi-check-circle';
      case 'project_invitation_rejected':
        return 'bi-x-circle';
      case 'agreement':
        return 'bi-file-earmark-text';
      case 'contribution_approved':
        return 'bi-check-circle-fill';
      case 'contribution_rejected':
        return 'bi-x-circle-fill';
      case 'contribution_changes_requested':
        return 'bi-pencil-square';
      case 'contribution_status_update':
        return 'bi-arrow-repeat';
      default:
        return 'bi-bell';
    }
  };

  // Handle click on notification
  const handleClick = () => {
    // Mark as read
    if (!notification.is_read) {
      onMarkAsRead(notification.id);
    }

    // Navigate to action URL if provided
    if (notification.action_url) {
      navigate(notification.action_url);
    }
  };

  // Format the time
  const timeAgo = formatDistanceToNow(new Date(notification.created_at), { addSuffix: true });

  return (
    <div
      className={`notification-item ${!notification.is_read ? 'unread' : ''}`}
      onClick={handleClick}
    >
      <div className="notification-icon">
        <i className={`bi ${getIcon(notification.type)}`}></i>
      </div>
      <div className="notification-content">
        <div className="notification-title">{notification.title}</div>
        <div className="notification-message">{notification.message}</div>
        <div className="notification-time">{timeAgo}</div>
      </div>
      {!notification.is_read && (
        <div className="notification-unread-indicator"></div>
      )}
    </div>
  );
};

export default NotificationItem;
