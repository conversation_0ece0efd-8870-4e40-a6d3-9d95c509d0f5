import React, { useEffect, useContext, useState } from 'react';
import { Card, CardBody, CardHeader, Button, Progress, Chip, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import AnalyticsDataService from '../../services/AnalyticsDataService';
import PerformanceOptimizer from '../../utils/analytics/PerformanceOptimizer';

/**
 * Analytics Integration Manager
 * 
 * Manages the integration and health of the entire analytics system:
 * - System health monitoring
 * - Performance metrics tracking
 * - Integration status verification
 * - Error handling and recovery
 * - Resource optimization
 */
const AnalyticsIntegrationManager = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [systemHealth, setSystemHealth] = useState({
    overall: 'unknown',
    components: {},
    performance: {},
    errors: []
  });
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [lastCheck, setLastCheck] = useState(null);

  // Component health checks
  const componentChecks = {
    dataService: {
      name: 'Analytics Data Service',
      check: async () => {
        try {
          await AnalyticsDataService.getAnalyticsOverview(currentUser?.id || 'test', '7d');
          return { status: 'healthy', message: 'Data service operational' };
        } catch (error) {
          return { status: 'error', message: error.message };
        }
      }
    },
    realTimeConnection: {
      name: 'Real-Time Connection',
      check: async () => {
        try {
          // Test WebSocket connection capability
          if (typeof WebSocket !== 'undefined') {
            return { status: 'healthy', message: 'WebSocket support available' };
          } else {
            return { status: 'warning', message: 'WebSocket not supported' };
          }
        } catch (error) {
          return { status: 'error', message: 'Real-time connection failed' };
        }
      }
    },
    performanceOptimizer: {
      name: 'Performance Optimizer',
      check: async () => {
        try {
          const metrics = PerformanceOptimizer.getPerformanceMetrics();
          const memoryUsage = metrics.memoryUsage?.percentage || 0;
          
          if (memoryUsage > 80) {
            return { status: 'warning', message: `High memory usage: ${memoryUsage.toFixed(1)}%` };
          } else {
            return { status: 'healthy', message: 'Performance optimal' };
          }
        } catch (error) {
          return { status: 'error', message: 'Performance monitoring failed' };
        }
      }
    },
    cacheSystem: {
      name: 'Cache System',
      check: async () => {
        try {
          const testKey = 'health_check_test';
          const testData = { test: true, timestamp: Date.now() };
          
          PerformanceOptimizer.setCache(testKey, testData);
          const retrieved = PerformanceOptimizer.getCache(testKey);
          
          if (retrieved && retrieved.test === true) {
            return { status: 'healthy', message: 'Cache system operational' };
          } else {
            return { status: 'warning', message: 'Cache system issues detected' };
          }
        } catch (error) {
          return { status: 'error', message: 'Cache system failed' };
        }
      }
    },
    apiEndpoints: {
      name: 'API Endpoints',
      check: async () => {
        try {
          // Test main analytics endpoint
          const response = await fetch('/.netlify/functions/enhanced-analytics', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
            }
          });
          
          if (response.ok) {
            return { status: 'healthy', message: 'API endpoints accessible' };
          } else {
            return { status: 'warning', message: `API returned ${response.status}` };
          }
        } catch (error) {
          return { status: 'error', message: 'API endpoints unreachable' };
        }
      }
    }
  };

  // Run comprehensive system health check
  const runHealthCheck = async () => {
    setIsMonitoring(true);
    const results = {};
    const errors = [];
    let healthyCount = 0;
    let totalCount = 0;

    try {
      // Run all component checks
      for (const [key, component] of Object.entries(componentChecks)) {
        totalCount++;
        try {
          const result = await component.check();
          results[key] = {
            name: component.name,
            ...result
          };
          
          if (result.status === 'healthy') {
            healthyCount++;
          } else if (result.status === 'error') {
            errors.push(`${component.name}: ${result.message}`);
          }
        } catch (error) {
          results[key] = {
            name: component.name,
            status: 'error',
            message: error.message
          };
          errors.push(`${component.name}: ${error.message}`);
        }
      }

      // Get performance metrics
      const performanceMetrics = PerformanceOptimizer.getPerformanceMetrics();

      // Determine overall health
      const healthPercentage = (healthyCount / totalCount) * 100;
      let overallStatus = 'healthy';
      
      if (healthPercentage < 50) {
        overallStatus = 'critical';
      } else if (healthPercentage < 80) {
        overallStatus = 'warning';
      }

      setSystemHealth({
        overall: overallStatus,
        components: results,
        performance: performanceMetrics,
        errors,
        healthPercentage,
        lastCheck: new Date().toISOString()
      });

      setLastCheck(new Date());

      // Show toast notification based on health
      if (overallStatus === 'healthy') {
        toast.success('Analytics system is healthy');
      } else if (overallStatus === 'warning') {
        toast.warning('Analytics system has some issues');
      } else {
        toast.error('Analytics system has critical issues');
      }

    } catch (error) {
      console.error('Health check failed:', error);
      toast.error('Failed to run system health check');
    } finally {
      setIsMonitoring(false);
    }
  };

  // Auto-run health check on mount and periodically
  useEffect(() => {
    if (currentUser) {
      runHealthCheck();
      
      // Set up periodic health checks (every 5 minutes)
      const interval = setInterval(runHealthCheck, 5 * 60 * 1000);
      
      return () => clearInterval(interval);
    }
  }, [currentUser]);

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'error': return 'danger';
      case 'critical': return 'danger';
      default: return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'critical': return '🚨';
      default: return '❓';
    }
  };

  // Optimize system performance
  const optimizeSystem = async () => {
    try {
      // Clean up cache
      PerformanceOptimizer.cleanupCache();
      
      // Clean up analytics data service
      AnalyticsDataService.cleanup();
      
      // Force garbage collection if available
      if ('gc' in window) {
        window.gc();
      }
      
      toast.success('System optimization completed');
      
      // Re-run health check
      setTimeout(runHealthCheck, 1000);
    } catch (error) {
      console.error('System optimization failed:', error);
      toast.error('Failed to optimize system');
    }
  };

  return (
    <div className={`analytics-integration-manager ${className}`}>
      {/* System Health Overview */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-2xl">🔧</span>
              <div>
                <h2 className="text-xl font-bold">Analytics System Health</h2>
                <p className="text-sm text-default-600">
                  Real-time monitoring and optimization
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge
                color={getStatusColor(systemHealth.overall)}
                variant="flat"
                size="lg"
              >
                {getStatusIcon(systemHealth.overall)} {systemHealth.overall?.toUpperCase()}
              </Badge>
              
              <Button
                size="sm"
                variant="flat"
                onClick={runHealthCheck}
                isLoading={isMonitoring}
              >
                Check Health
              </Button>
              
              <Button
                size="sm"
                color="secondary"
                variant="flat"
                onClick={optimizeSystem}
              >
                Optimize
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {systemHealth.healthPercentage !== undefined && (
          <CardBody className="pt-0">
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">System Health</span>
                  <span className="text-sm">{systemHealth.healthPercentage.toFixed(1)}%</span>
                </div>
                <Progress
                  value={systemHealth.healthPercentage}
                  color={getStatusColor(systemHealth.overall)}
                  size="sm"
                />
              </div>
              
              {lastCheck && (
                <div className="text-xs text-default-600">
                  Last checked: {lastCheck.toLocaleString()}
                </div>
              )}
            </div>
          </CardBody>
        )}
      </Card>

      {/* Component Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {Object.entries(systemHealth.components).map(([key, component]) => (
          <motion.div
            key={key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardBody className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-semibold text-sm">{component.name}</h3>
                  <Chip
                    color={getStatusColor(component.status)}
                    size="sm"
                    variant="flat"
                  >
                    {getStatusIcon(component.status)}
                  </Chip>
                </div>
                <p className="text-xs text-default-600">{component.message}</p>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Performance Metrics */}
      {systemHealth.performance && (
        <Card className="mb-6">
          <CardHeader>
            <h3 className="text-lg font-semibold">Performance Metrics</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-primary">
                  {systemHealth.performance.cacheSize || 0}
                </div>
                <div className="text-xs text-default-600">Cache Items</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-secondary">
                  {Math.round((systemHealth.performance.cacheMemoryUsage || 0) / 1024)} KB
                </div>
                <div className="text-xs text-default-600">Cache Memory</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-success">
                  {systemHealth.performance.activeTimers || 0}
                </div>
                <div className="text-xs text-default-600">Active Timers</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-warning">
                  {systemHealth.performance.queuedRequests || 0}
                </div>
                <div className="text-xs text-default-600">Queued Requests</div>
              </div>
            </div>
            
            {systemHealth.performance.memoryUsage && (
              <div className="mt-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Memory Usage</span>
                  <span className="text-sm">
                    {systemHealth.performance.memoryUsage.percentage.toFixed(1)}%
                  </span>
                </div>
                <Progress
                  value={systemHealth.performance.memoryUsage.percentage}
                  color={systemHealth.performance.memoryUsage.percentage > 80 ? 'danger' : 'success'}
                  size="sm"
                />
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Error Log */}
      {systemHealth.errors.length > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold text-danger">System Errors</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-2">
              {systemHealth.errors.map((error, index) => (
                <div key={index} className="text-sm text-danger bg-danger/10 p-2 rounded">
                  {error}
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default AnalyticsIntegrationManager;
