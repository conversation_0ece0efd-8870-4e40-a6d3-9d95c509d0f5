import React, { useState, useEffect } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { <PERSON>, CardBody, CardHeader, <PERSON>, <PERSON><PERSON>, Accordion, AccordionItem } from '@heroui/react';
import { motion } from 'framer-motion';
import { Bug, CheckCircle, Clock, AlertTriangle, ChevronDown, ChevronUp } from 'lucide-react';

// Default empty state for when no bugs are found
const DEFAULT_EMPTY_STATE = {
  title: 'No Known Issues',
  description: 'Great news! There are currently no known issues reported.',
  icon: '✅'
};
const KnownBugsList = () => {
  const [bugs, setBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [expandedBugId, setExpandedBugId] = useState(null);

  // Fetch public bugs from database
  useEffect(() => {
    const fetchBugs = async () => {
      try {
        setLoading(true);

        const { data, error } = await supabase
          .from('bug_reports')
          .select('*')
          .eq('is_public', true)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Database error fetching bugs:', error);
          // Set empty array if database query fails
          setBugs([]);
        } else {
          setBugs(data || []);
        }
      } catch (error) {
        console.error('Error fetching known bugs:', error);
        toast.error('Failed to load known bugs');
        setBugs([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBugs();
  }, []);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
        <CardBody className="p-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-3"></div>
            <span className="text-default-600">Loading known bugs...</span>
          </div>
        </CardBody>
      </Card>
    );
  }

  if (bugs.length === 0) {
    return (
      <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
        <CardBody className="p-8">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-success/20 flex items-center justify-center mx-auto mb-4">
              <CheckCircle size={32} className="text-success" />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">{DEFAULT_EMPTY_STATE.title}</h3>
            <p className="text-default-600">{DEFAULT_EMPTY_STATE.description}</p>
          </div>
        </CardBody>
      </Card>
    );
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'fixed':
        return <CheckCircle size={16} className="text-success" />;
      case 'in_progress':
        return <Clock size={16} className="text-warning" />;
      case 'open':
      default:
        return <AlertTriangle size={16} className="text-danger" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'fixed':
        return 'success';
      case 'in_progress':
        return 'warning';
      case 'open':
      default:
        return 'danger';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'fixed':
        return 'Fixed';
      case 'in_progress':
        return 'In Progress';
      case 'open':
      default:
        return 'Open';
    }
  };

  return (
    <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-secondary/20 flex items-center justify-center">
            <Bug size={20} className="text-secondary" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-foreground">Known Issues</h3>
            <p className="text-sm text-default-600">We're actively working on fixing these issues</p>
          </div>
        </div>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          {bugs.map((bug, index) => (
            <motion.div
              key={bug.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="border border-default-200 hover:border-default-300 transition-colors">
                <CardBody className="p-4">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(bug.status)}
                        <h4 className="font-semibold text-foreground">{bug.title}</h4>
                        <Chip
                          size="sm"
                          color={getStatusColor(bug.status)}
                          variant="flat"
                        >
                          {getStatusLabel(bug.status)}
                        </Chip>
                      </div>

                      <p className="text-default-600 mb-3 line-clamp-2">{bug.description}</p>

                      {bug.solution && (
                        <div className="bg-success/10 border border-success/20 rounded-lg p-3 mb-3">
                          <p className="text-sm font-medium text-success mb-1">Solution:</p>
                          <p className="text-sm text-success/80">{bug.solution}</p>
                        </div>
                      )}

                      <div className="flex items-center gap-4 text-xs text-default-500">
                        <span>Reported: {formatDate(bug.created_at)}</span>
                        {bug.status === 'fixed' && bug.fixed_at && (
                          <span>Fixed: {formatDate(bug.fixed_at)}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default KnownBugsList;
