import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Progress, Chip } from '@heroui/react';

/**
 * NavigationProgress Component
 * 
 * Displays progress indicators and clear next steps throughout the Start-Track-Earn journey.
 * Provides contextual guidance based on the current location in the spatial navigation system.
 */

const NavigationProgress = ({ 
  currentJourney = 'start', 
  currentStep = 1, 
  totalSteps = 3,
  nextSteps = [],
  className = "" 
}) => {
  
  // Journey definitions
  const journeys = {
    start: {
      title: 'Start Your Journey',
      icon: '🚀',
      color: 'primary',
      steps: [
        'Choose Your Path',
        'Create Project',
        'Set Up Team'
      ]
    },
    track: {
      title: 'Track Your Work',
      icon: '📊',
      color: 'secondary',
      steps: [
        'Log Contributions',
        'Track Time',
        'Monitor Progress'
      ]
    },
    earn: {
      title: 'Earn & Analyze',
      icon: '💰',
      color: 'success',
      steps: [
        'View Earnings',
        'Analyze Performance',
        'Claim Rewards'
      ]
    }
  };

  const journey = journeys[currentJourney] || journeys.start;
  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`w-full ${className}`}
    >
      <Card className="bg-gradient-to-r from-background/50 to-background/80 backdrop-blur-sm border border-divider/50">
        <CardBody className="p-6">
          {/* Journey Header */}
          <div className="flex items-center gap-3 mb-4">
            <div className="text-2xl">{journey.icon}</div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">{journey.title}</h3>
              <p className="text-sm text-muted-foreground">
                Step {currentStep} of {totalSteps}
              </p>
            </div>
            <div className="ml-auto">
              <Chip 
                color={journey.color} 
                variant="flat" 
                size="sm"
              >
                {Math.round(progressPercentage)}% Complete
              </Chip>
            </div>
          </div>

          {/* Progress Bar */}
          <Progress 
            value={progressPercentage}
            color={journey.color}
            className="mb-4"
            size="sm"
          />

          {/* Current Step Indicator */}
          <div className="flex items-center gap-2 mb-4">
            {journey.steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div 
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                    index + 1 < currentStep 
                      ? 'bg-success text-success-foreground' 
                      : index + 1 === currentStep
                      ? `bg-${journey.color} text-${journey.color}-foreground`
                      : 'bg-default-100 text-default-500'
                  }`}
                >
                  {index + 1 < currentStep ? '✓' : index + 1}
                </div>
                {index < journey.steps.length - 1 && (
                  <div 
                    className={`w-8 h-0.5 mx-1 transition-colors ${
                      index + 1 < currentStep ? 'bg-success' : 'bg-default-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          {/* Step Labels */}
          <div className="grid grid-cols-3 gap-2 mb-4">
            {journey.steps.map((step, index) => (
              <div 
                key={index}
                className={`text-xs text-center transition-colors ${
                  index + 1 === currentStep 
                    ? 'text-foreground font-medium' 
                    : index + 1 < currentStep
                    ? 'text-success'
                    : 'text-muted-foreground'
                }`}
              >
                {step}
              </div>
            ))}
          </div>

          {/* Next Steps */}
          {nextSteps.length > 0 && (
            <div className="border-t border-divider pt-4">
              <h4 className="text-sm font-medium text-foreground mb-2">Next Steps:</h4>
              <ul className="space-y-1">
                {nextSteps.map((step, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                    {step}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default NavigationProgress;
