import React from "react";
import { <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>ooter } from "@heroui/react";
import { cn } from "../../../lib/utils";

/**
 * Card Component - HeroUI Implementation
 *
 * A container component for grouping related content.
 * Compatible with shadcn/ui Card API for easy migration.
 */
const Card = React.forwardRef(({ className, ...props }, ref) => (
  <HeroUICard
    ref={ref}
    className={cn("", className)}
    {...props}
  />
));
Card.displayName = "Card";

/**
 * CardHeader Component
 */
const CardHeader = React.forwardRef(({ className, ...props }, ref) => (
  <HeroUICardHeader
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

/**
 * <PERSON><PERSON><PERSON>le Component
 */
const CardTitle = React.forwardRef(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn("text-2xl font-semibold leading-none tracking-tight", className)}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

/**
 * CardDescription Component
 */
const CardDescription = React.forwardRef(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

/**
 * CardContent Component
 */
const CardContent = React.forwardRef(({ className, ...props }, ref) => (
  <CardBody
    ref={ref}
    className={cn("p-6 pt-0", className)}
    {...props}
  />
));
CardContent.displayName = "CardContent";

/**
 * CardFooter Component
 */
const CardFooter = React.forwardRef(({ className, ...props }, ref) => (
  <HeroUICardFooter
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };
