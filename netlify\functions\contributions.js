// Contribution-related functions using Supabase
const { createClient } = require('@supabase/supabase-js');
const { NetlifyJwtVerifier } = require('@serverless-jwt/netlify');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Initialize the JWT verifier
const verifyJwt = NetlifyJwtVerifier({
  issuer: process.env.SITE_URL,
  audience: process.env.SITE_URL
});

// Get all contributions
const getContributions = async () => {
  try {
    const { data, error } = await supabase
      .from('contributions')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `);
    
    if (error) throw error;
    
    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Error getting contributions", error: error.message })
    };
  }
};

// Get a single contribution
const getContribution = async (event) => {
  const id = event.path.split('/').pop();
  
  try {
    const { data, error } = await supabase
      .from('contributions')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    
    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: "Error getting contribution", error: error.message })
    };
  }
};

// Create a new contribution
const createContribution = async (event, context) => {
  const user = context.clientContext.user;
  const data = JSON.parse(event.body);
  
  try {
    const { data: result, error } = await supabase
      .from('contributions')
      .insert([
        {
          ...data,
          user_id: user.sub,
          date_created: new Date().toISOString()
        }
      ])
      .select()
      .single();
    
    if (error) throw error;
    
    return {
      statusCode: 201,
      body: JSON.stringify(result)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Error creating contribution", error: error.message })
    };
  }
};

// Route requests to the appropriate handler
exports.handler = verifyJwt(async (event, context) => {
  const path = event.path.replace('/.netlify/functions/contributions', '');
  
  if (event.httpMethod === 'GET') {
    if (path === '' || path === '/') {
      return getContributions();
    } else {
      return getContribution(event);
    }
  } else if (event.httpMethod === 'POST') {
    return createContribution(event, context);
  }
  
  return {
    statusCode: 405,
    body: JSON.stringify({ message: "Method not allowed" })
  };
});
