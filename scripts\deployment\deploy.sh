#!/bin/bash
# Simple deploy script for Netlify

echo "=== Starting Netlify Deployment ==="

# Check if Netlify CLI is installed
if ! command -v netlify &> /dev/null; then
    echo "Netlify CLI not found. Installing..."
    npm install netlify-cli -g
fi

# Check if we need to build
if [ "$1" == "--build" ] || [ "$1" == "-b" ]; then
    echo "Building client application..."
    cd client
    npm run build
    cd ..
    echo "Build completed!"
fi

# Deploy to Netlify
echo "Deploying to Netlify..."
netlify deploy --prod --dir="client/dist"

echo "=== Deployment Complete ==="
