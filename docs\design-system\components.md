# Component Specifications
**Detailed Component Design & Behavior Documentation**

## 🧩 **Component Library Overview**

This file contains detailed specifications for all Royaltea platform components. When updated, coding agents will automatically create or modify the corresponding React components.

---

## 🎯 **Bento Grid Widgets**

### **BaseWidget Component**
```
Purpose: Foundation for all bento grid widgets
Size: Configurable (1x1, 2x1, 3x1, 4x1, 2x2, 4x2, 6x2)
```

**Visual Structure:**
```
┌─────────────────────────────────┐
│ [Status]              [Actions] │ ← Header (optional)
│                                 │
│         Main Content            │ ← Content area
│                                 │
│ [Footer Info]      [CTA Button] │ ← Footer (optional)
└─────────────────────────────────┘
```

**Props:**
- `size`: '1x1' | '2x1' | '3x1' | '4x1' | '2x2' | '4x2' | '6x2'
- `title`: string (optional)
- `status`: 'default' | 'success' | 'warning' | 'error'
- `actions`: ReactNode[] (optional)
- `footer`: ReactNode (optional)
- `onClick`: function (optional)
- `className`: string (optional)

**Styling:**
- Background: bg-primary with hover:bg-secondary transition
- Border: border-primary with rounded-lg
- Padding: 16px internal
- Shadow: subtle drop shadow on hover
- Transition: all properties 200ms ease

---

## 📊 **Data Display Components**

### **MetricWidget Component**
```
Purpose: Display key performance indicators and metrics
Sizes: 1x1 (single metric), 2x1 (metric with context)
```

**1x1 Layout:**
```
┌─────────────┐
│    RANK     │ ← Label
│     #47     │ ← Large value
│   ↑ +3      │ ← Change indicator
└─────────────┘
```

**2x1 Layout:**
```
┌─────────────────────────────┐
│ TREASURY        [💰]        │ ← Title with icon
│ $12,450         +$2,100     │ ← Main value + change
│ Available: $8,200           │ ← Supporting info
└─────────────────────────────┘
```

**Props:**
- `label`: string
- `value`: string | number
- `change`: number (optional, shows trend)
- `icon`: ReactNode (optional)
- `supportingText`: string (optional)
- `format`: 'currency' | 'percentage' | 'number' | 'rank'

### **ProgressWidget Component**
```
Purpose: Show progress on tasks, projects, or goals
Sizes: 2x1 (basic), 3x1 (detailed)
```

**Layout:**
```
┌─────────────────────────────────┐
│ Mission Progress    [⚡ Active] │ ← Title with status
│ ████████████░░░░░░░░ 65%       │ ← Progress bar
│ 13 of 20 tasks completed       │ ← Details
└─────────────────────────────────┘
```

**Props:**
- `title`: string
- `progress`: number (0-100)
- `current`: number
- `total`: number
- `status`: 'active' | 'paused' | 'completed' | 'overdue'
- `showPercentage`: boolean

---

## 🎮 **Interactive Components**

### **ActionButton Component**
```
Purpose: Primary and secondary action buttons
Variants: primary, secondary, ghost, danger
```

**Visual Variants:**
```
Primary:   [  Create Mission  ]  ← royal-500 background
Secondary: [  View Details   ]  ← border with royal-500 text
Ghost:     [  Cancel        ]  ← text only, hover background
Danger:    [  Delete        ]  ← error-500 background
```

**Props:**
- `variant`: 'primary' | 'secondary' | 'ghost' | 'danger'
- `size`: 'sm' | 'md' | 'lg'
- `icon`: ReactNode (optional)
- `loading`: boolean
- `disabled`: boolean
- `fullWidth`: boolean

### **QuickAction Component**
```
Purpose: 1x1 widget for single actions
Usage: Create buttons, settings, quick toggles
```

**Layout:**
```
┌─────────────┐
│     [+]     │ ← Large icon
│   Create    │ ← Action label
│   Mission   │ ← Context
└─────────────┘
```

**Props:**
- `icon`: ReactNode
- `label`: string
- `sublabel`: string (optional)
- `onClick`: function
- `variant`: 'default' | 'primary' | 'success'

---

## 📋 **List Components**

### **ItemList Component**
```
Purpose: Display lists of items (users, tasks, projects)
Sizes: 2x1 (3-5 items), 4x1 (8-12 items), 4x2 (detailed)
```

**2x1 Layout:**
```
┌─────────────────────────────┐
│ Team Members        [View]  │ ← Header with action
│ • Alice (Lead)      [●]     │ ← Item with status
│ • Bob (Dev)         [●]     │
│ • Carol (Design)    [○]     │
│ +2 more members             │ ← Overflow indicator
└─────────────────────────────┘
```

**Props:**
- `title`: string
- `items`: Array<{id, name, subtitle, status, avatar}>
- `maxVisible`: number
- `showStatus`: boolean
- `onItemClick`: function
- `headerAction`: ReactNode (optional)

### **TaskCard Component**
```
Purpose: Individual task/mission display
Usage: In lists, grids, detailed views
```

**Layout:**
```
┌─────────────────────────────────┐
│ [🎯] Fix Login Bug    [High]    │ ← Icon, title, priority
│ Due: Jan 20          [Assigned] │ ← Due date, status
│ Assigned to: Alice              │ ← Assignment info
│ ████████░░░░ 80%               │ ← Progress
└─────────────────────────────────┘
```

**Props:**
- `title`: string
- `description`: string (optional)
- `priority`: 'low' | 'medium' | 'high' | 'critical'
- `status`: 'todo' | 'in-progress' | 'review' | 'done'
- `assignee`: User object
- `dueDate`: Date
- `progress`: number (0-100)

---

## 🔧 **Implementation Instructions**

### **For Coding Agents**
When this file is updated:

1. **Generate React Components**
   - Create TypeScript interfaces for all props
   - Implement responsive behavior using Tailwind classes
   - Add proper accessibility attributes (ARIA labels, roles)
   - Include loading and error states

2. **Apply Design System**
   - Use colors from `colors.md`
   - Apply typography from `typography.md`
   - Follow spacing rules from `spacing.md`
   - Implement animations from `animations.md`

3. **Component Structure**
```typescript
// Example component structure
interface BaseWidgetProps {
  size: WidgetSize;
  title?: string;
  status?: WidgetStatus;
  children: ReactNode;
  className?: string;
}

export const BaseWidget: React.FC<BaseWidgetProps> = ({
  size,
  title,
  status = 'default',
  children,
  className
}) => {
  // Implementation with proper styling and behavior
};
```

4. **Export Structure**
```typescript
// components/index.ts
export { BaseWidget } from './BaseWidget';
export { MetricWidget } from './MetricWidget';
export { ProgressWidget } from './ProgressWidget';
// ... all components
```

### **Testing Requirements**
- Unit tests for all props and variants
- Visual regression tests for each component
- Accessibility testing with screen readers
- Responsive behavior testing across breakpoints

---

## ✅ **Design Team Checklist**

When adding/updating components:
- [ ] Specify exact dimensions and spacing
- [ ] Include all visual states (default, hover, active, disabled)
- [ ] Document responsive behavior
- [ ] Provide accessibility requirements
- [ ] Include usage examples and guidelines
- [ ] Specify animation/transition requirements

**Last Updated**: January 16, 2025
**Version**: 1.0
**Status**: Ready for implementation
