# Coding Instructions for AI Agents
**Automated Implementation Guidelines**

## 🎯 **Primary Directive**

When any design system file is updated, automatically implement the changes in the codebase following these exact instructions. The design team's specifications are the **single source of truth** - implement exactly what is documented.

---

## 📋 **Implementation Process**

### **Step 1: Parse Design Changes**
1. **Read Updated Files** - Identify which design system files have changed
2. **Check PRD Alignment** - Ensure changes align with [../../PRODUCT_REQUIREMENTS.md](../../PRODUCT_REQUIREMENTS.md)
3. **Process Design Assets** - Identify new/updated icons, wireframes, mockups
4. **Extract Specifications** - Parse all requirements, UI designs, and technical specs
5. **Identify Impact** - Determine which code files need to be created/modified
6. **Plan Implementation** - Create step-by-step implementation plan

### **Step 2: Asset Processing First**
```javascript
// Process design assets before code changes
// 1. Optimize and import new icons from client/src/assets/design-system/icons/
// 2. Process wireframe exports for implementation reference
// 3. Update Icon component with new asset mappings
// 4. Generate responsive image variants
// See asset-integration.md for complete asset processing instructions
```

### **Step 3: Database Changes**
```sql
-- Apply database changes before component changes
-- Check if schema updates are needed from system specifications
-- Apply migrations using Supabase CLI or direct SQL
-- Validate schema changes before proceeding
```

### **Step 4: Component Implementation**
```javascript
// Follow this exact pattern for all components:

// 1. Import required dependencies
import React, { useState, useEffect } from 'react';
import { Card, Button, Input } from '@heroui/react';
import { useSupabase } from '../hooks/useSupabase';

// 2. Component with exact specifications from design docs
const ComponentName = ({ ...props }) => {
  // 3. State management as specified in system docs
  const [state, setState] = useState(initialState);
  
  // 4. Business logic as documented
  const handleAction = async () => {
    // Implement exact logic from system specification
  };

  // 5. UI exactly matching wireframes and design specs
  return (
    <Card className="exact-classes-from-design-system">
      {/* Implement exact layout from wireframes */}
    </Card>
  );
};

export default ComponentName;
```

---

## 🎨 **Design System Implementation Rules**

### **Colors**
```javascript
// When colors.md is updated, automatically update:

// 1. Tailwind config
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        // Extract all colors from colors.md
        royal: { /* all royal color values */ },
        tea: { /* all tea color values */ }
      }
    }
  }
};

// 2. CSS variables
// src/index.css
:root {
  --color-royal-500: #8b5cf6;
  /* All color variables from colors.md */
}

// 3. HeroUI theme overrides
// src/lib/theme.js
export const theme = {
  colors: {
    // Map design system colors to HeroUI
  }
};
```

### **Components**
```javascript
// When components.md is updated:

// 1. Create/update component files
// 2. Apply exact styling from design system
// 3. Implement responsive behavior as specified
// 4. Add accessibility features as documented
// 5. Include all props and state management

// Example component structure:
const DesignedComponent = ({
  // Props exactly as specified in components.md
  size = 'medium',
  variant = 'primary',
  children,
  ...props
}) => {
  // Styling classes from design system
  const baseClasses = 'base-classes-from-design-system';
  const sizeClasses = {
    small: 'small-classes',
    medium: 'medium-classes',
    large: 'large-classes'
  };
  
  return (
    <div 
      className={`${baseClasses} ${sizeClasses[size]}`}
      {...props}
    >
      {children}
    </div>
  );
};
```

### **System Architecture**
```javascript
// When system files (studio-system.md, etc.) are updated:

// 1. Create all components listed in "Component Structure"
// 2. Implement exact database schema from "Data Requirements"
// 3. Create API endpoints from "API Endpoints Required"
// 4. Follow user flows from "User Experience Flow"
// 5. Apply UI designs from "User Interface Design"

// Example system implementation:
// For studio-system.md updates:

// Create components:
// client/src/components/alliance/AllianceCreationWizard.jsx
// client/src/components/alliance/AllianceDashboard.jsx
// etc.

// Apply database schema:
// Run SQL from "Database Schema" section

// Create API endpoints:
// netlify/functions/alliances.js with all specified endpoints
```

---

## 🔧 **File Organization Rules**

### **Component Placement**
```
client/src/components/
├── alliance/          # Alliance system components
├── social/           # Social system components  
├── gamification/     # Gamification components
├── navigation/       # Navigation system components
├── payments/         # Payment system components
├── ui/              # Reusable UI components
└── common/          # Shared utility components
```

### **System Implementation**
```
For each system (alliance, social, etc.):

1. Components: client/src/components/[system]/
2. Pages: client/src/pages/[system]/
3. Hooks: client/src/hooks/use[System].js
4. Services: client/src/services/[system]Service.js
5. Utils: client/src/utils/[system]/
6. Types: client/src/types/[system].ts
```

---

## 📊 **Data Integration**

### **Supabase Integration**
```javascript
// Always use this pattern for database operations:

import { useSupabase } from '../hooks/useSupabase';

const useSystemData = () => {
  const { supabase } = useSupabase();
  
  const createRecord = async (data) => {
    const { data: result, error } = await supabase
      .from('table_name')
      .insert(data)
      .select();
    
    if (error) throw error;
    return result;
  };
  
  return { createRecord };
};
```

### **API Endpoint Creation**
```javascript
// netlify/functions/[endpoint].js
export const handler = async (event, context) => {
  // Implement exact API specification from system docs
  
  const { httpMethod, path, body } = event;
  
  switch (httpMethod) {
    case 'GET':
      // Implement GET logic from API spec
      break;
    case 'POST':
      // Implement POST logic from API spec
      break;
    // etc.
  }
};
```

---

## 🎯 **Quality Assurance**

### **Implementation Checklist**
- [ ] All UI matches wireframes exactly
- [ ] All colors use design system values
- [ ] All components follow responsive specifications
- [ ] All accessibility features implemented
- [ ] All database schema applied correctly
- [ ] All API endpoints working as specified
- [ ] All user flows function as documented

### **Testing Requirements**
```javascript
// Create tests for all implemented features:

// Component tests
describe('ComponentName', () => {
  it('renders according to design specification', () => {
    // Test exact UI from wireframes
  });
  
  it('handles all user interactions', () => {
    // Test all flows from system docs
  });
});

// Integration tests
describe('System Integration', () => {
  it('implements complete user journey', () => {
    // Test end-to-end flows from system specs
  });
});
```

---

## 🚀 **Deployment Integration**

### **Automatic Deployment**
```bash
# After implementing changes:
1. Run tests to validate implementation
2. Build project with new changes
3. Deploy to staging for validation
4. Deploy to production after approval
```

### **Change Documentation**
```markdown
# Always document what was implemented:

## Changes Made
- Updated [system] based on [design file] changes
- Created components: [list]
- Modified database: [changes]
- Added API endpoints: [list]

## Validation
- [ ] UI matches design specifications
- [ ] All functionality works as documented
- [ ] Tests pass
- [ ] Deployment successful
```

---

## 🎨 **Design Fidelity Rules**

### **Exact Implementation Required**
- **Colors**: Use exact hex values from colors.md
- **Spacing**: Use exact measurements from spacing.md
- **Typography**: Use exact font sizes and weights
- **Layout**: Match wireframes pixel-perfect
- **Interactions**: Implement exact user flows
- **Animations**: Follow timing and easing from animations.md

### **No Creative Interpretation**
- Do not modify design specifications
- Do not add features not documented
- Do not change user flows
- Do not alter visual design
- Ask for clarification if specifications are unclear

---

**These instructions ensure that design team specifications are implemented exactly as documented, creating a seamless design-to-code pipeline.**
