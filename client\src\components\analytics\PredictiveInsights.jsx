import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Select, SelectItem, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * PredictiveInsights Component - Forecasting and Trend Analysis
 * 
 * Features:
 * - AI-powered revenue and growth forecasting
 * - Market trend analysis and opportunity identification
 * - Risk assessment and mitigation recommendations
 * - Performance prediction and optimization suggestions
 * - Seasonal pattern recognition and planning
 * - Competitive analysis and positioning insights
 */
const PredictiveInsights = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState('3m');
  const [activeTab, setActiveTab] = useState('revenue');
  const [predictiveData, setPredictiveData] = useState({});

  // Load predictive analytics data
  const loadPredictiveData = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Mock predictive data - in production this would call AI/ML analytics API
      const mockData = {
        revenueForecast: {
          current: 47200,
          predictions: [
            { period: 'Next Month', amount: 52000, confidence: 87, growth: 10.2 },
            { period: 'Next Quarter', amount: 165000, confidence: 82, growth: 15.8 },
            { period: 'Next 6 Months', amount: 340000, confidence: 75, growth: 18.5 },
            { period: 'Next Year', amount: 720000, confidence: 68, growth: 22.3 }
          ],
          factors: [
            { name: 'Seasonal Trends', impact: 15, type: 'positive' },
            { name: 'Market Growth', impact: 22, type: 'positive' },
            { name: 'Competition', impact: -8, type: 'negative' },
            { name: 'Skill Development', impact: 12, type: 'positive' }
          ]
        },
        marketTrends: {
          opportunities: [
            {
              title: 'AI/ML Development Surge',
              description: 'Machine learning projects are increasing by 45% in your skill areas',
              potential: 'High',
              timeframe: '2-3 months',
              confidence: 85
            },
            {
              title: 'Remote Work Tools Demand',
              description: 'Growing demand for collaboration and productivity tools',
              potential: 'Medium',
              timeframe: '1-2 months',
              confidence: 78
            },
            {
              title: 'E-commerce Platform Migration',
              description: 'Businesses migrating to modern e-commerce solutions',
              potential: 'High',
              timeframe: '3-6 months',
              confidence: 82
            }
          ],
          threats: [
            {
              title: 'Increased Competition',
              description: 'More developers entering your primary skill areas',
              severity: 'Medium',
              timeframe: '6 months',
              mitigation: 'Specialize in niche technologies'
            },
            {
              title: 'Economic Uncertainty',
              description: 'Potential reduction in project budgets',
              severity: 'Low',
              timeframe: '3-4 months',
              mitigation: 'Diversify client base'
            }
          ]
        },
        performancePredictions: {
          skillDevelopment: [
            { skill: 'React', currentLevel: 8, predictedLevel: 9, timeframe: '3 months', confidence: 92 },
            { skill: 'Node.js', currentLevel: 7, predictedLevel: 8, timeframe: '4 months', confidence: 88 },
            { skill: 'Python', currentLevel: 6, predictedLevel: 7, timeframe: '5 months', confidence: 85 },
            { skill: 'AI/ML', currentLevel: 4, predictedLevel: 6, timeframe: '6 months', confidence: 75 }
          ],
          projectSuccess: {
            nextProject: { successProbability: 94, factors: ['Strong skill match', 'Good client history'] },
            riskFactors: ['Tight timeline', 'New technology stack'],
            recommendations: ['Allocate extra time for learning', 'Consider team collaboration']
          }
        },
        seasonalPatterns: {
          busyPeriods: [
            { period: 'Q1', activity: 85, reason: 'New year project launches' },
            { period: 'Q2', activity: 92, reason: 'Mid-year initiatives' },
            { period: 'Q3', activity: 78, reason: 'Summer slowdown' },
            { period: 'Q4', activity: 95, reason: 'Year-end deadlines' }
          ],
          recommendations: [
            'Plan major skill development during Q3 slower period',
            'Prepare for increased demand in Q4',
            'Consider vacation scheduling around Q2 peak'
          ]
        },
        riskAssessment: {
          overall: 'Low',
          factors: [
            { category: 'Market Risk', level: 'Low', score: 25 },
            { category: 'Competition Risk', level: 'Medium', score: 45 },
            { category: 'Technology Risk', level: 'Low', score: 30 },
            { category: 'Client Risk', level: 'Low', score: 20 }
          ],
          mitigationStrategies: [
            'Diversify skill portfolio to reduce technology risk',
            'Build stronger client relationships for retention',
            'Monitor competitor activities and pricing'
          ]
        }
      };

      setPredictiveData(mockData);
    } catch (error) {
      console.error('Error loading predictive data:', error);
      toast.error('Failed to load predictive insights');
    } finally {
      setLoading(false);
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  // Get confidence color
  const getConfidenceColor = (confidence) => {
    if (confidence >= 85) return 'success';
    if (confidence >= 70) return 'primary';
    if (confidence >= 60) return 'warning';
    return 'danger';
  };

  // Get potential color
  const getPotentialColor = (potential) => {
    const colors = {
      'High': 'success',
      'Medium': 'warning',
      'Low': 'default'
    };
    return colors[potential] || 'default';
  };

  // Get risk color
  const getRiskColor = (level) => {
    const colors = {
      'Low': 'success',
      'Medium': 'warning',
      'High': 'danger'
    };
    return colors[level] || 'default';
  };

  // Initialize component
  useEffect(() => {
    loadPredictiveData();
  }, [selectedTimeframe]);

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading predictive insights...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`predictive-insights ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 mb-6">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🔮</span>
              <div>
                <h2 className="text-2xl font-bold">Predictive Insights</h2>
                <p className="text-default-600">AI-powered forecasting and trend analysis</p>
              </div>
            </div>
            <Select
              selectedKeys={[selectedTimeframe]}
              onSelectionChange={(keys) => setSelectedTimeframe(Array.from(keys)[0])}
              className="w-40"
              size="sm"
            >
              <SelectItem key="1m">1 Month</SelectItem>
              <SelectItem key="3m">3 Months</SelectItem>
              <SelectItem key="6m">6 Months</SelectItem>
              <SelectItem key="1y">1 Year</SelectItem>
            </Select>
          </div>
        </CardHeader>
      </Card>

      {/* Predictive Analytics Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs 
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            <Tab key="revenue" title={
              <div className="flex items-center space-x-2">
                <span>💰</span>
                <span>Revenue Forecast</span>
              </div>
            }>
              <div className="p-6">
                {/* Revenue Predictions */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                  {predictiveData.revenueForecast?.predictions.map((prediction, index) => (
                    <motion.div
                      key={prediction.period}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20">
                        <CardBody className="p-4">
                          <div className="text-center">
                            <div className="text-sm text-default-600 mb-1">{prediction.period}</div>
                            <div className="text-xl font-bold text-green-600 mb-2">
                              {formatCurrency(prediction.amount)}
                            </div>
                            <div className="flex items-center justify-center gap-2">
                              <Chip color="success" size="sm" variant="flat">
                                +{prediction.growth}%
                              </Chip>
                              <Chip color={getConfidenceColor(prediction.confidence)} size="sm" variant="flat">
                                {prediction.confidence}% confidence
                              </Chip>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                    </motion.div>
                  ))}
                </div>

                {/* Influencing Factors */}
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">📊 Influencing Factors</h3>
                  </CardHeader>
                  <CardBody className="pt-0">
                    <div className="space-y-3">
                      {predictiveData.revenueForecast?.factors.map((factor, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="font-medium">{factor.name}</span>
                          <div className="flex items-center gap-2">
                            <span className={`text-sm ${factor.type === 'positive' ? 'text-success' : 'text-danger'}`}>
                              {factor.type === 'positive' ? '+' : ''}{factor.impact}%
                            </span>
                            <Chip color={factor.type === 'positive' ? 'success' : 'danger'} size="sm" variant="flat">
                              {factor.type}
                            </Chip>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>
              </div>
            </Tab>

            <Tab key="market" title={
              <div className="flex items-center space-x-2">
                <span>📈</span>
                <span>Market Trends</span>
              </div>
            }>
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Opportunities */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">🚀 Opportunities</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-4">
                        {predictiveData.marketTrends?.opportunities.map((opportunity, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="border border-default-200 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-semibold">{opportunity.title}</h4>
                              <Chip color={getPotentialColor(opportunity.potential)} size="sm" variant="flat">
                                {opportunity.potential}
                              </Chip>
                            </div>
                            <p className="text-sm text-default-600 mb-3">{opportunity.description}</p>
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-default-500">Timeframe: {opportunity.timeframe}</span>
                              <span className="text-default-500">Confidence: {opportunity.confidence}%</span>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Threats */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">⚠️ Threats</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-4">
                        {predictiveData.marketTrends?.threats.map((threat, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, x: 20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="border border-default-200 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-semibold">{threat.title}</h4>
                              <Chip color={getRiskColor(threat.severity)} size="sm" variant="flat">
                                {threat.severity}
                              </Chip>
                            </div>
                            <p className="text-sm text-default-600 mb-3">{threat.description}</p>
                            <div className="text-xs text-default-500 mb-2">
                              Timeframe: {threat.timeframe}
                            </div>
                            <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded text-xs">
                              <strong>Mitigation:</strong> {threat.mitigation}
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>

            <Tab key="performance" title={
              <div className="flex items-center space-x-2">
                <span>🎯</span>
                <span>Performance</span>
              </div>
            }>
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Skill Development Predictions */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">📚 Skill Development Forecast</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-4">
                        {predictiveData.performancePredictions?.skillDevelopment.map((skill, index) => (
                          <div key={skill.skill} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{skill.skill}</span>
                              <div className="text-sm">
                                Level {skill.currentLevel} → {skill.predictedLevel}
                              </div>
                            </div>
                            <Progress 
                              value={(skill.predictedLevel / 10) * 100} 
                              color="primary" 
                              size="sm"
                            />
                            <div className="flex items-center justify-between text-xs text-default-500">
                              <span>{skill.timeframe}</span>
                              <span>{skill.confidence}% confidence</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Project Success Prediction */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">🎯 Project Success Prediction</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-4">
                        <div className="text-center">
                          <div className="text-3xl font-bold text-success mb-2">
                            {predictiveData.performancePredictions?.projectSuccess.nextProject.successProbability}%
                          </div>
                          <div className="text-sm text-default-600">Success Probability</div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Positive Factors</h4>
                          <div className="space-y-1">
                            {predictiveData.performancePredictions?.projectSuccess.nextProject.factors.map((factor, index) => (
                              <div key={index} className="text-sm text-success">✓ {factor}</div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Risk Factors</h4>
                          <div className="space-y-1">
                            {predictiveData.performancePredictions?.projectSuccess.riskFactors.map((factor, index) => (
                              <div key={index} className="text-sm text-warning">⚠ {factor}</div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Recommendations</h4>
                          <div className="space-y-1">
                            {predictiveData.performancePredictions?.projectSuccess.recommendations.map((rec, index) => (
                              <div key={index} className="text-sm text-primary">💡 {rec}</div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>

            <Tab key="seasonal" title={
              <div className="flex items-center space-x-2">
                <span>📅</span>
                <span>Seasonal Patterns</span>
              </div>
            }>
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Seasonal Activity */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">📊 Quarterly Activity Patterns</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-4">
                        {predictiveData.seasonalPatterns?.busyPeriods.map((period, index) => (
                          <div key={period.period} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{period.period}</span>
                              <span className="text-sm">{period.activity}% activity</span>
                            </div>
                            <Progress value={period.activity} color="secondary" size="sm" />
                            <div className="text-xs text-default-500">{period.reason}</div>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Recommendations */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">💡 Seasonal Recommendations</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-3">
                        {predictiveData.seasonalPatterns?.recommendations.map((recommendation, index) => (
                          <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <div className="text-sm">{recommendation}</div>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>

            <Tab key="risk" title={
              <div className="flex items-center space-x-2">
                <span>🛡️</span>
                <span>Risk Assessment</span>
              </div>
            }>
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Risk Overview */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">🛡️ Risk Assessment</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="text-center mb-6">
                        <div className="text-3xl font-bold text-success mb-2">
                          {predictiveData.riskAssessment?.overall} Risk
                        </div>
                        <div className="text-sm text-default-600">Overall Risk Level</div>
                      </div>

                      <div className="space-y-4">
                        {predictiveData.riskAssessment?.factors.map((factor, index) => (
                          <div key={factor.category} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{factor.category}</span>
                              <Chip color={getRiskColor(factor.level)} size="sm" variant="flat">
                                {factor.level}
                              </Chip>
                            </div>
                            <Progress value={factor.score} color={getRiskColor(factor.level)} size="sm" />
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>

                  {/* Mitigation Strategies */}
                  <Card>
                    <CardHeader>
                      <h3 className="text-lg font-semibold">🛠️ Mitigation Strategies</h3>
                    </CardHeader>
                    <CardBody className="pt-0">
                      <div className="space-y-3">
                        {predictiveData.riskAssessment?.mitigationStrategies.map((strategy, index) => (
                          <div key={index} className="p-3 border border-default-200 rounded-lg">
                            <div className="text-sm">{strategy}</div>
                          </div>
                        ))}
                      </div>
                    </CardBody>
                  </Card>
                </div>
              </div>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
};

export default PredictiveInsights;
