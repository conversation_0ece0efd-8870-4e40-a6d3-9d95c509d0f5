/**
 * Test Agreement Generator
 * 
 * This module provides functionality for generating test agreements
 * with predefined parameters for different project types.
 */

import { enhancedAgreementGenerator } from './enhancedAgreementGenerator';
import { TEMPLATE_TYPES } from './templateManager';
import { verifyProjectInformation } from './projectVerifier';

/**
 * Test project data for different project types
 */
export const TEST_PROJECTS = {
  GAME: {
    name: 'Test Game Project',
    description: 'A collaborative game development project for testing agreement generation',
    projectType: 'game',
    company_name: 'Game Studio Inc.',
    address: '123 Game Street, San Francisco, CA 94107',
    contact_email: '<EMAIL>',
    city: 'San Francisco',
    state: 'California',
    zip: '94107'
  },
  MUSIC: {
    name: 'Test Music Project',
    description: 'A collaborative music production project for testing agreement generation',
    projectType: 'music',
    company_name: 'Music Studio Inc.',
    address: '456 Music Avenue, Nashville, TN 37203',
    contact_email: '<EMAIL>',
    city: 'Nashville',
    state: 'Tennessee',
    zip: '37203'
  },
  SOFTWARE: {
    name: 'Test Software Project',
    description: 'A collaborative software development project for testing agreement generation',
    projectType: 'software',
    company_name: 'Software Solutions Inc.',
    address: '789 Tech Blvd, Austin, TX 78701',
    contact_email: '<EMAIL>',
    city: 'Austin',
    state: 'Texas',
    zip: '78701'
  },
  FILM: {
    name: 'Test Film Project',
    description: 'A collaborative film production project for testing agreement generation',
    projectType: 'film',
    company_name: 'Film Productions Inc.',
    address: '321 Hollywood Blvd, Los Angeles, CA 90028',
    contact_email: '<EMAIL>',
    city: 'Los Angeles',
    state: 'California',
    zip: '90028'
  }
};

/**
 * Generate a test agreement for a specific project type
 * @param {string} projectType - The project type (game, music, software, film)
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The generated agreement and verification results
 */
export const generateTestAgreement = async (projectType, options = {}) => {
  try {
    console.log(`TestAgreementGenerator: Generating test agreement for ${projectType} project`);
    
    // Get the test project data
    const projectKey = projectType.toUpperCase();
    if (!TEST_PROJECTS[projectKey]) {
      throw new Error(`Unknown project type: ${projectType}`);
    }
    
    const projectData = {
      ...TEST_PROJECTS[projectKey],
      ...options.projectOverrides
    };
    
    // Create mock user data
    const userData = {
      project: projectData,
      currentUser: {
        email: options.userEmail || '<EMAIL>',
        user_metadata: {
          full_name: options.userName || 'Test User'
        }
      },
      fullName: options.userName || 'Test User',
      contributors: options.contributors || [],
      royaltyModel: options.royaltyModel || {
        contributor_percentage: 33,
        min_payout: 10000,
        max_payout: 1000000,
        configuration: {
          tasks_weight: 30,
          hours_weight: 30,
          difficulty_weight: 40
        }
      },
      milestones: options.milestones || []
    };
    
    // Load the template
    const templateType = options.templateType || TEMPLATE_TYPES.STANDARD;
    const templateText = await enhancedAgreementGenerator.loadTemplate(templateType);
    
    // Generate the agreement
    const agreement = enhancedAgreementGenerator.generateAgreement(templateText, userData, {
      agreementDate: options.agreementDate || new Date()
    });
    
    // Verify the project information
    const verificationResults = await verifyProjectInformation(projectData);
    
    return {
      agreement,
      verificationResults,
      projectData
    };
  } catch (error) {
    console.error('TestAgreementGenerator: Error generating test agreement:', error);
    throw error;
  }
};

/**
 * Create a test project in the database
 * @param {string} projectType - The project type (game, music, software, film)
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The created project
 */
export const createTestProject = async (projectType, options = {}) => {
  try {
    console.log(`TestAgreementGenerator: Creating test project for ${projectType}`);
    
    // Import supabase client
    const { supabase } = await import('../../../utils/supabase/supabase.utils');
    
    // Get the test project data
    const projectKey = projectType.toUpperCase();
    if (!TEST_PROJECTS[projectKey]) {
      throw new Error(`Unknown project type: ${projectType}`);
    }
    
    const projectData = {
      ...TEST_PROJECTS[projectKey],
      ...options.projectOverrides,
      created_by: options.userId || null,
      is_public: options.isPublic !== undefined ? options.isPublic : true,
      created_at: new Date().toISOString()
    };
    
    // Create the project in the database
    const { data, error } = await supabase
      .from('projects')
      .insert([projectData])
      .select();
    
    if (error) {
      console.error('TestAgreementGenerator: Error creating test project:', error);
      throw error;
    }
    
    console.log('TestAgreementGenerator: Successfully created test project:', data[0]);
    return data[0];
  } catch (error) {
    console.error('TestAgreementGenerator: Error in createTestProject:', error);
    throw error;
  }
};
