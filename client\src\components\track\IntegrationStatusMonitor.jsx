import React, { useState, useEffect } from 'react';
import { Card, CardBody, Button, Chip, Tooltip } from '@heroui/react';
import { motion } from 'framer-motion';
import { 
  Github, 
  MessageSquare, 
  Trello, 
  Zap, 
  Settings, 
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertCircle,
  ExternalLink
} from 'lucide-react';

/**
 * Integration Status Monitor Component
 * 
 * Displays connection status for various third-party integrations
 * and provides sync capabilities for each service
 */
const IntegrationStatusMonitor = ({ className = "" }) => {
  const [integrations, setIntegrations] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock integration data - in real implementation, this would come from API
  const mockIntegrations = [
    {
      id: 'github',
      name: 'GitHub',
      icon: Github,
      status: 'connected',
      lastSync: '2 minutes ago',
      description: 'Repository and issue tracking',
      color: 'success',
      syncEnabled: true,
      url: 'https://github.com'
    },
    {
      id: 'slack',
      name: 'Slack',
      icon: MessageSquare,
      status: 'connected',
      lastSync: '5 minutes ago',
      description: 'Team communication and notifications',
      color: 'success',
      syncEnabled: true,
      url: 'https://slack.com'
    },
    {
      id: 'trello',
      name: 'Trello',
      icon: Trello,
      status: 'disconnected',
      lastSync: 'Never',
      description: 'Board and card management',
      color: 'danger',
      syncEnabled: false,
      url: 'https://trello.com'
    },
    {
      id: 'linear',
      name: 'Linear',
      icon: Zap,
      status: 'warning',
      lastSync: '1 hour ago',
      description: 'Issue tracking and project management',
      color: 'warning',
      syncEnabled: true,
      url: 'https://linear.app'
    },
    {
      id: 'jira',
      name: 'Jira',
      icon: Settings,
      status: 'disconnected',
      lastSync: 'Never',
      description: 'Agile project management',
      color: 'danger',
      syncEnabled: false,
      url: 'https://atlassian.com/software/jira'
    },
    {
      id: 'discord',
      name: 'Discord',
      icon: MessageSquare,
      status: 'connected',
      lastSync: '30 seconds ago',
      description: 'Community and team chat',
      color: 'success',
      syncEnabled: true,
      url: 'https://discord.com'
    }
  ];

  useEffect(() => {
    // Initialize with mock data
    setIntegrations(mockIntegrations);
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-400" />;
      case 'disconnected':
      default:
        return <XCircle className="w-4 h-4 text-red-400" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected':
        return 'success';
      case 'warning':
        return 'warning';
      case 'disconnected':
      default:
        return 'danger';
    }
  };

  const handleRefreshAll = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Update last sync times for connected integrations
    setIntegrations(prev => prev.map(integration => ({
      ...integration,
      lastSync: integration.status === 'connected' ? 'Just now' : integration.lastSync
    })));
    
    setIsRefreshing(false);
  };

  const handleSync = async (integrationId) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, lastSync: 'Syncing...' }
        : integration
    ));

    // Simulate sync
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, lastSync: 'Just now' }
        : integration
    ));
  };

  const handleConnect = (integration) => {
    // In real implementation, this would open OAuth flow or connection modal
    console.log(`Connecting to ${integration.name}...`);
    window.open(integration.url, '_blank');
  };

  const connectedCount = integrations.filter(i => i.status === 'connected').length;
  const totalCount = integrations.length;

  return (
    <Card className={`bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-white/10 ${className}`}>
      <CardBody className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-white">Integration Status</h3>
            <p className="text-sm text-white/60">
              {connectedCount} of {totalCount} services connected
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <Chip 
              color={connectedCount === totalCount ? 'success' : 'warning'}
              variant="flat"
              size="sm"
            >
              {Math.round((connectedCount / totalCount) * 100)}% Connected
            </Chip>
            
            <Button
              isIconOnly
              variant="light"
              size="sm"
              onClick={handleRefreshAll}
              isLoading={isRefreshing}
              className="text-white/70 hover:text-white"
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {integrations.map((integration, index) => (
            <motion.div
              key={integration.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-4 hover:bg-white/10 transition-colors duration-200"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-white/10">
                    <integration.icon className="w-5 h-5 text-white/70" />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-white">{integration.name}</h4>
                    <p className="text-xs text-white/50">{integration.description}</p>
                  </div>
                </div>
                
                {getStatusIcon(integration.status)}
              </div>

              <div className="flex items-center justify-between mb-3">
                <Chip 
                  color={getStatusColor(integration.status)}
                  variant="flat"
                  size="sm"
                  className="capitalize"
                >
                  {integration.status}
                </Chip>
                
                <span className="text-xs text-white/50">
                  Last sync: {integration.lastSync}
                </span>
              </div>

              <div className="flex gap-2">
                {integration.status === 'connected' ? (
                  <Button
                    size="sm"
                    variant="light"
                    onClick={() => handleSync(integration.id)}
                    className="flex-1 text-white/70 hover:text-white"
                    disabled={integration.lastSync === 'Syncing...'}
                  >
                    {integration.lastSync === 'Syncing...' ? 'Syncing...' : 'Sync Now'}
                  </Button>
                ) : (
                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    onClick={() => handleConnect(integration)}
                    className="flex-1"
                  >
                    Connect
                  </Button>
                )}
                
                <Tooltip content={`Open ${integration.name}`}>
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    onClick={() => window.open(integration.url, '_blank')}
                    className="text-white/70 hover:text-white"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </Tooltip>
              </div>
            </motion.div>
          ))}
        </div>
      </CardBody>
    </Card>
  );
};

export default IntegrationStatusMonitor;
