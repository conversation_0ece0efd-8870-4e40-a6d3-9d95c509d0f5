-- Fix the relationship between project_activities and users tables

-- Check if the project_activities table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'project_activities'
    ) THEN
        -- First drop the view if it exists to avoid column name conflicts
        DROP VIEW IF EXISTS public.project_activities_with_users;

        -- Create a view to expose the relationship between project_activities and users
        CREATE VIEW public.project_activities_with_users AS
        SELECT
            pa.*,
            u.id as user_account_id,
            u.email as user_email,
            u.display_name as user_display_name,
            u.avatar_url as user_avatar_url
        FROM
            public.project_activities pa
        LEFT JOIN
            public.users u ON pa.user_id = u.id;

        -- Grant permissions on the view
        GRANT SELECT ON public.project_activities_with_users TO authenticated;
        GRANT SELECT ON public.project_activities_with_users TO anon;

        RAISE NOTICE 'Created project_activities_with_users view';
    ELSE
        RAISE NOTICE 'project_activities table does not exist, skipping view creation';
    END IF;
END $$;
