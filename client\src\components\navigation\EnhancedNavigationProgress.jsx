import React from 'react';
import { motion } from 'framer-motion';
import { Progress, Card, CardBody, Chip, Button } from '@heroui/react';
import { useNavigation } from '../../contexts/NavigationContext';

/**
 * Enhanced NavigationProgress Component
 * 
 * Shows user progress through different navigation flows and journeys with:
 * - Real-time progress tracking
 * - Journey completion analytics
 * - Next step recommendations
 * - Achievement system integration
 * - Personalized progress insights
 */

const EnhancedNavigationProgress = ({ 
  currentCanvas = null,
  completedCanvases = [],
  totalCanvases = 0,
  onNavigateToNext,
  className = "" 
}) => {
  const { navigationHistory, sessionStartTime, navigationCount, favoriteCanvases } = useNavigation();

  // Calculate overall progress
  const progressPercentage = totalCanvases > 0 ? (completedCanvases.length / totalCanvases) * 100 : 0;

  // Enhanced journey definitions with progress tracking
  const journeys = {
    start: {
      title: 'Getting Started',
      icon: '🚀',
      color: 'primary',
      steps: ['profile', 'wizard', 'projects'],
      description: 'Set up your profile and create your first project',
      estimatedTime: '15 minutes',
      difficulty: 'Beginner'
    },
    track: {
      title: 'Track Progress',
      icon: '📊',
      color: 'secondary', 
      steps: ['contributions', 'validation', 'analytics'],
      description: 'Log work and track your contributions',
      estimatedTime: '10 minutes',
      difficulty: 'Intermediate'
    },
    earn: {
      title: 'Earn Revenue',
      icon: '💰',
      color: 'success',
      steps: ['revenue', 'escrow', 'royalty'],
      description: 'Set up payments and track earnings',
      estimatedTime: '20 minutes',
      difficulty: 'Advanced'
    },
    explore: {
      title: 'Explore Platform',
      icon: '🗺️',
      color: 'warning',
      steps: ['missions', 'teams', 'social', 'learn'],
      description: 'Discover all platform features',
      estimatedTime: '30 minutes',
      difficulty: 'Intermediate'
    }
  };

  // Get current journey based on canvas
  const getCurrentJourney = () => {
    for (const [key, journey] of Object.entries(journeys)) {
      if (journey.steps.includes(currentCanvas)) {
        return { key, ...journey };
      }
    }
    return null;
  };

  const currentJourney = getCurrentJourney();

  // Calculate journey progress
  const getJourneyProgress = (journey) => {
    const completedSteps = journey.steps.filter(step => completedCanvases.includes(step));
    return (completedSteps.length / journey.steps.length) * 100;
  };

  // Get next recommended step
  const getNextStep = (journey) => {
    const nextStep = journey.steps.find(step => !completedCanvases.includes(step));
    return nextStep;
  };

  // Calculate session stats
  const sessionDuration = Math.floor((Date.now() - sessionStartTime) / 1000 / 60); // minutes
  const averageTimePerNavigation = navigationCount > 0 ? sessionDuration / navigationCount : 0;

  // Get achievement status
  const getAchievements = () => {
    const achievements = [];
    
    if (completedCanvases.length >= 5) {
      achievements.push({ title: 'Explorer', icon: '🗺️', description: 'Visited 5+ sections' });
    }
    
    if (navigationCount >= 20) {
      achievements.push({ title: 'Navigator', icon: '🧭', description: '20+ navigations' });
    }
    
    if (favoriteCanvases.length >= 3) {
      achievements.push({ title: 'Curator', icon: '⭐', description: '3+ favorites' });
    }
    
    if (sessionDuration >= 30) {
      achievements.push({ title: 'Dedicated', icon: '⏰', description: '30+ minutes active' });
    }
    
    return achievements;
  };

  const achievements = getAchievements();

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`space-y-4 ${className}`}
    >
      {/* Overall Progress */}
      <Card className="bg-background/80 backdrop-blur-sm border border-divider">
        <CardBody className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium">Platform Exploration</h3>
            <div className="flex items-center gap-2">
              <span className="text-xs text-foreground-600">
                {completedCanvases.length}/{totalCanvases}
              </span>
              <Chip size="sm" variant="flat" color="primary">
                {Math.round(progressPercentage)}%
              </Chip>
            </div>
          </div>
          <Progress 
            value={progressPercentage} 
            className="w-full"
            color="primary"
            size="sm"
          />
          <div className="flex items-center justify-between mt-2 text-xs text-foreground-600">
            <span>{navigationCount} navigations</span>
            <span>{sessionDuration}m active</span>
          </div>
        </CardBody>
      </Card>

      {/* Current Journey Progress */}
      {currentJourney && (
        <Card className="bg-background/80 backdrop-blur-sm border border-divider">
          <CardBody className="p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <span className="text-lg">{currentJourney.icon}</span>
                <h3 className="text-sm font-medium">{currentJourney.title}</h3>
              </div>
              <Chip size="sm" variant="flat" color={currentJourney.color}>
                {currentJourney.difficulty}
              </Chip>
            </div>
            
            <p className="text-xs text-foreground-600 mb-3">
              {currentJourney.description}
            </p>
            
            <Progress 
              value={getJourneyProgress(currentJourney)} 
              className="w-full mb-2"
              color={currentJourney.color}
              size="sm"
            />
            
            <div className="flex items-center justify-between mb-3">
              <span className="text-xs text-foreground-600">
                {currentJourney.steps.filter(step => completedCanvases.includes(step)).length}/{currentJourney.steps.length} steps
              </span>
              <span className="text-xs text-foreground-600">
                ~{currentJourney.estimatedTime}
              </span>
            </div>

            {/* Next step recommendation */}
            {getNextStep(currentJourney) && (
              <div className="bg-primary/5 rounded-lg p-3 border border-primary/20">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs font-medium text-primary mb-1">Next Step</div>
                    <div className="text-xs text-foreground-600">
                      Continue to {getNextStep(currentJourney)}
                    </div>
                  </div>
                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    onClick={() => onNavigateToNext?.(getNextStep(currentJourney))}
                  >
                    Go →
                  </Button>
                </div>
              </div>
            )}
          </CardBody>
        </Card>
      )}

      {/* Journey Overview */}
      <div className="grid grid-cols-1 gap-2">
        {Object.entries(journeys).map(([key, journey]) => {
          const progress = getJourneyProgress(journey);
          const isActive = currentJourney?.key === key;
          const isCompleted = progress === 100;
          
          return (
            <motion.div
              key={key}
              className={`p-3 rounded-lg border transition-all ${
                isActive 
                  ? 'border-primary bg-primary/5' 
                  : isCompleted
                    ? 'border-success bg-success/5'
                    : 'border-divider bg-background/50'
              }`}
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm">{journey.icon}</span>
                  <span className="text-xs font-medium">{journey.title}</span>
                  {isCompleted && <span className="text-xs">✅</span>}
                </div>
                <span className="text-xs text-foreground-600">
                  {Math.round(progress)}%
                </span>
              </div>
              <Progress 
                value={progress} 
                className="w-full"
                color={isCompleted ? 'success' : journey.color}
                size="sm"
              />
            </motion.div>
          );
        })}
      </div>

      {/* Achievements */}
      {achievements.length > 0 && (
        <Card className="bg-background/80 backdrop-blur-sm border border-divider">
          <CardBody className="p-4">
            <h3 className="text-sm font-medium mb-3">Recent Achievements</h3>
            <div className="space-y-2">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.title}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center gap-2 p-2 bg-warning/5 rounded-lg border border-warning/20"
                >
                  <span className="text-sm">{achievement.icon}</span>
                  <div>
                    <div className="text-xs font-medium text-warning">{achievement.title}</div>
                    <div className="text-xs text-foreground-600">{achievement.description}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Session Stats */}
      <Card className="bg-background/80 backdrop-blur-sm border border-divider">
        <CardBody className="p-4">
          <h3 className="text-sm font-medium mb-3">Session Stats</h3>
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-primary">{navigationCount}</div>
              <div className="text-xs text-foreground-600">Navigations</div>
            </div>
            <div>
              <div className="text-lg font-bold text-secondary">{sessionDuration}m</div>
              <div className="text-xs text-foreground-600">Active Time</div>
            </div>
          </div>
          {averageTimePerNavigation > 0 && (
            <div className="mt-3 text-center">
              <div className="text-xs text-foreground-600">
                Avg: {averageTimePerNavigation.toFixed(1)}m per navigation
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default EnhancedNavigationProgress;
