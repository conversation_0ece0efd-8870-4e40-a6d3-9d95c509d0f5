import React, { useState } from 'react';
import { <PERSON>, CardBody, CardHeader, <PERSON><PERSON>, Chip } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * AI Insights Widget - 2x1 Bento Grid Component
 * 
 * Features:
 * - AI-powered analytics and recommendations
 * - Predictive insights and trend analysis
 * - Risk alerts and opportunity detection
 * - Actionable recommendations with confidence scores
 */
const AIInsights = ({ data, period, className = "" }) => {
  const [selectedInsightType, setSelectedInsightType] = useState('efficiency');

  // Mock AI insights data
  const aiInsights = {
    efficiency: {
      icon: '🎯',
      title: 'Efficiency Insights',
      insights: [
        {
          type: 'improvement',
          icon: '📈',
          text: 'Efficiency improved by +15% this month',
          confidence: 92,
          action: 'Continue current optimization strategies'
        },
        {
          type: 'speed',
          icon: '⚡',
          text: 'Average completion time: 8.2 days',
          confidence: 88,
          action: 'Target: Reduce to 7.5 days for +12% efficiency'
        }
      ]
    },
    opportunities: {
      icon: '💡',
      title: 'Growth Opportunities',
      insights: [
        {
          type: 'skill-gap',
          icon: '🔥',
          text: 'AI/ML skill gap limiting high-value bounties',
          confidence: 85,
          action: 'Launch AI learning path for +25% revenue potential'
        },
        {
          type: 'market',
          icon: '📱',
          text: 'Mobile usage declining -5% this month',
          confidence: 78,
          action: 'Implement mobile app improvements'
        }
      ]
    },
    risks: {
      icon: '⚠️',
      title: 'Risk Alerts',
      insights: [
        {
          type: 'engagement',
          icon: '📉',
          text: '3 projects showing declining engagement',
          confidence: 94,
          action: 'Implement project health monitoring system'
        },
        {
          type: 'capacity',
          icon: '⏰',
          text: 'Peak capacity reached in development category',
          confidence: 82,
          action: 'Consider expanding development team'
        }
      ]
    }
  };

  const currentInsights = aiInsights[selectedInsightType];

  // Get confidence color
  const getConfidenceColor = (confidence) => {
    if (confidence >= 90) return 'success';
    if (confidence >= 80) return 'primary';
    if (confidence >= 70) return 'warning';
    return 'danger';
  };

  // Get insight type color
  const getInsightTypeColor = (type) => {
    const colors = {
      'improvement': 'success',
      'speed': 'primary',
      'skill-gap': 'warning',
      'market': 'secondary',
      'engagement': 'danger',
      'capacity': 'warning'
    };
    return colors[type] || 'default';
  };

  return (
    <div className={`ai-insights ${className}`}>
      <Card className="bg-gradient-to-br from-violet-50 to-purple-100 dark:from-violet-900/20 dark:to-purple-800/20 border-2 border-violet-200 dark:border-violet-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💡</span>
              <h3 className="text-lg font-semibold">AI Insights</h3>
            </div>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant={selectedInsightType === 'efficiency' ? 'solid' : 'flat'}
                color={selectedInsightType === 'efficiency' ? 'primary' : 'default'}
                onClick={() => setSelectedInsightType('efficiency')}
              >
                🎯
              </Button>
              <Button
                size="sm"
                variant={selectedInsightType === 'opportunities' ? 'solid' : 'flat'}
                color={selectedInsightType === 'opportunities' ? 'primary' : 'default'}
                onClick={() => setSelectedInsightType('opportunities')}
              >
                💡
              </Button>
              <Button
                size="sm"
                variant={selectedInsightType === 'risks' ? 'solid' : 'flat'}
                color={selectedInsightType === 'risks' ? 'primary' : 'default'}
                onClick={() => setSelectedInsightType('risks')}
              >
                ⚠️
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Current Insight Type Header */}
          <div className="flex items-center gap-2 mb-4">
            <span className="text-xl">{currentInsights.icon}</span>
            <h4 className="font-semibold">{currentInsights.title}</h4>
          </div>

          {/* Insights List */}
          <div className="space-y-3">
            {currentInsights.insights.map((insight, index) => (
              <motion.div
                key={index}
                className="p-3 bg-white/50 dark:bg-slate-800/50 rounded-lg"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {/* Insight Header */}
                <div className="flex items-start gap-2 mb-2">
                  <span className="text-lg flex-shrink-0">{insight.icon}</span>
                  <div className="flex-1">
                    <p className="text-sm font-medium mb-1">{insight.text}</p>
                    <div className="flex items-center gap-2">
                      <Chip 
                        color={getConfidenceColor(insight.confidence)} 
                        variant="flat" 
                        size="sm"
                      >
                        {insight.confidence}% confidence
                      </Chip>
                      <Chip 
                        color={getInsightTypeColor(insight.type)} 
                        variant="bordered" 
                        size="sm"
                      >
                        {insight.type.replace('-', ' ')}
                      </Chip>
                    </div>
                  </div>
                </div>

                {/* Action Recommendation */}
                <div className="mt-2 p-2 bg-violet-50 dark:bg-violet-900/20 rounded text-xs">
                  <div className="flex items-center gap-1 mb-1">
                    <span>🎯</span>
                    <span className="font-medium">Recommended Action:</span>
                  </div>
                  <p className="text-violet-700 dark:text-violet-300">{insight.action}</p>
                </div>
              </motion.div>
            ))}
          </div>

          {/* AI Summary */}
          <div className="mt-4 p-3 bg-gradient-to-r from-violet-100 to-purple-100 dark:from-violet-900/30 dark:to-purple-900/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-lg">🤖</span>
              <span className="font-semibold text-sm">AI Summary</span>
            </div>
            <p className="text-xs text-violet-700 dark:text-violet-300">
              {selectedInsightType === 'efficiency' && 
                "Your platform efficiency is trending upward. Focus on reducing completion times to maximize growth potential."
              }
              {selectedInsightType === 'opportunities' && 
                "Two major growth opportunities identified. Addressing skill gaps could unlock significant revenue potential."
              }
              {selectedInsightType === 'risks' && 
                "Moderate risk levels detected. Proactive monitoring and intervention recommended for optimal performance."
              }
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mt-4">
            <Button size="sm" variant="flat" className="flex-1">
              View All
            </Button>
            <Button size="sm" variant="flat" className="flex-1">
              AI Recs
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default AIInsights;
