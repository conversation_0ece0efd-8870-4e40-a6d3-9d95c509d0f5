import { test, expect } from '@playwright/test';

// Debug test to understand what's actually on the page
test.describe('Debug Page Elements', () => {
  test('Debug Dashboard Elements', async ({ page }) => {
    console.log('🔍 Starting debug test...');
    
    // Go to dashboard
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take screenshot
    await page.screenshot({ path: 'test-results/debug-dashboard.png', fullPage: true });
    console.log('📸 Dashboard screenshot saved');
    
    // Get page title and URL
    const title = await page.title();
    const url = page.url();
    console.log(`📄 Page title: ${title}`);
    console.log(`🌐 Page URL: ${url}`);
    
    // Check if we're authenticated by looking for login elements
    const hasEmailInput = await page.locator('input[type="email"]').isVisible();
    const hasPasswordInput = await page.locator('input[type="password"]').isVisible();
    console.log(`🔐 Has login form: ${hasEmailInput && hasPasswordInput}`);
    
    if (hasEmailInput && hasPasswordInput) {
      console.log('❌ NOT AUTHENTICATED - Login form visible');
      return;
    }
    
    console.log('✅ AUTHENTICATED - No login form');
    
    // Debug: Get all buttons on the page
    const allButtons = await page.locator('button').all();
    console.log(`🔘 Found ${allButtons.length} buttons on page`);
    
    for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
      const button = allButtons[i];
      const text = await button.textContent();
      const isVisible = await button.isVisible();
      const classes = await button.getAttribute('class');
      console.log(`  Button ${i + 1}: "${text}" (visible: ${isVisible}) classes: ${classes}`);
    }
    
    // Debug: Look for specific text content
    const pageText = await page.locator('body').textContent();
    const hasStartText = pageText.includes('Start');
    const hasTrackText = pageText.includes('Track');
    const hasEarnText = pageText.includes('Earn');
    const hasProjectWizardText = pageText.includes('Project Wizard');
    const hasStartProjectWizardText = pageText.includes('Start Project Wizard');
    
    console.log(`📝 Page contains:`);
    console.log(`  - "Start": ${hasStartText}`);
    console.log(`  - "Track": ${hasTrackText}`);
    console.log(`  - "Earn": ${hasEarnText}`);
    console.log(`  - "Project Wizard": ${hasProjectWizardText}`);
    console.log(`  - "Start Project Wizard": ${hasStartProjectWizardText}`);
    
    // Debug: Look for navigation elements
    const navElements = await page.locator('nav').all();
    console.log(`🧭 Found ${navElements.length} nav elements`);
    
    // Debug: Look for links
    const allLinks = await page.locator('a').all();
    console.log(`🔗 Found ${allLinks.length} links on page`);
    
    for (let i = 0; i < Math.min(allLinks.length, 10); i++) {
      const link = allLinks[i];
      const text = await link.textContent();
      const href = await link.getAttribute('href');
      const isVisible = await link.isVisible();
      console.log(`  Link ${i + 1}: "${text}" -> ${href} (visible: ${isVisible})`);
    }
    
    // Debug: Look for elements with specific text
    const startElements = await page.locator('text=Start').all();
    const trackElements = await page.locator('text=Track').all();
    const earnElements = await page.locator('text=Earn').all();
    
    console.log(`🎯 Text-based element counts:`);
    console.log(`  - "Start" elements: ${startElements.length}`);
    console.log(`  - "Track" elements: ${trackElements.length}`);
    console.log(`  - "Earn" elements: ${earnElements.length}`);
    
    // Check visibility of these elements
    for (let i = 0; i < startElements.length; i++) {
      const isVisible = await startElements[i].isVisible();
      const tagName = await startElements[i].evaluate(el => el.tagName);
      console.log(`  Start element ${i + 1}: ${tagName} (visible: ${isVisible})`);
    }
    
    // Debug: Look for profile-related elements
    const profileSelectors = [
      'button:has-text("the")',
      '[data-testid="user-menu"]',
      '.user-menu',
      'button[aria-label*="profile"]',
      'button[aria-label*="user"]',
      '[class*="profile"]',
      '[class*="user"]',
      '[class*="avatar"]'
    ];
    
    console.log(`👤 Profile element search:`);
    for (const selector of profileSelectors) {
      const count = await page.locator(selector).count();
      if (count > 0) {
        const isVisible = await page.locator(selector).first().isVisible();
        console.log(`  ${selector}: ${count} found (first visible: ${isVisible})`);
      } else {
        console.log(`  ${selector}: 0 found`);
      }
    }
    
    // Debug: Get all elements with text content in top right area
    const topRightElements = await page.locator('body *').evaluateAll(elements => {
      return elements
        .filter(el => {
          const rect = el.getBoundingClientRect();
          const text = el.textContent?.trim();
          return text && 
                 text.length > 0 && 
                 text.length < 50 && 
                 rect.right > window.innerWidth * 0.7 && 
                 rect.top < window.innerHeight * 0.2;
        })
        .map(el => ({
          text: el.textContent?.trim(),
          tagName: el.tagName,
          className: el.className,
          id: el.id
        }))
        .slice(0, 10);
    });
    
    console.log(`🔝 Top-right elements:`);
    topRightElements.forEach((el, i) => {
      console.log(`  ${i + 1}: "${el.text}" (${el.tagName}) class: ${el.className} id: ${el.id}`);
    });
    
    console.log('✅ Debug test completed');
  });
});
