/**
 * Output Validator - Comprehensive Agreement Validation
 * 
 * Validates generated agreements against lawyer-approved template
 * and ensures legal accuracy and completeness.
 */

import { ValidationError, AccuracyError, LegalComplianceError, ErrorFactory } from './errors/AgreementErrors.js';

export class OutputValidator {
  constructor() {
    this.lawyerTemplate = null;
    this.requiredSections = [
      'CONTRIBUTOR AGREEMENT',
      'Recitals',
      '1. Definitions',
      '2. Treatment of Confidential Information',
      '3. Ownership of Work Product',
      '4. Non-Disparagement',
      '5. Termination',
      '6. Equitable Remedies',
      '7. Assignment',
      '8. Waivers and Amendments',
      '9. Survival',
      '10. Status as Independent Contractor',
      '11. Representations and Warranties',
      '12. Indemnification',
      '13. Entire Agreement',
      '14. Governing Law',
      '15. Consent to Jurisdiction',
      '16. Settlement of Disputes',
      '17. Titles and Subtitles',
      '18. Opportunity to Consult',
      '19. Gender; Singular and Plural',
      '20. Notice',
      '21. Counterparts',
      'SCHEDULE A',
      'SCHEDULE B'
    ];

    this.criticalElements = [
      'COMPANY:',
      'CONTRIBUTOR:',
      'Description of Services',
      'Description of Consideration',
      'IN WITNESS WHEREOF'
    ];
  }

  /**
   * Validate generated agreement output
   * @param {string} generatedAgreement - Generated agreement content
   * @param {string} template - Original template used
   * @param {Object} data - Input data used for generation
   * @returns {Promise<Object>} Validation results with accuracy score
   * @throws {ValidationError} If critical validation issues found
   */
  async validateOutput(generatedAgreement, template, data) {
    try {
      const validationResults = {
        accuracyScore: 0,
        completenessScore: 0,
        issues: [],
        criticalIssues: [],
        warnings: [],
        sections: {},
        placeholders: [],
        legalCompliance: {}
      };

      // Step 1: Load lawyer template for comparison
      await this._loadLawyerTemplate();

      // Step 2: Check for unreplaced placeholders (CRITICAL)
      this._validateNoPlaceholders(generatedAgreement, validationResults);

      // Step 3: Validate required sections (CRITICAL)
      this._validateRequiredSections(generatedAgreement, validationResults);

      // Step 4: Validate critical elements (CRITICAL)
      this._validateCriticalElements(generatedAgreement, validationResults);

      // Step 5: Validate data integration (CRITICAL)
      this._validateDataIntegration(generatedAgreement, data, validationResults);

      // Step 6: Validate legal compliance
      this._validateLegalCompliance(generatedAgreement, validationResults);

      // Step 7: Validate structure and formatting
      this._validateStructure(generatedAgreement, validationResults);

      // Step 8: Calculate accuracy scores
      this._calculateAccuracyScores(validationResults);

      // Step 9: Check if validation passes
      if (validationResults.criticalIssues.length > 0) {
        throw new ValidationError(
          `Critical validation issues found: ${validationResults.criticalIssues.length} issue(s)`,
          validationResults.criticalIssues
        );
      }

      return {
        content: generatedAgreement,
        validation: validationResults
      };

    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError(
        'Output validation failed',
        [{ message: error.message, stack: error.stack }]
      );
    }
  }

  // Private validation methods

  /**
   * Load lawyer-approved template for comparison
   * @private
   */
  async _loadLawyerTemplate() {
    if (this.lawyerTemplate) return;

    try {
      if (typeof window !== 'undefined') {
        const response = await fetch('/example-cog-contributor-agreement.md');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        this.lawyerTemplate = await response.text();
      } else {
        const fs = await import('fs');
        const path = await import('path');
        const templatePath = path.join(process.cwd(), 'client/public/example-cog-contributor-agreement.md');
        this.lawyerTemplate = fs.readFileSync(templatePath, 'utf8');
      }
    } catch (error) {
      console.warn('Could not load lawyer template for comparison:', error.message);
      this.lawyerTemplate = ''; // Continue without template comparison
    }
  }

  /**
   * Validate no unreplaced placeholders remain
   * @private
   */
  _validateNoPlaceholders(content, results) {
    const placeholderPatterns = [
      /\{\{[A-Z_]+\}\}/g,           // {{VARIABLE}} format
      /\{\{#IF\s+[A-Z_]+\}\}/g,    // {{#IF CONDITION}} format
      /\{\{\/IF\}\}/g,             // {{/IF}} format
      /\[[A-Z][^\]]*\]/g,          // [OLD STYLE] format
      /\[_+\]/g,                   // [____] format
      /\[Company[^\]]*\]/gi,       // [Company...] format
      /\[Project[^\]]*\]/gi,       // [Project...] format
      /\[Contributor[^\]]*\]/gi    // [Contributor...] format
    ];

    placeholderPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          results.placeholders.push(match);
          results.criticalIssues.push({
            type: 'UNREPLACED_PLACEHOLDER',
            severity: 'CRITICAL',
            message: `Unreplaced placeholder found: ${match}`,
            placeholder: match
          });
        });
      }
    });
  }

  /**
   * Validate all required sections are present
   * @private
   */
  _validateRequiredSections(content, results) {
    this.requiredSections.forEach(section => {
      const found = content.includes(section);
      results.sections[section] = found;
      
      if (!found) {
        results.criticalIssues.push({
          type: 'MISSING_SECTION',
          severity: 'CRITICAL',
          message: `Required section missing: ${section}`,
          section: section
        });
      }
    });
  }

  /**
   * Validate critical elements are present
   * @private
   */
  _validateCriticalElements(content, results) {
    this.criticalElements.forEach(element => {
      if (!content.includes(element)) {
        results.criticalIssues.push({
          type: 'MISSING_CRITICAL_ELEMENT',
          severity: 'CRITICAL',
          message: `Critical element missing: ${element}`,
          element: element
        });
      }
    });
  }

  /**
   * Validate data integration - ensure user data appears correctly
   * @private
   */
  _validateDataIntegration(content, data, results) {
    const requiredDataElements = [
      { field: 'company.name', value: data.company.name, label: 'Company Name' },
      { field: 'company.address', value: data.company.address, label: 'Company Address' },
      { field: 'company.state', value: data.company.state, label: 'Company State' },
      { field: 'company.signerName', value: data.company.signerName, label: 'Signer Name' },
      { field: 'company.signerTitle', value: data.company.signerTitle, label: 'Signer Title' },
      { field: 'company.billingEmail', value: data.company.billingEmail, label: 'Billing Email' },
      { field: 'project.name', value: data.project.name, label: 'Project Name' },
      { field: 'project.description', value: data.project.description, label: 'Project Description' },
      { field: 'contributor.name', value: data.contributor.name, label: 'Contributor Name' },
      { field: 'contributor.email', value: data.contributor.email, label: 'Contributor Email' }
    ];

    requiredDataElements.forEach(element => {
      if (!content.includes(element.value)) {
        results.criticalIssues.push({
          type: 'MISSING_DATA_INTEGRATION',
          severity: 'CRITICAL',
          message: `${element.label} not found in agreement: ${element.value}`,
          field: element.field,
          expectedValue: element.value
        });
      }
    });

    // Check for forbidden hardcoded content
    const forbiddenContent = [
      { content: 'City of Gamers Inc.', expected: data.company.name },
      { content: 'Village of The Ages', expected: data.project.name },
      { content: 'Gynell Journigan', expected: data.company.signerName },
      { content: '1205 43rd Street', expected: 'user address' },
      { content: 'Orange County', expected: 'user location' },
      { content: '32839', expected: 'user zip code' }
    ];

    forbiddenContent.forEach(item => {
      if (content.includes(item.content)) {
        results.criticalIssues.push({
          type: 'HARDCODED_CONTENT',
          severity: 'CRITICAL',
          message: `Hardcoded content found: "${item.content}" should be "${item.expected}"`,
          hardcodedContent: item.content,
          expectedContent: item.expected
        });
      }
    });
  }

  /**
   * Validate legal compliance requirements
   * @private
   */
  _validateLegalCompliance(content, results) {
    const legalRequirements = [
      {
        name: 'Signature Blocks',
        check: () => content.includes('COMPANY:') && content.includes('CONTRIBUTOR:'),
        message: 'Proper signature blocks required'
      },
      {
        name: 'Effective Date',
        check: () => /effective as of .+, \d{4}/.test(content),
        message: 'Proper effective date format required'
      },
      {
        name: 'Governing Law',
        check: () => content.includes('governed by and construed in accordance with the laws'),
        message: 'Governing law clause required'
      },
      {
        name: 'Jurisdiction',
        check: () => content.includes('jurisdiction and venue'),
        message: 'Jurisdiction clause required'
      },
      {
        name: 'Entire Agreement',
        check: () => content.includes('entire agreement and understanding'),
        message: 'Entire agreement clause required'
      }
    ];

    legalRequirements.forEach(requirement => {
      const passed = requirement.check();
      results.legalCompliance[requirement.name] = passed;
      
      if (!passed) {
        results.criticalIssues.push({
          type: 'LEGAL_COMPLIANCE_ISSUE',
          severity: 'CRITICAL',
          message: requirement.message,
          requirement: requirement.name
        });
      }
    });
  }

  /**
   * Validate document structure and formatting
   * @private
   */
  _validateStructure(content, results) {
    // Check for proper markdown formatting
    if (!content.includes('# ')) {
      results.warnings.push({
        type: 'FORMATTING_ISSUE',
        severity: 'LOW',
        message: 'Document should use proper markdown headers'
      });
    }

    // Check for proper section numbering
    const numberedSections = content.match(/^\d+\.\s+/gm);
    if (!numberedSections || numberedSections.length < 20) {
      results.warnings.push({
        type: 'STRUCTURE_ISSUE',
        severity: 'MEDIUM',
        message: 'Document may be missing numbered sections'
      });
    }

    // Check for schedules
    if (!content.includes('SCHEDULE A') || !content.includes('SCHEDULE B')) {
      results.criticalIssues.push({
        type: 'MISSING_SCHEDULES',
        severity: 'CRITICAL',
        message: 'Both Schedule A and Schedule B are required'
      });
    }
  }

  /**
   * Calculate accuracy and completeness scores
   * @private
   */
  _calculateAccuracyScores(results) {
    // Accuracy score based on critical issues
    const totalCriticalChecks = this.requiredSections.length + 
                               this.criticalElements.length + 
                               Object.keys(results.legalCompliance).length + 
                               10; // Additional critical checks

    const passedCriticalChecks = totalCriticalChecks - results.criticalIssues.length;
    results.accuracyScore = Math.max(0, Math.round((passedCriticalChecks / totalCriticalChecks) * 100));

    // Completeness score based on sections and elements
    const totalSections = this.requiredSections.length;
    const presentSections = Object.values(results.sections).filter(Boolean).length;
    results.completenessScore = Math.round((presentSections / totalSections) * 100);

    // Overall quality score
    results.overallScore = Math.round((results.accuracyScore + results.completenessScore) / 2);
  }

  /**
   * Get validation summary
   */
  getValidationSummary(results) {
    return {
      passed: results.criticalIssues.length === 0,
      accuracyScore: results.accuracyScore,
      completenessScore: results.completenessScore,
      overallScore: results.overallScore,
      totalIssues: results.issues.length + results.criticalIssues.length + results.warnings.length,
      criticalIssues: results.criticalIssues.length,
      warnings: results.warnings.length,
      readyForProduction: results.accuracyScore >= 95 && results.criticalIssues.length === 0
    };
  }
}
