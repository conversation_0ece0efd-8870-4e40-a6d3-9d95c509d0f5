import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from '../layout/LoadingAnimation';

/**
 * TeamList component displays a list of teams the user belongs to
 * and allows creating new teams
 */
const TeamList = () => {
  const { currentUser } = useContext(UserContext);
  const [teams, setTeams] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newTeam, setNewTeam] = useState({
    name: '',
    description: ''
  });

  // Fetch teams on component mount
  useEffect(() => {
    fetchTeams();
  }, [currentUser]);

  // Fetch teams the user belongs to
  const fetchTeams = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);

      // Get teams where the user is a member
      const { data: teamMembers, error: memberError } = await supabase
        .from('team_members')
        .select('team_id, role, is_admin')
        .eq('user_id', currentUser.id);

      if (memberError) throw memberError;

      if (teamMembers && teamMembers.length > 0) {
        // Get team details
        const teamIds = teamMembers.map(member => member.team_id);
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .select('*, team_members(count)')
          .in('id', teamIds);

        if (teamError) throw teamError;

        // Combine team data with member role
        const teamsWithRole = teamData.map(team => {
          const membership = teamMembers.find(member => member.team_id === team.id);
          return {
            ...team,
            role: membership?.role || 'member',
            is_admin: membership?.is_admin || false,
            member_count: team.team_members[0]?.count || 0
          };
        });

        setTeams(teamsWithRole);
      } else {
        setTeams([]);
      }
    } catch (error) {
      console.error('Error fetching teams:', error);
      toast.error('Failed to load teams');
    } finally {
      setLoading(false);
    }
  };

  // Handle creating a new team
  const handleCreateTeam = async (e) => {
    e.preventDefault();

    if (!newTeam.name.trim()) {
      toast.error('Team name is required');
      return;
    }

    try {
      setLoading(true);

      // Create the team
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .insert([{
          name: newTeam.name,
          description: newTeam.description,
          created_by: currentUser.id
        }])
        .select()
        .single();

      if (teamError) throw teamError;

      // Add the creator as an admin member
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: teamData.id,
          user_id: currentUser.id,
          role: 'owner',
          is_admin: true
        }]);

      if (memberError) throw memberError;

      toast.success('Team created successfully');
      setNewTeam({ name: '', description: '' });
      setShowCreateForm(false);
      fetchTeams();
    } catch (error) {
      console.error('Error creating team:', error);
      toast.error('Failed to create team');
    } finally {
      setLoading(false);
    }
  };

  if (loading && teams.length === 0) {
    return <LoadingAnimation />;
  }

  return (
    <div className="team-list-container">
      <div className="team-list-header">
        <h2>Your Teams</h2>
        <button
          className="create-team-button"
          onClick={() => setShowCreateForm(!showCreateForm)}
        >
          {showCreateForm ? 'Cancel' : 'Create Team'}
        </button>
      </div>

      {showCreateForm && (
        <div className="create-team-form">
          <h3>Create New Team</h3>
          <form onSubmit={handleCreateTeam}>
            <div className="form-group">
              <label htmlFor="team-name">Team Name</label>
              <input
                id="team-name"
                type="text"
                value={newTeam.name}
                onChange={(e) => setNewTeam({ ...newTeam, name: e.target.value })}
                placeholder="Enter team name"
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="team-description">Description (optional)</label>
              <textarea
                id="team-description"
                value={newTeam.description}
                onChange={(e) => setNewTeam({ ...newTeam, description: e.target.value })}
                placeholder="Enter team description"
                rows="3"
              />
            </div>
            <div className="form-actions">
              <button type="submit" className="submit-button">Create Team</button>
              <button
                type="button"
                className="cancel-button"
                onClick={() => setShowCreateForm(false)}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {teams.length > 0 ? (
        <div className="teams-grid">
          {teams.map(team => (
            <div key={team.id} className="team-card">
              <div className="team-card-header">
                <h3>{team.name}</h3>
                {team.is_admin && <span className="admin-badge">Admin</span>}
              </div>
              <p className="team-description">{team.description || 'No description'}</p>
              <div className="team-meta">
                <span className="member-count">{team.member_count} members</span>
                <span className="created-date">Created {new Date(team.created_at).toLocaleDateString()}</span>
              </div>
              <div className="team-actions">
                <Link to={`/teams/${team.id}`} className="view-team-button">
                  View Team
                </Link>
                {team.is_admin && (
                  <Link to={`/teams/${team.id}/manage`} className="manage-team-button">
                    Manage Team
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="no-teams">
          <p>You don't belong to any teams yet.</p>
          <button
            className="create-first-team-button"
            onClick={() => setShowCreateForm(true)}
          >
            Create Your First Team
          </button>
        </div>
      )}
    </div>
  );
};

export default TeamList;
