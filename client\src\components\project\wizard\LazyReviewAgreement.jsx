import React, { lazy } from 'react';
import SafeLazyComponentWrapper from '../../common/LazyComponentWrapper';

/**
 * Lazy-loaded Review Agreement Component
 * 
 * This component lazy-loads the large ReviewAgreement component to improve
 * initial bundle size and page load performance.
 * 
 * Task: O3 Performance Optimization - Component Lazy Loading
 */

// Lazy load the ReviewAgreement component
const ReviewAgreement = lazy(() => import('./ReviewAgreement'));

const LazyReviewAgreement = (props) => {
  return (
    <SafeLazyComponentWrapper
      className="w-full"
      minHeight="400px"
      showSpinner={true}
    >
      <ReviewAgreement {...props} />
    </SafeLazyComponentWrapper>
  );
};

export default LazyReviewAgreement;
