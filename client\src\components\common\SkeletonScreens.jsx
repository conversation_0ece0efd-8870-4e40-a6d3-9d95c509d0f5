import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader } from '@heroui/react';

/**
 * Skeleton Screens Component
 * 
 * Provides optimized loading states and skeleton screens for better perceived performance.
 * Includes various skeleton patterns for different content types.
 */

// Base skeleton animation
const skeletonAnimation = {
  animate: {
    opacity: [0.5, 1, 0.5],
  },
  transition: {
    duration: 1.5,
    repeat: Infinity,
    ease: "easeInOut"
  }
};

// Generic skeleton element
const SkeletonElement = ({ width = "100%", height = "1rem", className = "", ...props }) => (
  <motion.div
    className={`bg-default-200 rounded ${className}`}
    style={{ width, height }}
    {...skeletonAnimation}
    {...props}
  />
);

// Contribution card skeleton
export const ContributionCardSkeleton = ({ className = "" }) => (
  <Card className={`border border-divider ${className}`}>
    <CardBody className="p-4">
      <div className="space-y-3">
        <div className="flex items-start justify-between">
          <SkeletonElement width="60%" height="1.25rem" />
          <SkeletonElement width="4rem" height="1.5rem" className="rounded-full" />
        </div>
        <SkeletonElement width="100%" height="0.875rem" />
        <SkeletonElement width="80%" height="0.875rem" />
        <div className="flex items-center justify-between pt-2">
          <SkeletonElement width="3rem" height="1rem" />
          <SkeletonElement width="5rem" height="1rem" />
        </div>
      </div>
    </CardBody>
  </Card>
);

// Project card skeleton
export const ProjectCardSkeleton = ({ className = "" }) => (
  <Card className={`border border-divider ${className}`}>
    <CardHeader className="pb-2">
      <div className="flex items-center justify-between w-full">
        <SkeletonElement width="50%" height="1.5rem" />
        <SkeletonElement width="4rem" height="1.25rem" className="rounded-full" />
      </div>
    </CardHeader>
    <CardBody className="pt-0">
      <div className="space-y-4">
        <SkeletonElement width="100%" height="0.875rem" />
        <SkeletonElement width="90%" height="0.875rem" />
        
        <div className="grid grid-cols-3 gap-4 pt-2">
          <div className="text-center">
            <SkeletonElement width="2rem" height="1.5rem" className="mx-auto mb-1" />
            <SkeletonElement width="100%" height="0.75rem" />
          </div>
          <div className="text-center">
            <SkeletonElement width="2rem" height="1.5rem" className="mx-auto mb-1" />
            <SkeletonElement width="100%" height="0.75rem" />
          </div>
          <div className="text-center">
            <SkeletonElement width="2rem" height="1.5rem" className="mx-auto mb-1" />
            <SkeletonElement width="100%" height="0.75rem" />
          </div>
        </div>
        
        <SkeletonElement width="100%" height="0.5rem" className="rounded-full" />
      </div>
    </CardBody>
  </Card>
);

// Dashboard metrics skeleton
export const MetricsCardSkeleton = ({ className = "" }) => (
  <Card className={`border border-divider ${className}`}>
    <CardBody className="p-6 text-center">
      <div className="space-y-3">
        <SkeletonElement width="3rem" height="3rem" className="mx-auto rounded-lg" />
        <SkeletonElement width="4rem" height="2rem" className="mx-auto" />
        <SkeletonElement width="6rem" height="0.875rem" className="mx-auto" />
      </div>
    </CardBody>
  </Card>
);

// Chart skeleton
export const ChartSkeleton = ({ className = "" }) => (
  <Card className={`border border-divider ${className}`}>
    <CardHeader>
      <div className="flex items-center justify-between w-full">
        <SkeletonElement width="8rem" height="1.25rem" />
        <div className="flex gap-2">
          <SkeletonElement width="2rem" height="1.5rem" className="rounded" />
          <SkeletonElement width="2rem" height="1.5rem" className="rounded" />
          <SkeletonElement width="2rem" height="1.5rem" className="rounded" />
        </div>
      </div>
    </CardHeader>
    <CardBody>
      <div className="space-y-4">
        <div className="h-48 bg-default-100 rounded relative overflow-hidden">
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
            animate={{ x: [-100, 400] }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          />
          {/* Chart bars simulation */}
          <div className="absolute bottom-0 left-0 right-0 flex items-end justify-around p-4">
            {[40, 60, 35, 80, 45, 70, 55].map((height, index) => (
              <SkeletonElement
                key={index}
                width="1rem"
                height={`${height}%`}
                className="rounded-t"
              />
            ))}
          </div>
        </div>
        
        <div className="flex items-center justify-center gap-6">
          <div className="flex items-center gap-2">
            <SkeletonElement width="1rem" height="0.25rem" />
            <SkeletonElement width="4rem" height="0.875rem" />
          </div>
          <div className="flex items-center gap-2">
            <SkeletonElement width="1rem" height="0.25rem" />
            <SkeletonElement width="3rem" height="0.875rem" />
          </div>
        </div>
      </div>
    </CardBody>
  </Card>
);

// Table skeleton
export const TableSkeleton = ({ rows = 5, columns = 4, className = "" }) => (
  <Card className={`border border-divider ${className}`}>
    <CardHeader>
      <SkeletonElement width="10rem" height="1.25rem" />
    </CardHeader>
    <CardBody>
      <div className="space-y-3">
        {/* Table header */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <SkeletonElement key={index} width="80%" height="1rem" />
          ))}
        </div>
        
        {/* Table rows */}
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, colIndex) => (
              <SkeletonElement 
                key={colIndex} 
                width={colIndex === 0 ? "100%" : "60%"} 
                height="0.875rem" 
              />
            ))}
          </div>
        ))}
      </div>
    </CardBody>
  </Card>
);

// Form skeleton
export const FormSkeleton = ({ fields = 4, className = "" }) => (
  <Card className={`border border-divider ${className}`}>
    <CardHeader>
      <SkeletonElement width="8rem" height="1.5rem" />
    </CardHeader>
    <CardBody>
      <div className="space-y-6">
        {Array.from({ length: fields }).map((_, index) => (
          <div key={index} className="space-y-2">
            <SkeletonElement width="6rem" height="1rem" />
            <SkeletonElement width="100%" height="2.5rem" className="rounded-lg" />
          </div>
        ))}
        
        <div className="flex justify-end gap-3 pt-4">
          <SkeletonElement width="4rem" height="2.5rem" className="rounded-lg" />
          <SkeletonElement width="5rem" height="2.5rem" className="rounded-lg" />
        </div>
      </div>
    </CardBody>
  </Card>
);

// Navigation skeleton
export const NavigationSkeleton = ({ className = "" }) => (
  <div className={`space-y-4 ${className}`}>
    <div className="flex items-center justify-between">
      <SkeletonElement width="8rem" height="2rem" />
      <SkeletonElement width="2.5rem" height="2.5rem" className="rounded-full" />
    </div>
    
    <div className="flex gap-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <SkeletonElement key={index} width="6rem" height="2.5rem" className="rounded-lg" />
      ))}
    </div>
  </div>
);

// Canvas skeleton for full page loading
export const CanvasSkeleton = ({ type = "default", className = "" }) => {
  const skeletonContent = {
    track: (
      <div className="space-y-8">
        <NavigationSkeleton />
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <FormSkeleton fields={3} />
          </div>
          <div>
            <MetricsCardSkeleton />
          </div>
        </div>
      </div>
    ),
    earn: (
      <div className="space-y-8">
        <NavigationSkeleton />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <MetricsCardSkeleton key={index} />
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <ChartSkeleton />
          <TableSkeleton rows={6} columns={3} />
        </div>
      </div>
    ),
    project: (
      <div className="space-y-8">
        <NavigationSkeleton />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <ProjectCardSkeleton key={index} />
          ))}
        </div>
        <ChartSkeleton />
      </div>
    ),
    default: (
      <div className="space-y-8">
        <NavigationSkeleton />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <FormSkeleton />
          <MetricsCardSkeleton />
        </div>
      </div>
    )
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6 ${className}`}>
      <div className="container mx-auto max-w-6xl">
        {skeletonContent[type] || skeletonContent.default}
      </div>
    </div>
  );
};

// List skeleton for paginated content
export const ListSkeleton = ({ items = 5, type = "contribution", className = "" }) => {
  const ItemSkeleton = type === "project" ? ProjectCardSkeleton : ContributionCardSkeleton;
  
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: items }).map((_, index) => (
        <ItemSkeleton key={index} />
      ))}
    </div>
  );
};

// Progressive loading skeleton that adapts to content
export const ProgressiveLoadingSkeleton = ({ 
  stage = "initial", 
  contentType = "default",
  className = "" 
}) => {
  const stages = {
    initial: <SkeletonElement width="100%" height="2rem" />,
    loading: <CanvasSkeleton type={contentType} />,
    error: (
      <div className="text-center p-8">
        <SkeletonElement width="4rem" height="4rem" className="mx-auto mb-4 rounded-full" />
        <SkeletonElement width="12rem" height="1.5rem" className="mx-auto mb-2" />
        <SkeletonElement width="8rem" height="1rem" className="mx-auto" />
      </div>
    )
  };

  return (
    <div className={className}>
      {stages[stage] || stages.initial}
    </div>
  );
};

export default SkeletonScreens;
