import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Avatar } from '@heroui/react';

/**
 * Bounty Card Component - Individual Bounty Display
 * 
 * Features:
 * - High-value bounty presentation with premium styling
 * - Skill requirements and difficulty indicators
 * - Application count and deadline tracking
 * - Poster information with verification status
 * - Quick application and detailed view actions
 */
const BountyCard = ({ 
  bounty, 
  index, 
  onApply, 
  onView, 
  currentUser, 
  formatCurrency, 
  getDifficultyColor, 
  getTimeSincePosted, 
  getTimeUntilDeadline 
}) => {
  // Get category icon
  const getCategoryIcon = (category) => {
    const icons = {
      'development': '💻',
      'design': '🎨',
      'testing': '🧪',
      'writing': '✍️',
      'marketing': '📈'
    };
    return icons[category] || '💼';
  };

  // Get urgency indicator
  const getUrgencyLevel = (deadline) => {
    const now = new Date();
    const diffMs = deadline - now;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffHours <= 24) return 'urgent';
    if (diffHours <= 72) return 'soon';
    return 'normal';
  };

  const urgencyLevel = getUrgencyLevel(bounty.deadline);
  const isUrgent = urgencyLevel === 'urgent';
  const isSoon = urgencyLevel === 'soon';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      className="h-full"
    >
      <Card className={`h-full hover:shadow-lg transition-shadow duration-200 ${
        bounty.featured ? 'ring-2 ring-orange-400' : ''
      } ${isUrgent ? 'ring-2 ring-red-500' : ''}`}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                {bounty.featured && <span className="text-yellow-500">⭐</span>}
                {isUrgent && <span className="text-red-500">🔥</span>}
                {isSoon && <span className="text-orange-500">⚡</span>}
                <h3 className="text-lg font-semibold line-clamp-2">
                  {bounty.title}
                </h3>
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Chip
                  color="primary"
                  size="sm"
                  variant="flat"
                  startContent={getCategoryIcon(bounty.category)}
                >
                  {bounty.category}
                </Chip>
                <Chip
                  color={getDifficultyColor(bounty.difficulty)}
                  size="sm"
                  variant="flat"
                >
                  Level {bounty.difficulty}
                </Chip>
              </div>
            </div>
            <div className="text-right">
              <div className="text-xl font-bold text-green-600">
                {formatCurrency(bounty.value)}
              </div>
              <div className="text-xs text-default-500">
                {bounty.paymentType}
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Poster Info */}
          <div className="flex items-center gap-2 mb-3">
            <Avatar
              name={bounty.poster.name}
              size="sm"
            />
            <div>
              <div className="text-sm font-medium">{bounty.poster.name}</div>
              <div className="text-xs text-default-500">
                ⭐ {bounty.poster.rating} ({bounty.poster.reviewCount} reviews)
                {bounty.poster.verified && <span className="text-green-500 ml-1">✓</span>}
              </div>
            </div>
          </div>
          
          {/* Bounty Description */}
          <p className="text-sm text-default-600 line-clamp-3 mb-3">
            {bounty.description}
          </p>
          
          {/* Skills Required */}
          {bounty.skillsRequired.length > 0 && (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">Skills Required:</div>
              <div className="flex flex-wrap gap-1">
                {bounty.skillsRequired.slice(0, 3).map((skill, idx) => (
                  <Chip key={idx} size="sm" variant="bordered" className="text-xs">
                    {skill}
                  </Chip>
                ))}
                {bounty.skillsRequired.length > 3 && (
                  <Chip size="sm" variant="bordered" className="text-xs">
                    +{bounty.skillsRequired.length - 3}
                  </Chip>
                )}
              </div>
            </div>
          )}
          
          {/* Requirements Indicators */}
          {bounty.requirements && (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">Requirements:</div>
              <div className="flex flex-wrap gap-1">
                {bounty.requirements.portfolio && (
                  <Chip size="sm" color="secondary" variant="flat" className="text-xs">
                    Portfolio
                  </Chip>
                )}
                {bounty.requirements.skillVerification && (
                  <Chip size="sm" color="primary" variant="flat" className="text-xs">
                    Verified Skills
                  </Chip>
                )}
                {bounty.requirements.certification && (
                  <Chip size="sm" color="success" variant="flat" className="text-xs">
                    Certified
                  </Chip>
                )}
                {bounty.requirements.minRating && (
                  <Chip size="sm" color="warning" variant="flat" className="text-xs">
                    {bounty.requirements.minRating}★ Min
                  </Chip>
                )}
              </div>
            </div>
          )}
          
          {/* Bounty Metrics */}
          <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-blue-500">👥</span>
              <span className="font-medium">{bounty.applicantCount} applicants</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-500">👁️</span>
              <span>{bounty.viewCount} views</span>
            </div>
          </div>
          
          {/* Timeline Info */}
          <div className="mb-3">
            <div className="grid grid-cols-2 gap-2 text-xs text-default-500">
              <div>Posted {getTimeSincePosted(bounty.postedAt)}</div>
              <div className={isUrgent ? 'text-red-500 font-medium' : isSoon ? 'text-orange-500' : ''}>
                {getTimeUntilDeadline(bounty.deadline)}
              </div>
            </div>
            <div className="text-xs text-default-500 mt-1">
              Timeline: {bounty.timeline}
            </div>
          </div>
          
          {/* Milestones (for milestone-based bounties) */}
          {bounty.paymentType === 'milestone' && bounty.milestones && (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">Payment Milestones:</div>
              <div className="space-y-1">
                {bounty.milestones.slice(0, 2).map((milestone, idx) => (
                  <div key={idx} className="flex justify-between text-xs">
                    <span>{milestone.name}</span>
                    <span className="font-medium">{formatCurrency(milestone.amount)}</span>
                  </div>
                ))}
                {bounty.milestones.length > 2 && (
                  <div className="text-xs text-default-400">
                    +{bounty.milestones.length - 2} more milestones
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Tags */}
          {bounty.tags && bounty.tags.length > 0 && (
            <div className="mb-3">
              <div className="flex flex-wrap gap-1">
                {bounty.tags.slice(0, 3).map((tag, idx) => (
                  <span key={idx} className="text-xs bg-default-100 dark:bg-default-800 px-2 py-1 rounded">
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            <Button
              size="sm"
              variant="flat"
              onClick={onView}
              className="flex-1"
            >
              View Details
            </Button>
            
            <Button
              size="sm"
              color="primary"
              onClick={onApply}
              className="flex-1"
              disabled={bounty.applicantCount >= 20} // Example limit
            >
              {bounty.applicantCount >= 20 ? 'Applications Full' : 'Apply Now'}
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default BountyCard;
