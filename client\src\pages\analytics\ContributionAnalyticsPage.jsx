import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { useNavigate, useParams, Link } from 'react-router-dom';
import ContributionAnalyticsDashboard from '../../components/analytics/ContributionAnalyticsDashboard';
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Select, SelectItem } from '@heroui/react';
import { motion } from 'framer-motion';
import { ArrowLeft, BarChart3, TrendingUp } from 'lucide-react';

/**
 * ContributionAnalyticsPage Component
 *
 * Displays contribution analytics for all projects or a specific project
 */
const ContributionAnalyticsPage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const { projectId } = useParams();
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [userProjects, setUserProjects] = useState([]);
  const [selectedProjectId, setSelectedProjectId] = useState(projectId || 'all');

  // Fetch user's projects
  useEffect(() => {
    const fetchUserProjects = async () => {
      if (!currentUser) return;

      try {
        // Get projects where user is a contributor
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select(`
            project_id,
            role,
            projects(
              id,
              name,
              description,
              created_by
            )
          `)
          .eq('user_id', currentUser.id);

        if (contributorError) throw contributorError;

        // Extract projects from contributor data
        const projects = contributorData
          .filter(item => item.projects)
          .map(item => ({
            id: item.projects.id,
            name: item.projects.name,
            description: item.projects.description,
            role: item.role,
            isAdmin: item.role === 'admin' || item.projects.created_by === currentUser.id
          }));

        setUserProjects(projects);

        // If projectId is provided, check if it exists in user's projects
        if (projectId) {
          const projectExists = projects.some(p => p.id === projectId);
          if (!projectExists) {
            // Project not found or user doesn't have access
            navigate('/analytics/contributions');
            return;
          }

          // Fetch project details
          const { data: projectData, error: projectError } = await supabase
            .from('projects')
            .select('*')
            .eq('id', projectId)
            .single();

          if (projectError) throw projectError;
          setProject(projectData);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProjects();
  }, [currentUser, projectId, navigate]);

  // Handle project selection change
  const handleProjectChange = (e) => {
    const newProjectId = e.target.value;
    setSelectedProjectId(newProjectId);

    if (newProjectId === 'all') {
      navigate('/analytics/contributions');
    } else {
      navigate(`/analytics/contributions/${newProjectId}`);
    }
  };

  if (!currentUser) {
    return (
      <div className="contribution-analytics-page">
        <div className="auth-required">
          <h2>Authentication Required</h2>
          <p>Please log in to view contribution analytics.</p>
          <button
            className="btn btn-primary"
            onClick={() => navigate('/login')}
          >
            Log In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardBody className="p-8">
              <div className="flex flex-col lg:flex-row items-start justify-between gap-6">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-4">
                    <Button
                      as={Link}
                      to="/track"
                      variant="flat"
                      color="default"
                      startContent={<ArrowLeft size={20} />}
                      className="mb-2"
                    >
                      Back to Track Dashboard
                    </Button>
                  </div>

                  <div className="flex items-center gap-3 mb-4">
                    <BarChart3 className="text-purple-500" size={32} />
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                      Contribution Analytics
                    </h1>
                  </div>

                  <p className="text-lg text-default-600">
                    Track and analyze your contribution patterns and performance
                  </p>
                </div>

                <div className="w-full lg:w-80">
                  <label className="block text-sm font-medium text-default-700 mb-2">
                    Select Project:
                  </label>
                  <Select
                    selectedKeys={[selectedProjectId]}
                    onSelectionChange={(keys) => handleProjectChange({ target: { value: Array.from(keys)[0] } })}
                    isDisabled={loading}
                    className="w-full"
                    classNames={{
                      trigger: "bg-default-100 border-0"
                    }}
                    startContent={<TrendingUp size={16} className="text-default-400" />}
                  >
                    <SelectItem key="all">All Projects</SelectItem>
                    {userProjects.map(project => (
                      <SelectItem key={project.id}>{project.name}</SelectItem>
                    ))}
                  </Select>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Project Info */}
        {project && (
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border-purple-200 dark:border-purple-800">
              <CardBody className="p-6">
                <h2 className="text-2xl font-bold text-purple-800 dark:text-purple-200 mb-2">
                  {project.name}
                </h2>
                {project.description && (
                  <p className="text-purple-700 dark:text-purple-300">
                    {project.description}
                  </p>
                )}
              </CardBody>
            </Card>
          </motion.div>
        )}

        {/* Analytics Dashboard */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <ContributionAnalyticsDashboard
            projectId={selectedProjectId === 'all' ? null : selectedProjectId}
          />
        </motion.div>
      </div>
    </div>
  );
};

export default ContributionAnalyticsPage;
