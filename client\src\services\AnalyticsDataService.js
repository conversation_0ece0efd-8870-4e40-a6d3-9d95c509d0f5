/**
 * Enhanced Analytics Data Service
 *
 * Provides comprehensive data fetching, caching, and real-time updates
 * for the analytics and reporting system with performance optimizations.
 */

import { supabase } from '../utils/supabase/supabase.utils';
import PerformanceOptimizer from '../utils/analytics/PerformanceOptimizer';

class AnalyticsDataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.subscriptions = new Map();
    this.performanceOptimizer = PerformanceOptimizer;
  }

  /**
   * Get comprehensive analytics overview with performance optimization
   */
  async getAnalyticsOverview(userId, period = '30d') {
    const cacheKey = `overview_${userId}_${period}`;

    // Check optimized cache first
    const cachedData = this.performanceOptimizer.getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Use batched requests to avoid duplicate calls
    return this.performanceOptimizer.batchRequest(
      cacheKey,
      async () => {
        return this._fetchAnalyticsOverview(userId, period, cacheKey);
      }
    );
  }

  async _fetchAnalyticsOverview(userId, period, cacheKey) {

    try {
      // Fetch data from enhanced analytics API
      const response = await fetch(`/.netlify/functions/enhanced-analytics?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Analytics API error: ${response.status}`);
      }

      const data = await response.json();

      // Cache the result with performance optimizer
      this.performanceOptimizer.setCache(cacheKey, data);

      return data;
    } catch (error) {
      console.error('Error fetching analytics overview:', error);
      throw error;
    }
  }

  /**
   * Get financial analytics with detailed breakdown
   */
  async getFinancialAnalytics(userId, period = '30d') {
    const cacheKey = `financial_${userId}_${period}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const response = await fetch(`/.netlify/functions/enhanced-analytics/financial?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Financial analytics API error: ${response.status}`);
      }

      const data = await response.json();
      
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('Error fetching financial analytics:', error);
      throw error;
    }
  }

  /**
   * Get project insights and performance metrics
   */
  async getProjectInsights(userId, period = '30d') {
    const cacheKey = `projects_${userId}_${period}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const response = await fetch(`/.netlify/functions/enhanced-analytics/projects?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Project insights API error: ${response.status}`);
      }

      const data = await response.json();
      
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('Error fetching project insights:', error);
      throw error;
    }
  }

  /**
   * Get real-time analytics data with WebSocket connection
   */
  async subscribeToRealTimeAnalytics(userId, callback) {
    const subscriptionKey = `realtime_${userId}`;
    
    // Clean up existing subscription
    if (this.subscriptions.has(subscriptionKey)) {
      this.unsubscribeFromRealTimeAnalytics(userId);
    }

    try {
      // Subscribe to relevant tables for real-time updates
      const subscription = supabase
        .channel(`analytics_${userId}`)
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'financial_transactions',
          filter: `user_id=eq.${userId}`
        }, (payload) => {
          this.invalidateCache(`financial_${userId}`);
          callback({ type: 'financial', payload });
        })
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'projects',
          filter: `created_by=eq.${userId}`
        }, (payload) => {
          this.invalidateCache(`projects_${userId}`);
          callback({ type: 'projects', payload });
        })
        .on('postgres_changes', {
          event: '*',
          schema: 'public',
          table: 'contributions',
          filter: `user_id=eq.${userId}`
        }, (payload) => {
          this.invalidateCache(`overview_${userId}`);
          callback({ type: 'contributions', payload });
        })
        .subscribe();

      this.subscriptions.set(subscriptionKey, subscription);
      return subscription;
    } catch (error) {
      console.error('Error setting up real-time analytics subscription:', error);
      throw error;
    }
  }

  /**
   * Unsubscribe from real-time analytics updates
   */
  unsubscribeFromRealTimeAnalytics(userId) {
    const subscriptionKey = `realtime_${userId}`;
    
    if (this.subscriptions.has(subscriptionKey)) {
      const subscription = this.subscriptions.get(subscriptionKey);
      supabase.removeChannel(subscription);
      this.subscriptions.delete(subscriptionKey);
    }
  }

  /**
   * Generate custom report
   */
  async generateCustomReport(userId, reportConfig) {
    try {
      const response = await fetch('/.netlify/functions/analytics-metrics/generate-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          user_id: userId,
          report_type: reportConfig.type,
          period: reportConfig.period,
          metrics: reportConfig.metrics,
          format: reportConfig.format
        })
      });

      if (!response.ok) {
        throw new Error(`Report generation error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error generating custom report:', error);
      throw error;
    }
  }

  /**
   * Export analytics data in various formats
   */
  async exportAnalyticsData(userId, exportConfig) {
    try {
      const response = await fetch('/.netlify/functions/enhanced-analytics/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          user_id: userId,
          data_type: exportConfig.dataType,
          format: exportConfig.format,
          period: exportConfig.period,
          filters: exportConfig.filters
        })
      });

      if (!response.ok) {
        throw new Error(`Export error: ${response.status}`);
      }

      // Handle different response types
      if (exportConfig.format === 'json') {
        return await response.json();
      } else {
        return await response.blob();
      }
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      throw error;
    }
  }

  /**
   * Get predictive analytics and forecasts
   */
  async getPredictiveAnalytics(userId, predictionType = 'revenue', period = '90d') {
    const cacheKey = `predictive_${userId}_${predictionType}_${period}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    try {
      const response = await fetch(`/.netlify/functions/enhanced-analytics/predictive?type=${predictionType}&period=${period}`, {
        headers: {
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Predictive analytics API error: ${response.status}`);
      }

      const data = await response.json();
      
      this.cache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error('Error fetching predictive analytics:', error);
      throw error;
    }
  }

  /**
   * Schedule automated report
   */
  async scheduleReport(userId, scheduleConfig) {
    try {
      const response = await fetch('/.netlify/functions/enhanced-analytics/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          user_id: userId,
          report_name: scheduleConfig.name,
          report_config: scheduleConfig.config,
          schedule: scheduleConfig.schedule,
          delivery_method: scheduleConfig.deliveryMethod,
          recipients: scheduleConfig.recipients
        })
      });

      if (!response.ok) {
        throw new Error(`Schedule report error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error scheduling report:', error);
      throw error;
    }
  }

  /**
   * Invalidate cache for specific keys
   */
  invalidateCache(pattern) {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear all cache
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Clean up all subscriptions and cache
   */
  cleanup() {
    // Unsubscribe from all real-time subscriptions
    for (const [key, subscription] of this.subscriptions) {
      supabase.removeChannel(subscription);
    }
    this.subscriptions.clear();
    
    // Clear cache
    this.clearCache();
  }
}

// Export singleton instance
export default new AnalyticsDataService();
