import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, AvatarGroup } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import StudioCreationWizard from './StudioCreationWizard';
import MemberManagement from './MemberManagement';
import BusinessModelConfig from './BusinessModelConfig';
import StudioTreasury from './StudioTreasury';
import StudioProjects from './StudioProjects';
import StudioMembers from './StudioMembers';
import StudioAnalytics from './StudioAnalytics';

/**
 * Studio Dashboard Component - Main Studio Management Interface
 * 
 * Features:
 * - Bento grid layout following exact wireframe specifications
 * - Real-time studio data with automatic updates
 * - Integration with studio management APIs
 * - Member management and invitation system
 * - Business model configuration and revenue tracking
 * - Project integration and project management
 */
const StudioDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [studioData, setStudioData] = useState({
    currentStudio: null,
    userStudios: [],
    members: [],
    projects: [],
    invitations: [],
    statistics: {
      totalMembers: 0,
      activeProjects: 0,
      monthlyRevenue: 0,
      completedTasks: 0
    }
  });
  
  const [loading, setLoading] = useState(true);
  const [showCreationWizard, setShowCreationWizard] = useState(false);
  const [showMemberManagement, setShowMemberManagement] = useState(false);
  const [showBusinessConfig, setShowBusinessConfig] = useState(false);

  // Load studio data from backend APIs
  const loadStudioData = async () => {
    try {
      setLoading(true);
      
      // Fetch user's studios
      const response = await fetch('/api/studios', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch studios');
      
      const data = await response.json();
      
      setStudioData(prevData => ({
        ...prevData,
        userStudios: data.data || [],
        currentStudio: data.data?.[0] || null
      }));
      
      // Load additional data for current studio
      if (data.data?.[0]) {
        await loadStudioDetails(data.data[0].id);
      }
      
    } catch (error) {
      console.error('Error loading studio data:', error);
      toast.error('Failed to load studio data');
    } finally {
      setLoading(false);
    }
  };

  const loadStudioDetails = async (studioId) => {
    try {
      const response = await fetch(`/api/studios/${studioId}`, {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) throw new Error('Failed to fetch studio details');
      
      const data = await response.json();
      
      setStudioData(prevData => ({
        ...prevData,
        currentStudio: data.data,
        members: data.data.team_members || [],
        projects: data.data.projects || [],
        statistics: {
          totalMembers: data.data.team_members?.length || 0,
          activeProjects: data.data.projects?.filter(p => p.status === 'active').length || 0,
          monthlyRevenue: 0, // TODO: Calculate from revenue data
          completedTasks: 0 // TODO: Calculate from task data
        }
      }));
      
    } catch (error) {
      console.error('Error loading studio details:', error);
      toast.error('Failed to load studio details');
    }
  };

  // Initialize data loading
  useEffect(() => {
    if (currentUser) {
      loadStudioData();
    }
  }, [currentUser]);

  const handleCreateStudio = () => {
    setShowCreationWizard(true);
  };

  const handleStudioCreated = (newStudio) => {
    setStudioData(prevData => ({
      ...prevData,
      userStudios: [newStudio, ...prevData.userStudios],
      currentStudio: newStudio
    }));
    setShowCreationWizard(false);
    toast.success('Studio created successfully!');
  };

  const handleSwitchStudio = (studio) => {
    setStudioData(prevData => ({
      ...prevData,
      currentStudio: studio
    }));
    loadStudioDetails(studio.id);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading studio dashboard...</p>
        </div>
      </div>
    );
  }

  // Show creation wizard if no studios exist
  if (!studioData.currentStudio && !showCreationWizard) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-6xl mb-6">🏢</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to Studios</h2>
          <p className="text-gray-600 mb-8 max-w-md mx-auto">
            Create your first studio to start collaborating with other creatives and managing projects together.
          </p>
          <Button
            onClick={handleCreateStudio}
            color="primary"
            size="lg"
            className="bg-purple-600 hover:bg-purple-700"
          >
            Create Your First Studio
          </Button>
        </div>
      </div>
    );
  }

  if (showCreationWizard) {
    return (
      <StudioCreationWizard
        onComplete={handleStudioCreated}
        onCancel={() => setShowCreationWizard(false)}
      />
    );
  }

  return (
    <div className={`max-w-7xl mx-auto p-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {studioData.currentStudio?.name || 'Studio Dashboard'}
          </h1>
          <p className="text-gray-600">
            Manage your creative studio and collaborate with your team
          </p>
        </div>
        
        <div className="flex gap-3 mt-4 md:mt-0">
          <Button
            onClick={handleCreateStudio}
            color="primary"
            variant="flat"
          >
            Create New Studio
          </Button>
          <Button
            onClick={() => setShowMemberManagement(true)}
            color="primary"
          >
            Manage Members
          </Button>
        </div>
      </div>

      {/* Studio Selector */}
      {studioData.userStudios.length > 1 && (
        <Card className="mb-6">
          <CardBody className="p-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-gray-700">Switch Studio:</span>
              <div className="flex space-x-2">
                {studioData.userStudios.map((studio) => (
                  <Button
                    key={studio.id}
                    variant={studio.id === studioData.currentStudio?.id ? 'solid' : 'flat'}
                    color="primary"
                    size="sm"
                    onClick={() => handleSwitchStudio(studio)}
                  >
                    {studio.name}
                  </Button>
                ))}
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Members</p>
                <p className="text-2xl font-bold text-gray-900">{studioData.statistics.totalMembers}</p>
              </div>
              <div className="text-blue-500">👥</div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Projects</p>
                <p className="text-2xl font-bold text-gray-900">{studioData.statistics.activeProjects}</p>
              </div>
              <div className="text-green-500">🚀</div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Monthly Revenue</p>
                <p className="text-2xl font-bold text-gray-900">${studioData.statistics.monthlyRevenue}</p>
              </div>
              <div className="text-purple-500">💰</div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed Tasks</p>
                <p className="text-2xl font-bold text-gray-900">{studioData.statistics.completedTasks}</p>
              </div>
              <div className="text-orange-500">✅</div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Studio Projects */}
        <div className="lg:col-span-2">
          <StudioProjects 
            studioId={studioData.currentStudio?.id}
            projects={studioData.projects}
            onProjectsUpdate={loadStudioData}
          />
        </div>

        {/* Studio Members */}
        <div>
          <StudioMembers 
            studioId={studioData.currentStudio?.id}
            members={studioData.members}
            onMembersUpdate={loadStudioData}
          />
        </div>
      </div>

      {/* Analytics Section */}
      <div className="mt-8">
        <StudioAnalytics 
          studioId={studioData.currentStudio?.id}
          statistics={studioData.statistics}
        />
      </div>

      {/* Modals */}
      {showMemberManagement && (
        <MemberManagement
          studioId={studioData.currentStudio?.id}
          onClose={() => setShowMemberManagement(false)}
          onUpdate={loadStudioData}
        />
      )}

      {showBusinessConfig && (
        <BusinessModelConfig
          studioId={studioData.currentStudio?.id}
          onClose={() => setShowBusinessConfig(false)}
          onUpdate={loadStudioData}
        />
      )}
    </div>
  );
};

export default StudioDashboard;
