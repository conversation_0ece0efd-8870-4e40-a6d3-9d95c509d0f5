import React, { Suspense, lazy, useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Progress, Spinner } from '@heroui/react';

/**
 * Lazy Loader Component
 * 
 * Provides lazy loading for canvas components and large data sets with
 * optimized loading states and error boundaries.
 */

// Loading skeleton component
const LoadingSkeleton = ({ type = 'default', className = "" }) => {
  const skeletonVariants = {
    default: (
      <div className={`animate-pulse space-y-4 ${className}`}>
        <div className="h-8 bg-default-200 rounded w-1/3"></div>
        <div className="space-y-3">
          <div className="h-4 bg-default-200 rounded"></div>
          <div className="h-4 bg-default-200 rounded w-5/6"></div>
          <div className="h-4 bg-default-200 rounded w-4/6"></div>
        </div>
      </div>
    ),
    canvas: (
      <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center ${className}`}>
        <Card className="bg-white/10 border border-white/20">
          <CardBody className="p-8 text-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-12 h-12 mx-auto mb-4"
            >
              <Spinner size="lg" color="primary" />
            </motion.div>
            <h3 className="text-xl font-semibold text-white mb-2">Loading Canvas</h3>
            <p className="text-white/70">Preparing your workspace...</p>
          </CardBody>
        </Card>
      </div>
    ),
    dashboard: (
      <div className={`p-6 space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-default-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-default-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {[1, 2].map(i => (
              <div key={i} className="h-64 bg-default-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    ),
    chart: (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-default-200 rounded w-1/4"></div>
          <div className="h-48 bg-default-200 rounded"></div>
          <div className="flex justify-between">
            <div className="h-4 bg-default-200 rounded w-16"></div>
            <div className="h-4 bg-default-200 rounded w-16"></div>
          </div>
        </div>
      </div>
    ),
    table: (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-default-200 rounded w-1/3"></div>
          {[1, 2, 3, 4, 5].map(i => (
            <div key={i} className="flex space-x-4">
              <div className="h-4 bg-default-200 rounded flex-1"></div>
              <div className="h-4 bg-default-200 rounded w-20"></div>
              <div className="h-4 bg-default-200 rounded w-16"></div>
            </div>
          ))}
        </div>
      </div>
    )
  };

  return skeletonVariants[type] || skeletonVariants.default;
};

// Error boundary for lazy loaded components
class LazyErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Lazy loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-900 via-red-900 to-slate-900 flex items-center justify-center">
          <Card className="bg-white/10 border border-red-500/30">
            <CardBody className="p-8 text-center">
              <div className="text-4xl mb-4">⚠️</div>
              <h3 className="text-xl font-semibold text-white mb-2">Loading Error</h3>
              <p className="text-white/70 mb-4">
                Failed to load component. Please refresh the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
              >
                Refresh Page
              </button>
            </CardBody>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Progressive loading with retry mechanism
const ProgressiveLoader = ({ 
  loadingComponent, 
  errorComponent, 
  retryCount = 3,
  retryDelay = 1000,
  children 
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [attempts, setAttempts] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 100); // Minimum loading time for smooth UX

    return () => clearTimeout(timer);
  }, []);

  const retry = () => {
    if (attempts < retryCount) {
      setAttempts(attempts + 1);
      setError(null);
      setLoading(true);
      
      setTimeout(() => {
        setLoading(false);
      }, retryDelay);
    }
  };

  if (loading) {
    return loadingComponent || <LoadingSkeleton />;
  }

  if (error) {
    return errorComponent || (
      <div className="text-center p-8">
        <p className="text-red-500 mb-4">Failed to load component</p>
        {attempts < retryCount && (
          <button
            onClick={retry}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-600 transition-colors"
          >
            Retry ({retryCount - attempts} attempts left)
          </button>
        )}
      </div>
    );
  }

  return children;
};

// Main lazy loader component
const LazyLoader = ({ 
  component: Component, 
  fallback, 
  skeletonType = 'default',
  enableProgressiveLoading = true,
  ...props 
}) => {
  const LoadingFallback = fallback || <LoadingSkeleton type={skeletonType} />;

  if (enableProgressiveLoading) {
    return (
      <LazyErrorBoundary>
        <Suspense fallback={LoadingFallback}>
          <ProgressiveLoader loadingComponent={LoadingFallback}>
            <Component {...props} />
          </ProgressiveLoader>
        </Suspense>
      </LazyErrorBoundary>
    );
  }

  return (
    <LazyErrorBoundary>
      <Suspense fallback={LoadingFallback}>
        <Component {...props} />
      </Suspense>
    </LazyErrorBoundary>
  );
};

// Lazy loading factory for creating lazy components
export const createLazyComponent = (importFunction, options = {}) => {
  const LazyComponent = lazy(importFunction);
  
  return (props) => (
    <LazyLoader
      component={LazyComponent}
      {...options}
      {...props}
    />
  );
};

// Intersection Observer based lazy loading for data
export const useLazyData = (loadFunction, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);

  const {
    threshold = 0.1,
    rootMargin = '50px',
    enabled = true
  } = options;

  const loadData = async () => {
    if (hasLoaded || loading) return;

    try {
      setLoading(true);
      setError(null);
      const result = await loadFunction();
      setData(result);
      setHasLoaded(true);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const observerRef = React.useRef();

  useEffect(() => {
    if (!enabled) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadData();
          }
        });
      },
      { threshold, rootMargin }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
    };
  }, [enabled, threshold, rootMargin]);

  return {
    data,
    loading,
    error,
    hasLoaded,
    observerRef,
    reload: () => {
      setHasLoaded(false);
      loadData();
    }
  };
};

export { LoadingSkeleton, LazyErrorBoundary, ProgressiveLoader };
export default LazyLoader;
