import React from 'react';
import BugReportForm from './BugReportForm';
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
} from '../ui/heroui';

const BugReportModal = ({ isOpen, onClose, onSuccess }) => {
  return (
    <Modal isOpen={isOpen} onOpenChange={onClose} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-lg font-semibold">Report a Bug</h2>
          <p className="text-sm text-muted-foreground">
            Help us improve by reporting any issues you encounter
          </p>
        </ModalHeader>
        <ModalBody>
          <BugReportForm
            onSuccess={() => {
              if (onSuccess) onSuccess();
              onClose();
            }}
            onCancel={onClose}
          />
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default BugReportModal;
