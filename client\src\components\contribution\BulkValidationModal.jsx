import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

/**
 * Modal for bulk validation of contributions
 * @param {Object} props - Component props
 * @param {Array} props.selectedContributions - Array of selected contribution objects
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Function} props.onValidate - Function to call when contributions are validated
 * @param {string} props.initialStatus - Initial validation status to set
 */
const BulkValidationModal = ({ selectedContributions, onClose, onValidate, initialStatus = 'approved' }) => {
  const [status, setStatus] = useState(initialStatus);
  const [feedback, setFeedback] = useState('');
  const [processing, setProcessing] = useState(false);
  const [individualFeedback, setIndividualFeedback] = useState(false);
  const [contributionFeedback, setContributionFeedback] = useState({});

  // Handle status change
  const handleStatusChange = (newStatus) => {
    setStatus(newStatus);
  };

  // Handle feedback change
  const handleFeedbackChange = (e) => {
    setFeedback(e.target.value);
  };

  // Handle individual feedback toggle
  const handleIndividualFeedbackToggle = () => {
    setIndividualFeedback(!individualFeedback);

    // Initialize individual feedback if toggling on
    if (!individualFeedback) {
      const initialFeedback = {};
      selectedContributions.forEach(contribution => {
        initialFeedback[contribution.id] = feedback;
      });
      setContributionFeedback(initialFeedback);
    }
  };

  // Handle individual feedback change
  const handleIndividualFeedbackChange = (contributionId, value) => {
    setContributionFeedback(prev => ({
      ...prev,
      [contributionId]: value
    }));
  };

  // Handle validation
  const handleValidate = async () => {
    // Require feedback for rejections and change requests
    if ((status === 'rejected' || status === 'pending_changes') && !individualFeedback && !feedback.trim()) {
      toast.error(`Please provide feedback when ${status === 'rejected' ? 'rejecting' : 'requesting changes for'} contributions`);
      return;
    }

    // Validate individual feedback if enabled
    if (individualFeedback) {
      for (const contribution of selectedContributions) {
        const individualFeedbackText = contributionFeedback[contribution.id] || '';
        if ((status === 'rejected' || status === 'pending_changes') && !individualFeedbackText.trim()) {
          toast.error(`Please provide feedback for all contributions when ${status === 'rejected' ? 'rejecting' : 'requesting changes'}`);
          return;
        }
      }
    }

    setProcessing(true);

    try {
      // Call the onValidate function with the selected contributions and status
      await onValidate(
        selectedContributions.map(contribution => ({
          id: contribution.id,
          feedback: individualFeedback ? (contributionFeedback[contribution.id] || '') : feedback
        })),
        status
      );

      toast.success(`${selectedContributions.length} contributions ${status.replace('_', ' ')}`);
      onClose();
    } catch (error) {
      console.error('Error in bulk validation:', error);
      toast.error('Failed to validate contributions');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <div className="bulk-validation-modal-overlay">
      <div className="bulk-validation-modal">
        <div className="bulk-validation-modal-header">
          <h3>Bulk Validate Contributions</h3>
          <button className="close-modal-btn" onClick={onClose}>
            <i className="bi bi-x"></i>
          </button>
        </div>

        <div className="bulk-validation-modal-body">
          <div className="selected-count">
            <strong>{selectedContributions.length}</strong> contributions selected
          </div>

          <div className="validation-status-selector">
            <div className="status-label">Set status to:</div>
            <div className="status-options">
              <button
                className={`status-option ${status === 'approved' ? 'active' : ''}`}
                onClick={() => handleStatusChange('approved')}
              >
                <i className="bi bi-check-circle"></i>
                Approve
              </button>
              <button
                className={`status-option ${status === 'rejected' ? 'active' : ''}`}
                onClick={() => handleStatusChange('rejected')}
              >
                <i className="bi bi-x-circle"></i>
                Reject
              </button>
              <button
                className={`status-option ${status === 'pending_changes' ? 'active' : ''}`}
                onClick={() => handleStatusChange('pending_changes')}
              >
                <i className="bi bi-pencil-square"></i>
                Request Changes
              </button>
            </div>
          </div>

          {/* Only show feedback section for rejected or pending_changes status, or if already approved with feedback */}
          <div className={`feedback-section ${(status === 'rejected' || status === 'pending_changes') ? 'required' : ''}`}>
            <div className="feedback-header">
              <label htmlFor="bulk-feedback" className="feedback-label">
                Feedback {(status === 'rejected' || status === 'pending_changes') ?
                  <span className="required-indicator">(required)</span> :
                  <span className="optional-indicator">(optional)</span>}:
              </label>
              <div className="individual-feedback-toggle">
                <input
                  type="checkbox"
                  id="individual-feedback"
                  checked={individualFeedback}
                  onChange={handleIndividualFeedbackToggle}
                />
                <label htmlFor="individual-feedback">Provide individual feedback</label>
              </div>
            </div>

            {/* Show feedback instructions based on status */}
            {(status === 'rejected' || status === 'pending_changes') && (
              <div className="feedback-instructions">
                <i className="bi bi-info-circle"></i>
                {status === 'rejected' ?
                  'Please explain why these contributions are being rejected.' :
                  'Please describe what changes are needed for these contributions.'}
              </div>
            )}

            {!individualFeedback ? (
              <textarea
                id="bulk-feedback"
                className={`bulk-feedback-textarea ${(status === 'rejected' || status === 'pending_changes') ? 'required' : ''}`}
                value={feedback}
                onChange={handleFeedbackChange}
                placeholder={`Enter feedback for all ${selectedContributions.length} contributions...`}
                disabled={processing}
                required={status === 'rejected' || status === 'pending_changes'}
              />
            ) : (
              <div className="individual-feedback-list">
                {selectedContributions.map(contribution => (
                  <div key={contribution.id} className="individual-feedback-item">
                    <div className="individual-feedback-header">
                      <strong>{contribution.task_name}</strong>
                      {(status === 'rejected' || status === 'pending_changes') && (
                        <span className="required-indicator">(required)</span>
                      )}
                    </div>
                    <textarea
                      className={`individual-feedback-textarea ${(status === 'rejected' || status === 'pending_changes') ? 'required' : ''}`}
                      value={contributionFeedback[contribution.id] || ''}
                      onChange={(e) => handleIndividualFeedbackChange(contribution.id, e.target.value)}
                      placeholder={status === 'rejected' ?
                        "Explain why this contribution is being rejected..." :
                        status === 'pending_changes' ?
                        "Describe what changes are needed for this contribution..." :
                        "Enter feedback for this contribution..."}
                      disabled={processing}
                      required={status === 'rejected' || status === 'pending_changes'}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="bulk-validation-modal-footer">
          <button
            className="cancel-btn"
            onClick={onClose}
            disabled={processing}
          >
            Cancel
          </button>
          <button
            className={`validate-btn ${status === 'approved' ? 'approve' : status === 'rejected' ? 'reject' : 'changes'}`}
            onClick={handleValidate}
            disabled={processing}
          >
            {processing ? 'Processing...' : `${status === 'approved' ? 'Approve' : status === 'rejected' ? 'Reject' : 'Request Changes'} (${selectedContributions.length})`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BulkValidationModal;
