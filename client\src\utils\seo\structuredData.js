/**
 * Structured Data Utility for SEO
 * 
 * This utility generates JSON-LD structured data for improved
 * search engine understanding and rich snippets.
 */

export const generateOrganizationSchema = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "RoyalTea",
  "description": "Collaborative project platform for professionals",
  "url": "https://royaltea.app",
  "logo": "https://royaltea.app/logo.png",
  "sameAs": [
    "https://twitter.com/royaltea_app",
    "https://linkedin.com/company/royaltea",
    "https://github.com/royaltea"
  ],
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "******-ROYALTE<PERSON>",
    "contactType": "customer service",
    "availableLanguage": "English"
  }
});

export const generateWebsiteSchema = () => ({
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "Royal<PERSON>ea",
  "url": "https://royaltea.app",
  "description": "Collaborative project platform for skill verification and revenue sharing",
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://royaltea.app/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
});

export const generatePersonSchema = (user) => ({
  "@context": "https://schema.org",
  "@type": "Person",
  "name": user.display_name || user.full_name,
  "description": user.bio || "Professional on RoyalTea platform",
  "url": `https://royaltea.app/profile/${user.id}`,
  "image": user.avatar_url,
  "jobTitle": user.title,
  "worksFor": {
    "@type": "Organization",
    "name": "RoyalTea"
  },
  "knowsAbout": user.skills?.map(skill => skill.name) || []
});

export const generateProjectSchema = (project) => ({
  "@context": "https://schema.org",
  "@type": "CreativeWork",
  "name": project.title,
  "description": project.description,
  "url": `https://royaltea.app/project/${project.id}`,
  "creator": {
    "@type": "Person",
    "name": project.creator?.display_name
  },
  "dateCreated": project.created_at,
  "dateModified": project.updated_at,
  "keywords": project.skills?.map(skill => skill.name).join(', '),
  "genre": project.category
});

export const generateBreadcrumbSchema = (breadcrumbs) => ({
  "@context": "https://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": breadcrumbs.map((crumb, index) => ({
    "@type": "ListItem",
    "position": index + 1,
    "name": crumb.name,
    "item": crumb.url
  }))
});

export const generateFAQSchema = (faqs) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});

export default {
  generateOrganizationSchema,
  generateWebsiteSchema,
  generatePersonSchema,
  generateProjectSchema,
  generateBreadcrumbSchema,
  generateFAQSchema
};