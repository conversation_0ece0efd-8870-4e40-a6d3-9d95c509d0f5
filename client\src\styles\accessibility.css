/**
 * Accessibility Enhancement Styles
 * 
 * Provides comprehensive accessibility improvements:
 * - Focus indicators
 * - High contrast mode
 * - Reduced motion
 * - Screen reader utilities
 * - Keyboard navigation
 */

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only:focus,
.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Enhanced Focus Indicators */
.enhanced-focus *:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}

.enhanced-focus button:focus,
.enhanced-focus [role="button"]:focus {
  outline: 3px solid hsl(var(--primary));
  outline-offset: 2px;
  box-shadow: 0 0 0 5px hsl(var(--primary) / 0.2);
}

.enhanced-focus input:focus,
.enhanced-focus textarea:focus,
.enhanced-focus select:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 1px;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

.enhanced-focus a:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  text-decoration: underline;
  text-decoration-thickness: 2px;
}

/* High Contrast Mode */
.high-contrast {
  --background: 0 0% 100%;
  --foreground: 0 0% 0%;
  --primary: 240 100% 50%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 20%;
  --secondary-foreground: 0 0% 100%;
  --muted: 0 0% 90%;
  --muted-foreground: 0 0% 10%;
  --accent: 240 100% 50%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 20%;
  --input: 0 0% 90%;
  --ring: 240 100% 50%;
}

.high-contrast.dark {
  --background: 0 0% 0%;
  --foreground: 0 0% 100%;
  --primary: 240 100% 70%;
  --primary-foreground: 0 0% 0%;
  --secondary: 0 0% 80%;
  --secondary-foreground: 0 0% 0%;
  --muted: 0 0% 10%;
  --muted-foreground: 0 0% 90%;
  --accent: 240 100% 70%;
  --accent-foreground: 0 0% 0%;
  --destructive: 0 100% 70%;
  --destructive-foreground: 0 0% 0%;
  --border: 0 0% 80%;
  --input: 0 0% 10%;
  --ring: 240 100% 70%;
}

.high-contrast * {
  border-color: hsl(var(--border)) !important;
}

.high-contrast button,
.high-contrast [role="button"] {
  border: 2px solid hsl(var(--border)) !important;
  font-weight: 600 !important;
}

.high-contrast img {
  filter: contrast(1.2) brightness(1.1);
}

/* Reduced Motion */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.reduce-motion .animate-spin {
  animation: none !important;
}

.reduce-motion .animate-pulse {
  animation: none !important;
}

.reduce-motion .animate-bounce {
  animation: none !important;
}

/* Large Text Mode */
.large-text {
  font-size: 120% !important;
}

.large-text h1 {
  font-size: 3rem !important;
}

.large-text h2 {
  font-size: 2.5rem !important;
}

.large-text h3 {
  font-size: 2rem !important;
}

.large-text h4 {
  font-size: 1.5rem !important;
}

.large-text h5 {
  font-size: 1.25rem !important;
}

.large-text h6 {
  font-size: 1.125rem !important;
}

.large-text p,
.large-text span,
.large-text div {
  font-size: 1.125rem !important;
  line-height: 1.6 !important;
}

.large-text button {
  font-size: 1.125rem !important;
  padding: 0.75rem 1.5rem !important;
}

/* Keyboard Navigation Indicators */
.keyboard-navigation *:focus {
  outline: 3px solid hsl(var(--primary));
  outline-offset: 2px;
}

.keyboard-navigation button:focus,
.keyboard-navigation [role="button"]:focus {
  transform: scale(1.05);
  transition: transform 0.1s ease;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* Error States for Accessibility */
.error-field {
  border-color: hsl(var(--destructive)) !important;
  box-shadow: 0 0 0 2px hsl(var(--destructive) / 0.2) !important;
}

.error-message {
  color: hsl(var(--destructive));
  font-size: 0.875rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Loading States */
.loading-overlay {
  position: absolute;
  inset: 0;
  background: hsl(var(--background) / 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Focus Within Indicators */
.focus-within\:ring:focus-within {
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);
}

/* Hover and Focus States for Interactive Elements */
.interactive-element {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-element:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px hsl(var(--foreground) / 0.1);
}

.interactive-element:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

.interactive-element:active {
  transform: translateY(0);
}

/* Status Indicators */
.status-success {
  color: hsl(120 100% 25%);
  background: hsl(120 100% 95%);
}

.status-warning {
  color: hsl(45 100% 25%);
  background: hsl(45 100% 95%);
}

.status-error {
  color: hsl(0 100% 25%);
  background: hsl(0 100% 95%);
}

.status-info {
  color: hsl(210 100% 25%);
  background: hsl(210 100% 95%);
}

/* Dark mode status indicators */
.dark .status-success {
  color: hsl(120 100% 75%);
  background: hsl(120 100% 10%);
}

.dark .status-warning {
  color: hsl(45 100% 75%);
  background: hsl(45 100% 10%);
}

.dark .status-error {
  color: hsl(0 100% 75%);
  background: hsl(0 100% 10%);
}

.dark .status-info {
  color: hsl(210 100% 75%);
  background: hsl(210 100% 10%);
}

/* Responsive Text Scaling */
@media (max-width: 768px) {
  .large-text {
    font-size: 110% !important;
  }
}

/* Print Styles for Accessibility */
@media print {
  .sr-only {
    position: static !important;
    width: auto !important;
    height: auto !important;
    clip: auto !important;
    overflow: visible !important;
  }
  
  .skip-link {
    display: none !important;
  }
  
  * {
    background: white !important;
    color: black !important;
  }
}
