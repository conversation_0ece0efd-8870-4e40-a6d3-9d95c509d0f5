import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, CardBody, Button, Chip } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Missing Page Component
 * 
 * A placeholder component for pages that are referenced in routes but not yet implemented.
 * Provides helpful information and navigation options for users.
 */
const MissingPage = ({ 
  pageName = 'Unknown Page',
  description = 'This page is currently under development.',
  suggestedRoutes = ['/'],
  canvasId = null
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-6">
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="max-w-2xl w-full"
      >
        <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-md border border-white/20">
          <CardBody className="p-8 text-center">
            {/* Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="text-8xl mb-6"
            >
              🚧
            </motion.div>

            {/* Title */}
            <motion.h1
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-3xl font-bold text-slate-800 dark:text-white mb-4"
            >
              {pageName}
            </motion.h1>

            {/* Status Chip */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 }}
              className="mb-6"
            >
              <Chip color="warning" variant="flat" size="lg">
                Under Development
              </Chip>
            </motion.div>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="text-lg text-slate-600 dark:text-slate-300 mb-8"
            >
              {description}
            </motion.p>

            {/* Development Info */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-slate-100 dark:bg-slate-700 rounded-lg p-4 mb-8 text-left"
            >
              <h3 className="font-semibold text-slate-800 dark:text-white mb-3">
                Development Information
              </h3>
              <div className="space-y-2 text-sm text-slate-600 dark:text-slate-300">
                <div className="flex justify-between">
                  <span>Current Route:</span>
                  <code className="bg-slate-200 dark:bg-slate-600 px-2 py-1 rounded text-xs">
                    {location.pathname}
                  </code>
                </div>
                {canvasId && (
                  <div className="flex justify-between">
                    <span>Canvas ID:</span>
                    <code className="bg-slate-200 dark:bg-slate-600 px-2 py-1 rounded text-xs">
                      {canvasId}
                    </code>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Status:</span>
                  <span className="text-orange-600 dark:text-orange-400 font-medium">
                    Planned for Implementation
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Action Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                color="primary"
                size="lg"
                onClick={handleGoHome}
                className="min-w-32"
              >
                🏠 Go Home
              </Button>
              <Button
                color="default"
                variant="bordered"
                size="lg"
                onClick={handleGoBack}
                className="min-w-32"
              >
                ← Go Back
              </Button>
            </motion.div>

            {/* Suggested Routes */}
            {suggestedRoutes.length > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="mt-8 pt-6 border-t border-slate-200 dark:border-slate-600"
              >
                <h4 className="text-sm font-medium text-slate-600 dark:text-slate-400 mb-3">
                  You might be looking for:
                </h4>
                <div className="flex flex-wrap gap-2 justify-center">
                  {suggestedRoutes.slice(1).map((route, index) => (
                    <Button
                      key={route}
                      size="sm"
                      variant="light"
                      onClick={() => navigate(route)}
                      className="text-xs"
                    >
                      {route === '/' ? 'Dashboard' : route.replace('/', '').replace('-', ' ')}
                    </Button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Footer */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
              className="mt-8 pt-4 border-t border-slate-200 dark:border-slate-600"
            >
              <p className="text-xs text-slate-500 dark:text-slate-400">
                This page is part of the Royaltea platform's ongoing development.
                <br />
                Check back soon for updates!
              </p>
            </motion.div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default MissingPage;
