import React, { useState, useEffect, useContext, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { ThemeContext } from '../../contexts/theme.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { navigateTo } from '../../utils/routing-fix';
import InviteModal from '../user/InviteModal';
import ThemeToggle from '../theme/ThemeToggle';
import NotificationButton from '../notification/NotificationButton';
import { cn } from '../../lib/utils';
import {
  UserPlus,
  User,
  Mail,
  Users,
  Settings,
  Bug,
  Gauge,
  BarChart3,
  TrendingUp,
  LogOut
} from 'lucide-react';

const ModernNavbar = () => {
  const { currentUser, logout } = useContext(UserContext);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [userData, setUserData] = useState(null);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const profileMenuRef = useRef(null);
  const profileButtonRef = useRef(null);
  const searchResultsRef = useRef(null);
  const searchInputRef = useRef(null);
  const location = useLocation();
  const navigate = useNavigate();

  // Check if the user is an admin and fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser) return;

      try {
        const { data, error } = await supabase
          .from('users')
          .select('is_admin, display_name, avatar_url, is_premium')
          .eq('id', currentUser.id)
          .single();

        if (!error && data) {
          setIsAdmin(data.is_admin || false);
          setUserData(data);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };

    fetchUserData();
  }, [currentUser]);

  // Close profile menu and search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close profile menu if clicked outside
      if (
        profileMenuRef.current &&
        !profileMenuRef.current.contains(event.target) &&
        profileButtonRef.current &&
        !profileButtonRef.current.contains(event.target)
      ) {
        setShowProfileMenu(false);
      }

      // Close search results if clicked outside
      if (
        searchResultsRef.current &&
        !searchResultsRef.current.contains(event.target) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target)
      ) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Search users function - only by display name for privacy
  const searchUsers = async (query) => {
    if (!query || query.trim().length < 2) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setIsSearching(true);

    try {
      // Search by display name only (partial match)
      const { data: displayNameResults, error: displayNameError } = await supabase
        .from('users')
        .select('id, display_name, avatar_url, is_premium')
        .ilike('display_name', `%${query}%`)
        .limit(8);

      if (displayNameError) throw displayNameError;

      setSearchResults(displayNameResults || []);
      setShowSearchResults((displayNameResults || []).length > 0);
    } catch (error) {
      console.error('Error searching users:', error);
      toast.error('Failed to search users');
    } finally {
      setIsSearching(false);
    }
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);

    // Debounce search to avoid too many requests
    const timeoutId = setTimeout(() => {
      searchUsers(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  };

  // Handle search result click
  const handleSearchResultClick = (userId) => {
    setShowSearchResults(false);
    setSearchQuery('');
    navigateTo(`/profile/${userId}`);
    navigate(`/profile/${userId}`);
    toast.loading('Loading profile...');
  };

  // Handle logout
  const handleLogout = async (e) => {
    e.preventDefault();
    setShowProfileMenu(false);
    logout();
  };

  // Toggle profile menu
  const toggleProfileMenu = () => {
    setShowProfileMenu(!showProfileMenu);
  };

  // Check if a nav item is active
  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    // Special case for Track button - should highlight for track, projects, analytics, and validation
    if (path === '/track') {
      return location.pathname === '/track' ||
             location.pathname.startsWith('/projects') ||
             location.pathname.startsWith('/analytics') ||
             location.pathname.startsWith('/validation') ||
             (location.pathname.startsWith('/project/') &&
              !location.pathname.startsWith('/project/wizard') &&
              !location.pathname.includes('/revenue'));
    }
    // Special case for Start button - should highlight for start page and project wizard
    if (path === '/start') {
      return location.pathname === '/start' || location.pathname.startsWith('/project/wizard');
    }
    return location.pathname.startsWith(path);
  };

  return (
    <nav className="bg-background border-b border-border sticky top-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 flex justify-between items-center h-16">
        <div className="flex items-center">
          <Link to="/" className="flex items-center text-xl font-bold mr-4 text-foreground">
            <span className="mr-2">👑</span> Royaltea
          </Link>

          {isAdmin && (
            <Link
              to="/admin"
              className="flex items-center justify-center bg-muted hover:bg-muted/80 rounded-md p-2 ml-2 transition-colors"
              title="Admin Dashboard"
            >
              <span role="img" aria-label="Construction">🚧</span>
              <span role="img" aria-label="Map">🗺️</span>
            </Link>
          )}

          {currentUser && (
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                navigateTo('/learn');
              }}
              className={cn(
                "ml-3 p-2 rounded-md transition-colors",
                isActive('/learn') ? "text-foreground bg-muted" : "text-muted-foreground hover:text-foreground hover:bg-muted/50"
              )}
              title="Training"
            >
              <i className="bi bi-mortarboard-fill"></i>
            </a>
          )}
        </div>

        {currentUser && (
          <>
            <div className="flex-1 max-w-md mx-4 hidden md:block">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search users by name..."
                  className="w-full py-2 px-4 pr-10 rounded-full bg-muted text-foreground border border-input focus:outline-none focus:ring-2 focus:ring-ring focus:bg-background placeholder-muted-foreground"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  onFocus={() => searchQuery.trim().length >= 2 && setShowSearchResults(true)}
                />
                <button className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground">
                  {isSearching ? (
                    <div className="w-4 h-4 border-2 border-muted-foreground/30 border-t-foreground rounded-full animate-spin"></div>
                  ) : (
                    <i className="bi bi-search"></i>
                  )}
                </button>

                {showSearchResults && (
                  <div className="absolute top-full mt-2 w-full bg-white rounded-md shadow-lg overflow-hidden z-50" ref={searchResultsRef}>
                    {searchResults.length > 0 ? (
                      <>
                        <div className="px-4 py-2 text-xs font-semibold text-slate-500 bg-white border-b border-slate-200">Users</div>
                        <ul>
                          {searchResults.map(user => (
                            <li key={user.id} className="flex items-center justify-between px-4 py-3 hover:bg-gray-100">
                              <div
                                className="flex items-center cursor-pointer flex-1"
                                onClick={() => handleSearchResultClick(user.id)}
                              >
                                <div className="w-8 h-8 rounded-full overflow-hidden mr-3">
                                  <img
                                    src={user.avatar_url || (user.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png')}
                                    alt={`${user.display_name}'s avatar`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <div className="text-sm font-medium text-slate-900">
                                  {user.display_name}
                                </div>
                              </div>
                              <div>
                                <button
                                  className="w-7 h-7 flex items-center justify-center rounded-full bg-slate-100 text-primary hover:bg-primary hover:text-white transition-colors"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedUser(user);
                                    setShowInviteModal(true);
                                    setShowSearchResults(false);
                                  }}
                                  title="Send friend request"
                                >
                                  <i className="bi bi-person-plus text-sm"></i>
                                </button>
                              </div>
                            </li>
                          ))}
                        </ul>
                      </>
                    ) : (
                      <div className="py-6 text-center text-sm text-slate-500">
                        No users found
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="flex items-center">
              <div className="hidden md:flex items-center space-x-6 mr-4">
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateTo('/start');
                  }}
                  className={cn(
                    "px-4 py-2 font-medium transition-colors",
                    isActive('/start')
                      ? "text-foreground relative after:content-[''] after:absolute after:bottom-[-4px] after:left-1/2 after:-translate-x-1/2 after:w-6 after:h-0.5 after:bg-primary after:rounded-full"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  Start
                </a>
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateTo('/track');
                  }}
                  className={cn(
                    "px-4 py-2 font-medium transition-colors",
                    isActive('/track')
                      ? "text-foreground relative after:content-[''] after:absolute after:bottom-[-4px] after:left-1/2 after:-translate-x-1/2 after:w-6 after:h-0.5 after:bg-primary after:rounded-full"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  Track
                </a>
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateTo('/earn');
                  }}
                  className={cn(
                    "px-4 py-2 font-medium transition-colors",
                    isActive('/earn')
                      ? "text-foreground relative after:content-[''] after:absolute after:bottom-[-4px] after:left-1/2 after:-translate-x-1/2 after:w-6 after:h-0.5 after:bg-primary after:rounded-full"
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  Earn
                </a>
              </div>

              <button
                className="w-9 h-9 flex items-center justify-center rounded-full bg-muted hover:bg-primary hover:text-primary-foreground transition-colors ml-2 text-foreground"
                onClick={() => setShowInviteModal(true)}
                title="Invite by email"
              >
                <UserPlus className="h-4 w-4" />
              </button>

              <div className="ml-2">
                <NotificationButton />
              </div>

              <div className="w-4"></div>

              <div className="ml-2">
                <ThemeToggle />
              </div>

              <div className="relative ml-2">
                <button
                  ref={profileButtonRef}
                  className="p-2 text-xl rounded-full hover:bg-muted transition-colors text-foreground"
                  onClick={toggleProfileMenu}
                  aria-expanded={showProfileMenu}
                >
                  <User className="h-6 w-6" />
                </button>

                {showProfileMenu && (
                  <div className="absolute top-full right-0 mt-2 w-60 bg-white rounded-md shadow-lg overflow-hidden z-50" ref={profileMenuRef}>
                    <div className="p-4 bg-white border-b border-slate-200 flex items-center">
                      <div className="w-10 h-10 rounded-full overflow-hidden border border-slate-200 mr-3">
                        <img
                          src={userData?.avatar_url || (userData?.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png')}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex flex-col">
                        <span className="font-semibold text-sm text-slate-900">
                          {userData?.display_name || currentUser.user_metadata?.full_name || currentUser.email}
                        </span>
                        <span className="text-xs text-slate-500 truncate max-w-[180px]">{currentUser.email}</span>
                      </div>
                    </div>
                    <div className="py-1">
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                        e.preventDefault();
                        setShowProfileMenu(false);
                        navigateTo('/profile');
                      }}>
                        <User className="mr-2 h-4 w-4" /> Profile
                      </a>
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                        e.preventDefault();
                        setShowProfileMenu(false);
                        navigateTo('/invitations');
                      }}>
                        <Mail className="mr-2 h-4 w-4" /> Invitations
                      </a>
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                        e.preventDefault();
                        setShowProfileMenu(false);
                        navigateTo('/teams');
                      }}>
                        <Users className="mr-2 h-4 w-4" /> Teams
                      </a>
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                        e.preventDefault();
                        setShowProfileMenu(false);
                        navigateTo('/settings');
                      }}>
                        <Settings className="mr-2 h-4 w-4" /> Settings
                      </a>
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                        e.preventDefault();
                        setShowProfileMenu(false);
                        navigateTo('/bugs');
                      }}>
                        <Bug className="mr-2 h-4 w-4" /> Report Bug
                      </a>
                      {isAdmin && (
                        <>
                          <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                            e.preventDefault();
                            setShowProfileMenu(false);
                            navigateTo('/admin');
                          }}>
                            <Gauge className="mr-2 h-4 w-4" /> Admin Dashboard
                          </a>
                          <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                            e.preventDefault();
                            setShowProfileMenu(false);
                            navigateTo('/validation/metrics');
                          }}>
                            <TrendingUp className="mr-2 h-4 w-4" /> Validation Metrics
                          </a>
                          <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                            e.preventDefault();
                            setShowProfileMenu(false);
                            navigateTo('/analytics/contributions');
                          }}>
                            <BarChart3 className="mr-2 h-4 w-4" /> Contribution Analytics
                          </a>
                        </>
                      )}
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-slate-700 hover:bg-slate-50" onClick={(e) => {
                        e.preventDefault();
                        setShowProfileMenu(false);
                        navigateTo('/track');
                      }}>
                        <Gauge className="mr-2 h-4 w-4" /> Track Dashboard
                      </a>
                      <hr className="my-1 border-slate-200" />
                      <a href="#" className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50" onClick={handleLogout}>
                        <LogOut className="mr-2 h-4 w-4" /> Logout
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Invite Modal */}
      <InviteModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        inviteType="friend"
      />
    </nav>
  );
};

export default ModernNavbar;
