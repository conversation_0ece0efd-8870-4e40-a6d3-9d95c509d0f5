import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';

// Authentication function (copied from working test)
async function authenticateUser(page) {
  console.log('🔐 Starting authentication...');

  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);

  // Multi-step authentication flow
  console.log('🔘 Clicking Sign In button...');
  const signInButton = page.locator('text="Sign In"').first();
  await signInButton.click();
  await page.waitForTimeout(1000);

  console.log('🔘 Clicking LOGIN button...');
  const loginButton = page.locator('text="LOGIN"').first();
  await loginButton.click();
  await page.waitForTimeout(2000);

  // Find form inputs with more specific selectors
  const emailInput = page.locator('input[placeholder*="@"], input[type="email"]').first();
  const passwordInput = page.locator('input[placeholder*="password"], input[type="password"]').first();

  console.log('📧 Found email input with placeholder containing "@"');
  console.log('🔒 Found password input with placeholder containing "password"');

  console.log('📝 Filling login form...');
  await emailInput.fill('<EMAIL>');
  await passwordInput.fill('TestPassword123!');

  console.log('🔘 Clicking submit button...');
  const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  await submitButton.click();

  // Wait for navigation with longer timeout
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);

  const finalUrl = page.url();

  // Check multiple dashboard indicators (same as working test)
  const newProjectCard = page.locator('text="New Project"').isVisible().catch(() => false);
  const browseProjectsCard = page.locator('text="Browse Projects"').isVisible().catch(() => false);
  const trackContributionCard = page.locator('text="Track Contribution"').isVisible().catch(() => false);
  const viewAnalyticsCard = page.locator('text="View Analytics"').isVisible().catch(() => false);

  // Dashboard is considered loaded if we have the action cards
  const hasDashboard = (await newProjectCard) && (await browseProjectsCard) && (await trackContributionCard) && (await viewAnalyticsCard);
  const isAuthenticated = hasDashboard && finalUrl.includes('/dashboard');

  console.log(`🔐 Authentication ${isAuthenticated ? 'successful' : 'failed'}: ${finalUrl}`);
  console.log(`📊 Dashboard visible: ${hasDashboard}`);

  return isAuthenticated;
}

test.describe('Navigation Debug Tests', () => {
  test('Debug Track Contribution Navigation', async ({ page }) => {
    // Authenticate
    const isAuthenticated = await authenticateUser(page);
    expect(isAuthenticated).toBe(true);

    // Verify we're on dashboard with action cards
    const trackCard = page.locator('text="Track Contribution"');
    await expect(trackCard).toBeVisible({ timeout: 10000 });
    console.log('✅ Track Contribution card is visible');

    // Log current URL before click
    const beforeUrl = page.url();
    console.log(`📍 URL before click: ${beforeUrl}`);

    // Add console listener to catch any JavaScript errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Browser console error: ${msg.text()}`);
      }
    });

    // Add error listener
    page.on('pageerror', error => {
      console.log(`❌ Page error: ${error.message}`);
    });

    // Try to click the Track Contribution card
    console.log('🔘 Attempting to click Track Contribution card...');
    
    // First try: Click by text
    try {
      await trackCard.click();
      await page.waitForTimeout(2000);
      const afterUrl1 = page.url();
      console.log(`📍 URL after text click: ${afterUrl1}`);
    } catch (error) {
      console.log(`❌ Text click failed: ${error.message}`);
    }

    // Second try: Click by cursor-pointer class
    try {
      const clickableCard = page.locator('.cursor-pointer:has-text("Track Contribution")').first();
      await clickableCard.click();
      await page.waitForTimeout(2000);
      const afterUrl2 = page.url();
      console.log(`📍 URL after cursor-pointer click: ${afterUrl2}`);
    } catch (error) {
      console.log(`❌ Cursor-pointer click failed: ${error.message}`);
    }

    // Third try: Click by Card component
    try {
      const cardComponent = page.locator('[class*="card"]:has-text("Track Contribution")').first();
      await cardComponent.click();
      await page.waitForTimeout(2000);
      const afterUrl3 = page.url();
      console.log(`📍 URL after card component click: ${afterUrl3}`);
    } catch (error) {
      console.log(`❌ Card component click failed: ${error.message}`);
    }

    // Check final URL
    const finalUrl = page.url();
    console.log(`📍 Final URL: ${finalUrl}`);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'test-results/debug-navigation.png', fullPage: true });
  });
});
