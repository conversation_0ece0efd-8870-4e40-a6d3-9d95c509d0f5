import React from "react";
import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";
import { cn } from "../../../lib/utils";

/**
 * Button Component - HeroUI Implementation
 *
 * A versatile button component with multiple variants and sizes.
 * Compatible with shadcn/ui Button API for easy migration.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.variant] - Button variant (default, destructive, outline, secondary, ghost, link, primary, success, warning, info)
 * @param {string} [props.size] - Button size (default, sm, lg, icon)
 * @param {boolean} [props.asChild] - Whether to render as a child component
 * @param {React.ReactNode} props.children - Button content
 * @returns {React.ReactElement} - Button component
 */
const Button = React.forwardRef(({ 
  className, 
  variant = "default", 
  size = "default", 
  asChild = false, 
  children, 
  ...props 
}, ref) => {
  // Map shadcn/ui variants to HeroUI variants
  const getHeroUIVariant = (variant) => {
    switch (variant) {
      case "default":
      case "primary":
        return "solid";
      case "destructive":
        return "solid";
      case "outline":
        return "bordered";
      case "secondary":
        return "flat";
      case "ghost":
        return "light";
      case "link":
        return "light";
      case "success":
        return "solid";
      case "warning":
        return "solid";
      case "info":
        return "solid";
      default:
        return "solid";
    }
  };

  // Map shadcn/ui variants to HeroUI colors
  const getHeroUIColor = (variant) => {
    switch (variant) {
      case "default":
      case "primary":
        return "primary";
      case "destructive":
        return "danger";
      case "outline":
        return "default";
      case "secondary":
        return "secondary";
      case "ghost":
        return "default";
      case "link":
        return "primary";
      case "success":
        return "success";
      case "warning":
        return "warning";
      case "info":
        return "primary";
      default:
        return "primary";
    }
  };

  // Map shadcn/ui sizes to HeroUI sizes
  const getHeroUISize = (size) => {
    switch (size) {
      case "sm":
        return "sm";
      case "lg":
        return "lg";
      case "icon":
        return "sm";
      case "default":
      default:
        return "md";
    }
  };

  // Handle asChild prop for compatibility
  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      className: cn(className, children.props.className),
      ref,
      ...props
    });
  }

  return (
    <HeroUIButton
      ref={ref}
      variant={getHeroUIVariant(variant)}
      color={getHeroUIColor(variant)}
      size={getHeroUISize(size)}
      className={cn(
        // Additional styling for link variant
        variant === "link" && "underline-offset-4 hover:underline",
        // Icon button styling
        size === "icon" && "min-w-unit-10 w-10 h-10 px-0",
        className
      )}
      {...props}
    >
      {children}
    </HeroUIButton>
  );
});

Button.displayName = "Button";

export { Button };
