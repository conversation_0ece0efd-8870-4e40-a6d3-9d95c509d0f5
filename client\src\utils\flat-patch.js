/**
 * Flat Package Patch
 *
 * This module provides a compatibility layer for the flat package
 * to fix the ES module import issue with HeroUI.
 *
 * The issue: HeroUI tries to import flat with `import flatten from 'flat'`
 * but the flat package exports the function as the main export, not as default.
 */

// Import the flat package - Vite will handle the CommonJS to ESM conversion
import * as flatPackage from 'flat';

// The flat package exports the flatten function as the main export
// In CommonJS: module.exports = flatten; module.exports.unflatten = unflatten;
// In ESM conversion: { default: flatten, unflatten: unflatten }
const flatten = flatPackage.default || flatPackage;
const unflatten = flatPackage.unflatten;

// Export both named and default for maximum compatibility
export default flatten;
export { flatten, unflatten };
