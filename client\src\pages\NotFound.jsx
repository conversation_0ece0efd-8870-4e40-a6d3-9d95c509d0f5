import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>, Card, CardBody } from '@heroui/react';
import { motion } from 'framer-motion';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-2xl mx-auto text-center"
      >
        <Card className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border-0 shadow-2xl">
          <CardBody className="p-12">
            <motion.div
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h1 className="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 mb-4">
                404
              </h1>
              <h2 className="text-3xl font-bold text-default-800 mb-6">Page Not Found</h2>
              <p className="text-lg text-default-600 mb-8 leading-relaxed">
                The page you are looking for might have been removed, had its name changed,
                or is temporarily unavailable.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  as={Link}
                  to="/"
                  size="lg"
                  color="primary"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold px-8 py-4"
                >
                  Go to Homepage
                </Button>
                <Button
                  size="lg"
                  variant="bordered"
                  className="border-2 border-default-300 hover:bg-default-100 font-semibold px-8 py-4"
                  onPress={() => window.history.back()}
                >
                  Go Back
                </Button>
              </div>
            </motion.div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default NotFound;
