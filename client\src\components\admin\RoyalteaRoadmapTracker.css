/* Roadmap Tracker Styles - Colors handled by HeroUI */

/* Fix for zoom issues */
.roadmap-container {
  zoom: 1 !important;
  transform: none !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* General container */
.roadmap-wrapper {
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 0 auto;
  max-width: 1200px;
}

/* Header section */
.roadmap-header {
  margin-bottom: 2rem;
}

.roadmap-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.roadmap-buttons {
  display: flex;
  gap: 0.5rem;
}

.roadmap-button {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

/* Button colors handled by HeroUI */

/* Progress section */
.roadmap-progress {
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.roadmap-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.roadmap-progress-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.roadmap-progress-percentage {
  font-size: 1.125rem;
  font-weight: 700;
}

.roadmap-progress-bar-bg {
  width: 100%;
  height: 1rem;
  border-radius: 9999px;
}

.roadmap-progress-bar {
  height: 1rem;
  border-radius: 9999px;
  transition: width 0.5s ease-in-out;
}

.roadmap-progress-stats {
  margin-top: 0.5rem;
  text-align: right;
  font-size: 0.875rem;
}

/* Phase section - colors handled by HeroUI */
.roadmap-phase {
  border-radius: 0.5rem;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.roadmap-phase-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
}

.roadmap-phase-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roadmap-phase-title-text {
  font-size: 1.125rem;
  font-weight: 600;
}

.roadmap-phase-timeframe {
  font-size: 0.875rem;
}

.roadmap-phase-progress {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.roadmap-phase-progress-bar-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roadmap-phase-progress-bar-bg {
  width: 6rem;
  height: 0.625rem;
  border-radius: 9999px;
}

.roadmap-phase-progress-bar {
  height: 0.625rem;
  border-radius: 9999px;
  transition: width 0.3s;
}

.roadmap-phase-progress-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.roadmap-phase-chevron {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s;
}

.roadmap-phase-chevron.expanded {
  transform: rotate(180deg);
}

.roadmap-phase-edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  margin-right: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Section content */
.roadmap-phase-content {
  padding: 1rem;
}

.roadmap-section {
  padding-left: 1rem;
  margin-bottom: 1.5rem;
}

.roadmap-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.roadmap-section-title {
  font-size: 1rem;
  font-weight: 500;
}

.roadmap-section-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roadmap-section-progress-bar-bg {
  width: 5rem;
  height: 0.375rem;
  border-radius: 9999px;
}

.roadmap-section-progress-bar {
  height: 0.375rem;
  border-radius: 9999px;
}

.roadmap-section-progress-text {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Tasks */
.roadmap-tasks {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.roadmap-task {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.roadmap-task-checkbox {
  margin-top: 0.25rem;
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
}

.roadmap-task-text.completed {
  text-decoration: line-through;
}

/* Footer */
.roadmap-footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.875rem;
}

/* Loading animation */
.roadmap-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;
}

.roadmap-spinner {
  animation: spin 1s linear infinite;
  height: 3rem;
  width: 3rem;
  border-radius: 50%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Saving indicator */
.roadmap-saving {
  display: inline-block;
  animation: pulse 2s infinite;
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}
