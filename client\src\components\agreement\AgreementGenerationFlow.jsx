/**
 * Agreement Generation Flow Component
 * 
 * Seamless integration between project creation and agreement generation:
 * - Automatic template selection based on industry and collaboration type
 * - Dynamic agreement customization based on project configuration
 * - Real-time preview and editing capabilities
 * - Integration with all agreement system components
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  FileText, 
  Eye, 
  Download, 
  Send, 
  CheckCircle, 
  AlertCircle, 
  Info,
  Settings,
  Users,
  DollarSign,
  Shield
} from 'lucide-react';

// Import agreement system utilities
import { agreementTemplateEngine } from '@/utils/agreement/agreementTemplateEngine';
import { revenueCalculationEngine } from '@/utils/agreement/revenueCalculationEngine';
import { ipRightsFramework } from '@/utils/agreement/ipRightsFramework';

export default function AgreementGenerationFlow({ 
  ventureData, 
  allianceData, 
  onAgreementGenerated,
  onCancel 
}) {
  const [currentStep, setCurrentStep] = useState('template');
  const [agreementData, setAgreementData] = useState({
    templateId: '',
    customizations: {},
    variables: {},
    clauses: [],
    signatures: []
  });
  const [generatedAgreement, setGeneratedAgreement] = useState(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewContent, setPreviewContent] = useState('');

  useEffect(() => {
    // Auto-select template based on studio configuration
    selectRecommendedTemplate();
  }, [ventureData, allianceData]);

  const selectRecommendedTemplate = async () => {
    try {
      const recommendation = await agreementTemplateEngine.recommendTemplate({
        industry: allianceData.industry,
        collaborationType: allianceData.collaborationType,
        revenueModel: allianceData.revenueModel,
        ipOwnershipModel: allianceData.ipOwnershipModel,
        contributorCount: ventureData.contributors?.length || 0,
        hasEquity: allianceData.revenueModel === 'equity_based',
        hasIP: ventureData.deliverables?.some(d => d.type === 'software' || d.type === 'design'),
        isInternational: allianceData.jurisdiction !== 'US'
      });

      if (recommendation.template) {
        setAgreementData(prev => ({
          ...prev,
          templateId: recommendation.template.id,
          variables: recommendation.suggestedVariables || {}
        }));
      }
    } catch (error) {
      console.error('Error selecting template:', error);
    }
  };

  const generateAgreement = async () => {
    setIsGenerating(true);
    try {
      // Prepare agreement generation data
      const generationData = {
        templateId: agreementData.templateId,
        
        // Project information
        project: {
          name: ventureData.name,
          description: ventureData.description,
          objectives: ventureData.objectives,
          scope: ventureData.scope,
          deliverables: ventureData.deliverables,
          milestones: ventureData.milestones,
          startDate: ventureData.startDate,
          endDate: ventureData.endDate
        },
        
        // Studio context
        studio: {
          name: allianceData.name,
          industry: allianceData.industry,
          collaborationType: allianceData.collaborationType,
          jurisdiction: allianceData.jurisdiction,
          governingLaw: allianceData.governingLaw,
          disputeResolution: allianceData.disputeResolution
        },
        
        // Contributors and roles
        contributors: ventureData.contributors.map(contributor => ({
          email: contributor.email,
          role: contributor.role,
          responsibilities: contributor.responsibilities,
          revenueShare: contributor.revenueShare,
          ipRights: contributor.ipRights
        })),
        
        // Revenue model configuration
        revenueModel: {
          type: allianceData.revenueModel,
          currency: allianceData.currency,
          paymentFrequency: allianceData.paymentFrequency,
          minimumPayout: allianceData.minimumPayout,
          shares: ventureData.contributors.reduce((acc, c) => {
            acc[c.email] = c.revenueShare;
            return acc;
          }, {})
        },
        
        // IP rights configuration
        ipRights: {
          ownershipModel: allianceData.ipOwnershipModel,
          attributionRequired: allianceData.attributionRequired,
          assignments: ventureData.contributors.reduce((acc, c) => {
            acc[c.email] = c.ipRights;
            return acc;
          }, {})
        },
        
        // Custom variables and clauses
        customizations: agreementData.customizations,
        variables: agreementData.variables,
        additionalClauses: agreementData.clauses
      };

      // Generate the agreement
      const response = await fetch('/api/agreements/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(generationData),
      });

      if (response.ok) {
        const agreement = await response.json();
        setGeneratedAgreement(agreement);
        setPreviewContent(agreement.content);
        setCurrentStep('preview');
        
        if (onAgreementGenerated) {
          onAgreementGenerated(agreement);
        }
      } else {
        throw new Error('Failed to generate agreement');
      }
    } catch (error) {
      console.error('Error generating agreement:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsGenerating(false);
    }
  };

  const sendForSignatures = async () => {
    try {
      const response = await fetch(`/api/agreements/${generatedAgreement.id}/send-for-signatures`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contributors: ventureData.contributors.map(c => ({
            email: c.email,
            role: c.role
          }))
        }),
      });

      if (response.ok) {
        // Handle successful sending
        setCurrentStep('sent');
      }
    } catch (error) {
      console.error('Error sending for signatures:', error);
    }
  };

  const renderTemplateSelection = () => (
    <div className="space-y-6">
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          Based on your studio configuration ({allianceData.industry} - {allianceData.collaborationType}), 
          we've recommended the most appropriate agreement template.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle>Recommended Template</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Professional Collaboration Agreement</h4>
                <p className="text-sm text-gray-500">
                  Comprehensive agreement for {allianceData.industry} collaborations with {allianceData.revenueModel} revenue sharing
                </p>
              </div>
              <Badge variant="secondary">Recommended</Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Includes:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Revenue sharing terms</li>
                  <li>IP rights assignment</li>
                  <li>Milestone payments</li>
                  <li>Termination clauses</li>
                </ul>
              </div>
              <div>
                <strong>Compliance:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>{allianceData.jurisdiction} jurisdiction</li>
                  <li>Industry best practices</li>
                  <li>Tax considerations</li>
                  <li>Dispute resolution</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={() => setCurrentStep('customize')} className="bg-blue-600 hover:bg-blue-700">
          Customize Agreement
        </Button>
      </div>
    </div>
  );

  const renderCustomization = () => (
    <div className="space-y-6">
      <Tabs defaultValue="variables" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="variables">Variables</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="ip">IP Rights</TabsTrigger>
          <TabsTrigger value="clauses">Clauses</TabsTrigger>
        </TabsList>
        
        <TabsContent value="variables" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>Agreement Variables</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Effective Date</Label>
                  <Input
                    type="date"
                    value={agreementData.variables.effectiveDate || ventureData.startDate}
                    onChange={(e) => setAgreementData(prev => ({
                      ...prev,
                      variables: { ...prev.variables, effectiveDate: e.target.value }
                    }))}
                  />
                </div>
                <div>
                  <Label>Governing Law</Label>
                  <Select 
                    value={agreementData.variables.governingLaw || allianceData.jurisdiction}
                    onValueChange={(value) => setAgreementData(prev => ({
                      ...prev,
                      variables: { ...prev.variables, governingLaw: value }
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select jurisdiction" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="US">United States</SelectItem>
                      <SelectItem value="CA">Canada</SelectItem>
                      <SelectItem value="UK">United Kingdom</SelectItem>
                      <SelectItem value="EU">European Union</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label>Confidentiality Period (months)</Label>
                <Input
                  type="number"
                  value={agreementData.variables.confidentialityPeriod || 24}
                  onChange={(e) => setAgreementData(prev => ({
                    ...prev,
                    variables: { ...prev.variables, confidentialityPeriod: parseInt(e.target.value) }
                  }))}
                  min="12"
                  max="120"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5" />
                <span>Revenue Terms</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Revenue sharing configuration inherited from project setup. 
                  Total: {ventureData.contributors?.reduce((sum, c) => sum + (c.revenueShare || 0), 0)}%
                </AlertDescription>
              </Alert>
              
              <div className="space-y-2">
                {ventureData.contributors?.map((contributor) => (
                  <div key={contributor.id} className="flex justify-between items-center p-3 border rounded">
                    <div>
                      <div className="font-medium">{contributor.email}</div>
                      <div className="text-sm text-gray-500">{contributor.role}</div>
                    </div>
                    <Badge variant="outline">{contributor.revenueShare}%</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="ip" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="w-5 h-5" />
                <span>IP Rights</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  IP ownership model: <strong>{allianceData.ipOwnershipModel?.replace(/_/g, ' ')}</strong>
                </AlertDescription>
              </Alert>
              
              <div className="space-y-2">
                {ventureData.contributors?.map((contributor) => (
                  <div key={contributor.id} className="flex justify-between items-center p-3 border rounded">
                    <div>
                      <div className="font-medium">{contributor.email}</div>
                      <div className="text-sm text-gray-500">{contributor.role}</div>
                    </div>
                    <Badge variant="outline">{contributor.ipRights?.replace(/_/g, ' ')}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="clauses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Additional Clauses</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Add any additional clauses or special terms..."
                rows={6}
                value={agreementData.clauses.join('\n\n')}
                onChange={(e) => setAgreementData(prev => ({
                  ...prev,
                  clauses: e.target.value.split('\n\n').filter(clause => clause.trim())
                }))}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep('template')}>
          Back to Template
        </Button>
        <Button onClick={generateAgreement} disabled={isGenerating} className="bg-blue-600 hover:bg-blue-700">
          {isGenerating ? 'Generating...' : 'Generate Agreement'}
        </Button>
      </div>
    </div>
  );

  const renderPreview = () => (
    <div className="space-y-6">
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          Agreement successfully generated! Review the content below and send for signatures.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Agreement Preview</span>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Download PDF
              </Button>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                Full Preview
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
            <pre className="whitespace-pre-wrap text-sm">{previewContent}</pre>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={() => setCurrentStep('customize')}>
          Edit Agreement
        </Button>
        <Button onClick={sendForSignatures} className="bg-green-600 hover:bg-green-700">
          <Send className="w-4 h-4 mr-2" />
          Send for Signatures
        </Button>
      </div>
    </div>
  );

  const renderSent = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-2">Agreement Sent!</h3>
        <p className="text-gray-600">
          The agreement has been sent to all contributors for electronic signature.
          You'll receive notifications as each person signs.
        </p>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium">Sent to:</h4>
        {ventureData.contributors?.map((contributor) => (
          <div key={contributor.id} className="flex items-center justify-center space-x-2">
            <span>{contributor.email}</span>
            <Badge variant="outline">Pending</Badge>
          </div>
        ))}
      </div>

      <Button onClick={() => window.close()} className="bg-blue-600 hover:bg-blue-700">
        Continue to Project
      </Button>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'template':
        return renderTemplateSelection();
      case 'customize':
        return renderCustomization();
      case 'preview':
        return renderPreview();
      case 'sent':
        return renderSent();
      default:
        return renderTemplateSelection();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Generate Agreement</h1>
        <p className="text-gray-600">
          Create a comprehensive legal agreement for <strong>{ventureData.name}</strong>
        </p>
      </div>

      {/* Step Indicator */}
      <div className="mb-8">
        <div className="flex justify-center space-x-8">
          {[
            { id: 'template', title: 'Template', icon: FileText },
            { id: 'customize', title: 'Customize', icon: Settings },
            { id: 'preview', title: 'Preview', icon: Eye },
            { id: 'sent', title: 'Send', icon: Send }
          ].map((step, index) => {
            const Icon = step.icon;
            const isActive = step.id === currentStep;
            const isCompleted = ['template', 'customize', 'preview'].indexOf(currentStep) > ['template', 'customize', 'preview'].indexOf(step.id);
            
            return (
              <div
                key={step.id}
                className={`flex flex-col items-center space-y-2 ${
                  isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                }`}
              >
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                    isActive
                      ? 'border-blue-600 bg-blue-50'
                      : isCompleted
                      ? 'border-green-600 bg-green-50'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                </div>
                <span className="text-sm font-medium">{step.title}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {renderCurrentStep()}
        </CardContent>
      </Card>

      {/* Cancel Button */}
      {onCancel && (
        <div className="mt-6 text-center">
          <Button variant="ghost" onClick={onCancel}>
            Cancel Agreement Generation
          </Button>
        </div>
      )}
    </div>
  );
}
