-- Check the actual structure of the contribution_tracking_config table
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'contribution_tracking_config'
ORDER BY ordinal_position;

-- Also check if the table exists at all
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'contribution_tracking_config'
) as table_exists;

-- Check what data is in the table
SELECT * FROM public.contribution_tracking_config LIMIT 5;
