// User-related functions using Netlify Identity and Supabase
const { createClient } = require('@supabase/supabase-js');
const { NetlifyJwtVerifier } = require('@serverless-jwt/netlify');

// Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Initialize the JWT verifier
const verifyJwt = NetlifyJwtVerifier({
  issuer: process.env.SITE_URL,
  audience: process.env.SITE_URL
});

// Get the current user's profile
const getCurrentUser = async (event, context) => {
  try {
    // The user's identity is available in context.clientContext.user
    const netlifyUser = context.clientContext.user;

    if (!netlifyUser) {
      return {
        statusCode: 401,
        body: JSON.stringify({ message: "Not authenticated" })
      };
    }

    // Check if user exists in Supabase, create if not
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', netlifyUser.sub)
      .single();

    if (error && error.code === 'PGRST116') {
      // User not found, create a new user record
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert([
          {
            id: netlifyUser.sub,
            email: netlifyUser.email,
            display_name: netlifyUser.user_metadata.full_name || netlifyUser.email,
            date_created: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (createError) throw createError;

      return {
        statusCode: 200,
        body: JSON.stringify(newUser)
      };
    } else if (error) {
      throw error;
    }

    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Error getting user", error: error.message })
    };
  }
};

// Get all users
const getUsers = async () => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*');

    if (error) throw error;

    return {
      statusCode: 200,
      body: JSON.stringify(data)
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Error getting users", error: error.message })
    };
  }
};

// Route requests to the appropriate handler
exports.handler = verifyJwt(async (event, context) => {
  const path = event.path.replace('/.netlify/functions/users', '');

  switch (path) {
    case '/me':
      return getCurrentUser(event, context);
    case '/index':
      return getUsers();
    default:
      return {
        statusCode: 404,
        body: JSON.stringify({ message: "Not found" })
      };
  }
});
