import { test, expect } from '@playwright/test';

test.describe('Track Page Content Analysis', () => {
  test('Analyze what content is actually on the Track page', async ({ page }) => {
    console.log('🔍 Analyzing Track page content...');
    
    // Navigate to login page
    await page.goto('https://royalty.technology/login');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
    
    console.log('✅ Successfully logged in');
    
    // Navigate to Track page
    console.log('🎯 Navigating to Track page...');
    await page.goto('https://royalty.technology/track');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'track-page-content.png', fullPage: true });
    
    // Check for various text content that should be on the page
    const textChecks = [
      'Project Management Hub',
      'Track progress, manage tasks, and collaborate with your team',
      'Task Board',
      'Quick Actions',
      'Project Overview',
      'Loading Track Page',
      'Authentication Required',
      'My Project',
      'Default project for task management'
    ];
    
    console.log('📝 Checking for text content:');
    for (const text of textChecks) {
      const isVisible = await page.locator(`text=${text}`).isVisible().catch(() => false);
      console.log(`  "${text}": ${isVisible ? '✅' : '❌'}`);
    }
    
    // Check for specific UI elements
    const elementChecks = [
      { selector: '[data-testid="project-overview"]', name: 'Project Overview Cards' },
      { selector: '[data-testid="quick-actions"]', name: 'Quick Actions Panel' },
      { selector: '[data-testid="kanban-board"]', name: 'Kanban Board' },
      { selector: '[data-testid="task-board"]', name: 'Task Board' },
      { selector: '.bg-gradient-to-br', name: 'Gradient Background' },
      { selector: 'h1', name: 'Main Heading' },
      { selector: 'h3', name: 'Section Headings' }
    ];
    
    console.log('🎯 Checking for UI elements:');
    for (const { selector, name } of elementChecks) {
      const count = await page.locator(selector).count();
      const isVisible = count > 0 && await page.locator(selector).first().isVisible().catch(() => false);
      console.log(`  ${name} (${selector}): ${count} found, visible: ${isVisible ? '✅' : '❌'}`);
    }
    
    // Get the main heading text
    const mainHeading = await page.locator('h1').first().textContent().catch(() => 'Not found');
    console.log(`📋 Main heading: "${mainHeading}"`);
    
    // Check for any cards or panels
    const cardCount = await page.locator('.card, [class*="card"], [class*="Card"]').count();
    console.log(`🃏 Card elements found: ${cardCount}`);
    
    // Check for navigation elements
    const navElements = await page.locator('nav, [class*="nav"], [class*="Nav"]').count();
    console.log(`🧭 Navigation elements found: ${navElements}`);
    
    // Check for buttons
    const buttonCount = await page.locator('button').count();
    console.log(`🔘 Buttons found: ${buttonCount}`);
    
    // Get page structure
    const bodyContent = await page.locator('body').innerHTML().catch(() => 'Error getting body content');
    const hasTrackContent = bodyContent.includes('Project Management Hub') || 
                           bodyContent.includes('Track progress') ||
                           bodyContent.includes('Task Board');
    
    console.log(`📄 Page has Track-related content: ${hasTrackContent ? '✅' : '❌'}`);
    
    // Check if the page is actually showing the Track page or something else
    const currentUrl = page.url();
    console.log(`🌐 Current URL: ${currentUrl}`);
    
    // The test should pass if we can see the main Track page content
    expect(mainHeading).toContain('Project Management Hub');
    
    console.log('✅ Track page content analysis complete');
  });
});
