import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Tabs, Tab, Badge, Progress, Button, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { useSEO } from '../../utils/seo/SEOManager';
import { useAccessibility } from '../../utils/accessibility/AccessibilityManager';
import { usePerformanceAudit } from '../../utils/performance/PerformanceAuditor';
import { useMobileOptimization } from '../../utils/mobile/MobileOptimizer';

/**
 * SEO & Accessibility Dashboard
 * 
 * Comprehensive dashboard for monitoring and optimizing:
 * - SEO performance and meta tag optimization
 * - Accessibility compliance and WCAG guidelines
 * - Performance metrics and Core Web Vitals
 * - Mobile optimization and responsive design
 * - Structured data and social media optimization
 */
const SEOAccessibilityDashboard = ({ className = "" }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [auditResults, setAuditResults] = useState({
    seo: null,
    accessibility: null,
    performance: null,
    mobile: null
  });

  // Hooks for optimization utilities
  const { getCurrentMeta } = useSEO();
  const { auditPage, checkContrast, getStats } = useAccessibility();
  const { report } = usePerformanceAudit();
  const { deviceInfo, isMobile, isTablet } = useMobileOptimization();

  // Run comprehensive audit
  const runAudit = async () => {
    try {
      const results = {
        seo: await runSEOAudit(),
        accessibility: auditPage(),
        performance: report,
        mobile: {
          deviceInfo,
          isMobile,
          isTablet,
          optimizations: getMobileOptimizations()
        }
      };
      
      setAuditResults(results);
    } catch (error) {
      console.error('Audit failed:', error);
    }
  };

  // SEO audit
  const runSEOAudit = async () => {
    const meta = getCurrentMeta();
    const issues = [];
    
    // Check meta tags
    if (!meta.title || meta.title.length < 30) {
      issues.push({
        type: 'title',
        severity: 'error',
        message: 'Title tag is missing or too short (should be 30-60 characters)'
      });
    }
    
    if (!meta.description || meta.description.length < 120) {
      issues.push({
        type: 'description',
        severity: 'error',
        message: 'Meta description is missing or too short (should be 120-160 characters)'
      });
    }

    // Check images
    const images = document.querySelectorAll('img');
    const imagesWithoutAlt = Array.from(images).filter(img => !img.alt);
    if (imagesWithoutAlt.length > 0) {
      issues.push({
        type: 'images',
        severity: 'warning',
        message: `${imagesWithoutAlt.length} images missing alt text`
      });
    }

    // Check headings structure
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const h1Count = document.querySelectorAll('h1').length;
    if (h1Count !== 1) {
      issues.push({
        type: 'headings',
        severity: h1Count === 0 ? 'error' : 'warning',
        message: h1Count === 0 ? 'Missing H1 tag' : 'Multiple H1 tags found'
      });
    }

    // Check structured data
    const structuredData = document.querySelector('script[type="application/ld+json"]');
    if (!structuredData) {
      issues.push({
        type: 'structured-data',
        severity: 'warning',
        message: 'No structured data found'
      });
    }

    return {
      meta,
      issues,
      score: calculateSEOScore(issues),
      recommendations: getSEORecommendations(issues)
    };
  };

  // Get mobile optimizations
  const getMobileOptimizations = () => {
    const optimizations = [];
    
    if (!document.querySelector('meta[name="viewport"]')) {
      optimizations.push({
        type: 'viewport',
        message: 'Missing viewport meta tag',
        severity: 'error'
      });
    }

    const touchTargets = document.querySelectorAll('button, a, input[type="button"]');
    const smallTargets = Array.from(touchTargets).filter(el => {
      const rect = el.getBoundingClientRect();
      return rect.width < 44 || rect.height < 44;
    });

    if (smallTargets.length > 0) {
      optimizations.push({
        type: 'touch-targets',
        message: `${smallTargets.length} touch targets smaller than 44px`,
        severity: 'warning'
      });
    }

    return optimizations;
  };

  // Calculate SEO score
  const calculateSEOScore = (issues) => {
    let score = 100;
    issues.forEach(issue => {
      if (issue.severity === 'error') score -= 20;
      else if (issue.severity === 'warning') score -= 10;
    });
    return Math.max(0, score);
  };

  // Get SEO recommendations
  const getSEORecommendations = (issues) => {
    return issues.map(issue => ({
      ...issue,
      solution: getSEOSolution(issue.type)
    }));
  };

  // Get SEO solution
  const getSEOSolution = (type) => {
    const solutions = {
      title: 'Add a descriptive title tag between 30-60 characters',
      description: 'Add a compelling meta description between 120-160 characters',
      images: 'Add descriptive alt text to all images',
      headings: 'Use proper heading hierarchy with one H1 per page',
      'structured-data': 'Add JSON-LD structured data for better search visibility'
    };
    return solutions[type] || 'Review and optimize this element';
  };

  // Get status color
  const getStatusColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'danger';
  };

  // Run initial audit
  useEffect(() => {
    runAudit();
  }, []);

  return (
    <div className={`seo-accessibility-dashboard ${className}`}>
      {/* Header */}
      <Card className="mb-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🎯</span>
              <div>
                <h1 className="text-2xl font-bold">SEO & Accessibility Dashboard</h1>
                <p className="text-default-600">
                  Comprehensive optimization monitoring and recommendations
                </p>
              </div>
            </div>
            
            <Button
              color="primary"
              variant="flat"
              onClick={runAudit}
            >
              Run Audit
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-primary mb-1">
                {auditResults.seo?.score || 0}
              </div>
              <div className="text-sm text-default-600">SEO Score</div>
              <Progress
                value={auditResults.seo?.score || 0}
                color={getStatusColor(auditResults.seo?.score || 0)}
                className="mt-2"
                size="sm"
              />
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-success mb-1">
                {auditResults.accessibility?.summary?.total || 0}
              </div>
              <div className="text-sm text-default-600">A11y Issues</div>
              <div className="text-xs text-default-500 mt-1">
                {auditResults.accessibility?.summary?.errors || 0} errors, {auditResults.accessibility?.summary?.warnings || 0} warnings
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-warning mb-1">
                {auditResults.performance?.score?.score || 0}
              </div>
              <div className="text-sm text-default-600">Performance</div>
              <div className="text-xs text-default-500 mt-1">
                Grade: {auditResults.performance?.score?.grade || 'N/A'}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-secondary mb-1">
                {auditResults.mobile?.optimizations?.length || 0}
              </div>
              <div className="text-sm text-default-600">Mobile Issues</div>
              <div className="text-xs text-default-500 mt-1">
                {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'} view
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Analysis Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            <Tab
              key="overview"
              title={
                <div className="flex items-center space-x-2">
                  <span>📊</span>
                  <span>Overview</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Optimization Summary</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* SEO Summary */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      🔍 SEO Analysis
                      <Badge
                        color={getStatusColor(auditResults.seo?.score || 0)}
                        variant="flat"
                      >
                        {auditResults.seo?.score || 0}/100
                      </Badge>
                    </h4>
                    {auditResults.seo?.issues?.slice(0, 3).map((issue, index) => (
                      <div key={index} className="mb-2 text-sm">
                        <Chip
                          color={issue.severity === 'error' ? 'danger' : 'warning'}
                          variant="flat"
                          size="sm"
                          className="mr-2"
                        >
                          {issue.severity}
                        </Chip>
                        {issue.message}
                      </div>
                    ))}
                  </div>

                  {/* Accessibility Summary */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3 flex items-center gap-2">
                      ♿ Accessibility
                      <Badge
                        color={auditResults.accessibility?.summary?.errors > 0 ? 'danger' : 'success'}
                        variant="flat"
                      >
                        {auditResults.accessibility?.summary?.total || 0} issues
                      </Badge>
                    </h4>
                    {auditResults.accessibility?.issues?.slice(0, 3).map((issue, index) => (
                      <div key={index} className="mb-2 text-sm">
                        <Chip
                          color={issue.severity === 'error' ? 'danger' : 'warning'}
                          variant="flat"
                          size="sm"
                          className="mr-2"
                        >
                          {issue.severity}
                        </Chip>
                        {issue.message}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </Tab>

            <Tab
              key="seo"
              title={
                <div className="flex items-center space-x-2">
                  <span>🔍</span>
                  <span>SEO</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">SEO Analysis</h3>
                
                {auditResults.seo && (
                  <div className="space-y-6">
                    {/* Meta Tags */}
                    <div className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-3">Meta Tags</h4>
                      <div className="space-y-2 text-sm">
                        <div>
                          <strong>Title:</strong> {auditResults.seo.meta?.title || 'Not set'}
                          <span className="text-default-500 ml-2">
                            ({auditResults.seo.meta?.title?.length || 0} characters)
                          </span>
                        </div>
                        <div>
                          <strong>Description:</strong> {auditResults.seo.meta?.description || 'Not set'}
                          <span className="text-default-500 ml-2">
                            ({auditResults.seo.meta?.description?.length || 0} characters)
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Issues */}
                    <div className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-3">Issues & Recommendations</h4>
                      <div className="space-y-3">
                        {auditResults.seo.recommendations?.map((rec, index) => (
                          <div key={index} className="border-l-4 border-warning pl-4">
                            <div className="font-medium">{rec.message}</div>
                            <div className="text-sm text-default-600 mt-1">{rec.solution}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Tab>

            <Tab
              key="accessibility"
              title={
                <div className="flex items-center space-x-2">
                  <span>♿</span>
                  <span>Accessibility</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Accessibility Analysis</h3>
                
                {auditResults.accessibility && (
                  <div className="space-y-6">
                    {/* Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 border rounded-lg">
                        <div className="text-2xl font-bold text-danger">
                          {auditResults.accessibility.summary.errors}
                        </div>
                        <div className="text-sm text-default-600">Errors</div>
                      </div>
                      <div className="text-center p-4 border rounded-lg">
                        <div className="text-2xl font-bold text-warning">
                          {auditResults.accessibility.summary.warnings}
                        </div>
                        <div className="text-sm text-default-600">Warnings</div>
                      </div>
                      <div className="text-center p-4 border rounded-lg">
                        <div className="text-2xl font-bold text-success">
                          {auditResults.accessibility.summary.total}
                        </div>
                        <div className="text-sm text-default-600">Total Issues</div>
                      </div>
                    </div>

                    {/* Issues */}
                    <div className="space-y-3">
                      {auditResults.accessibility.issues?.map((issue, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">{issue.message}</div>
                            <Chip
                              color={issue.severity === 'error' ? 'danger' : 'warning'}
                              variant="flat"
                              size="sm"
                            >
                              {issue.severity}
                            </Chip>
                          </div>
                          <div className="text-sm text-default-600">
                            Type: {issue.type}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Tab>

            <Tab
              key="performance"
              title={
                <div className="flex items-center space-x-2">
                  <span>⚡</span>
                  <span>Performance</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Performance Analysis</h3>
                
                {auditResults.performance && (
                  <div className="space-y-6">
                    {/* Core Web Vitals */}
                    <div className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-3">Core Web Vitals</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {Object.entries(auditResults.performance.coreWebVitals || {}).map(([key, vital]) => (
                          <div key={key} className="text-center p-3 border rounded">
                            <div className="font-semibold">{key}</div>
                            <div className="text-2xl font-bold mt-1">
                              {vital.value ? `${vital.value.toFixed(0)}${key === 'CLS' ? '' : 'ms'}` : 'N/A'}
                            </div>
                            <Badge
                              color={
                                vital.rating === 'good' ? 'success' :
                                vital.rating === 'needs-improvement' ? 'warning' : 'danger'
                              }
                              variant="flat"
                              size="sm"
                              className="mt-1"
                            >
                              {vital.rating || 'unknown'}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Recommendations */}
                    {auditResults.performance.recommendations?.length > 0 && (
                      <div className="border rounded-lg p-4">
                        <h4 className="font-semibold mb-3">Recommendations</h4>
                        <div className="space-y-3">
                          {auditResults.performance.recommendations.map((rec, index) => (
                            <div key={index} className="border-l-4 border-primary pl-4">
                              <div className="font-medium">{rec.message}</div>
                              <ul className="text-sm text-default-600 mt-1 ml-4">
                                {rec.suggestions?.map((suggestion, i) => (
                                  <li key={i} className="list-disc">{suggestion}</li>
                                ))}
                              </ul>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </Tab>

            <Tab
              key="mobile"
              title={
                <div className="flex items-center space-x-2">
                  <span>📱</span>
                  <span>Mobile</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Mobile Optimization</h3>
                
                <div className="space-y-6">
                  {/* Device Info */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-semibold mb-3">Device Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Device Type:</strong> {isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}
                      </div>
                      <div>
                        <strong>Viewport:</strong> {deviceInfo.viewportWidth}×{deviceInfo.viewportHeight}
                      </div>
                      <div>
                        <strong>Screen:</strong> {deviceInfo.screenWidth}×{deviceInfo.screenHeight}
                      </div>
                      <div>
                        <strong>Pixel Ratio:</strong> {deviceInfo.devicePixelRatio}
                      </div>
                    </div>
                  </div>

                  {/* Mobile Issues */}
                  {auditResults.mobile?.optimizations?.length > 0 && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-semibold mb-3">Mobile Issues</h4>
                      <div className="space-y-2">
                        {auditResults.mobile.optimizations.map((opt, index) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <span>{opt.message}</span>
                            <Chip
                              color={opt.severity === 'error' ? 'danger' : 'warning'}
                              variant="flat"
                              size="sm"
                            >
                              {opt.severity}
                            </Chip>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
};

export default SEOAccessibilityDashboard;
