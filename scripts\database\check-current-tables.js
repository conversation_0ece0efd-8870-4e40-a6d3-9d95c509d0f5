import { createClient } from '@supabase/supabase-js';

// Use hardcoded credentials from the client config
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM4MjA1NTksImV4cCI6MjA1OTM5NjU1OX0.e0oaDNVbA563SZJHPm6UTFOXcLbWorKn42a_brChpKQ';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkCurrentTables() {
  console.log('🔍 Checking current database tables...\n');

  try {
    // Get all tables in public schema
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_table_info');

    if (tablesError) {
      console.log('Using alternative method to check tables...');
      
      // Try to query specific tables we know should exist
      const tablesToCheck = [
        'users', 'projects', 'teams', 'team_members', 
        'user_activity', 'user_activity_logs', 'tasks',
        'project_contributors', 'contributions'
      ];

      console.log('📋 Checking specific tables:');
      for (const table of tablesToCheck) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);
          
          if (error) {
            console.log(`❌ ${table}: ${error.message}`);
          } else {
            console.log(`✅ ${table}: exists (${data?.length || 0} sample records)`);
          }
        } catch (err) {
          console.log(`❌ ${table}: ${err.message}`);
        }
      }
    } else {
      console.log('✅ Available tables:', tables);
    }

    // Check for user_activity specifically since it's causing 404s
    console.log('\n🔍 Checking user_activity table specifically...');
    try {
      const { data, error } = await supabase
        .from('user_activity')
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ user_activity table error: ${error.message}`);
        console.log('This explains the 404 errors in the frontend!');
      } else {
        console.log(`✅ user_activity table exists with ${data?.length || 0} records`);
      }
    } catch (err) {
      console.log(`❌ user_activity table error: ${err.message}`);
    }

    // Check projects table structure
    console.log('\n🔍 Checking projects table structure...');
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('id, name, description, created_by, team_id')
        .limit(1);
      
      if (error) {
        console.log(`❌ projects table error: ${error.message}`);
      } else {
        console.log(`✅ projects table accessible`);
      }
    } catch (err) {
      console.log(`❌ projects table error: ${err.message}`);
    }

    // Check team_members table structure  
    console.log('\n🔍 Checking team_members table structure...');
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select('team_id, user_id, role, status')
        .limit(1);
      
      if (error) {
        console.log(`❌ team_members table error: ${error.message}`);
      } else {
        console.log(`✅ team_members table accessible`);
      }
    } catch (err) {
      console.log(`❌ team_members table error: ${err.message}`);
    }

  } catch (error) {
    console.error('❌ Error checking database:', error.message);
  }
}

checkCurrentTables();
