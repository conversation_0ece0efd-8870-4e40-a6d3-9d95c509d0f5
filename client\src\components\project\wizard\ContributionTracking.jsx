import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import {
  Card,
  CardBody,
  Input,
  Button,
  Select,
  SelectItem,
  Chip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell
} from '@heroui/react';

const ContributionTracking = ({ projectData, setProjectData }) => {
  // Common task types with default difficulty values based on project type
  const commonTaskTypes = {
    game: [
      { name: 'Concept Art', difficulty: 4 },
      { name: 'Character Design', difficulty: 5 },
      { name: 'Environment Art', difficulty: 4 },
      { name: 'UI Design', difficulty: 3 },
      { name: 'Animation', difficulty: 5 },
      { name: 'Sound Design', difficulty: 4 },
      { name: 'Music Composition', difficulty: 4 },
      { name: 'Voice Acting', difficulty: 3 },
      { name: 'Level Design', difficulty: 4 },
      { name: 'Game Design Document', difficulty: 3 },
      { name: 'Programming - Core Systems', difficulty: 5 },
      { name: 'Programming - UI', difficulty: 3 },
      { name: 'Programming - AI', difficulty: 5 },
      { name: 'Programming - Physics', difficulty: 5 },
      { name: 'Programming - Networking', difficulty: 5 },
      { name: 'QA Testing', difficulty: 2 },
      { name: 'Bug Fixing', difficulty: 3 },
      { name: 'Project Management', difficulty: 3 },
      { name: 'Marketing Materials', difficulty: 3 },
      { name: 'Trailer Production', difficulty: 4 },
      { name: 'Attend Meeting', difficulty: 1 }
    ],
    app: [
      { name: 'UI/UX Design', difficulty: 4 },
      { name: 'Wireframing', difficulty: 3 },
      { name: 'Frontend Development', difficulty: 4 },
      { name: 'Backend Development', difficulty: 5 },
      { name: 'API Integration', difficulty: 4 },
      { name: 'Database Design', difficulty: 4 },
      { name: 'User Testing', difficulty: 2 },
      { name: 'Documentation', difficulty: 2 },
      { name: 'Bug Fixing', difficulty: 3 },
      { name: 'Performance Optimization', difficulty: 5 },
      { name: 'Security Implementation', difficulty: 5 },
      { name: 'Attend Meeting', difficulty: 1 }
    ],
    website: [
      { name: 'UI/UX Design', difficulty: 4 },
      { name: 'Wireframing', difficulty: 3 },
      { name: 'Frontend Development', difficulty: 4 },
      { name: 'Backend Development', difficulty: 5 },
      { name: 'Content Creation', difficulty: 3 },
      { name: 'SEO Optimization', difficulty: 3 },
      { name: 'Responsive Design', difficulty: 4 },
      { name: 'Cross-browser Testing', difficulty: 3 },
      { name: 'Performance Optimization', difficulty: 4 },
      { name: 'Attend Meeting', difficulty: 1 }
    ],
    other: [
      { name: 'Design', difficulty: 4 },
      { name: 'Development', difficulty: 4 },
      { name: 'Documentation', difficulty: 2 },
      { name: 'Testing', difficulty: 2 },
      { name: 'Project Management', difficulty: 3 },
      { name: 'Content Creation', difficulty: 3 },
      { name: 'Research', difficulty: 3 },
      { name: 'Analysis', difficulty: 4 },
      { name: 'Attend Meeting', difficulty: 1 }
    ]
  };

  const [newTaskType, setNewTaskType] = useState('');
  const [newTaskDifficulty, setNewTaskDifficulty] = useState(3);
  const [taskTypes, setTaskTypes] = useState([]);

  // Initialize task types based on project type
  useEffect(() => {
    if (!projectData.contribution_tracking.task_types || projectData.contribution_tracking.task_types.length === 0) {
      const projectType = projectData.project_type || 'other';
      const defaultTaskTypes = commonTaskTypes[projectType] || commonTaskTypes.other;

      setTaskTypes(defaultTaskTypes);

      setProjectData({
        ...projectData,
        contribution_tracking: {
          ...projectData.contribution_tracking,
          task_types: defaultTaskTypes
        }
      });

      toast.success(`Added default task types for ${projectType} project`);
    } else {
      setTaskTypes(projectData.contribution_tracking.task_types);
    }
  }, []);

  // Update task difficulty
  const updateTaskDifficulty = (index, newDifficulty) => {
    const updatedTaskTypes = [...taskTypes];
    updatedTaskTypes[index].difficulty = newDifficulty;

    setTaskTypes(updatedTaskTypes);

    setProjectData({
      ...projectData,
      contribution_tracking: {
        ...projectData.contribution_tracking,
        task_types: updatedTaskTypes
      }
    });
  };

  // Add new task type
  const addTaskType = () => {
    if (!newTaskType) {
      toast.error('Task type name is required');
      return;
    }

    if (taskTypes.some(task => task.name === newTaskType)) {
      toast.error('This task type already exists');
      return;
    }

    const newTask = {
      name: newTaskType,
      difficulty: newTaskDifficulty
    };

    const updatedTaskTypes = [...taskTypes, newTask];
    setTaskTypes(updatedTaskTypes);

    setProjectData({
      ...projectData,
      contribution_tracking: {
        ...projectData.contribution_tracking,
        task_types: updatedTaskTypes
      }
    });

    setNewTaskType('');
    setNewTaskDifficulty(3);
    toast.success('Task type added successfully');
  };

  // Remove task type
  const removeTaskType = (index) => {
    const updatedTaskTypes = taskTypes.filter((_, i) => i !== index);

    setTaskTypes(updatedTaskTypes);

    setProjectData({
      ...projectData,
      contribution_tracking: {
        ...projectData.contribution_tracking,
        task_types: updatedTaskTypes
      }
    });

    toast.success('Task type removed');
  };

  // Integration options
  const integrations = [
    {
      name: 'GitHub',
      icon: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png',
      status: 'coming soon'
    },
    {
      name: 'Trello',
      icon: 'https://cdn.iconscout.com/icon/free/png-256/trello-226534.png',
      status: 'coming soon'
    },
    {
      name: 'Codecks',
      icon: 'https://codecks.io/favicon/favicon-32x32.png',
      status: 'coming soon'
    },
    {
      name: 'Jira',
      icon: 'https://cdn.worldvectorlogo.com/logos/jira-1.svg',
      status: 'coming soon'
    },
    {
      name: 'Discord',
      icon: 'https://assets-global.website-files.com/6257adef93867e50d84d30e2/636e0a6a49cf127bf92de1e2_icon_clyde_blurple_RGB.png',
      status: 'coming soon'
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold text-foreground mb-2">Contribution Tracking</h2>
        <p className="text-default-500 mb-6">
          Configure common task types and their difficulty levels for your project.
        </p>
      </div>

      <Card className="bg-primary/5 border-primary/20">
        <CardBody className="p-4">
          <div className="flex items-start gap-3">
            <div className="text-primary text-xl">ℹ️</div>
            <div>
              <h4 className="text-sm font-medium text-foreground mb-2">Task Difficulty Levels</h4>
              <p className="text-sm text-default-600">
                We've pre-populated common task types for your project with default difficulty levels.
                You can adjust these values or add custom task types as needed.
              </p>
            </div>
          </div>
        </CardBody>
      </Card>

      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium text-foreground mb-2">Common Task Types</h3>
          <p className="text-default-500 mb-4">
            These task types will be available when tracking contributions. Adjust difficulty levels as needed.
          </p>
        </div>

        <Card>
          <CardBody className="p-0">
            <Table aria-label="Task types table">
              <TableHeader>
                <TableColumn>TASK TYPE</TableColumn>
                <TableColumn>DIFFICULTY LEVEL</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {taskTypes.map((task, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{task.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="bordered"
                          isIconOnly
                          onPress={() => updateTaskDifficulty(index, Math.max(1, task.difficulty - 1))}
                          isDisabled={task.difficulty <= 1}
                        >
                          −
                        </Button>
                        <Chip
                          size="sm"
                          color={
                            task.difficulty === 1 ? 'success' :
                            task.difficulty === 2 ? 'primary' :
                            task.difficulty === 3 ? 'warning' :
                            task.difficulty === 4 ? 'secondary' : 'danger'
                          }
                          variant="flat"
                        >
                          {task.difficulty}
                        </Chip>
                        <Button
                          size="sm"
                          variant="bordered"
                          isIconOnly
                          onPress={() => updateTaskDifficulty(index, Math.min(5, task.difficulty + 1))}
                          isDisabled={task.difficulty >= 5}
                        >
                          +
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        color="danger"
                        variant="bordered"
                        onPress={() => removeTaskType(index)}
                        startContent={<span className="text-xs">🗑️</span>}
                      >
                        Remove
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-6">
            <h4 className="text-md font-medium text-foreground mb-4">Add Custom Task Type</h4>
            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-end">
              <div className="md:col-span-6">
                <Input
                  label="Task Name"
                  placeholder="e.g., Character Rigging"
                  value={newTaskType}
                  onChange={(e) => setNewTaskType(e.target.value)}
                  variant="bordered"
                  size="lg"
                />
              </div>
              <div className="md:col-span-4">
                <Select
                  label="Difficulty (1-5)"
                  selectedKeys={[newTaskDifficulty.toString()]}
                  onSelectionChange={(keys) => setNewTaskDifficulty(parseInt(Array.from(keys)[0]))}
                  variant="bordered"
                  size="lg"
                >
                  <SelectItem key="1">1 - Very Easy</SelectItem>
                  <SelectItem key="2">2 - Easy</SelectItem>
                  <SelectItem key="3">3 - Medium</SelectItem>
                  <SelectItem key="4">4 - Hard</SelectItem>
                  <SelectItem key="5">5 - Very Hard</SelectItem>
                </Select>
              </div>
              <div className="md:col-span-2">
                <Button
                  color="primary"
                  onPress={addTaskType}
                  size="lg"
                  className="w-full"
                >
                  Add
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-default-50">
          <CardBody className="p-6">
            <h4 className="text-md font-medium text-foreground mb-4">Difficulty Scale Reference</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Chip size="sm" color="success" variant="flat">1</Chip>
                  <span className="text-sm text-default-600">Very Easy - Minimal effort (e.g., attending meetings)</span>
                </div>
                <div className="flex items-center gap-3">
                  <Chip size="sm" color="primary" variant="flat">2</Chip>
                  <span className="text-sm text-default-600">Easy - Straightforward tasks (e.g., basic documentation)</span>
                </div>
                <div className="flex items-center gap-3">
                  <Chip size="sm" color="warning" variant="flat">3</Chip>
                  <span className="text-sm text-default-600">Medium - Average effort (e.g., standard programming tasks)</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Chip size="sm" color="secondary" variant="flat">4</Chip>
                  <span className="text-sm text-default-600">Hard - Significant effort (e.g., complex design work)</span>
                </div>
                <div className="flex items-center gap-3">
                  <Chip size="sm" color="danger" variant="flat">5</Chip>
                  <span className="text-sm text-default-600">Very Hard - Complex tasks (e.g., engine programming)</span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium text-foreground mb-2">External Integrations</h3>
          <p className="text-default-500 mb-4">
            Connect with external tools to automatically track contributions (coming soon).
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {integrations.map((integration, index) => (
            <Card key={index} className="bg-default-50">
              <CardBody className="p-4 text-center">
                <img
                  src={integration.icon}
                  alt={integration.name}
                  className="w-8 h-8 mx-auto mb-2 object-contain"
                />
                <div className="text-sm font-medium text-foreground mb-1">{integration.name}</div>
                <Chip size="sm" color="default" variant="flat">
                  {integration.status}
                </Chip>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ContributionTracking;
