import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import EnhancedTrackPage from '../../pages/track/EnhancedTrackPage';

// Mock the context providers
const MockProviders = ({ children }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

// Mock data
const mockTasks = [
  {
    id: '1',
    title: 'Test Task 1',
    description: 'Test description 1',
    status: 'todo',
    difficulty: 'medium',
    priority: 'high',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    title: 'Test Task 2',
    description: 'Test description 2',
    status: 'in_progress',
    difficulty: 'hard',
    priority: 'medium',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

const mockProjects = [
  {
    id: '1',
    name: 'Test Project',
    description: 'Test project description',
    status: 'active',
  },
];

// Mock hooks and services
vi.mock('../../hooks/useSupabase', () => ({
  default: () => ({
    supabase: {
      from: vi.fn(() => ({
        select: vi.fn().mockReturnThis(),
        insert: vi.fn().mockReturnThis(),
        update: vi.fn().mockReturnThis(),
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({ data: mockProjects[0], error: null }),
        then: vi.fn().mockResolvedValue({ data: mockTasks, error: null }),
      })),
      channel: vi.fn(() => ({
        on: vi.fn().mockReturnThis(),
        subscribe: vi.fn(),
        unsubscribe: vi.fn(),
      })),
    },
  }),
}));

vi.mock('../../contexts/supabase-auth.context', () => ({
  useSupabaseAuth: () => ({
    user: { id: 'test-user-id', email: '<EMAIL>' },
    loading: false,
  }),
}));

describe('EnhancedTrackPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the main track page components', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // Check for main page elements
    expect(screen.getByText('Track')).toBeInTheDocument();
    
    // Wait for components to load
    await waitFor(() => {
      // Check for project overview cards
      expect(screen.getByText('Active Projects')).toBeInTheDocument();
      expect(screen.getByText('Tasks This Week')).toBeInTheDocument();
      expect(screen.getByText('Team Velocity')).toBeInTheDocument();
      expect(screen.getByText('Upcoming Deadlines')).toBeInTheDocument();
    });
  });

  it('renders the kanban board', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    await waitFor(() => {
      // Check for kanban columns
      expect(screen.getByText('To Do')).toBeInTheDocument();
      expect(screen.getByText('In Progress')).toBeInTheDocument();
      expect(screen.getByText('Review')).toBeInTheDocument();
      expect(screen.getByText('Done')).toBeInTheDocument();
      expect(screen.getByText('Blocked')).toBeInTheDocument();
    });
  });

  it('renders the integration status monitor', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    await waitFor(() => {
      expect(screen.getByText('Integration Status')).toBeInTheDocument();
      expect(screen.getByText('GitHub')).toBeInTheDocument();
      expect(screen.getByText('Slack')).toBeInTheDocument();
      expect(screen.getByText('Trello')).toBeInTheDocument();
      expect(screen.getByText('Linear')).toBeInTheDocument();
      expect(screen.getByText('Jira')).toBeInTheDocument();
      expect(screen.getByText('Discord')).toBeInTheDocument();
    });
  });

  it('opens task creation modal when add task button is clicked', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // Find and click the add task button
    const addTaskButton = screen.getByText('Create Task');
    fireEvent.click(addTaskButton);

    await waitFor(() => {
      expect(screen.getByText('Create New Task')).toBeInTheDocument();
    });
  });

  it('displays mobile floating action button on mobile screens', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // The FAB should be present (though hidden by CSS on desktop)
    const fab = screen.getByRole('button', { name: /create task/i });
    expect(fab).toBeInTheDocument();
  });

  it('handles task updates correctly', async () => {
    const mockUpdateTask = vi.fn();
    
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // Wait for tasks to load
    await waitFor(() => {
      expect(screen.getByText('Test Task 1')).toBeInTheDocument();
    });

    // Simulate task update (this would normally be triggered by drag-drop or edit)
    // Since we're testing the component structure, we'll verify the task is displayed
    expect(screen.getByText('Test Task 1')).toBeInTheDocument();
    expect(screen.getByText('Test Task 2')).toBeInTheDocument();
  });

  it('displays quick actions panel', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
      expect(screen.getByText('Create Task')).toBeInTheDocument();
      expect(screen.getByText('Invite Collaborator')).toBeInTheDocument();
      expect(screen.getByText('Check Mission Board')).toBeInTheDocument();
      expect(screen.getByText('Project Analytics')).toBeInTheDocument();
    });
  });

  it('shows time tracking panel when enabled', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // The time tracking panel should be present
    await waitFor(() => {
      expect(screen.getByText('Time Tracking')).toBeInTheDocument();
    });
  });

  it('handles responsive design correctly', () => {
    // Test desktop view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    const { rerender } = render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // Vertical navigation should be present on desktop
    expect(document.querySelector('.hidden.md\\:flex')).toBeInTheDocument();

    // Test mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    rerender(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    // Mobile FAB should be present
    expect(screen.getByRole('button', { name: /create task/i })).toBeInTheDocument();
  });

  it('displays integration sync functionality', async () => {
    render(
      <MockProviders>
        <EnhancedTrackPage />
      </MockProviders>
    );

    await waitFor(() => {
      // Check for sync buttons in integration monitor
      const syncButtons = screen.getAllByText(/sync now|connect/i);
      expect(syncButtons.length).toBeGreaterThan(0);
    });
  });
});
