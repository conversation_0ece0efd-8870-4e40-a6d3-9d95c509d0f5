import { test, expect } from '@playwright/test';

/**
 * Mobile Navigation Test Suite
 * 
 * Tests mobile-specific navigation functionality including:
 * - Mobile responsive design
 * - Touch interactions
 * - Hamburger menu functionality
 * - Mobile navigation patterns
 * - Responsive breakpoints
 */

// Test configuration
const BASE_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Mobile viewport configurations
const MOBILE_VIEWPORTS = {
  mobile: { width: 375, height: 667 }, // iPhone SE
  tablet: { width: 768, height: 1024 }, // iPad
  largeMobile: { width: 414, height: 896 } // iPhone 11 Pro Max
};

test.describe('Mobile Navigation Tests', () => {
  
  // Helper function for authentication
  async function authenticateUser(page) {
    console.log('🔐 Starting mobile authentication...');
    
    await page.goto(BASE_URL);
    await page.waitForLoadState('networkidle');
    
    // Click Sign In button
    const signInButton = page.locator('button:has-text("Sign In")').first();
    await signInButton.waitFor({ state: 'visible', timeout: 10000 });
    await signInButton.click();
    console.log('🔘 Clicked Sign In button...');
    
    // Click LOGIN button
    const loginButton = page.locator('button:has-text("LOGIN")').first();
    await loginButton.waitFor({ state: 'visible', timeout: 10000 });
    await loginButton.click();
    console.log('🔘 Clicked LOGIN button...');
    
    // Fill login form
    const emailInput = page.locator('input[type="email"], input[placeholder*="@"]').first();
    await emailInput.waitFor({ state: 'visible', timeout: 10000 });
    await emailInput.fill(TEST_CREDENTIALS.email);
    console.log('📧 Filled email input...');
    
    const passwordInput = page.locator('input[type="password"], input[placeholder*="password" i]').first();
    await passwordInput.waitFor({ state: 'visible', timeout: 10000 });
    await passwordInput.fill(TEST_CREDENTIALS.password);
    console.log('🔒 Filled password input...');
    
    // Submit form
    const submitButton = page.locator('button[type="submit"], button:has-text("Submit"), button:has-text("Sign In")').last();
    await submitButton.click();
    console.log('🔘 Clicked submit button...');
    
    // Wait for authentication to complete
    await page.waitForURL(/dashboard|\/$/);
    console.log('🔐 Mobile authentication successful');
    
    return page.url();
  }

  // Test mobile viewport responsiveness
  Object.entries(MOBILE_VIEWPORTS).forEach(([deviceName, viewport]) => {
    test(`Mobile Navigation - ${deviceName} (${viewport.width}x${viewport.height})`, async ({ browser }) => {
      // Create context with touch support
      const context = await browser.newContext({
        viewport,
        hasTouch: true,
        isMobile: viewport.width < 768
      });
      const page = await context.newPage();

      try {
        // Authenticate user
        const finalUrl = await authenticateUser(page);
        console.log(`📱 Testing ${deviceName} navigation at ${finalUrl}`);

        // Take screenshot of mobile dashboard
        await page.screenshot({
          path: `test-results/mobile-${deviceName}-dashboard-${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
          fullPage: true
        });

        // Check if mobile navigation elements are visible
        const isMobileBreakpoint = viewport.width < 768;

        if (isMobileBreakpoint) {
          // Mobile-specific navigation should be visible
          console.log('📱 Testing mobile-specific navigation...');

          // Look for new mobile navigation elements
          const mobileNavElements = [
            'button[aria-label="Open navigation menu"]', // New hamburger menu
            'button[aria-label*="menu" i]',
            '.md\\:hidden', // Tailwind mobile-only classes
            '[class*="mobile"]'
          ];

          let mobileNavFound = false;
          for (const selector of mobileNavElements) {
            const element = page.locator(selector).first();
            if (await element.isVisible().catch(() => false)) {
              console.log(`✅ Found mobile navigation element: ${selector}`);
              mobileNavFound = true;

              // Test hamburger menu functionality
              if (selector.includes('Open navigation menu')) {
                console.log('🍔 Testing hamburger menu...');
                await element.click();
                await page.waitForTimeout(1000);

                // Take screenshot of opened menu
                await page.screenshot({
                  path: `test-results/mobile-${deviceName}-menu-open-${new Date().toISOString().replace(/[:.]/g, '-')}.png`
                });

                // Look for drawer content
                const drawerContent = page.locator('[role="dialog"], .drawer, [class*="drawer"]');
                if (await drawerContent.isVisible().catch(() => false)) {
                  console.log('✅ Mobile drawer menu opened successfully');

                  // Test navigation items in drawer
                  const navItems = page.locator('button:has-text("Start"), button:has-text("Track"), button:has-text("Earn")');
                  const navItemCount = await navItems.count();
                  console.log(`📊 Found ${navItemCount} navigation items in drawer`);

                  // Close drawer
                  const closeButton = page.locator('button[aria-label="Close navigation menu"]');
                  if (await closeButton.isVisible().catch(() => false)) {
                    await closeButton.click();
                    await page.waitForTimeout(500);
                    console.log('✅ Successfully closed mobile drawer');
                  }
                } else {
                  console.log('⚠️ Mobile drawer did not open');
                }
              }
              break;
            }
          }

          if (!mobileNavFound) {
            console.log('⚠️ No mobile navigation elements found');
          }

          // Test bottom navigation
          console.log('📱 Testing bottom navigation...');
          const bottomNavButtons = page.locator('.fixed.bottom-0 button');
          const bottomNavCount = await bottomNavButtons.count();
          console.log(`📊 Found ${bottomNavCount} bottom navigation buttons`);

          if (bottomNavCount > 0) {
            // Test first bottom nav button with force click to bypass overlays
            const firstBottomNav = bottomNavButtons.first();
            await firstBottomNav.waitFor({ state: 'visible', timeout: 5000 });

            // Take screenshot before interaction
            await page.screenshot({
              path: `test-results/mobile-${deviceName}-before-bottom-nav-${new Date().toISOString().replace(/[:.]/g, '-')}.png`
            });

            // Force click to bypass any overlays
            await firstBottomNav.click({ force: true });
            await page.waitForTimeout(2000);

            // Take screenshot after interaction
            await page.screenshot({
              path: `test-results/mobile-${deviceName}-after-bottom-nav-${new Date().toISOString().replace(/[:.]/g, '-')}.png`
            });

            console.log(`✅ Successfully tested bottom navigation on ${deviceName}`);
          }

        } else {
          // Tablet-specific navigation
          console.log('📱 Testing tablet navigation...');

          // Desktop navigation should be visible on tablet
          const desktopNavElements = page.locator('.hidden.md\\:flex, .hidden.md\\:block');
          const desktopNavCount = await desktopNavElements.count();
          console.log(`📊 Found ${desktopNavCount} desktop navigation elements on tablet`);
        }

        // Test navigation to core pages
        const corePages = ['/start', '/track', '/earn'];

        for (const pagePath of corePages) {
          try {
            // Try to navigate to the page
            await page.goto(`${BASE_URL}${pagePath}`);
            await page.waitForLoadState('networkidle', { timeout: 10000 });

            // Take screenshot of the page
            await page.screenshot({
              path: `test-results/mobile-${deviceName}-${pagePath.replace('/', '')}-${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
              fullPage: true
            });

            console.log(`✅ Successfully loaded ${pagePath} on ${deviceName}`);
          } catch (error) {
            console.log(`⚠️ Failed to load ${pagePath} on ${deviceName}: ${error.message}`);
          }
        }

        console.log(`✅ Completed ${deviceName} mobile navigation test`);

      } finally {
        await context.close();
      }
    });
  });

  // Test mobile menu functionality
  test('Mobile Menu Functionality', async ({ browser }) => {
    // Create context with touch support
    const context = await browser.newContext({
      viewport: MOBILE_VIEWPORTS.mobile,
      hasTouch: true,
      isMobile: true
    });
    const page = await context.newPage();

    try {
      // Authenticate user
      await authenticateUser(page);

      // Look for mobile menu trigger
      const menuTriggers = [
        'button[aria-label="Open navigation menu"]', // New hamburger menu
        'button[aria-label*="menu" i]',
        'button[aria-label*="navigation" i]'
      ];

      let menuTrigger = null;
      for (const selector of menuTriggers) {
        const element = page.locator(selector).first();
        if (await element.isVisible().catch(() => false)) {
          menuTrigger = element;
          console.log(`✅ Found mobile menu trigger: ${selector}`);
          break;
        }
      }

      if (menuTrigger) {
        // Test menu opening
        await menuTrigger.click();
        await page.waitForTimeout(1000);

        // Take screenshot of opened menu
        await page.screenshot({
          path: `test-results/mobile-menu-open-${new Date().toISOString().replace(/[:.]/g, '-')}.png`
        });

        // Look for drawer content
        const drawerContent = page.locator('[role="dialog"], .drawer, [class*="drawer"]');
        if (await drawerContent.isVisible().catch(() => false)) {
          console.log('✅ Mobile drawer opened successfully');

          // Look for menu items
          const menuItems = page.locator('button:has-text("Start"), button:has-text("Track"), button:has-text("Earn")');
          const menuItemCount = await menuItems.count();
          console.log(`📊 Found ${menuItemCount} menu items`);

          if (menuItemCount > 0) {
            // Test menu item interaction
            const firstMenuItem = menuItems.first();
            await firstMenuItem.click();
            await page.waitForTimeout(1000);

            console.log('✅ Successfully tested mobile menu functionality');
          }

          // Test close functionality
          const closeButton = page.locator('button[aria-label="Close navigation menu"]');
          if (await closeButton.isVisible().catch(() => false)) {
            await closeButton.click();
            await page.waitForTimeout(500);
            console.log('✅ Successfully closed mobile menu');
          }
        } else {
          console.log('⚠️ Mobile drawer did not open properly');
        }
      } else {
        console.log('⚠️ No mobile menu trigger found');
      }

    } finally {
      await context.close();
    }
  });

});
