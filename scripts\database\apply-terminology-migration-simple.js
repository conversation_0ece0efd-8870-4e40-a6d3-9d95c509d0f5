#!/usr/bin/env node

/**
 * Apply Terminology Migration - Simple Version
 * Applies the terminology migration using direct SQL execution
 */

import { createClient } from '@supabase/supabase-js';

// Use the known Supabase URL and service key
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  console.log('🔄 Applying Terminology Migration...');
  console.log('=====================================');

  try {
    // Step 1: Add studio_type to teams table
    console.log('\n1️⃣ Adding studio_type to teams table...');
    const { error: studioTypeError } = await supabase.rpc('exec', {
      sql: `
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'teams' AND column_name = 'studio_type') THEN
                ALTER TABLE public.teams ADD COLUMN studio_type TEXT DEFAULT 'emerging' CHECK (studio_type IN ('emerging', 'established', 'solo'));
                RAISE NOTICE 'Added studio_type column to teams table';
            ELSE
                RAISE NOTICE 'studio_type column already exists in teams table';
            END IF;
        END $$;
      `
    });

    if (studioTypeError) {
      console.log(`❌ Error: ${studioTypeError.message}`);
    } else {
      console.log('✅ studio_type column added successfully');
    }

    // Step 2: Add project terminology to projects table
    console.log('\n2️⃣ Adding project terminology to projects table...');
    const { error: projectError } = await supabase.rpc('exec', {
      sql: `
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'project_type') THEN
                ALTER TABLE public.projects ADD COLUMN project_type TEXT DEFAULT 'software' CHECK (project_type IN ('software', 'game', 'film', 'music', 'art', 'business', 'research', 'other'));
                RAISE NOTICE 'Added project_type column to projects table';
            ELSE
                RAISE NOTICE 'project_type column already exists in projects table';
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'projects' AND column_name = 'studio_id') THEN
                ALTER TABLE public.projects ADD COLUMN studio_id UUID REFERENCES public.teams(id);
                RAISE NOTICE 'Added studio_id column to projects table';
            ELSE
                RAISE NOTICE 'studio_id column already exists in projects table';
            END IF;
        END $$;
      `
    });

    if (projectError) {
      console.log(`❌ Error: ${projectError.message}`);
    } else {
      console.log('✅ Project terminology added successfully');
    }

    // Step 3: Add mission terminology to tasks table
    console.log('\n3️⃣ Adding mission terminology to tasks table...');
    const { error: missionError } = await supabase.rpc('exec', {
      sql: `
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'task_category') THEN
                ALTER TABLE public.tasks ADD COLUMN task_category TEXT DEFAULT 'task' CHECK (task_category IN ('mission', 'bounty', 'task'));
                RAISE NOTICE 'Added task_category column to tasks table';
            ELSE
                ALTER TABLE public.tasks DROP CONSTRAINT IF EXISTS tasks_task_category_check;
                ALTER TABLE public.tasks ADD CONSTRAINT tasks_task_category_check 
                    CHECK (task_category IN ('mission', 'bounty', 'task'));
                RAISE NOTICE 'Updated task_category constraint to include mission';
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_type') THEN
                ALTER TABLE public.tasks ADD COLUMN mission_type TEXT CHECK (mission_type IN ('skill', 'collaboration', 'achievement', 'exploration', 'social'));
                RAISE NOTICE 'Added mission_type column to tasks table';
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_requirements') THEN
                ALTER TABLE public.tasks ADD COLUMN mission_requirements JSONB;
                RAISE NOTICE 'Added mission_requirements column to tasks table';
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tasks' AND column_name = 'mission_rewards') THEN
                ALTER TABLE public.tasks ADD COLUMN mission_rewards JSONB;
                RAISE NOTICE 'Added mission_rewards column to tasks table';
            END IF;
        END $$;
      `
    });

    if (missionError) {
      console.log(`❌ Error: ${missionError.message}`);
    } else {
      console.log('✅ Mission terminology added successfully');
    }

    // Step 4: Add people type system to team_members
    console.log('\n4️⃣ Adding people type system to team_members...');
    const { error: peopleTypeError } = await supabase.rpc('exec', {
      sql: `
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'collaboration_type') THEN
                ALTER TABLE public.team_members ADD COLUMN collaboration_type TEXT DEFAULT 'studio_member' 
                    CHECK (collaboration_type IN ('studio_member', 'contractor', 'specialist'));
                RAISE NOTICE 'Added collaboration_type column to team_members table';
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'engagement_duration') THEN
                ALTER TABLE public.team_members ADD COLUMN engagement_duration TEXT DEFAULT 'permanent'
                    CHECK (engagement_duration IN ('permanent', 'project_based', 'one_off'));
                RAISE NOTICE 'Added engagement_duration column to team_members table';
            END IF;
            
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_members' AND column_name = 'specialization') THEN
                ALTER TABLE public.team_members ADD COLUMN specialization TEXT[];
                RAISE NOTICE 'Added specialization column to team_members table';
            END IF;
        END $$;
      `
    });

    if (peopleTypeError) {
      console.log(`❌ Error: ${peopleTypeError.message}`);
    } else {
      console.log('✅ People type system added successfully');
    }

    // Step 5: Test the new columns
    console.log('\n5️⃣ Testing new columns...');
    
    // Test teams.studio_type
    const { data: teamsTest, error: teamsError } = await supabase
      .from('teams')
      .select('id, name, studio_type')
      .limit(1);

    if (teamsError) {
      console.log(`❌ Teams test failed: ${teamsError.message}`);
    } else {
      console.log('✅ teams.studio_type column working');
    }

    // Test projects columns
    const { data: projectsTest, error: projectsTestError } = await supabase
      .from('projects')
      .select('id, name, project_type, studio_id')
      .limit(1);

    if (projectsTestError) {
      console.log(`❌ Projects test failed: ${projectsTestError.message}`);
    } else {
      console.log('✅ projects new columns working');
    }

    // Test tasks columns
    const { data: tasksTest, error: tasksTestError } = await supabase
      .from('tasks')
      .select('id, title, task_category, mission_type')
      .limit(1);

    if (tasksTestError) {
      console.log(`❌ Tasks test failed: ${tasksTestError.message}`);
    } else {
      console.log('✅ tasks mission columns working');
    }

    console.log('\n🎉 Terminology Migration Complete!');
    console.log('📋 Summary:');
    console.log('   • Alliance → Studio (teams.studio_type)');
    console.log('   • Venture → Project (projects.project_type, projects.studio_id)');
    console.log('   • Quest → Mission (tasks.task_category, mission_type, etc.)');
    console.log('   • People Types (team_members.collaboration_type, etc.)');

  } catch (error) {
    console.error('\n❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Test database connection first
async function testConnection() {
  console.log('🔌 Testing database connection...');
  
  try {
    const { data, error } = await supabase
      .from('teams')
      .select('id')
      .limit(1);

    if (error) {
      throw error;
    }

    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
async function main() {
  const connected = await testConnection();
  if (!connected) {
    process.exit(1);
  }

  await applyMigration();
}

// Handle command line execution
main().catch(console.error);
