import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import {
  calculateRoyaltyDistribution,
  formatCurrency,
  formatPercentage
} from '../../utils/royalty/royalty-calculator';

/**
 * RoyaltyCalculator Component
 *
 * A comprehensive calculator for royalty distributions based on different models
 * @param {Object} props - Component props
 * @param {string} props.projectId - Project ID
 * @param {string} props.revenueId - Optional revenue ID for specific calculation
 * @param {function} props.onSave - Callback function when distribution is saved
 * @param {function} props.onCancel - Callback function when calculation is canceled
 */
const RoyaltyCalculator = ({ projectId, revenueId, onSave, onCancel }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [project, setProject] = useState(null);
  const [revenue, setRevenue] = useState(null);
  const [contributors, setContributors] = useState([]);
  const [contributions, setContributions] = useState([]);
  const [calculationModel, setCalculationModel] = useState('equal_split');
  const [distribution, setDistribution] = useState([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [currency, setCurrency] = useState('USD');
  const [modelOptions, setModelOptions] = useState({
    tasks: { weight: 33.33 },
    time: { weight: 33.33 },
    difficulty: { weight: 33.34 },
    roles: {}
  });
  const [manualAdjustments, setManualAdjustments] = useState({});
  const [showAdjustments, setShowAdjustments] = useState(false);
  const [showModelDetails, setShowModelDetails] = useState(false);

  // Fetch project, contributors, and contributions data
  useEffect(() => {
    const fetchData = async () => {
      if (!projectId || !currentUser) return;

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;
        setProject(projectData);

        // Fetch royalty model data separately
        const { data: royaltyModelData, error: royaltyModelError } = await supabase
          .from('royalty_models')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (royaltyModelError && royaltyModelError.code !== 'PGRST116') {
          console.error('Error fetching royalty model:', royaltyModelError);
        }

        // Add royalty model to project data
        if (royaltyModelData) {
          projectData.royalty_model = royaltyModelData;

          // Set initial calculation model from project settings if available
          const { model_type, model_schema } = royaltyModelData;
          if (model_type === 'equal_split') {
            setCalculationModel('equal_split');
          } else if (model_type === 'task_based') {
            setCalculationModel('task_based');
          } else if (model_type === 'time_based') {
            setCalculationModel('time_based');
          } else if (model_type === 'role_based') {
            setCalculationModel('role_based');
          } else if (model_type === 'custom' && model_schema === 'cog') {
            setCalculationModel('cog_model');

            // Set weights from project settings
            if (royaltyModelData.configuration) {
              const { tasks_weight, hours_weight, difficulty_weight } = royaltyModelData.configuration;
              setModelOptions(prev => ({
                ...prev,
                tasks: { weight: tasks_weight || 33.33 },
                time: { weight: hours_weight || 33.33 },
                difficulty: { weight: difficulty_weight || 33.34 }
              }));
            }
          }
        }

        // Fetch contributors
        const { data: contributorsData, error: contributorsError } = await supabase
          .from('project_contributors')
          .select(`
            id,
            user_id,
            role,
            status
          `)
          .eq('project_id', projectId)
          .eq('status', 'active');

        if (contributorsError) throw contributorsError;

        // Fetch user data for all contributors
        const userIds = contributorsData.map(c => c.user_id).filter(Boolean);
        let userData = {};

        if (userIds.length > 0) {
          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('id, display_name, email')
            .in('id', userIds);

          if (usersError) throw usersError;

          // Create a map of user data by ID
          userData = usersData.reduce((acc, user) => {
            acc[user.id] = user;
            return acc;
          }, {});
        }

        const formattedContributors = contributorsData
          .filter(c => c.user_id && userData[c.user_id])
          .map(c => ({
            id: c.id,
            user_id: c.user_id,
            name: userData[c.user_id]?.display_name || userData[c.user_id]?.email || 'Unknown',
            role: c.role,
            weight: 1 // Default weight for role-based calculation
          }));

        setContributors(formattedContributors);

        // Initialize role weights
        const roleWeights = {};
        formattedContributors.forEach(c => {
          roleWeights[c.user_id] = 1;
        });

        setModelOptions(prev => ({
          ...prev,
          roles: roleWeights
        }));

        // Fetch contributions (only approved ones)
        const { data: contributionsData, error: contributionsError } = await supabase
          .from('contributions')
          .select('*')
          .eq('project_id', projectId)
          .eq('validation_status', 'approved');

        if (contributionsError) throw contributionsError;
        setContributions(contributionsData || []);

        // If revenueId is provided, fetch revenue data
        if (revenueId) {
          const { data: revenueData, error: revenueError } = await supabase
            .from('revenue')
            .select('*')
            .eq('id', revenueId)
            .single();

          if (revenueError) throw revenueError;
          setRevenue(revenueData);
          setTotalAmount(revenueData.amount);
          setCurrency(revenueData.currency || 'USD');
        }

      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load data for royalty calculation');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId, revenueId, currentUser]);

  // Calculate distribution when model or options change
  useEffect(() => {
    if (loading) return;

    calculateDistribution();
  }, [calculationModel, totalAmount, modelOptions, loading]);

  // Calculate distribution based on selected model
  const calculateDistribution = () => {
    if (!contributors || contributors.length === 0) return;

    let options = {};

    // Prepare options based on model
    switch (calculationModel) {
      case 'cog_model':
        options = {
          weights: {
            tasks: modelOptions.tasks.weight / 100,
            time: modelOptions.time.weight / 100,
            difficulty: modelOptions.difficulty.weight / 100
          }
        };
        break;
      case 'role_based':
        // Update contributor weights
        const contributorsWithWeights = contributors.map(c => ({
          ...c,
          weight: modelOptions.roles[c.user_id] || 1
        }));
        setContributors(contributorsWithWeights);
        break;
      default:
        break;
    }

    // Calculate distribution
    const result = calculateRoyaltyDistribution(
      calculationModel,
      totalAmount,
      contributors,
      contributions,
      options
    );

    // Apply manual adjustments if any
    if (Object.keys(manualAdjustments).length > 0) {
      const adjustedResult = result.map(item => {
        const adjustment = manualAdjustments[item.user_id];
        if (adjustment) {
          return {
            ...item,
            amount: item.amount + adjustment.amount,
            percentage: item.percentage + adjustment.percentage,
            manual_adjustment: adjustment
          };
        }
        return item;
      });

      setDistribution(adjustedResult);
    } else {
      setDistribution(result);
    }
  };

  // Handle model change
  const handleModelChange = (e) => {
    setCalculationModel(e.target.value);
    setManualAdjustments({});
  };

  // Handle amount change
  const handleAmountChange = (e) => {
    const value = parseFloat(e.target.value) || 0;
    setTotalAmount(value);
  };

  // Handle currency change
  const handleCurrencyChange = (e) => {
    setCurrency(e.target.value);
  };

  // Handle weight change for CoG model
  const handleWeightChange = (type, value) => {
    const numValue = parseFloat(value) || 0;

    // Update the specified weight
    setModelOptions(prev => {
      const newOptions = { ...prev };
      newOptions[type].weight = numValue;

      // Adjust other weights to ensure total is 100%
      const remainingWeight = 100 - numValue;
      const otherTypes = ['tasks', 'time', 'difficulty'].filter(t => t !== type);
      const currentSum = otherTypes.reduce((sum, t) => sum + (prev[t].weight || 0), 0);

      if (currentSum > 0) {
        const ratio = remainingWeight / currentSum;
        otherTypes.forEach(t => {
          newOptions[t].weight = parseFloat((prev[t].weight * ratio).toFixed(2));
        });
      } else {
        // If other weights are 0, distribute evenly
        otherTypes.forEach(t => {
          newOptions[t].weight = parseFloat((remainingWeight / otherTypes.length).toFixed(2));
        });
      }

      return newOptions;
    });
  };

  // Handle role weight change
  const handleRoleWeightChange = (userId, value) => {
    const numValue = parseFloat(value) || 1;

    setModelOptions(prev => ({
      ...prev,
      roles: {
        ...prev.roles,
        [userId]: numValue
      }
    }));
  };

  // Handle manual adjustment
  const handleManualAdjustment = (userId, type, value) => {
    const numValue = parseFloat(value) || 0;

    setManualAdjustments(prev => {
      const newAdjustments = { ...prev };

      if (!newAdjustments[userId]) {
        newAdjustments[userId] = { amount: 0, percentage: 0 };
      }

      newAdjustments[userId][type] = numValue;

      // If adjusting percentage, calculate amount
      if (type === 'percentage') {
        newAdjustments[userId].amount = (numValue / 100) * totalAmount;
      }

      // If adjusting amount, calculate percentage
      if (type === 'amount') {
        newAdjustments[userId].percentage = totalAmount > 0 ? (numValue / totalAmount) * 100 : 0;
      }

      return newAdjustments;
    });

    // Recalculate distribution with adjustments
    calculateDistribution();
  };

  // Reset weights to equal distribution
  const resetWeights = () => {
    setModelOptions(prev => ({
      ...prev,
      tasks: { weight: 33.33 },
      time: { weight: 33.33 },
      difficulty: { weight: 33.34 }
    }));
    toast.success('Weights reset to equal distribution');
  };

  // Reset manual adjustments
  const resetAdjustments = () => {
    setManualAdjustments({});
    calculateDistribution();
    toast.success('Manual adjustments reset');
  };

  // Save distribution
  const handleSaveDistribution = async () => {
    if (!projectId || !distribution || distribution.length === 0) {
      toast.error('No distribution to save');
      return;
    }

    try {
      setSaving(true);

      // Prepare distribution data for saving
      const distributionData = distribution.map(item => ({
        revenue_id: revenueId,
        project_id: projectId,
        contributor_id: item.contributor_id,
        user_id: item.user_id,
        amount: item.amount,
        currency: currency,
        percentage: item.percentage,
        calculation_method: calculationModel,
        calculation_details: {
          ...item.calculation_details,
          manual_adjustment: item.manual_adjustment
        },
        status: 'pending'
      }));

      // Insert distribution data
      const { data, error } = await supabase
        .from('revenue_distribution')
        .insert(distributionData);

      if (error) throw error;

      // Update revenue status if revenueId is provided
      if (revenueId) {
        const { error: updateError } = await supabase
          .from('revenue')
          .update({ distribution_status: 'in_progress' })
          .eq('id', revenueId);

        if (updateError) throw updateError;
      }

      toast.success('Royalty distribution saved successfully');

      // Call onSave callback if provided
      if (onSave) {
        onSave(distributionData);
      }
    } catch (error) {
      console.error('Error saving distribution:', error);
      toast.error('Failed to save royalty distribution');
    } finally {
      setSaving(false);
    }
  };

  // Get model description
  const getModelDescription = () => {
    switch (calculationModel) {
      case 'equal_split':
        return 'Equal Split distributes revenue equally among all contributors, regardless of their contribution amount or type.';
      case 'task_based':
        return 'Task-Based distribution allocates revenue based on the number of tasks completed by each contributor.';
      case 'time_based':
        return 'Time-Based distribution allocates revenue based on the hours spent by each contributor.';
      case 'role_based':
        return 'Role-Based distribution allocates revenue based on the role and weight assigned to each contributor.';
      case 'cog_model':
        return 'The CoG Model (Tasks-Time-Difficulty) distributes revenue based on a weighted combination of tasks completed, hours tracked, and task difficulty.';
      default:
        return '';
    }
  };

  if (loading) {
    return (
      <div className="royalty-calculator loading">
        <div className="loading-spinner">
          <i className="bi bi-arrow-repeat spinning"></i>
          <span>Loading royalty calculator...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="royalty-calculator">
      <div className="calculator-header">
        <h2>Royalty Calculator</h2>
        {project && (
          <div className="project-info">
            <span className="project-name">{project.name}</span>
          </div>
        )}
      </div>

      <div className="calculator-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="calculation-model">Calculation Model</label>
            <select
              id="calculation-model"
              value={calculationModel}
              onChange={handleModelChange}
              className="form-select"
            >
              <option value="equal_split">Equal Split</option>
              <option value="task_based">Task-Based</option>
              <option value="time_based">Time-Based</option>
              <option value="role_based">Role-Based</option>
              <option value="cog_model">CoG Model (Tasks-Time-Difficulty)</option>
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="total-amount">Total Amount</label>
            <div className="input-group">
              <input
                id="total-amount"
                type="number"
                value={totalAmount}
                onChange={handleAmountChange}
                className="form-control"
                min="0"
                step="0.01"
                disabled={!!revenueId}
              />
              <select
                value={currency}
                onChange={handleCurrencyChange}
                className="form-select currency-select"
                disabled={!!revenueId}
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="CAD">CAD</option>
                <option value="AUD">AUD</option>
                <option value="JPY">JPY</option>
              </select>
            </div>
          </div>
        </div>

        <div className="model-description">
          <button
            className="toggle-details-btn"
            onClick={() => setShowModelDetails(!showModelDetails)}
          >
            <i className={`bi bi-info-circle${showModelDetails ? '-fill' : ''}`}></i>
            {showModelDetails ? 'Hide Model Details' : 'Show Model Details'}
          </button>

          {showModelDetails && (
            <div className="model-details">
              <p>{getModelDescription()}</p>

              {calculationModel === 'cog_model' && (
                <div className="cog-weights">
                  <h4>CoG Model Weights</h4>
                  <div className="weights-container">
                    <div className="weight-group">
                      <label>Tasks Weight</label>
                      <input
                        type="number"
                        value={modelOptions.tasks.weight}
                        onChange={(e) => handleWeightChange('tasks', e.target.value)}
                        min="0"
                        max="100"
                        step="0.01"
                      />
                      <span className="weight-percentage">%</span>
                    </div>

                    <div className="weight-group">
                      <label>Time Weight</label>
                      <input
                        type="number"
                        value={modelOptions.time.weight}
                        onChange={(e) => handleWeightChange('time', e.target.value)}
                        min="0"
                        max="100"
                        step="0.01"
                      />
                      <span className="weight-percentage">%</span>
                    </div>

                    <div className="weight-group">
                      <label>Difficulty Weight</label>
                      <input
                        type="number"
                        value={modelOptions.difficulty.weight}
                        onChange={(e) => handleWeightChange('difficulty', e.target.value)}
                        min="0"
                        max="100"
                        step="0.01"
                      />
                      <span className="weight-percentage">%</span>
                    </div>

                    <button
                      className="reset-weights-btn"
                      onClick={resetWeights}
                      title="Reset to equal weights"
                    >
                      <i className="bi bi-arrow-counterclockwise"></i>
                    </button>
                  </div>
                </div>
              )}

              {calculationModel === 'role_based' && (
                <div className="role-weights">
                  <h4>Role Weights</h4>
                  <div className="role-weights-container">
                    {contributors.map(contributor => (
                      <div key={contributor.user_id} className="role-weight-group">
                        <label>{contributor.name}</label>
                        <input
                          type="number"
                          value={modelOptions.roles[contributor.user_id] || 1}
                          onChange={(e) => handleRoleWeightChange(contributor.user_id, e.target.value)}
                          min="0.1"
                          step="0.1"
                        />
                        <span className="role-label">{contributor.role || 'Contributor'}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="distribution-preview">
        <div className="preview-header">
          <h3>Distribution Preview</h3>
          <div className="preview-actions">
            <button
              className={`toggle-adjustments-btn ${showAdjustments ? 'active' : ''}`}
              onClick={() => setShowAdjustments(!showAdjustments)}
            >
              <i className={`bi bi-sliders${showAdjustments ? '-fill' : ''}`}></i>
              {showAdjustments ? 'Hide Adjustments' : 'Manual Adjustments'}
            </button>
          </div>
        </div>

        {distribution.length > 0 ? (
          <div className="distribution-table-container">
            <table className="distribution-table">
              <thead>
                <tr>
                  <th>Contributor</th>
                  <th>Amount</th>
                  <th>Percentage</th>
                  {showAdjustments && <th>Adjustment</th>}
                </tr>
              </thead>
              <tbody>
                {distribution.map((item) => (
                  <tr key={item.user_id}>
                    <td className="contributor-name">
                      {contributors.find(c => c.user_id === item.user_id)?.name || 'Unknown'}
                    </td>
                    <td className="amount">
                      {formatCurrency(item.amount, currency)}
                    </td>
                    <td className="percentage">
                      {formatPercentage(item.percentage)}
                    </td>
                    {showAdjustments && (
                      <td className="adjustment">
                        <div className="adjustment-inputs">
                          <div className="adjustment-input-group">
                            <input
                              type="number"
                              value={manualAdjustments[item.user_id]?.amount || 0}
                              onChange={(e) => handleManualAdjustment(item.user_id, 'amount', e.target.value)}
                              step="0.01"
                              placeholder="Amount"
                            />
                            <span className="adjustment-currency">{currency}</span>
                          </div>
                          <div className="adjustment-input-group">
                            <input
                              type="number"
                              value={manualAdjustments[item.user_id]?.percentage || 0}
                              onChange={(e) => handleManualAdjustment(item.user_id, 'percentage', e.target.value)}
                              step="0.01"
                              placeholder="Percentage"
                            />
                            <span className="adjustment-percentage">%</span>
                          </div>
                        </div>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
              <tfoot>
                <tr>
                  <td><strong>Total</strong></td>
                  <td>
                    <strong>
                      {formatCurrency(
                        distribution.reduce((sum, item) => sum + item.amount, 0),
                        currency
                      )}
                    </strong>
                  </td>
                  <td>
                    <strong>
                      {formatPercentage(
                        distribution.reduce((sum, item) => sum + item.percentage, 0)
                      )}
                    </strong>
                  </td>
                  {showAdjustments && (
                    <td>
                      <button
                        className="reset-adjustments-btn"
                        onClick={resetAdjustments}
                        disabled={Object.keys(manualAdjustments).length === 0}
                      >
                        <i className="bi bi-arrow-counterclockwise"></i>
                        Reset
                      </button>
                    </td>
                  )}
                </tr>
              </tfoot>
            </table>
          </div>
        ) : (
          <div className="no-distribution">
            <p>No distribution data available. Please check your calculation model and settings.</p>
          </div>
        )}

        <div className="distribution-visualization">
          {distribution.length > 0 && (
            <div className="distribution-chart">
              {distribution.map((item, index) => {
                const contributor = contributors.find(c => c.user_id === item.user_id);
                const colors = [
                  'var(--primary-color)',
                  'var(--success-color)',
                  'var(--info-color)',
                  'var(--warning-color)',
                  'var(--danger-color)'
                ];

                return (
                  <div
                    key={item.user_id}
                    className="distribution-bar"
                    style={{
                      width: `${item.percentage}%`,
                      backgroundColor: colors[index % colors.length]
                    }}
                    title={`${contributor?.name || 'Unknown'}: ${formatPercentage(item.percentage)}`}
                  >
                    {item.percentage >= 5 && (
                      <span className="bar-label">
                        {contributor?.name || 'Unknown'}
                      </span>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>

      <div className="calculator-actions">
        {onCancel && (
          <button
            className="cancel-btn"
            onClick={onCancel}
            disabled={saving}
          >
            Cancel
          </button>
        )}

        <button
          className="save-btn"
          onClick={handleSaveDistribution}
          disabled={saving || distribution.length === 0}
        >
          {saving ? (
            <>
              <i className="bi bi-arrow-repeat spinning"></i>
              Saving...
            </>
          ) : (
            <>
              <i className="bi bi-save"></i>
              Save Distribution
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default RoyaltyCalculator;
