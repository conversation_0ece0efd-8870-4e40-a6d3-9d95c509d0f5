/**
 * Project Templates
 *
 * This file contains predefined project templates for different project types.
 * Each template includes default values for project settings, contribution tracking,
 * milestones, and royalty model configuration.
 */

const projectTemplates = {
  // Game Development Template
  game: {
    name: 'New Game Project',
    description: 'A collaborative game development project',
    project_type: 'game',
    estimated_duration: 12, // 12 months
    is_public: true,
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 30,
        hours_weight: 30,
        difficulty_weight: 40
      },
      is_pre_expense: true
    },
    contribution_tracking: {
      categories: ['Design', 'Programming', 'Art', 'Audio', 'Testing', 'Production'],
      difficulty_scale: [1, 2, 3, 5, 8], // Fibonacci-style scale
      task_types: [
        { name: 'Game Design', difficulty: 4 },
        { name: 'Level Design', difficulty: 3 },
        { name: 'Character Design', difficulty: 4 },
        { name: 'Programming', difficulty: 4 },
        { name: 'UI Programming', difficulty: 3 },
        { name: 'AI Programming', difficulty: 5 },
        { name: 'Concept Art', difficulty: 3 },
        { name: 'Character Art', difficulty: 4 },
        { name: 'Environment Art', difficulty: 4 },
        { name: 'UI Art', difficulty: 3 },
        { name: 'Animation', difficulty: 4 },
        { name: 'Music Composition', difficulty: 4 },
        { name: 'Sound Effects', difficulty: 3 },
        { name: 'Voice Acting', difficulty: 3 },
        { name: 'QA Testing', difficulty: 2 },
        { name: 'Project Management', difficulty: 3 },
        { name: 'Marketing', difficulty: 3 }
      ]
    },
    // Empty milestones array - will be populated from predefined options if user chooses
    milestones: [],

    // Predefined milestones available for selection
    predefinedMilestones: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Game design document', 'Team assignments', 'Project timeline'],
        approval_criteria: 'All team members have access to project resources and understand their roles',
        status: 'pending'
      },
      {
        name: 'Prototype',
        description: 'First playable prototype with core mechanics',
        deliverables: ['Playable prototype', 'Core mechanics implemented', 'Basic placeholder assets'],
        approval_criteria: 'Core gameplay loop is functional and demonstrates the game concept',
        status: 'pending'
      },
      {
        name: 'Vertical Slice',
        description: 'Polished section of the game that represents the final quality',
        deliverables: ['One complete level/area', 'Final-quality art assets for the slice', 'Implemented game systems'],
        approval_criteria: 'The slice demonstrates the intended player experience and visual quality',
        status: 'pending'
      },
      {
        name: 'Alpha',
        description: 'Feature-complete but with placeholder assets and known issues',
        deliverables: ['All major features implemented', 'Game is playable from start to finish', 'Bug tracking system in place'],
        approval_criteria: 'All core features are implemented and the game can be played through',
        status: 'pending'
      },
      {
        name: 'Beta',
        description: 'Content-complete with focus on bug fixing and polishing',
        deliverables: ['All content implemented', 'QA test results', 'Performance optimization'],
        approval_criteria: 'Game is stable with no critical bugs and all content is in place',
        status: 'pending'
      },
      {
        name: 'Gold/Release',
        description: 'Final version ready for distribution',
        deliverables: ['Release build', 'Marketing materials', 'Launch plan'],
        approval_criteria: 'Game meets quality standards and is ready for public release',
        status: 'pending'
      }
    ],
    revenue_tranches: [
      {
        name: 'Initial Release',
        description: 'Revenue from game launch',
        platform_fee_config: {
          apply_before: true,
          percentage: 30 // Standard platform fee for most game stores
        },
        distribution_thresholds: {
          minimum_revenue: 100, // Only distribute when at least $100 is collected
          per_contributor_minimum: 10 // Minimum $10 per contributor
        },
        rollover_config: 'next_period'
      },
      {
        name: 'Post-Launch Content',
        description: 'Revenue from DLC and additional content',
        platform_fee_config: {
          apply_before: true,
          percentage: 30
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 10
        },
        rollover_config: 'next_period'
      }
    ]
  },

  // App Development Template
  app: {
    name: 'New App Project',
    description: 'A collaborative app development project',
    project_type: 'app',
    estimated_duration: 6, // 6 months
    is_public: true,
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 35,
        hours_weight: 25,
        difficulty_weight: 40
      },
      is_pre_expense: true
    },
    contribution_tracking: {
      categories: ['Design', 'Frontend', 'Backend', 'Testing', 'DevOps', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: [
        { name: 'UI/UX Design', difficulty: 4 },
        { name: 'Frontend Development', difficulty: 3 },
        { name: 'Backend Development', difficulty: 4 },
        { name: 'API Development', difficulty: 4 },
        { name: 'Database Design', difficulty: 4 },
        { name: 'Testing', difficulty: 3 },
        { name: 'DevOps', difficulty: 4 },
        { name: 'Documentation', difficulty: 2 },
        { name: 'Project Management', difficulty: 3 }
      ]
    },
    // Empty milestones array - will be populated from predefined options if user chooses
    milestones: [],

    // Predefined milestones available for selection
    predefinedMilestones: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Project requirements', 'Technical specifications', 'Project timeline'],
        approval_criteria: 'All team members have access to project resources and understand their roles',
        status: 'pending'
      },
      {
        name: 'Design Approval',
        description: 'Finalize UI/UX design and user flows',
        deliverables: ['UI mockups', 'User flow diagrams', 'Design system'],
        approval_criteria: 'Design meets requirements and has been approved by stakeholders',
        status: 'pending'
      },
      {
        name: 'MVP Development',
        description: 'Develop minimum viable product with core features',
        deliverables: ['Working MVP', 'Core functionality implemented', 'Basic testing completed'],
        approval_criteria: 'MVP demonstrates core functionality and passes basic tests',
        status: 'pending'
      },
      {
        name: 'Beta Release',
        description: 'Release beta version for testing',
        deliverables: ['Beta version', 'Test plan', 'Feedback collection system'],
        approval_criteria: 'Beta version is stable and ready for user testing',
        status: 'pending'
      },
      {
        name: 'Public Launch',
        description: 'Release app to the public',
        deliverables: ['Production-ready app', 'Marketing materials', 'Support documentation'],
        approval_criteria: 'App meets quality standards and is ready for public release',
        status: 'pending'
      }
    ],
    revenue_tranches: [
      {
        name: 'App Store Revenue',
        description: 'Revenue from app store sales and in-app purchases',
        platform_fee_config: {
          apply_before: true,
          percentage: 30 // Standard app store fee
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 10
        },
        rollover_config: 'next_period'
      },
      {
        name: 'Subscription Revenue',
        description: 'Revenue from subscription services',
        platform_fee_config: {
          apply_before: true,
          percentage: 15 // Lower fee for subscriptions after first year
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 10
        },
        rollover_config: 'next_period'
      }
    ]
  },

  // Website Development Template
  website: {
    name: 'New Website Project',
    description: 'A collaborative website development project',
    project_type: 'website',
    estimated_duration: 3, // 3 months
    is_public: true,
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 40,
        hours_weight: 20,
        difficulty_weight: 40
      },
      is_pre_expense: true
    },
    contribution_tracking: {
      categories: ['Design', 'Frontend', 'Backend', 'Content', 'SEO', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: [
        { name: 'UI/UX Design', difficulty: 4 },
        { name: 'Frontend Development', difficulty: 3 },
        { name: 'Backend Development', difficulty: 4 },
        { name: 'Content Creation', difficulty: 3 },
        { name: 'SEO Optimization', difficulty: 3 },
        { name: 'Testing', difficulty: 2 },
        { name: 'Project Management', difficulty: 3 }
      ]
    },
    // Empty milestones array - will be populated from predefined options if user chooses
    milestones: [],

    // Predefined milestones available for selection
    predefinedMilestones: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Project requirements', 'Site map', 'Project timeline'],
        approval_criteria: 'All team members have access to project resources and understand their roles',
        status: 'pending'
      },
      {
        name: 'Design Approval',
        description: 'Finalize website design and user experience',
        deliverables: ['UI mockups', 'Responsive design layouts', 'Design system'],
        approval_criteria: 'Design meets requirements and has been approved by stakeholders',
        status: 'pending'
      },
      {
        name: 'Development Completion',
        description: 'Complete website development',
        deliverables: ['Functioning website', 'Content management system', 'Documentation'],
        approval_criteria: 'Website functions as expected across all required devices and browsers',
        status: 'pending'
      },
      {
        name: 'Content Population',
        description: 'Add all content to the website',
        deliverables: ['Populated pages', 'Media assets', 'SEO metadata'],
        approval_criteria: 'All content is in place and properly formatted',
        status: 'pending'
      },
      {
        name: 'Launch',
        description: 'Deploy website to production',
        deliverables: ['Live website', 'Analytics setup', 'Maintenance plan'],
        approval_criteria: 'Website is live and functioning correctly in production environment',
        status: 'pending'
      }
    ],
    revenue_tranches: [
      {
        name: 'Initial Payment',
        description: 'Revenue from initial website development',
        platform_fee_config: {
          apply_before: false,
          percentage: 5 // Lower platform fee for direct client work
        },
        distribution_thresholds: {
          minimum_revenue: 500,
          per_contributor_minimum: 50
        },
        rollover_config: 'none'
      },
      {
        name: 'Maintenance Revenue',
        description: 'Revenue from ongoing maintenance and updates',
        platform_fee_config: {
          apply_before: false,
          percentage: 5
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 20
        },
        rollover_config: 'next_period'
      }
    ]
  },

  // Art/Asset Pack Template
  art: {
    name: 'New Art Project',
    description: 'A collaborative art or asset pack project',
    project_type: 'art',
    estimated_duration: 3, // 3 months
    is_public: true,
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 25,
        hours_weight: 25,
        difficulty_weight: 50
      },
      is_pre_expense: true
    },
    contribution_tracking: {
      categories: ['Concept Art', '2D Art', '3D Art', 'Animation', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: [
        { name: 'Concept Art', difficulty: 3 },
        { name: '2D Asset Creation', difficulty: 4 },
        { name: '3D Modeling', difficulty: 5 },
        { name: 'Texturing', difficulty: 4 },
        { name: 'Animation', difficulty: 5 },
        { name: 'Documentation', difficulty: 2 },
        { name: 'Project Management', difficulty: 3 }
      ]
    },
    milestones: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Art style guide', 'Asset list', 'Project timeline'],
        approval_criteria: 'All team members have access to project resources and understand their roles',
        status: 'pending'
      },
      {
        name: 'Concept Approval',
        description: 'Finalize concept art and style',
        deliverables: ['Concept art', 'Style guide', 'Color palette'],
        approval_criteria: 'Art style meets requirements and has been approved by the team',
        status: 'pending'
      },
      {
        name: 'Asset Creation',
        description: 'Create all planned assets',
        deliverables: ['Completed assets', 'Asset documentation', 'Preview renders'],
        approval_criteria: 'All assets meet quality standards and follow the style guide',
        status: 'pending'
      },
      {
        name: 'Package Preparation',
        description: 'Prepare assets for distribution',
        deliverables: ['Organized asset package', 'Documentation', 'Preview materials'],
        approval_criteria: 'Assets are properly organized and documented for end users',
        status: 'pending'
      },
      {
        name: 'Release',
        description: 'Release asset pack to marketplace or clients',
        deliverables: ['Final asset package', 'Marketing materials', 'Support documentation'],
        approval_criteria: 'Asset pack meets quality standards and is ready for distribution',
        status: 'pending'
      }
    ],
    revenue_tranches: [
      {
        name: 'Marketplace Sales',
        description: 'Revenue from marketplace sales',
        platform_fee_config: {
          apply_before: true,
          percentage: 30 // Standard marketplace fee
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 10
        },
        rollover_config: 'next_period'
      }
    ]
  },

  // Music/Sound Template
  music: {
    name: 'New Music Project',
    description: 'A collaborative music or sound project',
    project_type: 'music',
    estimated_duration: 3, // 3 months
    is_public: true,
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 25,
        hours_weight: 25,
        difficulty_weight: 50
      },
      is_pre_expense: true
    },
    contribution_tracking: {
      categories: ['Composition', 'Performance', 'Production', 'Sound Design', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: [
        { name: 'Composition', difficulty: 4 },
        { name: 'Arrangement', difficulty: 3 },
        { name: 'Performance', difficulty: 4 },
        { name: 'Recording', difficulty: 3 },
        { name: 'Mixing', difficulty: 4 },
        { name: 'Mastering', difficulty: 5 },
        { name: 'Sound Design', difficulty: 4 },
        { name: 'Project Management', difficulty: 3 }
      ]
    },
    milestones: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Project brief', 'Reference tracks', 'Project timeline'],
        approval_criteria: 'All team members have access to project resources and understand their roles',
        status: 'pending'
      },
      {
        name: 'Demo Approval',
        description: 'Create and approve demo tracks',
        deliverables: ['Demo tracks', 'Style guide', 'Feedback documentation'],
        approval_criteria: 'Demo tracks meet the project direction and have been approved',
        status: 'pending'
      },
      {
        name: 'Production',
        description: 'Complete production of all tracks',
        deliverables: ['Produced tracks', 'Session files', 'Production notes'],
        approval_criteria: 'All tracks are produced to professional standards',
        status: 'pending'
      },
      {
        name: 'Mixing & Mastering',
        description: 'Complete mixing and mastering',
        deliverables: ['Mixed tracks', 'Mastered tracks', 'Technical specifications'],
        approval_criteria: 'Tracks are properly mixed and mastered for intended platforms',
        status: 'pending'
      },
      {
        name: 'Release',
        description: 'Release music to platforms or clients',
        deliverables: ['Final audio files', 'Metadata', 'Release plan'],
        approval_criteria: 'Music meets quality standards and is ready for distribution',
        status: 'pending'
      }
    ],
    revenue_tranches: [
      {
        name: 'Streaming Revenue',
        description: 'Revenue from music streaming platforms',
        platform_fee_config: {
          apply_before: true,
          percentage: 30 // Standard streaming platform fee
        },
        distribution_thresholds: {
          minimum_revenue: 50,
          per_contributor_minimum: 5
        },
        rollover_config: 'next_period'
      },
      {
        name: 'Licensing Revenue',
        description: 'Revenue from licensing music for other projects',
        platform_fee_config: {
          apply_before: false,
          percentage: 10
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 20
        },
        rollover_config: 'next_period'
      }
    ]
  },

  // Generic/Other Template
  other: {
    name: 'New Project',
    description: 'A collaborative project',
    project_type: 'other',
    estimated_duration: 6, // 6 months
    is_public: true,
    royalty_model: {
      model_type: 'custom',
      model_schema: 'cog',
      configuration: {
        tasks_weight: 33.33,
        hours_weight: 33.33,
        difficulty_weight: 33.34
      },
      is_pre_expense: true
    },
    contribution_tracking: {
      categories: ['Design', 'Development', 'Content', 'Management'],
      difficulty_scale: [1, 2, 3, 5, 8],
      task_types: [
        { name: 'Design', difficulty: 3 },
        { name: 'Development', difficulty: 4 },
        { name: 'Content Creation', difficulty: 3 },
        { name: 'Testing', difficulty: 2 },
        { name: 'Documentation', difficulty: 2 },
        { name: 'Project Management', difficulty: 3 }
      ]
    },
    milestones: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Project plan', 'Team assignments', 'Project timeline'],
        approval_criteria: 'All team members have access to project resources and understand their roles',
        status: 'pending'
      },
      {
        name: 'Planning Phase Complete',
        description: 'Complete project planning and requirements',
        deliverables: ['Detailed requirements', 'Technical specifications', 'Project schedule'],
        approval_criteria: 'Project plan is comprehensive and approved by stakeholders',
        status: 'pending'
      },
      {
        name: 'Development Phase Complete',
        description: 'Complete main development work',
        deliverables: ['Completed deliverables', 'Testing results', 'Documentation'],
        approval_criteria: 'All planned deliverables are complete and meet quality standards',
        status: 'pending'
      },
      {
        name: 'Project Completion',
        description: 'Finalize project and deliver to stakeholders',
        deliverables: ['Final deliverables', 'Project documentation', 'Handover materials'],
        approval_criteria: 'Project meets all requirements and is accepted by stakeholders',
        status: 'pending'
      }
    ],
    revenue_tranches: [
      {
        name: 'Project Revenue',
        description: 'Revenue from project completion',
        platform_fee_config: {
          apply_before: false,
          percentage: 5
        },
        distribution_thresholds: {
          minimum_revenue: 100,
          per_contributor_minimum: 20
        },
        rollover_config: 'none'
      }
    ]
  }
};

export default projectTemplates;
