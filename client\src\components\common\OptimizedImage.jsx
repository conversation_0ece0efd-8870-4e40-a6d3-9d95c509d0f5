import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';

/**
 * Optimized Image Component with Lazy Loading
 *
 * High-performance image component providing:
 * - Lazy loading with Intersection Observer
 * - Progressive image loading with blur-up effect
 * - Automatic WebP format detection and fallback
 * - Responsive image sizing and optimization
 * - Error handling with fallback images
 * - Loading states and smooth transitions
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder = 'blur',
  blurDataURL,
  priority = false,
  quality = 75,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  onLoad,
  onError,
  fallbackSrc,
  lazy = true,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(priority ? src : null);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            setCurrentSrc(src);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before the image enters viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
      observerRef.current = observer;
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [lazy, priority, isInView, src]);

  // Update src when in view
  useEffect(() => {
    if (isInView && !currentSrc) {
      setCurrentSrc(src);
    }
  }, [isInView, src, currentSrc]);

  // Handle image load
  const handleLoad = (event) => {
    setIsLoaded(true);
    setHasError(false);
    if (onLoad) {
      onLoad(event);
    }
  };

  // Handle image error
  const handleError = (event) => {
    setHasError(true);
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setHasError(false);
    } else if (onError) {
      onError(event);
    }
  };

  // Generate optimized image URL
  const getOptimizedSrc = (originalSrc, options = {}) => {
    if (!originalSrc) return null;

    // If it's already an optimized URL or external URL, return as-is
    if (originalSrc.includes('?') || originalSrc.startsWith('http')) {
      return originalSrc;
    }

    // For local images, add optimization parameters
    const params = new URLSearchParams();

    if (options.width) params.set('w', options.width);
    if (options.height) params.set('h', options.height);
    if (options.quality) params.set('q', options.quality);
    if (options.format) params.set('f', options.format);

    return params.toString() ? `${originalSrc}?${params.toString()}` : originalSrc;
  };

  // Generate srcSet for responsive images
  const generateSrcSet = (baseSrc) => {
    if (!baseSrc || !width) return undefined;

    const breakpoints = [480, 768, 1024, 1280, 1920];
    const srcSet = breakpoints
      .filter(bp => bp <= width * 2) // Don't generate larger than 2x the display size
      .map(bp => {
        const optimizedSrc = getOptimizedSrc(baseSrc, {
          width: bp,
          quality: quality,
          format: 'webp'
        });
        return `${optimizedSrc} ${bp}w`;
      })
      .join(', ');

    return srcSet;
  };

  // Generate blur placeholder
  const getBlurPlaceholder = () => {
    if (blurDataURL) return blurDataURL;

    // Generate a simple blur placeholder
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${width || 400}" height="${height || 300}" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#e5e7eb;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grad)" />
      </svg>
    `)}`;
  };

  // Container styles
  const containerStyles = {
    position: fill ? 'absolute' : 'relative',
    width: fill ? '100%' : width,
    height: fill ? '100%' : height,
    overflow: 'hidden',
    ...(fill && { inset: 0 })
  };

  // Image styles
  const imageStyles = {
    width: fill ? '100%' : width,
    height: fill ? '100%' : height,
    objectFit: fill ? objectFit : undefined,
    objectPosition: fill ? objectPosition : undefined,
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 1 : 0
  };

  // Placeholder styles
  const placeholderStyles = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: fill ? objectFit : 'cover',
    objectPosition: fill ? objectPosition : 'center',
    filter: 'blur(10px)',
    transform: 'scale(1.1)', // Slightly larger to hide blur edges
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 0 : 1
  };

  return (
    <div
      ref={imgRef}
      className={`optimized-image-container ${className}`}
      style={containerStyles}
      {...props}
    >
      {/* Blur placeholder */}
      {placeholder === 'blur' && !isLoaded && (
        <img
          src={getBlurPlaceholder()}
          alt=""
          style={placeholderStyles}
          aria-hidden="true"
        />
      )}

      {/* Loading skeleton */}
      {placeholder === 'skeleton' && !isLoaded && (
        <div
          className="animate-pulse bg-gray-200 dark:bg-gray-700"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            opacity: isLoaded ? 0 : 1,
            transition: 'opacity 0.3s ease-in-out'
          }}
        />
      )}

      {/* Main image */}
      {(isInView || priority) && currentSrc && (
        <motion.img
          src={getOptimizedSrc(currentSrc, { width, height, quality })}
          srcSet={generateSrcSet(currentSrc)}
          sizes={sizes}
          alt={alt}
          style={imageStyles}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazy && !priority ? 'lazy' : 'eager'}
          decoding="async"
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 0.3 }}
        />
      )}

      {/* Error fallback */}
      {hasError && !fallbackSrc && (
        <div
          className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%'
          }}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">🖼️</div>
            <div className="text-sm">Image not available</div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {!isLoaded && !hasError && (isInView || priority) && currentSrc && (
        <div
          className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900"
          style={{
            opacity: isLoaded ? 0 : 1,
            transition: 'opacity 0.3s ease-in-out'
          }}
        >
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
};

/**
 * Optimized Avatar Component
 */
export const OptimizedAvatar = ({
  src,
  name,
  size = 40,
  className = '',
  fallbackColor = 'bg-primary',
  ...props
}) => {
  const initials = name
    ? name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    : '?';

  return (
    <div
      className={`relative rounded-full overflow-hidden ${className}`}
      style={{ width: size, height: size }}
    >
      {src ? (
        <OptimizedImage
          src={src}
          alt={name || 'Avatar'}
          width={size}
          height={size}
          fill
          objectFit="cover"
          fallbackSrc={`https://ui-avatars.com/api/?name=${encodeURIComponent(name || 'User')}&size=${size}&background=random`}
          {...props}
        />
      ) : (
        <div
          className={`w-full h-full flex items-center justify-center text-white font-medium ${fallbackColor}`}
          style={{ fontSize: size * 0.4 }}
        >
          {initials}
        </div>
      )}
    </div>
  );
};

/**
 * Optimized Background Image Component
 */
export const OptimizedBackgroundImage = ({
  src,
  alt,
  className = '',
  overlay = false,
  overlayOpacity = 0.5,
  children,
  ...props
}) => {
  return (
    <div className={`relative overflow-hidden ${className}`} {...props}>
      <OptimizedImage
        src={src}
        alt={alt}
        fill
        objectFit="cover"
        className="absolute inset-0"
        priority={false}
      />

      {overlay && (
        <div
          className="absolute inset-0 bg-black"
          style={{ opacity: overlayOpacity }}
        />
      )}

      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
