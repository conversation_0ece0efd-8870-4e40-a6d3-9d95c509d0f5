// EnhancedRevenueDashboard - Comprehensive revenue overview with bento grid layout
// Implements revenue dashboard following wireframe specifications
import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Badge, Progress, Tabs, Tab } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  DollarSign, 
  TrendingUp, 
  Target,
  Award,
  Wallet,
  BarChart3,
  Download,
  Settings,
  Eye,
  Calendar,
  Users,
  Star,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

const EnhancedRevenueDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [revenueData, setRevenueData] = useState({
    totalEarnings: {
      total: 12450.00,
      thisMonth: 3200.00,
      available: 8200.00,
      pending: 1050.00,
      growth: 15.2,
      hourlyRate: 42.50
    },
    orbWallet: {
      balance: 15420,
      value: 154.20,
      earned: 2400,
      dailyPoints: 340,
      conversionRate: 23
    },
    goals: {
      monthlyTarget: 4000,
      currentProgress: 3200,
      percentage: 80
    },
    ranking: {
      global: 47,
      orbEarned: 340
    },
    activeVentures: [
      {
        id: 1,
        name: 'TaskMaster Pro',
        icon: '⚔️',
        earned: 7200,
        share: 15.5,
        progress: 85,
        monthlyEarning: 1200,
        nextMilestone: 500,
        estimatedTime: '2 weeks',
        members: 8,
        rating: 4.8,
        missions: 23
      },
      {
        id: 2,
        name: 'Creative Studio Platform',
        icon: '🎨',
        earned: 3800,
        share: 22.3,
        progress: 50,
        monthlyEarning: 800,
        nextMilestone: 300,
        estimatedTime: '3 weeks',
        members: 6,
        rating: 4.6,
        missions: 15
      },
      {
        id: 3,
        name: 'Testing Automation Tool',
        icon: '🧪',
        earned: 1450,
        share: 8.7,
        progress: 60,
        monthlyEarning: 200,
        nextMilestone: 150,
        estimatedTime: '1 week',
        members: 4,
        rating: 4.4,
        missions: 9
      }
    ],
    breakdown: {
      byWorkType: {
        coding: { amount: 8200, percentage: 66 },
        design: { amount: 2800, percentage: 22 },
        testing: { amount: 1000, percentage: 8 },
        planning: { amount: 450, percentage: 4 }
      },
      byPaymentType: {
        fixed: { amount: 4800, percentage: 39 },
        revenue: { amount: 6200, percentage: 50 },
        bonuses: { amount: 1450, percentage: 11 }
      }
    },
    trends: {
      sixMonthData: [2800, 3200, 3600, 3800, 3400, 3200],
      labels: ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan']
    },
    recentTransactions: [
      {
        id: 1,
        date: 'Jan 15',
        project: 'TaskMaster Pro',
        type: 'Revenue Share Payment',
        amount: 1200.00,
        icon: '⚔️'
      },
      {
        id: 2,
        date: 'Jan 10',
        project: 'Creative Studio',
        type: 'Mission Completion',
        amount: 800.00,
        icon: '🎨'
      },
      {
        id: 3,
        date: 'Jan 08',
        project: 'Testing Tool',
        type: 'Bug Bounty Reward',
        amount: 150.00,
        icon: '🧪'
      }
    ]
  });

  useEffect(() => {
    loadRevenueData();
  }, [currentUser]);

  const loadRevenueData = async () => {
    try {
      setIsLoading(true);
      
      // In production, this would fetch real revenue data
      // For now, using enhanced mock data
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock data is already set in state
      
    } catch (error) {
      console.error('Error loading revenue data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (value) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getGrowthIcon = (value) => {
    return value >= 0 ? <ArrowUpRight size={16} /> : <ArrowDownRight size={16} />;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading treasury overview...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`enhanced-revenue-dashboard space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <DollarSign className="text-green-500" size={32} />
            Treasury Overview
          </h1>
          <p className="text-gray-600">Comprehensive financial tracking and earnings management</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="light" startContent={<Download size={18} />}>
            Export
          </Button>
          <Button variant="light" startContent={<Settings size={18} />}>
            Settings
          </Button>
        </div>
      </div>

      {/* Bento Grid Layout - Top Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Earnings - 2x2 */}
        <motion.div
          className="lg:col-span-2 lg:row-span-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="h-full bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 border-2 border-green-200">
            <CardHeader>
              <div className="flex items-center gap-2">
                <DollarSign className="text-green-500" size={24} />
                <h3 className="text-lg font-semibold">Total Earnings</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-4">
                <div>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {formatCurrency(revenueData.totalEarnings.total)}
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">This Month:</span>
                      <div className="font-semibold">{formatCurrency(revenueData.totalEarnings.thisMonth)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Available:</span>
                      <div className="font-semibold">{formatCurrency(revenueData.totalEarnings.available)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Pending:</span>
                      <div className="font-semibold text-yellow-600">{formatCurrency(revenueData.totalEarnings.pending)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Hourly Rate:</span>
                      <div className="font-semibold">{formatCurrency(revenueData.totalEarnings.hourlyRate)}</div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <div className={`flex items-center gap-1 ${getGrowthColor(revenueData.totalEarnings.growth)}`}>
                    {getGrowthIcon(revenueData.totalEarnings.growth)}
                    <span className="font-medium">{formatPercentage(revenueData.totalEarnings.growth)} Growth</span>
                  </div>
                  <span className="text-gray-500 text-sm">MoM</span>
                </div>
                
                <div className="flex gap-2">
                  <Button color="success" size="sm">
                    Withdraw
                  </Button>
                  <Button variant="light" size="sm" startContent={<Eye size={16} />}>
                    History
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* ORB Wallet - 2x2 */}
        <motion.div
          className="lg:col-span-2 lg:row-span-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="h-full bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-purple-900/20 dark:to-indigo-800/20 border-2 border-purple-200">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Wallet className="text-purple-500" size={24} />
                <h3 className="text-lg font-semibold">ORB Wallet</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-4">
                <div>
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {revenueData.orbWallet.balance.toLocaleString()} ORB
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Value:</span>
                      <div className="font-semibold">{formatCurrency(revenueData.orbWallet.value)}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Earned:</span>
                      <div className="font-semibold text-green-600">+{revenueData.orbWallet.earned}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Daily:</span>
                      <div className="font-semibold">{revenueData.orbWallet.dailyPoints} pts</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Conversion:</span>
                      <div className="font-semibold">{revenueData.orbWallet.conversionRate}%</div>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button color="secondary" size="sm">
                    Trade
                  </Button>
                  <Button variant="light" size="sm">
                    Convert
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Second Row - Goals and Ranking */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Monthly Goal - 1x1 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Target className="text-blue-500" size={20} />
                <h3 className="font-semibold">Monthly Goal</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {revenueData.goals.percentage}%
                </div>
                <Progress 
                  value={revenueData.goals.percentage} 
                  color="primary" 
                  size="lg"
                  className="mb-3"
                />
                <div className="text-sm text-gray-600">
                  {formatCurrency(revenueData.goals.currentProgress)} / {formatCurrency(revenueData.goals.monthlyTarget)}
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Global Ranking - 1x1 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Award className="text-yellow-500" size={20} />
                <h3 className="font-semibold">Global Rank</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600 mb-2">
                  #{revenueData.ranking.global}
                </div>
                <div className="text-sm text-gray-600 mb-2">Global</div>
                <div className="text-lg font-semibold text-purple-600">
                  {revenueData.ranking.orbEarned} ORB
                </div>
                <div className="text-xs text-gray-500">Today</div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Active Projects Section - 6x2 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <span className="text-2xl">🚀</span>
                <h3 className="text-lg font-semibold">Active Projects</h3>
              </div>
              <Button variant="light" size="sm">
                View All (5)
              </Button>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              {revenueData.activeVentures.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{project.icon}</span>
                      <div>
                        <h4 className="font-semibold">{project.name}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>{formatCurrency(project.earned)} earned</span>
                          <span>{project.share}% share</span>
                          <span>+{formatCurrency(project.monthlyEarning)} this month</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right text-sm">
                      <div className="flex items-center gap-2 mb-1">
                        <Users size={14} />
                        <span>{project.members} members</span>
                        <Star size={14} className="text-yellow-500" />
                        <span>{project.rating}★</span>
                      </div>
                      <div className="text-gray-600">{project.missions} missions completed</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex-1 mr-4">
                      <Progress 
                        value={project.progress} 
                        color="success" 
                        size="sm"
                        className="mb-1"
                      />
                      <div className="text-xs text-gray-600">
                        {project.progress}% to tranche • Next milestone: {formatCurrency(project.nextMilestone)} • Est: {project.estimatedTime}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Bottom Row - Breakdown and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Breakdown - 3x2 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <BarChart3 className="text-blue-500" size={20} />
                <h3 className="font-semibold">Revenue Breakdown</h3>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <Tabs selectedKey={activeTab} onSelectionChange={setActiveTab} size="sm">
                <Tab key="workType" title="By Work Type">
                  <div className="space-y-3 mt-4">
                    {Object.entries(revenueData.breakdown.byWorkType).map(([type, data]) => (
                      <div key={type} className="flex items-center justify-between">
                        <span className="capitalize font-medium">{type}</span>
                        <div className="flex items-center gap-3">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${data.percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium w-16 text-right">
                            {formatCurrency(data.amount)}
                          </span>
                          <span className="text-xs text-gray-500 w-8">
                            {data.percentage}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </Tab>
                <Tab key="paymentType" title="By Payment Type">
                  <div className="space-y-3 mt-4">
                    {Object.entries(revenueData.breakdown.byPaymentType).map(([type, data]) => (
                      <div key={type} className="flex items-center justify-between">
                        <span className="capitalize font-medium">{type}</span>
                        <div className="flex items-center gap-3">
                          <div className="w-24 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-500 h-2 rounded-full"
                              style={{ width: `${data.percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium w-16 text-right">
                            {formatCurrency(data.amount)}
                          </span>
                          <span className="text-xs text-gray-500 w-8">
                            {data.percentage}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </Tab>
              </Tabs>
            </CardBody>
          </Card>
        </motion.div>

        {/* Recent Transactions - 3x2 */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-2">
                  <Calendar className="text-purple-500" size={20} />
                  <h3 className="font-semibold">Recent Transactions</h3>
                </div>
                <Button variant="light" size="sm">
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-3">
                {revenueData.recentTransactions.map((transaction, index) => (
                  <motion.div
                    key={transaction.id}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{transaction.icon}</span>
                      <div>
                        <h5 className="font-medium text-sm">{transaction.project}</h5>
                        <p className="text-xs text-gray-600">{transaction.type}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-green-600">
                        +{formatCurrency(transaction.amount)}
                      </div>
                      <div className="text-xs text-gray-500">{transaction.date}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Trends Chart Placeholder - 6x1 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2">
              <TrendingUp className="text-green-500" size={20} />
              <h3 className="font-semibold">6-Month Earnings Trend</h3>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="text-center py-12 text-gray-500">
              <BarChart3 size={48} className="mx-auto mb-4 opacity-50" />
              <p>Interactive earnings chart coming soon!</p>
              <p className="text-sm">Track your earnings trends over time</p>
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default EnhancedRevenueDashboard;
