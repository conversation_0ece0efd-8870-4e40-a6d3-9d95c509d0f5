# Organize Migrations Script
# This script organizes Supabase migrations by removing duplicates and organizing them by category

Write-Host "=== Organizing Supabase Migrations ===" -ForegroundColor Cyan

# Define the directories
$migrationsDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations"
$backupDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations_backup"
$archiveDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations_archive"

# Ensure the backup and archive directories exist
if (-not (Test-Path $backupDir)) {
    New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
}

if (-not (Test-Path $archiveDir)) {
    New-Item -Path $archiveDir -ItemType Directory -Force | Out-Null
}

# List of migrations to archive (old migrations that have been applied)
$migrationsToArchive = @(
    # Old migrations from 2023
    "20231101000000_create_contributions_table.sql",
    "20231102000000_update_milestones_table.sql",
    "20231103000000_create_bug_reports_table.sql",
    
    # Old migrations from early 2024
    "20240101000000_create_revenue_table.sql",
    "20240102000000_create_revenue_distribution_table.sql",
    
    # April 2024 migrations
    "20240414000001_add_category_to_contributions.sql",
    "20240414000002_fix_users_policy_recursion.sql",
    "20240414000003_add_difficulty_to_contributions.sql",
    "20240414000004_add_status_to_contributions.sql",
    "20240414000005_add_missing_columns_to_contributions.sql"
)

# List of migrations to remove (duplicates or unnecessary)
$migrationsToRemove = @(
    # Duplicate notifications table migrations
    "20240601000000_create_notifications_table_fixed.sql",
    
    # Duplicate task table migrations
    "20240501000018_create_tasks_table.sql",
    
    # Duplicate project thumbnails migration
    "20240501000020_ensure_project_thumbnails.sql",
    
    # Duplicate latest feature column migration
    "20240515000001_add_latest_feature_column.sql"
)

# Archive old migrations
Write-Host "`nArchiving old migrations..." -ForegroundColor Yellow
foreach ($migrationFile in $migrationsToArchive) {
    $filePath = Join-Path -Path $migrationsDir -ChildPath $migrationFile
    $archivePath = Join-Path -Path $archiveDir -ChildPath $migrationFile
    
    if (Test-Path $filePath) {
        Write-Host "  Archiving $migrationFile..." -ForegroundColor Gray
        
        # If a file with the same name already exists in the archive directory, append a timestamp
        if (Test-Path $archivePath) {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $archivePath = Join-Path -Path $archiveDir -ChildPath "$([System.IO.Path]::GetFileNameWithoutExtension($migrationFile))_$timestamp$([System.IO.Path]::GetExtension($migrationFile))"
        }
        
        # Move the file to the archive directory
        Move-Item -Path $filePath -Destination $archivePath -Force
    } else {
        Write-Host "  File not found: $migrationFile" -ForegroundColor Gray
    }
}

# Remove duplicate migrations
Write-Host "`nRemoving duplicate migrations..." -ForegroundColor Yellow
foreach ($migrationFile in $migrationsToRemove) {
    $filePath = Join-Path -Path $migrationsDir -ChildPath $migrationFile
    $backupPath = Join-Path -Path $backupDir -ChildPath $migrationFile
    
    if (Test-Path $filePath) {
        Write-Host "  Moving $migrationFile to backup directory..." -ForegroundColor Gray
        
        # If a file with the same name already exists in the backup directory, append a timestamp
        if (Test-Path $backupPath) {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $backupPath = Join-Path -Path $backupDir -ChildPath "$([System.IO.Path]::GetFileNameWithoutExtension($migrationFile))_$timestamp$([System.IO.Path]::GetExtension($migrationFile))"
        }
        
        # Move the file to the backup directory
        Move-Item -Path $filePath -Destination $backupPath -Force
    } else {
        Write-Host "  File not found: $migrationFile" -ForegroundColor Gray
    }
}

# List remaining migration files
Write-Host "`nRemaining migration files:" -ForegroundColor Green
Get-ChildItem -Path $migrationsDir -File | ForEach-Object {
    Write-Host "- $($_.Name)" -ForegroundColor Gray
}

Write-Host "`n=== Migration Organization Complete ===" -ForegroundColor Cyan
Write-Host "Old migrations have been moved to: $archiveDir" -ForegroundColor Cyan
Write-Host "Duplicate migrations have been moved to: $backupDir" -ForegroundColor Cyan
