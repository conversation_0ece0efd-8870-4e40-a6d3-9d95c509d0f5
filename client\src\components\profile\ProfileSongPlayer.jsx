import React, { useState, useEffect, useRef } from 'react';

const ProfileSongPlayer = ({ 
  songUrl, 
  songTitle = 'Profile Song', 
  isOwnProfile = false,
  onChangeSong = null
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(0.5);
  const [showVolumeControl, setShowVolumeControl] = useState(false);
  
  const audioRef = useRef(null);
  const progressBarRef = useRef(null);
  
  // Initialize audio element
  useEffect(() => {
    if (!songUrl) return;
    
    const audio = audioRef.current;
    
    // Set up event listeners
    audio.addEventListener('loadedmetadata', () => {
      setDuration(audio.duration);
    });
    
    audio.addEventListener('timeupdate', () => {
      setCurrentTime(audio.currentTime);
    });
    
    audio.addEventListener('ended', () => {
      setIsPlaying(false);
      audio.currentTime = 0;
    });
    
    // Set initial volume
    audio.volume = volume;
    
    // Clean up event listeners
    return () => {
      audio.removeEventListener('loadedmetadata', () => {});
      audio.removeEventListener('timeupdate', () => {});
      audio.removeEventListener('ended', () => {});
    };
  }, [songUrl]);
  
  // Handle play/pause
  const togglePlay = () => {
    if (!songUrl) return;
    
    const audio = audioRef.current;
    
    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    
    setIsPlaying(!isPlaying);
  };
  
  // Handle progress bar click
  const handleProgressBarClick = (e) => {
    if (!songUrl) return;
    
    const progressBar = progressBarRef.current;
    const audio = audioRef.current;
    
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    
    audio.currentTime = clickPosition * duration;
  };
  
  // Handle volume change
  const handleVolumeChange = (e) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  };
  
  // Format time (seconds to MM:SS)
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  // Calculate progress percentage
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;
  
  // Get song filename from URL
  const getSongFilename = (url) => {
    if (!url) return '';
    
    try {
      const filename = url.split('/').pop().split('?')[0];
      // Decode URI components and remove file extension
      return decodeURIComponent(filename).replace(/\.[^/.]+$/, '');
    } catch (error) {
      return 'Unknown Song';
    }
  };
  
  // Display song title or filename
  const displayTitle = songTitle || getSongFilename(songUrl);
  
  if (!songUrl) return null;
  
  return (
    <div className="profile-song-player">
      <audio ref={audioRef} src={songUrl} preload="metadata" />
      
      <div className="player-controls">
        <button 
          className="play-pause-btn"
          onClick={togglePlay}
          title={isPlaying ? 'Pause' : 'Play'}
        >
          <i className={`bi ${isPlaying ? 'bi-pause-fill' : 'bi-play-fill'}`}></i>
        </button>
        
        <div className="song-info">
          <div className="now-playing">
            <i className="bi bi-music-note-beamed"></i>
            <span>Now Playing:</span>
          </div>
          <div className="song-title">{displayTitle}</div>
        </div>
        
        <div className="player-right-controls">
          <div className="time-display">
            <span>{formatTime(currentTime)}</span>
            <span>/</span>
            <span>{formatTime(duration)}</span>
          </div>
          
          <div className="volume-control-container">
            <button 
              className="volume-btn"
              onClick={() => setShowVolumeControl(!showVolumeControl)}
              title="Volume"
            >
              <i className={`bi ${
                volume === 0 ? 'bi-volume-mute' : 
                volume < 0.5 ? 'bi-volume-down' : 
                'bi-volume-up'
              }`}></i>
            </button>
            
            {showVolumeControl && (
              <div className="volume-slider-container">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.01"
                  value={volume}
                  onChange={handleVolumeChange}
                  className="volume-slider"
                />
              </div>
            )}
          </div>
          
          {isOwnProfile && onChangeSong && (
            <button 
              className="change-song-btn"
              onClick={onChangeSong}
              title="Change Song"
            >
              <i className="bi bi-music-note-list"></i>
            </button>
          )}
        </div>
      </div>
      
      <div 
        className="progress-bar" 
        ref={progressBarRef}
        onClick={handleProgressBarClick}
      >
        <div 
          className="progress" 
          style={{ width: `${progressPercentage}%` }}
        ></div>
      </div>
    </div>
  );
};

export default ProfileSongPlayer;
