import { useState, useEffect } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import { getAuth } from "firebase/auth";

const ContributorSelect = (props) => {
  const { data, setData } = props;
  const [users, setUsers] = useState([]); // State to store users
  const auth = getAuth(); // Initialize Firebase Auth

  // Function to get Firebase auth token
  const getToken = async () => {
    const currentUser = auth.currentUser;
    if (currentUser) {
      return await currentUser.getIdToken();
    }
    return null;
  };

  // Fetch the list of users when component mounts
  useEffect(() => {
    const fetchUsers = async () => {
      const token = await getToken();
      if (!token) {
        toast.error("Authentication error. Please log in again.");
        return;
      }

      try {
        const response = await axios.get("/user/index", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }); // Fetch users from user index
        if (response.status === 200) {
          setUsers(response.data); // Make sure to only set users if response is OK
        } else {
          throw new Error("Failed to load users.");
        }
      } catch (error) {
        console.log("Error fetching users:", error);
        toast.error("Failed to load users.");
      }
    };
    fetchUsers();
  }, []);
  return (
    <select
      id="contributor"
      className="form-control"
      value={data.contributor || ""} // Use the selected contributor's ID or defalut to an emptry string
      onChange={(e) => setData({ ...data, contributor: e.target.value })} // Update the contributor when selection changes
    >
      {/* Blank/default option */}
      <option value="" disabled>
        -- Select a User --
      </option>

      {users.map((user) => (
        <option key={user._id} value={user._id}>
          {user.email} {/* Display the contributor's name in the dropdown */}
        </option>
      ))}
    </select>
  );
};

export default ContributorSelect;
