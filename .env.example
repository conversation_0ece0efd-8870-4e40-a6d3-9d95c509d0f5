# Royaltea Environment Variables Template
# Copy this file to client/.env.local and fill in your actual values

# Supabase Configuration (Required)
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Service Key (For server-side operations)
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Database Configuration (Required for direct database access)
supabase_database_password=your_database_password_here

# Site Configuration
SITE_URL=https://royalty.technology

# Analytics (Optional)
VITE_GA_TRACKING_ID=G-S7SFML469V

# Development Settings (Optional)
NODE_ENV=development
VITE_DEBUG=false

# Teller Configuration (Required for payment processing)
TELLER_APPLICATION_ID=app_pelk82mrrofp6upddo000
TELLER_ENVIRONMENT=sandbox
TELLER_WEBHOOK_URL=https://royalty.technology/.netlify/functions/teller-webhook
TELLER_CERTIFICATE_PATH=./teller/certificate.pem
TELLER_PRIVATE_KEY_PATH=./teller/private_key.pem

# Payment Processing Configuration
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_here
PAYMENT_ENCRYPTION_KEY=your_encryption_key_here

# Instructions:
# 1. Copy this file to client/.env.local
# 2. Replace placeholder values with actual keys from Supabase dashboard
# 3. Get keys from: https://supabase.com/dashboard/project/hqqlrrqvjcetoxbdjgzx/settings/api
# 4. Get Teller certificates from: https://teller.io/console
# 5. Never commit the actual .env.local file to version control
