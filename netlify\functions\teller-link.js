// Teller Bank Account Linking API
// Integration & Services Agent: Core Teller integration for secure bank account linking

const { createClient } = require('@supabase/supabase-js');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Teller configuration
const TELLER_CONFIG = {
  environment: process.env.TELLER_ENVIRONMENT || 'sandbox',
  applicationId: process.env.TELLER_APPLICATION_ID,
  baseUrl: process.env.TELLER_ENVIRONMENT === 'production' 
    ? 'https://api.teller.io' 
    : 'https://api.teller.io',
  certificatePath: process.env.TELLER_CERTIFICATE_PATH || './teller/certificate.pem',
  privateKeyPath: process.env.TELLER_PRIVATE_KEY_PATH || './teller/private_key.pem'
};

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Create authenticated HTTPS agent for Teller API
const createTellerAgent = () => {
  try {
    const cert = fs.readFileSync(path.resolve(TELLER_CONFIG.certificatePath));
    const key = fs.readFileSync(path.resolve(TELLER_CONFIG.privateKeyPath));
    
    return new https.Agent({
      cert,
      key,
      rejectUnauthorized: true
    });
  } catch (error) {
    console.error('Failed to create Teller agent:', error);
    throw new Error('Teller certificate configuration error');
  }
};

// Make authenticated request to Teller API
const tellerRequest = async (endpoint, options = {}) => {
  const agent = createTellerAgent();
  const url = `${TELLER_CONFIG.baseUrl}${endpoint}`;
  
  const requestOptions = {
    method: options.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Teller-Application-Id': TELLER_CONFIG.applicationId,
      ...options.headers
    },
    agent
  };

  if (options.body) {
    requestOptions.body = JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url, requestOptions);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Teller API error: ${data.error || response.statusText}`);
    }
    
    return data;
  } catch (error) {
    console.error('Teller request failed:', error);
    throw error;
  }
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Create link token for bank account connection
const createLinkToken = async (user, products = ['auth', 'transactions', 'transfer']) => {
  try {
    // Create link token with Teller
    const linkTokenData = await tellerRequest('/link/token/create', {
      method: 'POST',
      body: {
        user_id: user.id,
        products,
        country_codes: ['US'],
        language: 'en',
        webhook: process.env.TELLER_WEBHOOK_URL,
        client_name: 'Royaltea Platform'
      }
    });

    // Store link token session in database
    const { error: sessionError } = await supabase
      .from('teller_link_sessions')
      .insert({
        user_id: user.id,
        link_token: linkTokenData.link_token,
        products,
        expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes
        status: 'active'
      });

    if (sessionError) {
      console.error('Failed to store link session:', sessionError);
    }

    return linkTokenData;
  } catch (error) {
    console.error('Create link token error:', error);
    throw error;
  }
};

// Exchange public token for access token
const exchangePublicToken = async (user, publicToken) => {
  try {
    // Exchange token with Teller
    const exchangeData = await tellerRequest('/link/token/exchange', {
      method: 'POST',
      body: {
        public_token: publicToken
      }
    });

    // Get account information
    const accountsData = await tellerRequest('/accounts', {
      headers: {
        'Authorization': `Bearer ${exchangeData.access_token}`
      }
    });

    // Store account information in database
    const accountInserts = accountsData.accounts.map(account => ({
      user_id: user.id,
      teller_account_id: account.id,
      teller_item_id: exchangeData.item_id,
      teller_access_token: exchangeData.access_token,
      account_type: account.type,
      account_subtype: account.subtype,
      account_name: account.name,
      institution_name: account.institution.name,
      institution_id: account.institution.id,
      account_mask: account.mask,
      available_balance: account.balances?.available || 0,
      current_balance: account.balances?.current || 0,
      supports_ach: account.capabilities?.includes('transfer') || true,
      supports_same_day_ach: account.capabilities?.includes('same_day_ach') || false,
      supports_rtp: account.capabilities?.includes('rtp') || false,
      supports_wire: account.capabilities?.includes('wire') || false,
      is_verified: true,
      is_active: true
    }));

    const { data: accounts, error: accountError } = await supabase
      .from('teller_accounts')
      .insert(accountInserts)
      .select();

    if (accountError) {
      throw new Error(`Failed to store account data: ${accountError.message}`);
    }

    // Update link session status
    await supabase
      .from('teller_link_sessions')
      .update({ 
        status: 'completed',
        completed_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('status', 'active');

    return {
      access_token: exchangeData.access_token,
      item_id: exchangeData.item_id,
      accounts: accounts
    };
  } catch (error) {
    console.error('Exchange token error:', error);
    throw error;
  }
};

// Get user's linked accounts
const getLinkedAccounts = async (user) => {
  try {
    const { data: accounts, error } = await supabase
      .from('teller_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch accounts: ${error.message}`);
    }

    return accounts || [];
  } catch (error) {
    console.error('Get linked accounts error:', error);
    throw error;
  }
};

// Remove linked account
const removeLinkedAccount = async (user, accountId) => {
  try {
    const { error } = await supabase
      .from('teller_accounts')
      .update({ 
        is_active: false,
        removed_at: new Date().toISOString()
      })
      .eq('user_id', user.id)
      .eq('id', accountId);

    if (error) {
      throw new Error(`Failed to remove account: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Remove account error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};

    let result;

    switch (action) {
      case 'create-link-token':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await createLinkToken(user, body.products);
        break;

      case 'exchange-token':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        if (!body.public_token) {
          throw new Error('Public token is required');
        }
        result = await exchangePublicToken(user, body.public_token);
        break;

      case 'accounts':
        if (httpMethod === 'GET') {
          result = await getLinkedAccounts(user);
        } else if (httpMethod === 'DELETE') {
          const accountId = event.queryStringParameters?.account_id;
          if (!accountId) {
            throw new Error('Account ID is required');
          }
          result = await removeLinkedAccount(user, accountId);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Teller Link API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
