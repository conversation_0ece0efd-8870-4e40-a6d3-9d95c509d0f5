import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, Button, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import { toast } from 'react-hot-toast';

/**
 * TeamInvitations Section Component
 * Shows pending invitations and sent invitations
 * Part of the experimental navigation system
 */
const TeamInvitations = ({ canvasId, sectionId }) => {
  const { currentUser } = useContext(UserContext);
  const [receivedInvitations, setReceivedInvitations] = useState([]);
  const [sentInvitations, setSentInvitations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentUser) {
      fetchInvitations();
    }
  }, [currentUser]);

  const fetchInvitations = async () => {
    try {
      // Fetch received invitations
      const { data: received, error: receivedError } = await supabase
        .from('team_invitations')
        .select(`
          *,
          teams:team_id (
            id,
            name,
            description,
            studio_type
          ),
          inviter:invited_by (
            email,
            user_metadata
          )
        `)
        .eq('invited_user_id', currentUser.id)
        .eq('status', 'pending');

      if (receivedError) {
        console.error('Error fetching received invitations:', receivedError);
      } else {
        setReceivedInvitations(received || []);
      }

      // Fetch sent invitations
      const { data: sent, error: sentError } = await supabase
        .from('team_invitations')
        .select(`
          *,
          teams:team_id (
            id,
            name,
            description,
            studio_type
          ),
          invitee:invited_user_id (
            email,
            user_metadata
          )
        `)
        .eq('invited_by', currentUser.id)
        .in('status', ['pending', 'accepted', 'declined']);

      if (sentError) {
        console.error('Error fetching sent invitations:', sentError);
      } else {
        setSentInvitations(sent || []);
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInvitationResponse = async (invitationId, response) => {
    try {
      const { error } = await supabase
        .from('team_invitations')
        .update({
          status: response,
          responded_at: new Date().toISOString()
        })
        .eq('id', invitationId);

      if (error) {
        throw error;
      }

      if (response === 'accepted') {
        // Add user to team
        const invitation = receivedInvitations.find(inv => inv.id === invitationId);
        const { error: memberError } = await supabase
          .from('team_members')
          .insert({
            team_id: invitation.team_id,
            user_id: currentUser.id,
            role: invitation.role || 'member',
            is_admin: invitation.role === 'admin'
          });

        if (memberError) {
          throw memberError;
        }

        toast.success('Invitation accepted! You are now a member of the team.');
      } else {
        toast.success('Invitation declined.');
      }

      fetchInvitations();
    } catch (error) {
      console.error('Error responding to invitation:', error);
      toast.error('Failed to respond to invitation');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'accepted': return 'success';
      case 'declined': return 'danger';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'accepted': return '✅';
      case 'declined': return '❌';
      default: return '📧';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardBody className="p-8">
            <LoadingAnimation />
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6 space-y-6"
    >
      {/* Section Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-white mb-2">📧 Team Invitations</h2>
        <p className="text-white/70">
          Manage your team invitations and collaboration requests
        </p>
      </div>

      {/* Received Invitations */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">📥 Received Invitations</h3>

          {receivedInvitations.length > 0 ? (
            <div className="space-y-4">
              {receivedInvitations.map((invitation) => (
                <motion.div
                  key={invitation.id}
                  whileHover={{ scale: 1.02 }}
                  className="bg-white/5 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-3xl">🏰</div>
                      <div>
                        <h4 className="text-lg font-bold text-white">
                          {invitation.teams?.name}
                        </h4>
                        <p className="text-white/70 text-sm">
                          Invited by: {invitation.inviter?.user_metadata?.full_name || invitation.inviter?.email}
                        </p>
                        <p className="text-white/60 text-sm">
                          Role: {invitation.role || 'Member'}
                        </p>
                        {invitation.message && (
                          <p className="text-white/60 text-sm mt-2 italic">
                            "{invitation.message}"
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        color="success"
                        size="sm"
                        startContent="✅"
                        onClick={() => handleInvitationResponse(invitation.id, 'accepted')}
                      >
                        Accept
                      </Button>
                      <Button
                        color="danger"
                        variant="flat"
                        size="sm"
                        startContent="❌"
                        onClick={() => handleInvitationResponse(invitation.id, 'declined')}
                      >
                        Decline
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📭</div>
              <p className="text-white/70">No pending invitations</p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Sent Invitations */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">📤 Sent Invitations</h3>

          {sentInvitations.length > 0 ? (
            <div className="space-y-4">
              {sentInvitations.map((invitation) => (
                <motion.div
                  key={invitation.id}
                  whileHover={{ scale: 1.02 }}
                  className="bg-white/5 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-3xl">
                        {getStatusIcon(invitation.status)}
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-white">
                          {invitation.teams?.name}
                        </h4>
                        <p className="text-white/70 text-sm">
                          Invited: {invitation.invitee?.user_metadata?.full_name || invitation.invitee?.email}
                        </p>
                        <p className="text-white/60 text-sm">
                          Role: {invitation.role || 'Member'}
                        </p>
                        <p className="text-white/60 text-xs">
                          Sent: {new Date(invitation.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Badge color={getStatusColor(invitation.status)} variant="flat">
                        {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
                      </Badge>
                      {invitation.status === 'pending' && (
                        <Button
                          color="warning"
                          variant="flat"
                          size="sm"
                          onClick={async () => {
                            try {
                              // Cancel the invitation
                              const { error } = await supabase
                                .from('team_invitations')
                                .update({ status: 'cancelled' })
                                .eq('id', invitation.id);

                              if (error) throw error;

                              toast.success('Invitation cancelled successfully');

                              // Update local state
                              setSentInvitations(prev =>
                                prev.map(inv =>
                                  inv.id === invitation.id
                                    ? { ...inv, status: 'cancelled' }
                                    : inv
                                )
                              );
                            } catch (error) {
                              console.error('Error cancelling invitation:', error);
                              toast.error('Failed to cancel invitation');
                            }
                          }}
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📤</div>
              <p className="text-white/70">No invitations sent</p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Quick Actions */}
      <Card className="bg-white/10 backdrop-blur-md border-white/20">
        <CardBody className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">🚀 Quick Actions</h3>
          <div className="flex flex-wrap gap-4 justify-center">
            <Button
              color="primary"
              variant="solid"
              startContent="🏰"
              onClick={() => window.location.href = '/teams/create'}
            >
              Create New Studio
            </Button>
            <Button
              color="secondary"
              variant="solid"
              startContent="🔍"
              onClick={() => window.location.href = '/teams/discover'}
            >
              Discover Teams
            </Button>
            <Button
              color="success"
              variant="solid"
              startContent="👥"
              onClick={() => window.location.href = '/teams'}
            >
              View My Teams
            </Button>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default TeamInvitations;
