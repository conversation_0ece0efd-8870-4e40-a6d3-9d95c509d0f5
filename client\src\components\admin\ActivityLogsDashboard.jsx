import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Tabs, Tab, Input, Select, SelectItem, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * Activity Logs Dashboard
 *
 * Admin interface for viewing and analyzing user activity logs
 * to help debug and improve the experimental navigation system.
 */
const ActivityLogsDashboard = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    eventType: 'all',
    timeRange: '1h',
    userId: '',
    sessionId: ''
  });
  const [activeTab, setActiveTab] = useState('recent');
  const [stats, setStats] = useState({});

  // Fetch activity logs
  const fetchLogs = async () => {
    setLoading(true);
    try {
      let query = supabase
        .from('user_activity_logs')
        .select(`
          *,
          users:user_id (
            email,
            full_name
          )
        `)
        .order('timestamp', { ascending: false })
        .limit(100);

      // Apply filters
      if (filters.eventType !== 'all') {
        query = query.eq('event_type', filters.eventType);
      }

      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters.sessionId) {
        query = query.eq('session_id', filters.sessionId);
      }

      // Time range filter
      const now = new Date();
      let timeFilter;
      switch (filters.timeRange) {
        case '1h':
          timeFilter = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          timeFilter = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          timeFilter = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          timeFilter = new Date(now.getTime() - 60 * 60 * 1000);
      }
      query = query.gte('timestamp', timeFilter.toISOString());

      const { data, error } = await query;

      if (error) throw error;
      setLogs(data || []);
    } catch (error) {
      console.error('Error fetching logs:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStats = async () => {
    try {
      const { data, error } = await supabase
        .rpc('get_activity_stats', {
          time_range_hours: filters.timeRange === '1h' ? 1 : filters.timeRange === '24h' ? 24 : 168
        });

      if (!error && data) {
        setStats(data[0] || {});
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchLogs();
    fetchStats();
  }, [filters]);

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Get event type color
  const getEventTypeColor = (eventType) => {
    switch (eventType) {
      case 'navigation': return 'primary';
      case 'interaction': return 'secondary';
      case 'error': return 'danger';
      case 'debug': return 'warning';
      case 'performance': return 'success';
      default: return 'default';
    }
  };

  // Render log entry
  const renderLogEntry = (log) => (
    <Card key={log.id} className="mb-4">
      <CardBody className="p-4">
        <div className="flex justify-between items-start mb-2">
          <div className="flex items-center gap-2">
            <Chip color={getEventTypeColor(log.event_type)} size="sm">
              {log.event_type}
            </Chip>
            <span className="text-sm font-medium">{log.event_category}</span>
            <span className="text-xs text-gray-500">{log.event_action}</span>
          </div>
          <span className="text-xs text-gray-400">
            {formatTimestamp(log.timestamp)}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>User:</strong> {log.users?.email || 'Unknown'}
          </div>
          <div>
            <strong>Session:</strong> {log.session_id?.slice(-8)}
          </div>

          {log.from_page && (
            <div>
              <strong>From:</strong> {log.from_page}
            </div>
          )}

          {log.to_page && (
            <div>
              <strong>To:</strong> {log.to_page}
            </div>
          )}

          {log.view_mode && (
            <div>
              <strong>View Mode:</strong> {log.view_mode}
            </div>
          )}

          {log.element_id && (
            <div>
              <strong>Element:</strong> {log.element_id}
            </div>
          )}
        </div>

        {log.mouse_position && (
          <div className="mt-2 text-xs text-gray-500">
            Mouse: ({log.mouse_position.x}, {log.mouse_position.y})
          </div>
        )}

        {log.error_message && (
          <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-700">
            <strong>Error:</strong> {log.error_message}
          </div>
        )}

        {log.debug_data && Object.keys(log.debug_data).length > 0 && (
          <details className="mt-2">
            <summary className="text-xs text-gray-500 cursor-pointer">Debug Data</summary>
            <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-auto">
              {JSON.stringify(log.debug_data, null, 2)}
            </pre>
          </details>
        )}
      </CardBody>
    </Card>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">Activity Logs Dashboard</h1>
        <p className="text-gray-600">Monitor user navigation and interactions in real-time</p>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <h3 className="text-lg font-semibold">Filters</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              label="Event Type"
              selectedKeys={[filters.eventType]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, eventType: Array.from(keys)[0] }))}
            >
              <SelectItem key="all">All Events</SelectItem>
              <SelectItem key="navigation">Navigation</SelectItem>
              <SelectItem key="interaction">Interaction</SelectItem>
              <SelectItem key="error">Errors</SelectItem>
              <SelectItem key="debug">Debug</SelectItem>
              <SelectItem key="performance">Performance</SelectItem>
            </Select>

            <Select
              label="Time Range"
              selectedKeys={[filters.timeRange]}
              onSelectionChange={(keys) => setFilters(prev => ({ ...prev, timeRange: Array.from(keys)[0] }))}
            >
              <SelectItem key="1h">Last Hour</SelectItem>
              <SelectItem key="24h">Last 24 Hours</SelectItem>
              <SelectItem key="7d">Last 7 Days</SelectItem>
            </Select>

            <Input
              label="User ID"
              placeholder="Filter by user ID"
              value={filters.userId}
              onChange={(e) => setFilters(prev => ({ ...prev, userId: e.target.value }))}
            />

            <Input
              label="Session ID"
              placeholder="Filter by session ID"
              value={filters.sessionId}
              onChange={(e) => setFilters(prev => ({ ...prev, sessionId: e.target.value }))}
            />
          </div>

          <div className="mt-4 flex gap-2">
            <Button onClick={fetchLogs} disabled={loading}>
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
            <Button
              variant="bordered"
              onClick={() => setFilters({ eventType: 'all', timeRange: '1h', userId: '', sessionId: '' })}
            >
              Clear Filters
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Statistics */}
      {stats && Object.keys(stats).length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <h3 className="text-lg font-semibold">Statistics</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.total_events || 0}</div>
                <div className="text-sm text-gray-500">Total Events</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.unique_users || 0}</div>
                <div className="text-sm text-gray-500">Active Users</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.navigation_events || 0}</div>
                <div className="text-sm text-gray-500">Navigation Events</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{stats.error_events || 0}</div>
                <div className="text-sm text-gray-500">Errors</div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Logs */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Activity Logs ({logs.length})</h3>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="text-center py-8">Loading logs...</div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">No logs found for the selected filters</div>
          ) : (
            <div className="max-h-96 overflow-y-auto">
              {logs.map(renderLogEntry)}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default ActivityLogsDashboard;
