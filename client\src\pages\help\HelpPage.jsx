import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Input, Accordion, AccordionItem } from '@heroui/react';

/**
 * Help Page Component
 * 
 * Comprehensive help center with FAQ, guides, support contact, and search functionality.
 * Follows the spatial-first design philosophy with intuitive information architecture.
 */
const HelpPage = () => {
  const [searchQuery, setSearchQuery] = useState('');

  // FAQ data
  const faqData = [
    {
      question: "How does the royalty system work?",
      answer: "Royalties are calculated based on your contributions to projects. Each contribution is weighted by time spent and difficulty level. When a project generates revenue, it's distributed proportionally to all contributors based on their contribution scores."
    },
    {
      question: "How do I track my time accurately?",
      answer: "Use the built-in time tracker in the Track section. Start the timer when you begin work, add a task description, and stop when you're done. You can also manually log time for work completed outside the platform."
    },
    {
      question: "What happens to money in escrow?",
      answer: "Escrow funds are held until project milestones are met or specific release conditions are satisfied. This protects both contributors and project owners by ensuring fair distribution of revenue."
    },
    {
      question: "Can I work on multiple projects?",
      answer: "Yes! You can contribute to multiple projects simultaneously. Each project tracks your contributions separately, and you'll earn royalties from each based on your individual contribution to that project."
    },
    {
      question: "How are contributions validated?",
      answer: "Project owners and designated validators review submitted contributions. They check the quality, accuracy, and relevance of the work before approving it for royalty calculations."
    }
  ];

  const guides = [
    {
      title: "Getting Started Guide",
      description: "Complete walkthrough for new users",
      icon: "🚀",
      duration: "10 min read"
    },
    {
      title: "Time Tracking Best Practices",
      description: "Maximize your contribution accuracy",
      icon: "⏱️",
      duration: "5 min read"
    },
    {
      title: "Project Creation Tutorial",
      description: "Set up your first collaborative project",
      icon: "📁",
      duration: "15 min read"
    },
    {
      title: "Understanding Royalties",
      description: "How revenue sharing works",
      icon: "💰",
      duration: "8 min read"
    }
  ];

  const filteredFAQ = faqData.filter(item =>
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <motion.div
        className="relative z-10 pt-8 pb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 10, -10, 0]
              }}
              transition={{ 
                duration: 3, 
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              ❓
            </motion.div>
            <h1 className="text-4xl font-bold text-white mb-2">
              Help Center
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Find answers, learn best practices, and get support for your Royaltea journey.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <Input
              placeholder="Search for help articles, guides, or FAQ..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={
                <svg className="w-5 h-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              }
              className="text-white"
              classNames={{
                input: "text-white",
                inputWrapper: "bg-white/10 border-white/20 hover:bg-white/20"
              }}
            />
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* FAQ Section */}
            <div className="lg:col-span-2">
              <Card className="bg-white/5 border border-white/10">
                <CardHeader className="pb-3">
                  <h2 className="text-2xl font-semibold text-white">
                    Frequently Asked Questions
                  </h2>
                </CardHeader>
                <CardBody>
                  <Accordion variant="splitted" className="px-0">
                    {filteredFAQ.map((item, index) => (
                      <AccordionItem
                        key={index}
                        title={item.question}
                        className="bg-white/5 border border-white/10 text-white"
                        classNames={{
                          title: "text-white font-medium",
                          content: "text-white/80"
                        }}
                      >
                        {item.answer}
                      </AccordionItem>
                    ))}
                  </Accordion>

                  {filteredFAQ.length === 0 && searchQuery && (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">🔍</div>
                      <p className="text-white/70">No results found for "{searchQuery}"</p>
                      <Button 
                        className="mt-4 bg-blue-500 hover:bg-blue-600 text-white"
                        onClick={() => setSearchQuery('')}
                      >
                        Clear Search
                      </Button>
                    </div>
                  )}
                </CardBody>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Guides */}
              <Card className="bg-white/5 border border-white/10">
                <CardHeader className="pb-3">
                  <h3 className="text-xl font-semibold text-white">Quick Guides</h3>
                </CardHeader>
                <CardBody className="space-y-3">
                  {guides.map((guide, index) => (
                    <motion.div
                      key={index}
                      className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-start gap-3">
                        <div className="text-2xl">{guide.icon}</div>
                        <div className="flex-1">
                          <h4 className="text-white font-medium text-sm">{guide.title}</h4>
                          <p className="text-white/60 text-xs mb-1">{guide.description}</p>
                          <span className="text-blue-400 text-xs">{guide.duration}</span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </CardBody>
              </Card>

              {/* Contact Support */}
              <Card className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30">
                <CardBody className="p-6 text-center">
                  <div className="text-4xl mb-4">💬</div>
                  <h3 className="text-xl font-semibold text-white mb-2">Need More Help?</h3>
                  <p className="text-white/80 text-sm mb-4">
                    Can't find what you're looking for? Our support team is here to help.
                  </p>
                  <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                    Contact Support
                  </Button>
                </CardBody>
              </Card>

              {/* Community */}
              <Card className="bg-white/5 border border-white/10">
                <CardBody className="p-6 text-center">
                  <div className="text-4xl mb-4">🌍</div>
                  <h3 className="text-lg font-semibold text-white mb-2">Community Forum</h3>
                  <p className="text-white/70 text-sm mb-4">
                    Connect with other users and share knowledge.
                  </p>
                  <Button className="w-full bg-white/10 hover:bg-white/20 text-white">
                    Join Discussion
                  </Button>
                </CardBody>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default HelpPage;
