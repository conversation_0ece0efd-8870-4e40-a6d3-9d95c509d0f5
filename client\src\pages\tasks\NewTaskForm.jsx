import { useState, useContext, useEffect } from "react";
import axios from "axios";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { UserContext } from "../../../contexts/user.context";

const NewTaskForm = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();

  const [data, setData] = useState({
    user: "",
    title: "",
    description: "",
    difficulty: "1",
  });

  useEffect(() => {
    if (currentUser) {
      setData((prev) => ({ ...prev, user: currentUser._id })); // Ensure `user` is set once available
    }
  }, [currentUser]);

  const createTask = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      toast.error("Authentication error. Please log in.");
      return;
    }

    try {
      const token = await currentUser.getIdToken(); // Get Firebase token
      const response = await axios.post("/task/new", data, {
        headers: { Authorization: `Bearer ${token}` }, // Send token for verification
        withCredentials: true,
      });
      if (response.data.error) {
        toast.error(response.data.error);
      } else {
        setData({
          user: currentUser.uid,
          title: "",
          description: "",
          difficulty: "1",
        });
        toast.success("Task Created");
        navigate("/task/index");
      }
    } catch (error) {
      console.error("Error creating task:", error);
      toast.error("Failed to create task.");
    }
  };

  return (
    <>
      <h1>New Task</h1>
      <form
        onSubmit={createTask}
        style={{
          display: "flex",
          flexDirection: "column",
          gap: "20px",
          alignItems: "flex-start",
          width: "100%",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "5px",
            alignItems: "flex-start",
            width: "100%",
          }}
        >
          <label htmlFor="title">Title</label>
          <input
            id="title"
            name="title"
            type="text"
            value={data.title}
            onChange={(e) => {
              setData({ ...data, title: e.target.value });
            }}
            style={{ width: "100%" }}
          />
        </div>
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: "5px",
            alignItems: "flex-start",
            width: "100%",
          }}
        >
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={data.description}
            onChange={(e) => {
              setData({ ...data, description: e.target.value });
            }}
            style={{ width: "100%" }}
          ></textarea>
        </div>
        <div
          style={{
            display: "flex",
            gap: "20px",
            alignItems: "flex-start",
            width: "100%",
          }}
        >
          <label htmlFor="difficulty">Difficulty Rating</label>
          <select
            name="difficulty"
            id="difficulty"
            required
            onChange={(e) => {
              setData({ ...data, difficulty: e.target.value });
            }}
          >
            <option value={1} selected>
              1
            </option>
            <option value={2}>2</option>
            <option value={3}>3</option>
            <option value={4}>4</option>
            <option value={5}>5</option>
            <option value={6}>6</option>
            <option value={7}>7</option>
            <option value={8}>8</option>
            <option value={9}>9</option>
            <option value={10}>10</option>
          </select>
        </div>
        <button type="submit">Create Task</button>
      </form>
    </>
  );
};

export default NewTaskForm;
