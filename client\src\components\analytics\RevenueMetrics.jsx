import React from 'react';
import { Card, CardBody, CardHeader, <PERSON><PERSON>, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { RevenueChart } from '../charts';

/**
 * Revenue Metrics Widget - 2x2 Bento Grid Component
 * 
 * Features:
 * - Total revenue display with growth indicators
 * - Monthly revenue breakdown
 * - Platform fees and commission tracking
 * - Average earnings and hourly rate
 * - Interactive drill-down capabilities
 */
const RevenueMetrics = ({ data, period, className = "" }) => {
  const {
    total = 0,
    thisMonth = 0,
    growth = 0,
    platformFees = 0,
    avgMonthly = 0,
    hourlyRate = 0
  } = data || {};

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get growth indicator
  const getGrowthIndicator = (growth) => {
    if (growth > 0) return { icon: '↗️', color: 'success', text: `+${growth}%` };
    if (growth < 0) return { icon: '↘️', color: 'danger', text: `${growth}%` };
    return { icon: '→', color: 'default', text: '0%' };
  };

  const growthIndicator = getGrowthIndicator(growth);

  // Calculate platform fee percentage
  const feePercentage = total > 0 ? ((platformFees / total) * 100).toFixed(1) : 0;

  return (
    <div className={`revenue-metrics ${className}`}>
      <Card className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-800/20 border-2 border-green-200 dark:border-green-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💰</span>
              <h3 className="text-lg font-semibold">Revenue Metrics</h3>
            </div>
            <Chip color="success" variant="flat" size="sm">
              {period.toUpperCase()}
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Total Revenue */}
          <div className="mb-4">
            <div className="flex items-baseline gap-2 mb-1">
              <span className="text-3xl font-bold text-green-600">
                {formatCurrency(total)}
              </span>
              <Chip 
                color={growthIndicator.color} 
                variant="flat" 
                size="sm"
                startContent={<span>{growthIndicator.icon}</span>}
              >
                {growthIndicator.text}
              </Chip>
            </div>
            <p className="text-sm text-default-600">Total Revenue</p>
          </div>

          {/* This Month Revenue */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-default-700">This Month</span>
              <span className="text-lg font-semibold text-green-600">
                {formatCurrency(thisMonth)}
              </span>
            </div>
            <div className="w-full bg-default-200 rounded-full h-2">
              <motion.div
                className="bg-green-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${Math.min((thisMonth / (total * 0.4)) * 100, 100)}%` }}
                transition={{ duration: 1, delay: 0.5 }}
              />
            </div>
          </div>

          {/* Platform Fees */}
          <div className="mb-4 p-3 bg-white/50 dark:bg-slate-800/50 rounded-lg">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-2">
                <span className="text-sm">💎</span>
                <span className="text-sm font-medium">Platform Fees</span>
              </div>
              <span className="text-sm font-semibold">
                {formatCurrency(platformFees)} ({feePercentage}%)
              </span>
            </div>
            <div className="text-xs text-default-500">
              Commission on completed missions
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="text-center p-2 bg-default-50 dark:bg-slate-800/30 rounded-lg">
              <div className="text-lg font-bold text-green-600">
                {formatCurrency(avgMonthly)}
              </div>
              <div className="text-xs text-default-600">Avg/Month</div>
            </div>
            <div className="text-center p-2 bg-default-50 dark:bg-slate-800/30 rounded-lg">
              <div className="text-lg font-bold text-green-600">
                ${hourlyRate}
              </div>
              <div className="text-xs text-default-600">Hourly Rate</div>
            </div>
          </div>

          {/* Revenue Chart */}
          <div className="mb-4">
            <RevenueChart
              data={data.chartData}
              type="area"
              height={150}
              showLegend={false}
              className="bg-white/50 dark:bg-slate-800/50 rounded-lg"
              title=""
            />
          </div>

          {/* Action Button */}
          <Button
            variant="flat"
            color="success"
            size="sm"
            className="w-full"
          >
            View Details
          </Button>
        </CardBody>
      </Card>
    </div>
  );
};

export default RevenueMetrics;
