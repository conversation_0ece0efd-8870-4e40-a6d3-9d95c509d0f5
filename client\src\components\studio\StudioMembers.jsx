import React from 'react';
import { Card, CardBody, CardHeader, Button, Avatar, Badge } from '@heroui/react';
import { UserPlus, Mail } from 'lucide-react';

const StudioMembers = ({ studioId, members = [], onMembersUpdate }) => {
  const handleInviteMember = () => {
    // TODO: Open invite member modal
    console.log('Invite member to studio:', studioId);
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'founder': return 'warning';
      case 'owner': return 'warning';
      case 'admin': return 'primary';
      case 'member': return 'default';
      default: return 'default';
    }
  };

  const getCollaborationTypeIcon = (type) => {
    switch (type) {
      case 'studio_member': return '👥';
      case 'contractor': return '🤝';
      case 'specialist': return '⭐';
      default: return '👤';
    }
  };

  return (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Studio Members</h3>
        <Button
          size="sm"
          color="primary"
          startContent={<UserPlus size={16} />}
          onClick={handleInviteMember}
        >
          Invite
        </Button>
      </CardHeader>
      <CardBody>
        {members.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">👥</div>
            <p className="text-gray-600 mb-4">No members yet</p>
            <Button
              color="primary"
              variant="flat"
              startContent={<Mail size={16} />}
              onClick={handleInviteMember}
            >
              Invite First Member
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {members.map((member) => (
              <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar
                    src={member.users?.avatar_url}
                    name={member.users?.display_name}
                    size="sm"
                  />
                  <div>
                    <p className="font-medium text-sm">{member.users?.display_name}</p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{getCollaborationTypeIcon(member.collaboration_type)}</span>
                      <span>{member.collaboration_type?.replace('_', ' ')}</span>
                      {member.engagement_duration && (
                        <>
                          <span>•</span>
                          <span>{member.engagement_duration}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge 
                    color={getRoleColor(member.role)} 
                    variant="flat" 
                    size="sm"
                  >
                    {member.role}
                  </Badge>
                  {member.status === 'pending' && (
                    <Badge color="warning" variant="flat" size="sm">
                      Pending
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default StudioMembers;
