import React, { useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Card, CardBody, Input, Button, Textarea, Select, SelectItem } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { LoadingButton, FormLoadingOverlay } from '../common/LoadingStates';
import { useEnhancedLoading } from '../../hooks/useEnhancedLoading';
import { ScreenReaderAnnouncement } from '../accessibility/AccessibilityEnhancements';

/**
 * Enhanced Form Component
 * 
 * Provides comprehensive form functionality with:
 * - Loading states and error handling
 * - Accessibility features
 * - Validation and feedback
 * - Progress tracking
 * - Auto-save functionality
 */

export const EnhancedForm = ({
  fields = [],
  onSubmit,
  validationSchema,
  defaultValues = {},
  title,
  description,
  submitText = "Submit",
  enableAutoSave = false,
  autoSaveDelay = 2000,
  showProgress = false,
  className = "",
  ...props
}) => {
  const formRef = useRef(null);
  const [showPasswords, setShowPasswords] = React.useState({});
  const [announcement, setAnnouncement] = React.useState('');
  
  const {
    register,
    handleSubmit,
    formState: { errors, isValid, isDirty },
    watch,
    setValue,
    reset
  } = useForm({
    defaultValues,
    mode: 'onChange'
  });

  const {
    isAnyLoading,
    startLoading,
    stopLoading,
    setLoadingError,
    updateProgress,
    executeWithLoading,
    getLoadingState
  } = useEnhancedLoading();

  const watchedValues = watch();

  // Auto-save functionality
  useEffect(() => {
    if (!enableAutoSave || !isDirty) return;

    const timeoutId = setTimeout(() => {
      handleAutoSave();
    }, autoSaveDelay);

    return () => clearTimeout(timeoutId);
  }, [watchedValues, enableAutoSave, autoSaveDelay, isDirty]);

  const handleAutoSave = async () => {
    try {
      await executeWithLoading('autosave', async () => {
        // Implement auto-save logic here
        console.log('Auto-saving form data:', watchedValues);
        setAnnouncement('Form auto-saved');
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  };

  const onFormSubmit = async (data) => {
    try {
      await executeWithLoading('submit', async (updateProgress) => {
        if (showProgress) {
          updateProgress(25, 'Validating data...');
          await new Promise(resolve => setTimeout(resolve, 500));
          
          updateProgress(50, 'Processing...');
          await new Promise(resolve => setTimeout(resolve, 500));
          
          updateProgress(75, 'Saving...');
        }
        
        const result = await onSubmit(data);
        
        if (showProgress) {
          updateProgress(100, 'Complete!');
        }
        
        setAnnouncement('Form submitted successfully');
        return result;
      }, {
        showToast: true,
        successMessage: 'Form submitted successfully!'
      });
    } catch (error) {
      setAnnouncement(`Form submission failed: ${error.message}`);
      throw error;
    }
  };

  const togglePasswordVisibility = (fieldName) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  const renderField = (field) => {
    const {
      name,
      label,
      type = 'text',
      placeholder,
      required = false,
      options = [],
      validation = {},
      helpText,
      ...fieldProps
    } = field;

    const error = errors[name];
    const hasError = !!error;

    const commonProps = {
      ...register(name, { required, ...validation }),
      label,
      placeholder,
      isRequired: required,
      isInvalid: hasError,
      errorMessage: error?.message,
      description: helpText,
      className: hasError ? 'error-field' : '',
      'aria-describedby': `${name}-help ${name}-error`,
      ...fieldProps
    };

    switch (type) {
      case 'textarea':
        return (
          <Textarea
            key={name}
            {...commonProps}
            minRows={3}
          />
        );

      case 'select':
        return (
          <Select
            key={name}
            {...commonProps}
            aria-label={label}
          >
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </Select>
        );

      case 'password':
        return (
          <Input
            key={name}
            {...commonProps}
            type={showPasswords[name] ? 'text' : 'password'}
            endContent={
              <button
                type="button"
                onClick={() => togglePasswordVisibility(name)}
                className="focus:outline-none"
                aria-label={showPasswords[name] ? 'Hide password' : 'Show password'}
              >
                {showPasswords[name] ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            }
          />
        );

      default:
        return (
          <Input
            key={name}
            {...commonProps}
            type={type}
          />
        );
    }
  };

  const submitLoadingState = getLoadingState('submit');
  const autoSaveLoadingState = getLoadingState('autosave');

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardBody className="p-6">
        <FormLoadingOverlay 
          isLoading={isAnyLoading} 
          message={submitLoadingState.progress?.message || 'Processing...'}
        >
          <form 
            ref={formRef}
            onSubmit={handleSubmit(onFormSubmit)}
            className="space-y-6"
            noValidate
            {...props}
          >
            {/* Form Header */}
            {(title || description) && (
              <div className="text-center mb-6">
                {title && (
                  <h2 className="text-2xl font-bold text-foreground mb-2">
                    {title}
                  </h2>
                )}
                {description && (
                  <p className="text-default-600">
                    {description}
                  </p>
                )}
              </div>
            )}

            {/* Progress Bar */}
            {showProgress && submitLoadingState.progress && (
              <div className="mb-6">
                <div className="flex justify-between text-sm text-default-600 mb-2">
                  <span>{submitLoadingState.progress.message}</span>
                  <span>{submitLoadingState.progress.value}%</span>
                </div>
                <div className="w-full bg-default-200 rounded-full h-2">
                  <motion.div
                    className="bg-primary h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${submitLoadingState.progress.value}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>
            )}

            {/* Form Fields */}
            <div className="space-y-4">
              {fields.map(renderField)}
            </div>

            {/* Form Actions */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <LoadingButton
                type="submit"
                color="primary"
                variant="solid"
                isLoading={submitLoadingState.isLoading}
                loadingText="Submitting..."
                disabled={!isValid}
                className="flex-1"
              >
                {submitText}
              </LoadingButton>

              <Button
                type="button"
                color="default"
                variant="bordered"
                onClick={() => reset()}
                disabled={isAnyLoading}
              >
                Reset
              </Button>
            </div>

            {/* Auto-save Indicator */}
            {enableAutoSave && (
              <div className="flex items-center justify-center gap-2 text-sm text-default-600">
                {autoSaveLoadingState.isLoading ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full"
                    />
                    <span>Auto-saving...</span>
                  </>
                ) : isDirty ? (
                  <>
                    <AlertCircle size={16} className="text-warning" />
                    <span>Unsaved changes</span>
                  </>
                ) : (
                  <>
                    <CheckCircle size={16} className="text-success" />
                    <span>All changes saved</span>
                  </>
                )}
              </div>
            )}

            {/* Error Summary */}
            <AnimatePresence>
              {Object.keys(errors).length > 0 && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="bg-danger-50 dark:bg-danger-900/20 border border-danger-200 rounded-lg p-4"
                >
                  <h3 className="font-semibold text-danger mb-2">
                    Please fix the following errors:
                  </h3>
                  <ul className="list-disc list-inside space-y-1 text-sm text-danger">
                    {Object.entries(errors).map(([field, error]) => (
                      <li key={field}>
                        {fields.find(f => f.name === field)?.label || field}: {error.message}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              )}
            </AnimatePresence>
          </form>
        </FormLoadingOverlay>

        {/* Screen Reader Announcements */}
        <ScreenReaderAnnouncement message={announcement} />
      </CardBody>
    </Card>
  );
};

export default EnhancedForm;
