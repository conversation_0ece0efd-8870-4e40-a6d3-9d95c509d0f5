import React from 'react';
import { Card, CardBody, Button, Chip, Progress } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Mission Card Component - Mission Display Component for Bento Grid
 * 
 * Features:
 * - Compact mission information display with story elements
 * - Progress tracking and difficulty indicators
 * - Reward preview and skill requirements
 * - Action buttons for different mission states
 * - Responsive design for bento grid layout
 */
const MissionCard = ({ 
  mission, 
  index = 0,
  type = 'available', // 'available', 'active', 'completed'
  onStart,
  onContinue,
  onReview,
  getDifficultyColor,
  className = "" 
}) => {
  
  // Get mission type icon
  const getMissionTypeIcon = (missionType) => {
    const icons = {
      'skill_development': '📚',
      'leadership': '👑',
      'development': '💻',
      'design': '🎨',
      'research': '🔬',
      'collaboration': '🤝'
    };
    return icons[missionType] || '⚔️';
  };

  // Get mission status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return 'success';
      case 'active': return 'warning';
      case 'completed': return 'primary';
      default: return 'default';
    }
  };

  // Calculate progress percentage
  const getProgressPercentage = () => {
    if (type === 'completed') return 100;
    if (type === 'available') return 0;
    return mission.progress || 0;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className={className}
    >
      <Card className="h-full hover:shadow-lg transition-shadow">
        <CardBody className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">
                {getMissionTypeIcon(mission.mission_type)}
              </div>
              <div>
                <h3 className="font-semibold text-lg line-clamp-1">{mission.title}</h3>
                <Chip 
                  color={getDifficultyColor(mission.difficulty_level)}
                  variant="flat"
                  size="sm"
                >
                  {mission.difficulty_level}
                </Chip>
              </div>
            </div>
            
            <Chip 
              color={getStatusColor(type)}
              variant="flat"
              size="sm"
            >
              {type}
            </Chip>
          </div>

          {/* Description */}
          <p className="text-gray-600 text-sm mb-4 line-clamp-3">
            {mission.description}
          </p>

          {/* Progress Bar (for active missions) */}
          {type === 'active' && (
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Progress</span>
                <span className="text-sm font-medium">{getProgressPercentage()}%</span>
              </div>
              <Progress 
                value={getProgressPercentage()} 
                color="primary"
                size="sm"
              />
            </div>
          )}

          {/* Mission Details */}
          <div className="space-y-2 mb-4">
            {mission.difficulty_points && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Points:</span>
                <span className="font-medium text-purple-600">
                  {mission.difficulty_points} ⭐
                </span>
              </div>
            )}
            
            {mission.estimated_hours && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Estimated Time:</span>
                <span className="font-medium">
                  {mission.estimated_hours}h
                </span>
              </div>
            )}
            
            {mission.deadline && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Deadline:</span>
                <span className="font-medium text-orange-600">
                  {new Date(mission.deadline).toLocaleDateString()}
                </span>
              </div>
            )}
          </div>

          {/* Rewards Preview */}
          {mission.mission_rewards && Object.keys(mission.mission_rewards).length > 0 && (
            <div className="mb-4">
              <div className="text-sm text-gray-600 mb-2">Rewards:</div>
              <div className="flex flex-wrap gap-1">
                {Object.entries(mission.mission_rewards).map(([key, value]) => (
                  <Chip key={key} variant="flat" size="sm" color="secondary">
                    {key}: {value}
                  </Chip>
                ))}
              </div>
            </div>
          )}

          {/* Requirements Preview */}
          {mission.mission_requirements && Object.keys(mission.mission_requirements).length > 0 && (
            <div className="mb-4">
              <div className="text-sm text-gray-600 mb-2">Requirements:</div>
              <div className="text-xs text-gray-500">
                {Object.keys(mission.mission_requirements).length} requirement(s)
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="mt-auto pt-4">
            {type === 'available' && onStart && (
              <Button
                onClick={onStart}
                color="primary"
                className="w-full"
                size="sm"
              >
                Start Mission
              </Button>
            )}
            
            {type === 'active' && onContinue && (
              <Button
                onClick={onContinue}
                color="warning"
                className="w-full"
                size="sm"
              >
                Continue Mission
              </Button>
            )}
            
            {type === 'completed' && onReview && (
              <Button
                onClick={onReview}
                color="primary"
                variant="flat"
                className="w-full"
                size="sm"
              >
                Review Mission
              </Button>
            )}
          </div>

          {/* Creator Info */}
          {mission.creator && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-xs">
                    {mission.creator.display_name?.charAt(0) || '?'}
                  </span>
                </div>
                <span className="text-xs text-gray-500">
                  by {mission.creator.display_name}
                </span>
              </div>
            </div>
          )}
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default MissionCard;
