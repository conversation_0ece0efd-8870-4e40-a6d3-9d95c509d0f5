import { describe, it, expect } from 'vitest';

describe('Simple Test', () => {
  it('should work', () => {
    expect(1 + 1).toBe(2);
  });

  it('should have document available', () => {
    expect(typeof document).toBe('object');
    expect(document).toBeDefined();
  });

  it('should have window available', () => {
    expect(typeof window).toBe('object');
    expect(window).toBeDefined();
  });
});
