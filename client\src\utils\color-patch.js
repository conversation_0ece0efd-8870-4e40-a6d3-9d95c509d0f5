/**
 * Color Package Patch
 * 
 * This module provides a compatibility layer for the color package
 * to fix the ES module import issue with HeroUI.
 * 
 * The issue: HeroUI tries to import color with `import Color from 'color'`
 * but the color package exports the function as the main export, not as default.
 */

// Import the color package - Vite will handle the CommonJS to ESM conversion
import * as colorPackage from 'color';

// The color package exports the Color function as the main export
// In CommonJS: module.exports = Color;
// In ESM conversion: { default: Color }
const Color = colorPackage.default || colorPackage;

// Export both named and default for maximum compatibility
export default Color;
export { Color };
