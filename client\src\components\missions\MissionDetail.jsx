import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Badge } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';

const MissionDetail = () => {
  const { id: missionId } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  
  const [mission, setMission] = useState(null);
  const [userMission, setUserMission] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (currentUser && missionId) {
      loadMission();
    }
  }, [currentUser, missionId]);

  const loadMission = async () => {
    try {
      setLoading(true);
      
      // Load mission details
      const missionResponse = await fetch(`/api/missions/${missionId}`, {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (missionResponse.ok) {
        const missionData = await missionResponse.json();
        setMission(missionData.data);
      }

      // Load user's progress on this mission
      const progressResponse = await fetch(`/api/missions/${missionId}/progress`, {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (progressResponse.ok) {
        const progressData = await progressResponse.json();
        setUserMission(progressData.data);
      }
      
    } catch (error) {
      console.error('Error loading mission:', error);
      toast.error('Failed to load mission');
      navigate('/missions');
    } finally {
      setLoading(false);
    }
  };

  const handleStartMission = async () => {
    try {
      const response = await fetch(`/api/missions/${missionId}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        toast.success('Mission started!');
        loadMission();
      } else {
        throw new Error('Failed to start mission');
      }
    } catch (error) {
      console.error('Error starting mission:', error);
      toast.error('Failed to start mission');
    }
  };

  const handleCompleteMission = async () => {
    try {
      const response = await fetch(`/api/missions/${missionId}/complete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        toast.success('Mission completed! 🎉');
        loadMission();
      } else {
        throw new Error('Failed to complete mission');
      }
    } catch (error) {
      console.error('Error completing mission:', error);
      toast.error('Failed to complete mission');
    }
  };

  const getMissionTypeIcon = (missionType) => {
    const icons = {
      'skill': '📚',
      'collaboration': '🤝',
      'achievement': '🏆',
      'exploration': '🔍',
      'social': '👥'
    };
    return icons[missionType] || '⚔️';
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'danger';
      case 'expert': return 'secondary';
      default: return 'default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'warning';
      case 'in_progress': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'danger';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading mission...</p>
        </div>
      </div>
    );
  }

  if (!mission) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-900 mb-4">Mission Not Found</h2>
          <p className="text-gray-600 mb-6">The mission you're looking for doesn't exist or is no longer available.</p>
          <Button onClick={() => navigate('/missions')} color="primary">
            Back to Mission Board
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div className="flex items-center space-x-4">
          <div className="text-4xl">
            {getMissionTypeIcon(mission.mission_type)}
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{mission.title}</h1>
            <div className="flex items-center space-x-3">
              <Chip 
                color={getDifficultyColor(mission.difficulty_level)}
                variant="flat"
              >
                {mission.difficulty_level}
              </Chip>
              {userMission && (
                <Badge 
                  color={getStatusColor(userMission.status)}
                  variant="flat"
                >
                  {userMission.status}
                </Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex gap-3 mt-4 md:mt-0">
          <Button
            variant="flat"
            onClick={() => navigate('/missions')}
          >
            Back to Missions
          </Button>
          
          {!userMission && (
            <Button
              onClick={handleStartMission}
              color="primary"
            >
              Start Mission
            </Button>
          )}
          
          {userMission && userMission.status === 'active' && (
            <Button
              onClick={handleCompleteMission}
              color="success"
            >
              Complete Mission
            </Button>
          )}
        </div>
      </div>

      {/* Mission Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Mission Description</h3>
            </CardHeader>
            <CardBody>
              <p className="text-gray-700 leading-relaxed">{mission.description}</p>
            </CardBody>
          </Card>

          {/* Requirements */}
          {mission.mission_requirements && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Requirements</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-3">
                  {Object.entries(mission.mission_requirements).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium capitalize">{key.replace('_', ' ')}</span>
                      <span className="text-gray-600">{JSON.stringify(value)}</span>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Progress */}
          {userMission && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Your Progress</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">Overall Progress</span>
                      <span className="text-sm font-medium">{userMission.progress?.percentage || 0}%</span>
                    </div>
                    <Progress 
                      value={userMission.progress?.percentage || 0} 
                      color="primary"
                    />
                  </div>
                  
                  {userMission.started_at && (
                    <div className="text-sm text-gray-600">
                      Started: {new Date(userMission.started_at).toLocaleDateString()}
                    </div>
                  )}
                  
                  {userMission.completed_at && (
                    <div className="text-sm text-green-600">
                      Completed: {new Date(userMission.completed_at).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Mission Info */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Mission Info</h3>
            </CardHeader>
            <CardBody>
              <div className="space-y-3">
                {mission.difficulty_points && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Points:</span>
                    <span className="font-medium text-purple-600">{mission.difficulty_points} ⭐</span>
                  </div>
                )}
                
                {mission.estimated_hours && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Estimated Time:</span>
                    <span className="font-medium">{mission.estimated_hours}h</span>
                  </div>
                )}
                
                {mission.deadline && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Deadline:</span>
                    <span className="font-medium text-orange-600">
                      {new Date(mission.deadline).toLocaleDateString()}
                    </span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium capitalize">{mission.mission_type}</span>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Rewards */}
          {mission.mission_rewards && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Rewards</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-2">
                  {Object.entries(mission.mission_rewards).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-gray-600 capitalize">{key.replace('_', ' ')}:</span>
                      <span className="font-medium">{value}</span>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Creator */}
          {mission.creator && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Created By</h3>
              </CardHeader>
              <CardBody>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium">
                      {mission.creator.display_name?.charAt(0) || '?'}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{mission.creator.display_name}</p>
                    <p className="text-sm text-gray-600">Mission Creator</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default MissionDetail;
