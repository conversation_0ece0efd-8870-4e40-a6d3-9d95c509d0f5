import React from 'react';
import { <PERSON>, CardBody, CardHeader } from '@heroui/react';

const StudioAnalytics = ({ studioId, statistics }) => {
  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">Studio Analytics</h3>
      </CardHeader>
      <CardBody>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{statistics?.totalMembers || 0}</div>
            <div className="text-sm text-blue-600">Total Members</div>
          </div>
          
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{statistics?.activeProjects || 0}</div>
            <div className="text-sm text-green-600">Active Projects</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">${statistics?.monthlyRevenue || 0}</div>
            <div className="text-sm text-purple-600">Monthly Revenue</div>
          </div>
          
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{statistics?.completedTasks || 0}</div>
            <div className="text-sm text-orange-600">Completed Tasks</div>
          </div>
        </div>
        
        <div className="mt-6 text-center text-gray-500">
          <p>Detailed analytics coming soon...</p>
        </div>
      </CardBody>
    </Card>
  );
};

export default StudioAnalytics;
