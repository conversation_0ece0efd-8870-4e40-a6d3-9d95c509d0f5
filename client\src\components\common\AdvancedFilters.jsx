import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  Input, 
  Select, 
  SelectItem, 
  Button, 
  Chip, 
  DatePicker,
  Slider,
  Switch
} from '@heroui/react';

/**
 * Advanced Filters Component
 * 
 * Provides comprehensive filtering and search capabilities for TrackCanvas and EarnCanvas.
 * Supports text search, date ranges, difficulty filters, status filters, and more.
 */

const AdvancedFilters = ({ 
  onFiltersChange, 
  filterType = 'contributions', // 'contributions' | 'earnings' | 'projects'
  className = "" 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    dateRange: {
      start: null,
      end: null
    },
    status: 'all',
    difficulty: [1, 5],
    projectType: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc',
    showOnlyMine: false
  });

  // Filter options based on filter type
  const getFilterOptions = () => {
    const baseOptions = {
      status: [
        { key: 'all', label: 'All Status' },
        { key: 'pending', label: 'Pending' },
        { key: 'approved', label: 'Approved' },
        { key: 'rejected', label: 'Rejected' }
      ],
      sortBy: [
        { key: 'created_at', label: 'Date Created' },
        { key: 'updated_at', label: 'Last Updated' }
      ]
    };

    if (filterType === 'contributions') {
      return {
        ...baseOptions,
        sortBy: [
          ...baseOptions.sortBy,
          { key: 'hours_tracked', label: 'Hours Tracked' },
          { key: 'difficulty_rating', label: 'Difficulty' }
        ]
      };
    }

    if (filterType === 'earnings') {
      return {
        ...baseOptions,
        status: [
          { key: 'all', label: 'All Status' },
          { key: 'pending', label: 'Pending' },
          { key: 'completed', label: 'Completed' },
          { key: 'failed', label: 'Failed' }
        ],
        sortBy: [
          ...baseOptions.sortBy,
          { key: 'amount', label: 'Amount' },
          { key: 'payment_date', label: 'Payment Date' }
        ]
      };
    }

    if (filterType === 'projects') {
      return {
        ...baseOptions,
        status: [
          { key: 'all', label: 'All Status' },
          { key: 'active', label: 'Active' },
          { key: 'completed', label: 'Completed' },
          { key: 'paused', label: 'Paused' }
        ],
        projectType: [
          { key: 'all', label: 'All Types' },
          { key: 'software', label: 'Software' },
          { key: 'game', label: 'Game' },
          { key: 'web', label: 'Web App' },
          { key: 'mobile', label: 'Mobile App' },
          { key: 'other', label: 'Other' }
        ]
      };
    }

    return baseOptions;
  };

  const options = getFilterOptions();

  // Update filters and notify parent
  const updateFilters = (newFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    const defaultFilters = {
      search: '',
      dateRange: { start: null, end: null },
      status: 'all',
      difficulty: [1, 5],
      projectType: 'all',
      sortBy: 'created_at',
      sortOrder: 'desc',
      showOnlyMine: false
    };
    setFilters(defaultFilters);
    onFiltersChange?.(defaultFilters);
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.status !== 'all') count++;
    if (filters.difficulty[0] !== 1 || filters.difficulty[1] !== 5) count++;
    if (filters.projectType !== 'all') count++;
    if (filters.showOnlyMine) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className={className}>
      {/* Filter Toggle Button */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="flat"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2"
        >
          <span>🔍</span>
          <span>Filters</span>
          {activeFilterCount > 0 && (
            <Chip size="sm" color="primary" variant="flat">
              {activeFilterCount}
            </Chip>
          )}
          <motion.span
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            ▼
          </motion.span>
        </Button>

        {activeFilterCount > 0 && (
          <Button
            size="sm"
            variant="light"
            onClick={clearFilters}
            className="text-danger"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Search Bar - Always Visible */}
      <div className="mb-4">
        <Input
          placeholder={`Search ${filterType}...`}
          value={filters.search}
          onChange={(e) => updateFilters({ search: e.target.value })}
          startContent={<span className="text-default-400">🔍</span>}
          isClearable
          onClear={() => updateFilters({ search: '' })}
        />
      </div>

      {/* Expanded Filters */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardBody className="p-6 space-y-6">
                {/* Date Range */}
                <div>
                  <h4 className="font-medium mb-3">Date Range</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <DatePicker
                      label="Start Date"
                      value={filters.dateRange.start}
                      onChange={(date) => updateFilters({ 
                        dateRange: { ...filters.dateRange, start: date }
                      })}
                    />
                    <DatePicker
                      label="End Date"
                      value={filters.dateRange.end}
                      onChange={(date) => updateFilters({ 
                        dateRange: { ...filters.dateRange, end: date }
                      })}
                    />
                  </div>
                </div>

                {/* Status and Type Filters */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Status</h4>
                    <Select
                      selectedKeys={[filters.status]}
                      onSelectionChange={(keys) => updateFilters({ status: Array.from(keys)[0] })}
                    >
                      {options.status.map((option) => (
                        <SelectItem key={option.key} value={option.key}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>

                  {filterType === 'projects' && (
                    <div>
                      <h4 className="font-medium mb-3">Project Type</h4>
                      <Select
                        selectedKeys={[filters.projectType]}
                        onSelectionChange={(keys) => updateFilters({ projectType: Array.from(keys)[0] })}
                      >
                        {options.projectType.map((option) => (
                          <SelectItem key={option.key} value={option.key}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </Select>
                    </div>
                  )}
                </div>

                {/* Difficulty Range (for contributions) */}
                {filterType === 'contributions' && (
                  <div>
                    <h4 className="font-medium mb-3">
                      Difficulty Range: {filters.difficulty[0]} - {filters.difficulty[1]}
                    </h4>
                    <Slider
                      step={1}
                      minValue={1}
                      maxValue={5}
                      value={filters.difficulty}
                      onChange={(value) => updateFilters({ difficulty: value })}
                      className="max-w-md"
                    />
                  </div>
                )}

                {/* Sorting */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Sort By</h4>
                    <Select
                      selectedKeys={[filters.sortBy]}
                      onSelectionChange={(keys) => updateFilters({ sortBy: Array.from(keys)[0] })}
                    >
                      {options.sortBy.map((option) => (
                        <SelectItem key={option.key} value={option.key}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">Sort Order</h4>
                    <Select
                      selectedKeys={[filters.sortOrder]}
                      onSelectionChange={(keys) => updateFilters({ sortOrder: Array.from(keys)[0] })}
                    >
                      <SelectItem key="desc" value="desc">Newest First</SelectItem>
                      <SelectItem key="asc" value="asc">Oldest First</SelectItem>
                    </Select>
                  </div>
                </div>

                {/* Additional Options */}
                <div>
                  <h4 className="font-medium mb-3">Additional Options</h4>
                  <div className="flex items-center gap-3">
                    <Switch
                      isSelected={filters.showOnlyMine}
                      onValueChange={(value) => updateFilters({ showOnlyMine: value })}
                    >
                      Show only my {filterType}
                    </Switch>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-4 flex flex-wrap gap-2"
        >
          {filters.search && (
            <Chip
              onClose={() => updateFilters({ search: '' })}
              variant="flat"
              color="primary"
            >
              Search: {filters.search}
            </Chip>
          )}
          {filters.status !== 'all' && (
            <Chip
              onClose={() => updateFilters({ status: 'all' })}
              variant="flat"
              color="secondary"
            >
              Status: {options.status.find(s => s.key === filters.status)?.label}
            </Chip>
          )}
          {filters.showOnlyMine && (
            <Chip
              onClose={() => updateFilters({ showOnlyMine: false })}
              variant="flat"
              color="success"
            >
              My {filterType} only
            </Chip>
          )}
        </motion.div>
      )}
    </div>
  );
};

export default AdvancedFilters;
