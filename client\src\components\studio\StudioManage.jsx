import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardBody, CardHeader, Button, Tabs, Tab } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import StudioMembers from './StudioMembers';
import BusinessModelConfig from './BusinessModelConfig';
import StudioTreasury from './StudioTreasury';

const StudioManage = () => {
  const { id: studioId } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  
  const [studio, setStudio] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('members');

  useEffect(() => {
    if (currentUser && studioId) {
      loadStudio();
    }
  }, [currentUser, studioId]);

  const loadStudio = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      const response = await fetch(`/api/studios/${studioId}`, {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStudio(data.data);
      } else {
        throw new Error('Failed to load studio');
      }
    } catch (error) {
      console.error('Error loading studio:', error);
      toast.error('Failed to load studio');
      navigate('/studios');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading studio management...</p>
        </div>
      </div>
    );
  }

  if (!studio) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-900 mb-4">Studio Not Found</h2>
          <p className="text-gray-600 mb-6">The studio you're looking for doesn't exist or you don't have access to it.</p>
          <Button onClick={() => navigate('/studios')} color="primary">
            Back to Studios
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Manage Studio</h1>
          <p className="text-gray-600">{studio.name}</p>
        </div>
        
        <div className="flex gap-3 mt-4 md:mt-0">
          <Button
            variant="flat"
            onClick={() => navigate(`/studios/${studioId}`)}
          >
            Back to Studio
          </Button>
        </div>
      </div>

      {/* Management Tabs */}
      <Card>
        <CardHeader>
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            className="w-full"
          >
            <Tab key="members" title="Members">
              <div className="p-6">
                <StudioMembers 
                  studioId={studioId}
                  members={studio.team_members || []}
                  onMembersUpdate={loadStudio}
                />
              </div>
            </Tab>
            
            <Tab key="settings" title="Settings">
              <div className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Studio Settings</h3>
                    <p className="text-gray-600">Studio settings management coming soon...</p>
                  </div>
                </div>
              </div>
            </Tab>
            
            <Tab key="business" title="Business Model">
              <div className="p-6">
                <BusinessModelConfig 
                  studioId={studioId}
                  onUpdate={loadStudio}
                />
              </div>
            </Tab>
            
            <Tab key="treasury" title="Treasury">
              <div className="p-6">
                <StudioTreasury 
                  studioId={studioId}
                />
              </div>
            </Tab>
          </Tabs>
        </CardHeader>
      </Card>
    </div>
  );
};

export default StudioManage;
