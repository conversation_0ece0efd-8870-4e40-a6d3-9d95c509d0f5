# Delete Applied Migrations Script
# This script deletes all migrations that have already been applied to the database

Write-Host "=== Deleting Applied Migrations ===" -ForegroundColor Cyan

# Define the directories
$migrationsDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations"
$archiveDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations_archive"
$backupDir = Join-Path -Path $PSScriptRoot -ChildPath "supabase\migrations_backup"

# Delete all migrations in the archive folder (these are old and already applied)
if (Test-Path $archiveDir) {
    Write-Host "Deleting archived migrations..." -ForegroundColor Yellow
    $archivedFiles = Get-ChildItem -Path $archiveDir -File
    foreach ($file in $archivedFiles) {
        Write-Host "  Deleting $($file.Name)" -ForegroundColor Gray
        Remove-Item -Path $file.FullName -Force
    }
    
    # Remove the empty directory
    if ((Get-ChildItem -Path $archiveDir).Count -eq 0) {
        Remove-Item -Path $archiveDir -Force
        Write-Host "  Removed empty archive directory" -ForegroundColor Gray
    }
}

# Delete all migrations in the backup folder (these are duplicates)
if (Test-Path $backupDir) {
    Write-Host "Deleting backup migrations..." -ForegroundColor Yellow
    $backupFiles = Get-ChildItem -Path $backupDir -File
    foreach ($file in $backupFiles) {
        Write-Host "  Deleting $($file.Name)" -ForegroundColor Gray
        Remove-Item -Path $file.FullName -Force
    }
    
    # Remove the empty directory
    if ((Get-ChildItem -Path $backupDir).Count -eq 0) {
        Remove-Item -Path $backupDir -Force
        Write-Host "  Removed empty backup directory" -ForegroundColor Gray
    }
}

# Delete all applied migrations in the main migrations folder
# Since all migrations have been applied except for potentially new ones,
# we'll delete all migrations except for the most recent ones (from June 2024)
Write-Host "Deleting applied migrations from main folder..." -ForegroundColor Yellow
$appliedMigrations = Get-ChildItem -Path $migrationsDir -File | Where-Object { 
    # Keep only the most recent migrations (June 2024 and newer)
    $_.Name -notlike "202406*" 
}

foreach ($file in $appliedMigrations) {
    Write-Host "  Deleting $($file.Name)" -ForegroundColor Gray
    Remove-Item -Path $file.FullName -Force
}

# List remaining migration files (if any)
$remainingMigrations = Get-ChildItem -Path $migrationsDir -File
if ($remainingMigrations.Count -gt 0) {
    Write-Host "`nRemaining migration files:" -ForegroundColor Green
    foreach ($file in $remainingMigrations) {
        Write-Host "- $($file.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "`nNo migration files remain." -ForegroundColor Green
}

Write-Host "`n=== Migration Cleanup Complete ===" -ForegroundColor Cyan
Write-Host "All applied migrations have been deleted." -ForegroundColor Cyan
