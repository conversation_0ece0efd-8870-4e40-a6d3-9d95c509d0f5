import { useContext, useEffect, useState } from "react";
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Navigate } from "react-router-dom";
import DirectSupabaseRoadmapTracker from "../../components/admin/DirectSupabaseRoadmapTracker";
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from "../../components/layout/LoadingAnimation";

const RoadmapPage = () => {
  const { currentUser } = useContext(UserContext);
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Check if the user is an admin in the Supabase database
        const { data, error } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (error) {
          console.error("Error checking admin status:", error);
          setIsAdmin(false);
        } else {
          setIsAdmin(data?.is_admin || false);
        }
      } catch (error) {
        console.error("Error checking admin status:", error);
        setIsAdmin(false);
      } finally {
        setLoading(false);
      }
    };

    checkAdminStatus();
  }, [currentUser]);

  if (loading) {
    return <LoadingAnimation />;
  }

  // If not logged in, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  // If not an admin, show access denied
  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Access Denied</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>You do not have permission to access this page. This area is restricted to administrators only.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If admin, show the roadmap tracker
  return (
    <div className="container mx-auto px-4 py-8">
      <DirectSupabaseRoadmapTracker />
    </div>
  );
};

export default RoadmapPage;
