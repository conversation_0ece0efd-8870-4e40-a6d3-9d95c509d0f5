import axios from "axios";
import { useState, useEffect, useContext } from "react";
import { usePara<PERSON>, Link } from "react-router-dom";
import moment from "moment";
import LoadingAnimation from "../../components/layout/LoadingAnimation";
import { UserContext } from "../../../contexts/user.context";

// GET ID FROM URL PARAMS IN REACT AND PASS IT IN TO THE AXIOS URL

const UserProfile = () => {
  const { id } = useParams();
  const [user, setUser] = useState(null);
  const [canEdit, setCanEdit] = useState(false);
  const [contributions, setContributions] = useState(null);
  const { currentUser } = useContext(UserContext);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        // Get Firebase auth token
        const token = await currentUser.getIdToken();
        const { data } = await axios.get(`/user/${id}`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        const { user, loggedInUser } = data;

        setUser(user);
        if (loggedInUser.isAdmin || loggedInUser._id.toString() === user._id) {
          setCanEdit(true);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setUser("error"); // Set user to "error" to handle it in the UI
      }
    };

    // USE MONGODB USER INSTEAD TO DETERMIN IF IS ADMIN AND DISPLAY EDIT. THEN PROTECT EDIT ROUTES TOO

    fetchUser();
  }, [id]); // Add `id` to the dependency array to refetch if it changes

  // Separate useEffect to handle contributions when user changes
  useEffect(() => {
    if (user && user.contributions) {
      setContributions(user.contributions);
    }
  }, [user]); // Update contributions whenever the user changes

  return (
    <>
      {user ? (
        user === "error" ? (
          <div className="alert alert-danger text-center" role="alert">
            User not found
          </div>
        ) : (
          <div className="container mt-5">
            <div className="card shadow-lg border-0 rounded-lg wide-card">
              <div className="card-body">
                <h3 className="card-title text-center mb-4">User Profile</h3>
                <h1 className="text-center mb-4">{user.displayName}</h1>
                <div className="text-start">
                  <p className="mb-1">
                    <strong>ID:</strong> {user._id}
                  </p>
                  <p className="mb-1">
                    <strong>Email:</strong> {user.email}
                  </p>
                  <p className="mb-1">
                    <strong>Date Created:</strong>{" "}
                    {moment(user.dateCreated).format("l")}
                  </p>
                  {user.hoursWorked > 0 && (
                    <p className="mb-1">
                      <strong>Hours Worked:</strong> {user.hoursWorked}
                    </p>
                  )}
                  {canEdit && (
                    <div className="mt-3 mb-4">
                      <Link to={`/user/${id}/edit`}>Edit Profile</Link>
                    </div>
                  )}
                </div>

                <div className="mt-4">
                  {contributions && contributions.length > 0 ? (
                    <>
                      <h3 className="text-center mb-4">Contributions</h3>

                      <div className="table-responsive">
                        <table className="table table-striped table-bordered align-middle">
                          <thead className="table-light">
                            <tr>
                              <th className="text-center text-md-start">
                                Date Added
                              </th>
                              <th className="text-center text-md-start">
                                Contribution ID
                              </th>
                              <th className="text-center text-md-start">
                                Hours
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {contributions.map((contribution) => (
                              <tr key={contribution._id}>
                                <td className="text-center text-md-start">
                                  {moment(contribution.dateCreated).format("l")}
                                </td>
                                <td className="text-center text-md-start">
                                  <Link
                                    to={`/contribution/${contribution._id}`}
                                    className="link-primary d-block text-truncate truncate-id flex-grow-1"
                                  >
                                    {contribution._id}
                                  </Link>
                                </td>
                                <td className="text-center text-md-start">
                                  {contribution.hours}
                                </td>
                              </tr>
                            ))}
                            <tr>
                              <td
                                colSpan="2"
                                className="text-center text-md-end"
                              >
                                Total Contribution
                              </td>
                              <td className="text-center text-md-start fw-bold">
                                {contributions.reduce(
                                  (total, contribution) =>
                                    total + contribution.hours,
                                  0
                                )}
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </>
                  ) : (
                    <p className="text-center">No contributions yet</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )
      ) : (
        <LoadingAnimation />
      )}
    </>
  );
};

export default UserProfile;
