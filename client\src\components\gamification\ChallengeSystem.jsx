import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Progress, Chip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';

/**
 * ChallengeSystem Component
 * 
 * Enhanced challenge system following wireframe specifications
 * Features active challenges, time limits, reward preview, and custom challenges
 */
const ChallengeSystem = ({ 
  activeChallenges = [],
  onCreateChallenge,
  onViewAllChallenges,
  className = ""
}) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newChallenge, setNewChallenge] = useState({
    title: '',
    description: '',
    reward: '',
    timeLimit: ''
  });

  // Default active challenges
  const defaultChallenges = [
    {
      id: 1,
      type: 'weekly',
      icon: '🚀',
      title: 'Complete 5 Tasks',
      description: 'Complete 5 tasks this week',
      progress: 4,
      target: 5,
      reward: '100 ORBs + Badge',
      timeLeft: '2 days, 14 hours',
      timeLeftMs: 2 * 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000,
      color: 'primary'
    },
    {
      id: 2,
      type: 'monthly',
      icon: '🤝',
      title: 'Collaborate with 3 New Allies',
      description: 'Work with 3 different people you haven\'t collaborated with before',
      progress: 2,
      target: 3,
      reward: '250 ORBs + Title',
      timeLeft: '18 days',
      timeLeftMs: 18 * 24 * 60 * 60 * 1000,
      color: 'success'
    },
    {
      id: 3,
      type: 'skill',
      icon: '🎨',
      title: 'Master TypeScript',
      description: 'Complete TypeScript skill challenges and projects',
      progress: 80,
      target: 100,
      reward: '500 ORBs + Certification',
      timeLeft: 'No time limit',
      timeLeftMs: null,
      color: 'warning'
    }
  ];

  const challenges = activeChallenges.length > 0 ? activeChallenges : defaultChallenges;

  // Format time remaining
  const formatTimeLeft = (timeLeftMs) => {
    if (!timeLeftMs) return 'No time limit';
    
    const days = Math.floor(timeLeftMs / (24 * 60 * 60 * 1000));
    const hours = Math.floor((timeLeftMs % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
    
    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}${hours > 0 ? `, ${hours} hour${hours > 1 ? 's' : ''}` : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return 'Less than 1 hour';
    }
  };

  // Get challenge type color
  const getChallengeTypeColor = (type) => {
    const colors = {
      'weekly': 'primary',
      'monthly': 'success',
      'skill': 'warning',
      'custom': 'secondary'
    };
    return colors[type] || 'default';
  };

  // Handle challenge creation
  const handleCreateChallenge = () => {
    if (newChallenge.title.trim()) {
      if (onCreateChallenge) {
        onCreateChallenge(newChallenge);
      }
      setNewChallenge({ title: '', description: '', reward: '', timeLimit: '' });
      setShowCreateModal(false);
    }
  };

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-cyan-900/20 dark:to-blue-800/20 border-2 border-cyan-200 dark:border-cyan-700 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🎯</span>
              <h3 className="text-lg font-semibold text-cyan-800 dark:text-cyan-200">Active Challenges</h3>
            </div>
            <Chip color="primary" variant="flat" size="sm">
              {challenges.length} Active
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Active Challenges */}
          <div className="space-y-4 mb-4">
            {challenges.map((challenge, idx) => (
              <motion.div
                key={challenge.id}
                className="p-3 bg-white/50 dark:bg-black/20 rounded-lg border border-default-200"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: idx * 0.1 }}
              >
                {/* Challenge Header */}
                <div className="flex items-start gap-3 mb-3">
                  <span className="text-2xl">{challenge.icon}</span>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-sm">{challenge.title}</h4>
                      <Chip 
                        size="sm" 
                        color={getChallengeTypeColor(challenge.type)}
                        variant="flat"
                      >
                        {challenge.type}
                      </Chip>
                    </div>
                    <p className="text-xs text-default-600">{challenge.description}</p>
                  </div>
                </div>

                {/* Progress */}
                <div className="mb-3">
                  <div className="flex justify-between text-xs mb-1">
                    <span>Progress</span>
                    <span>
                      {challenge.progress}/{challenge.target}
                      {challenge.target === 100 ? '%' : ''}
                    </span>
                  </div>
                  <Progress 
                    value={(challenge.progress / challenge.target) * 100} 
                    color={challenge.color}
                    className="w-full"
                  />
                </div>

                {/* Reward and Time */}
                <div className="flex justify-between items-center text-xs">
                  <div>
                    <span className="text-default-500">Reward: </span>
                    <span className="font-medium text-success">{challenge.reward}</span>
                  </div>
                  <div>
                    <span className="text-default-500">Time Left: </span>
                    <span className={`font-medium ${
                      challenge.timeLeftMs && challenge.timeLeftMs < 24 * 60 * 60 * 1000 
                        ? 'text-danger' 
                        : 'text-primary'
                    }`}>
                      {formatTimeLeft(challenge.timeLeftMs)}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}

            {challenges.length === 0 && (
              <div className="text-center py-6 text-default-500">
                <div className="text-4xl mb-2">🎯</div>
                <div className="text-sm">No active challenges</div>
                <div className="text-xs mt-1">Create a custom challenge to get started!</div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 gap-2">
            <Button 
              color="primary" 
              variant="flat" 
              size="sm"
              onPress={() => onViewAllChallenges && onViewAllChallenges()}
            >
              🏆 View All Challenges
            </Button>
            <Button 
              color="secondary" 
              variant="flat" 
              size="sm"
              onPress={() => setShowCreateModal(true)}
            >
              ➕ Create Custom Challenge
            </Button>
          </div>

          {/* Challenge Tips */}
          <div className="mt-4 p-2 bg-cyan-50 dark:bg-cyan-900/20 border border-cyan-200 rounded-lg">
            <div className="text-xs text-center">
              <div className="font-semibold text-cyan-700 dark:text-cyan-300 mb-1">
                💡 Challenge Tips
              </div>
              <div className="text-cyan-600 dark:text-cyan-400">
                Complete challenges to earn bonus ORBs and unlock achievements!
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Create Challenge Modal */}
      <Modal 
        isOpen={showCreateModal} 
        onClose={() => setShowCreateModal(false)}
        size="md"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">🎯 Create Custom Challenge</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Challenge Title
                </label>
                <input
                  type="text"
                  value={newChallenge.title}
                  onChange={(e) => setNewChallenge({...newChallenge, title: e.target.value})}
                  className="w-full p-3 border border-default-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="e.g., Complete 10 code reviews"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Description
                </label>
                <textarea
                  value={newChallenge.description}
                  onChange={(e) => setNewChallenge({...newChallenge, description: e.target.value})}
                  className="w-full p-3 border border-default-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  rows="3"
                  placeholder="Describe what you want to achieve..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Reward
                  </label>
                  <input
                    type="text"
                    value={newChallenge.reward}
                    onChange={(e) => setNewChallenge({...newChallenge, reward: e.target.value})}
                    className="w-full p-3 border border-default-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="e.g., 100 ORBs"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Time Limit
                  </label>
                  <select
                    value={newChallenge.timeLimit}
                    onChange={(e) => setNewChallenge({...newChallenge, timeLimit: e.target.value})}
                    className="w-full p-3 border border-default-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">No limit</option>
                    <option value="1_day">1 Day</option>
                    <option value="1_week">1 Week</option>
                    <option value="1_month">1 Month</option>
                    <option value="3_months">3 Months</option>
                  </select>
                </div>
              </div>

              <div className="bg-primary-50 border border-primary-200 rounded-lg p-3">
                <div className="text-sm text-primary-700">
                  <strong>💡 Challenge Tips:</strong>
                  <ul className="mt-1 space-y-1 text-xs">
                    <li>• Make challenges specific and measurable</li>
                    <li>• Set realistic but motivating goals</li>
                    <li>• Consider adding time pressure for urgency</li>
                  </ul>
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={() => setShowCreateModal(false)}
            >
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleCreateChallenge}
              disabled={!newChallenge.title.trim()}
            >
              🎯 Create Challenge
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ChallengeSystem;
