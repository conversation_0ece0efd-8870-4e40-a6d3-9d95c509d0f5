import { useState, useEffect, useCallback, useContext } from "react";
import axios from "axios";
import { toast } from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { UserContext } from "../../../contexts/user.context";
import ContributorSelect from "../../components/form/ContributorSelect";
import ProjectSelect from "../../components/form/ProjectSelect";
import DeleteButton from "../../components/form/DeleteButton";

const ContributionForm = ({ create }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { currentUser } = useContext(UserContext); // Use context instead of local auth state

  const [data, setData] = useState({ contributor: "", hours: "", project: "" });
  const [loading, setLoading] = useState(true);

  // Fetch Contribution Data
  const fetchContribution = useCallback(async () => {
    if (create || !currentUser) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const token = await currentUser.getIdToken();
      const response = await axios.get(`/contribution/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      setData({
        contributor: response.data.contribution.contributor._id,
        hours: response.data.contribution.hours,
        project: response.data.contribution.project,
      });
    } catch (error) {
      console.error("Error fetching contribution:", error);
      toast.error(error.response?.data?.error || "Failed to load data.");
    } finally {
      setLoading(false); // Ensure loading is false after fetch
    }
  }, [id, create, currentUser]);

  useEffect(() => {
    fetchContribution();
  }, [fetchContribution]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!currentUser) {
      toast.error("Authentication error. Please log in again.");
      return;
    }

    try {
      const token = await currentUser.getIdToken();
      const endpoint = create
        ? "/contribution/new"
        : `/contribution/${id}/edit`;
      const method = create ? "post" : "patch";

      await axios[method](endpoint, data, {
        headers: { Authorization: `Bearer ${token}` },
      });

      toast.success(
        `Contribution ${create ? "added" : "updated"} successfully!`
      );
      navigate(create ? "/contribution/index" : `/contribution/${id}`);
    } catch (error) {
      console.error("Error submitting contribution:", error);
      toast.error(error.response?.data?.error || "Failed to submit.");
    }
  };

  if (loading) return null;

  return (
    <div className="mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6">
          <div className="card shadow-sm border-0 rounded-lg">
            <div className="card-body p-4">
              <h3 className="text-center">
                {create ? "Add a Contribution" : "Update Contribution"}
              </h3>
              {!create && <p>{id}</p>}
              <form onSubmit={handleSubmit} className="text-start">
                <div className="mb-3">
                  <label htmlFor="contributor" className="form-label">
                    Contributor
                  </label>
                  <ContributorSelect data={data} setData={setData} />
                </div>
                <div className="mb-3">
                  <label htmlFor="hours" className="form-label">
                    Hours
                  </label>
                  <input
                    id="hours"
                    type="number"
                    min="0"
                    className="form-control"
                    value={data.hours}
                    onChange={(e) =>
                      setData({ ...data, hours: e.target.value })
                    }
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="project" className="form-label">
                    Project
                  </label>
                  <ProjectSelect data={data} setData={setData} />
                </div>
                <div className="d-flex justify-content-between mt-4">
                  {!create && (
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={() => navigate(`/contribution/${id}`)}
                    >
                      Cancel
                    </button>
                  )}
                  <button type="submit" className="btn btn-primary">
                    {create ? "Add" : "Update"}
                  </button>
                </div>
                {!create && <DeleteButton id={id} item="contribution" />}
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContributionForm;
