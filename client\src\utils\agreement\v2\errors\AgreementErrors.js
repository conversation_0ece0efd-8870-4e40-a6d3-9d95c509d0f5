/**
 * Agreement Generation Error Classes
 * 
 * Comprehensive error handling for the agreement generation system.
 * Provides specific error types for different failure scenarios.
 */

/**
 * Base class for all agreement generation errors
 */
export class AgreementError extends Error {
  constructor(message, details = []) {
    super(message);
    this.name = this.constructor.name;
    this.details = details;
    this.timestamp = new Date().toISOString();
    
    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Validation errors - input data or output validation failures
 */
export class ValidationError extends AgreementError {
  constructor(message, details = []) {
    super(message, details);
    this.severity = 'CRITICAL';
  }
}

/**
 * Template errors - template loading or processing failures
 */
export class TemplateError extends AgreementError {
  constructor(message, templateType = null, details = []) {
    super(message, details);
    this.templateType = templateType;
    this.severity = 'HIGH';
  }
}

/**
 * Replacement errors - variable replacement failures
 */
export class ReplacementError extends AgreementError {
  constructor(message, variable = null, details = []) {
    super(message, details);
    this.variable = variable;
    this.severity = 'CRITICAL';
  }
}

/**
 * Accuracy errors - generated agreement doesn't meet accuracy requirements
 */
export class AccuracyError extends AgreementError {
  constructor(message, accuracyScore = 0, requiredScore = 95, details = []) {
    super(message, details);
    this.accuracyScore = accuracyScore;
    this.requiredScore = requiredScore;
    this.severity = 'CRITICAL';
  }
}

/**
 * Configuration errors - system configuration issues
 */
export class ConfigurationError extends AgreementError {
  constructor(message, configKey = null, details = []) {
    super(message, details);
    this.configKey = configKey;
    this.severity = 'HIGH';
  }
}

/**
 * Legal compliance errors - legal requirements not met
 */
export class LegalComplianceError extends AgreementError {
  constructor(message, complianceIssues = [], details = []) {
    super(message, details);
    this.complianceIssues = complianceIssues;
    this.severity = 'CRITICAL';
  }
}

/**
 * Error factory for creating appropriate error types
 */
export class ErrorFactory {
  static createValidationError(field, message, value = null) {
    return new ValidationError(`Validation failed for ${field}: ${message}`, [
      { field, message, value, type: 'VALIDATION_FAILED' }
    ]);
  }

  static createMissingDataError(field) {
    return new ValidationError(`Required field missing: ${field}`, [
      { field, message: 'This field is required', type: 'MISSING_REQUIRED_FIELD' }
    ]);
  }

  static createTemplateNotFoundError(templateType) {
    return new TemplateError(`Template not found: ${templateType}`, templateType, [
      { templateType, message: 'Template file not found or not accessible', type: 'TEMPLATE_NOT_FOUND' }
    ]);
  }

  static createUnreplacedVariableError(variable) {
    return new ReplacementError(`Unreplaced variable found: ${variable}`, variable, [
      { variable, message: 'Variable placeholder was not replaced with actual data', type: 'UNREPLACED_VARIABLE' }
    ]);
  }

  static createAccuracyError(actualScore, requiredScore, issues = []) {
    return new AccuracyError(
      `Agreement accuracy ${actualScore}% below required ${requiredScore}%`,
      actualScore,
      requiredScore,
      issues
    );
  }

  static createLegalComplianceError(issues) {
    return new LegalComplianceError(
      `Legal compliance issues found: ${issues.length} issue(s)`,
      issues,
      issues.map(issue => ({ ...issue, type: 'LEGAL_COMPLIANCE_ISSUE' }))
    );
  }
}

/**
 * Error handler utility for consistent error processing
 */
export class ErrorHandler {
  static handleError(error, context = {}) {
    const errorInfo = {
      ...error.toJSON(),
      context,
      handled: true,
      handledAt: new Date().toISOString()
    };

    // Log error based on severity
    if (error.severity === 'CRITICAL') {
      console.error('[CRITICAL ERROR]', errorInfo);
    } else if (error.severity === 'HIGH') {
      console.warn('[HIGH PRIORITY ERROR]', errorInfo);
    } else {
      console.log('[ERROR]', errorInfo);
    }

    return errorInfo;
  }

  static formatErrorForUser(error) {
    const userFriendlyMessages = {
      ValidationError: 'Please check your input data and correct any errors.',
      TemplateError: 'There was an issue with the agreement template. Please try again.',
      ReplacementError: 'There was an issue processing your information. Please verify all fields are complete.',
      AccuracyError: 'The generated agreement did not meet quality standards. Please contact support.',
      LegalComplianceError: 'The agreement could not be generated due to legal compliance issues.',
      ConfigurationError: 'There is a system configuration issue. Please contact support.'
    };

    return {
      message: userFriendlyMessages[error.name] || 'An unexpected error occurred. Please try again.',
      details: error.details.filter(detail => !detail.sensitive),
      errorId: error.timestamp,
      canRetry: !['AccuracyError', 'LegalComplianceError', 'ConfigurationError'].includes(error.name)
    };
  }

  static isRetryableError(error) {
    const retryableErrors = ['TemplateError', 'ReplacementError'];
    return retryableErrors.includes(error.name);
  }

  static getCriticalErrors(errors) {
    return errors.filter(error => error.severity === 'CRITICAL');
  }

  static getErrorSummary(errors) {
    const summary = {
      total: errors.length,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      byType: {}
    };

    errors.forEach(error => {
      // Count by severity
      if (error.severity === 'CRITICAL') summary.critical++;
      else if (error.severity === 'HIGH') summary.high++;
      else if (error.severity === 'MEDIUM') summary.medium++;
      else summary.low++;

      // Count by type
      summary.byType[error.name] = (summary.byType[error.name] || 0) + 1;
    });

    return summary;
  }
}

/**
 * Error recovery utilities
 */
export class ErrorRecovery {
  static canRecover(error) {
    const recoverableErrors = ['TemplateError', 'ReplacementError'];
    return recoverableErrors.includes(error.name);
  }

  static getRecoveryStrategy(error) {
    const strategies = {
      TemplateError: 'Try loading a different template or check template availability',
      ReplacementError: 'Verify all required data is provided and retry',
      ValidationError: 'Correct the validation errors and resubmit',
      AccuracyError: 'Review system configuration and template accuracy',
      LegalComplianceError: 'Contact legal team for compliance review',
      ConfigurationError: 'Check system configuration and contact administrator'
    };

    return strategies[error.name] || 'Contact support for assistance';
  }

  static createRecoveryPlan(errors) {
    const plan = {
      canRecover: false,
      steps: [],
      estimatedTime: 0,
      requiresSupport: false
    };

    const criticalErrors = ErrorHandler.getCriticalErrors(errors);
    
    if (criticalErrors.length === 0) {
      plan.canRecover = true;
      plan.steps = errors.map(error => ({
        error: error.name,
        strategy: this.getRecoveryStrategy(error),
        priority: error.severity
      }));
    } else {
      plan.requiresSupport = true;
      plan.steps = [{
        error: 'CRITICAL_ERRORS_FOUND',
        strategy: 'Critical errors require immediate attention - contact support',
        priority: 'CRITICAL'
      }];
    }

    return plan;
  }
}
