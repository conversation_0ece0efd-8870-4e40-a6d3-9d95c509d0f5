import React from 'react';
import { usePerformanceMonitor, withMemo } from '../../utils/performance/componentOptimization';

const withPerformanceMonitoring = (Component) => {
  const PerformanceMonitoredComponent = (props) => {
    usePerformanceMonitor(Component.displayName || Component.name);
    return <Component {...props} />;
  };
  
  PerformanceMonitoredComponent.displayName = `PerformanceMonitored(${Component.displayName || Component.name})`;
  return PerformanceMonitoredComponent;
};

export default withPerformanceMonitoring;