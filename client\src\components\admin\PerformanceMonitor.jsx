import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Tabs, Tab, Badge, Progress, Button } from '@heroui/react';
import { motion } from 'framer-motion';
import { usePerformanceMonitor } from '../../utils/performance/PerformanceOptimizer';
import { useQueryOptimization } from '../../utils/database/QueryOptimizer';
import { supabase } from '../../utils/supabase/supabase.utils';
import {
  startDatabaseMonitoring,
  getCurrentHealth,
  getHealthTrends,
  exportHealthData
} from '../../utils/database/DatabaseHealthMonitor';
import {
  getOptimizationRecommendations,
  trackQueryPerformance
} from '../../utils/database/DatabaseOptimizer';

/**
 * Performance Monitoring Dashboard
 * 
 * Comprehensive performance monitoring interface providing:
 * - Real-time performance metrics and analytics
 * - Memory usage monitoring and optimization
 * - Database query performance tracking
 * - Bundle size and loading performance
 * - Cache efficiency and optimization recommendations
 * - System health monitoring and alerts
 */
const PerformanceMonitor = ({ className = "" }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [systemHealth, setSystemHealth] = useState({
    status: 'healthy',
    score: 95,
    issues: []
  });
  const [databaseHealth, setDatabaseHealth] = useState({
    status: 'unknown',
    checks: {},
    trends: [],
    recommendations: []
  });

  // Performance monitoring hooks
  const performanceStats = usePerformanceMonitor();
  const queryOptimization = useQueryOptimization(supabase);

  // Initialize database monitoring
  useEffect(() => {
    // Start database health monitoring
    startDatabaseMonitoring(supabase);

    // Load initial database health
    loadDatabaseHealth();

    // Set up periodic database health updates
    const dbHealthInterval = setInterval(loadDatabaseHealth, 30000); // Every 30 seconds

    return () => {
      clearInterval(dbHealthInterval);
    };
  }, []);

  // Load database health data
  const loadDatabaseHealth = async () => {
    try {
      const health = getCurrentHealth();
      const trends = getHealthTrends();
      const recommendations = getOptimizationRecommendations();

      setDatabaseHealth({
        status: health?.overallStatus || 'unknown',
        checks: health?.checks || {},
        trends: trends || [],
        recommendations: recommendations || []
      });
    } catch (error) {
      console.error('Failed to load database health:', error);
      setDatabaseHealth(prev => ({ ...prev, status: 'error' }));
    }
  };

  // Load system health data
  useEffect(() => {
    const checkSystemHealth = () => {
      const issues = [];
      let score = 100;

      // Check memory usage
      if (performanceStats?.memory?.length > 0) {
        const latestMemory = performanceStats.memory[performanceStats.memory.length - 1];
        const memoryUsage = (latestMemory.usedJSHeapSize / latestMemory.jsHeapSizeLimit) * 100;
        
        if (memoryUsage > 80) {
          issues.push({
            type: 'memory',
            severity: 'high',
            message: `High memory usage: ${memoryUsage.toFixed(1)}%`
          });
          score -= 20;
        } else if (memoryUsage > 60) {
          issues.push({
            type: 'memory',
            severity: 'medium',
            message: `Moderate memory usage: ${memoryUsage.toFixed(1)}%`
          });
          score -= 10;
        }
      }

      // Check cache efficiency
      if (performanceStats?.cache) {
        const cacheHitRate = performanceStats.cache.totalItems > 0 ? 
          ((performanceStats.cache.totalItems - performanceStats.cache.expiredItems) / performanceStats.cache.totalItems) * 100 : 100;
        
        if (cacheHitRate < 70) {
          issues.push({
            type: 'cache',
            severity: 'medium',
            message: `Low cache hit rate: ${cacheHitRate.toFixed(1)}%`
          });
          score -= 15;
        }
      }

      // Check query performance
      if (queryOptimization?.recommendations?.length > 0) {
        const highPriorityIssues = queryOptimization.recommendations.filter(r => r.priority === 'high');
        if (highPriorityIssues.length > 0) {
          issues.push({
            type: 'database',
            severity: 'high',
            message: `${highPriorityIssues.length} high-priority database optimization needed`
          });
          score -= 25;
        }
      }

      // Check database health
      if (databaseHealth.status === 'error') {
        issues.push({
          type: 'database-health',
          severity: 'high',
          message: 'Database health monitoring detected errors'
        });
        score -= 30;
      } else if (databaseHealth.status === 'unhealthy') {
        issues.push({
          type: 'database-health',
          severity: 'medium',
          message: 'Database health issues detected'
        });
        score -= 15;
      }

      // Check database optimization recommendations
      if (databaseHealth.recommendations?.length > 0) {
        const criticalRecs = databaseHealth.recommendations.filter(r => r.priority === 'high');
        if (criticalRecs.length > 0) {
          issues.push({
            type: 'database-optimization',
            severity: 'medium',
            message: `${criticalRecs.length} database optimization recommendations`
          });
          score -= 10;
        }
      }

      const status = score >= 90 ? 'healthy' : score >= 70 ? 'warning' : 'critical';
      
      setSystemHealth({ status, score: Math.max(0, score), issues });
    };

    checkSystemHealth();
    const interval = setInterval(checkSystemHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [performanceStats, queryOptimization, databaseHealth]);

  // Format bytes
  const formatBytes = (bytes) => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return 'success';
      case 'warning': return 'warning';
      case 'critical': return 'danger';
      default: return 'default';
    }
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high': return 'danger';
      case 'medium': return 'warning';
      case 'low': return 'primary';
      default: return 'default';
    }
  };

  return (
    <div className={`performance-monitor ${className}`}>
      {/* Header */}
      <Card className="mb-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">📊</span>
              <div>
                <h1 className="text-2xl font-bold">Performance Monitor</h1>
                <p className="text-default-600">
                  Real-time system performance and optimization insights
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Badge
                color={getStatusColor(systemHealth.status)}
                variant="flat"
                size="lg"
              >
                {systemHealth.status.toUpperCase()}
              </Badge>
              <div className="text-right">
                <div className="text-2xl font-bold">{systemHealth.score}</div>
                <div className="text-sm text-default-600">Health Score</div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-primary mb-1">
                {performanceStats?.cache?.totalItems || 0}
              </div>
              <div className="text-sm text-default-600">Cache Items</div>
              <div className="text-xs text-default-500 mt-1">
                {formatBytes(performanceStats?.cache?.totalMemory || 0)}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-success mb-1">
                {performanceStats?.memory?.length > 0 ? 
                  formatBytes(performanceStats.memory[performanceStats.memory.length - 1]?.usedJSHeapSize) : 
                  '0 MB'
                }
              </div>
              <div className="text-sm text-default-600">Memory Usage</div>
              <div className="text-xs text-default-500 mt-1">
                {performanceStats?.memory?.length > 0 ? 
                  `${((performanceStats.memory[performanceStats.memory.length - 1]?.usedJSHeapSize / 
                      performanceStats.memory[performanceStats.memory.length - 1]?.jsHeapSizeLimit) * 100).toFixed(1)}%` : 
                  '0%'
                }
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-warning mb-1">
                {Object.keys(queryOptimization?.stats || {}).length}
              </div>
              <div className="text-sm text-default-600">Query Types</div>
              <div className="text-xs text-default-500 mt-1">Monitored</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-secondary mb-1">
                {systemHealth.issues.length}
              </div>
              <div className="text-sm text-default-600">Active Issues</div>
              <div className="text-xs text-default-500 mt-1">
                {systemHealth.issues.filter(i => i.severity === 'high').length} critical
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-primary mb-1">
                {databaseHealth.recommendations?.length || 0}
              </div>
              <div className="text-sm text-default-600">DB Health</div>
              <div className="text-xs text-default-500 mt-1">
                <Badge
                  color={
                    databaseHealth.status === 'healthy' ? 'success' :
                    databaseHealth.status === 'unhealthy' ? 'warning' : 'danger'
                  }
                  variant="flat"
                  size="sm"
                >
                  {databaseHealth.status}
                </Badge>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Monitoring Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            <Tab
              key="overview"
              title={
                <div className="flex items-center space-x-2">
                  <span>📊</span>
                  <span>Overview</span>
                </div>
              }
            >
              <div className="p-6">
                {/* System Health */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-4">System Health</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>Overall Health Score</span>
                      <div className="flex items-center gap-2">
                        <Progress
                          value={systemHealth.score}
                          color={getStatusColor(systemHealth.status)}
                          className="w-32"
                        />
                        <span className="font-semibold">{systemHealth.score}/100</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Active Issues */}
                {systemHealth.issues.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-4">Active Issues</h3>
                    <div className="space-y-2">
                      {systemHealth.issues.map((issue, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <Badge
                              color={getSeverityColor(issue.severity)}
                              variant="flat"
                              size="sm"
                            >
                              {issue.severity}
                            </Badge>
                            <span>{issue.message}</span>
                          </div>
                          <Badge color="default" variant="flat" size="sm">
                            {issue.type}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button
                      variant="flat"
                      color="primary"
                      onClick={() => {
                        if (performanceStats?.cache) {
                          performanceStats.cache.clear();
                        }
                      }}
                    >
                      Clear Cache
                    </Button>
                    <Button
                      variant="flat"
                      color="secondary"
                      onClick={() => {
                        if (queryOptimization?.clearStats) {
                          queryOptimization.clearStats();
                        }
                      }}
                    >
                      Reset Query Stats
                    </Button>
                    <Button
                      variant="flat"
                      color="success"
                      onClick={() => {
                        if (typeof window !== 'undefined' && window.gc) {
                          window.gc();
                        }
                      }}
                    >
                      Force GC
                    </Button>
                  </div>
                </div>
              </div>
            </Tab>

            <Tab
              key="memory"
              title={
                <div className="flex items-center space-x-2">
                  <span>🧠</span>
                  <span>Memory</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Memory Usage</h3>
                
                {performanceStats?.memory?.length > 0 ? (
                  <div className="space-y-4">
                    {performanceStats.memory.slice(-5).map((stat, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <div className="text-sm text-default-600">Used Heap</div>
                            <div className="font-semibold">{formatBytes(stat.usedJSHeapSize)}</div>
                          </div>
                          <div>
                            <div className="text-sm text-default-600">Total Heap</div>
                            <div className="font-semibold">{formatBytes(stat.totalJSHeapSize)}</div>
                          </div>
                          <div>
                            <div className="text-sm text-default-600">Heap Limit</div>
                            <div className="font-semibold">{formatBytes(stat.jsHeapSizeLimit)}</div>
                          </div>
                        </div>
                        <div className="mt-3">
                          <Progress
                            value={(stat.usedJSHeapSize / stat.jsHeapSizeLimit) * 100}
                            color={
                              (stat.usedJSHeapSize / stat.jsHeapSizeLimit) > 0.8 ? 'danger' :
                              (stat.usedJSHeapSize / stat.jsHeapSizeLimit) > 0.6 ? 'warning' : 'success'
                            }
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <span className="text-4xl mb-4 block">🧠</span>
                    <h4 className="text-lg font-semibold mb-2">No Memory Data</h4>
                    <p className="text-default-600">Memory monitoring is not available in this environment</p>
                  </div>
                )}
              </div>
            </Tab>

            <Tab
              key="database"
              title={
                <div className="flex items-center space-x-2">
                  <span>🗄️</span>
                  <span>Database</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Database Performance</h3>

                {Object.keys(queryOptimization?.stats || {}).length > 0 ? (
                  <div className="space-y-4">
                    {Object.entries(queryOptimization.stats).map(([key, stat]) => (
                      <div key={key} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{key}</h4>
                          <Badge
                            color={stat.avgDuration > 500 ? 'danger' : stat.avgDuration > 200 ? 'warning' : 'success'}
                            variant="flat"
                          >
                            {stat.avgDuration.toFixed(2)}ms avg
                          </Badge>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <div className="text-default-600">Count</div>
                            <div className="font-semibold">{stat.count}</div>
                          </div>
                          <div>
                            <div className="text-default-600">Min Duration</div>
                            <div className="font-semibold">{stat.minDuration.toFixed(2)}ms</div>
                          </div>
                          <div>
                            <div className="text-default-600">Max Duration</div>
                            <div className="font-semibold">{stat.maxDuration.toFixed(2)}ms</div>
                          </div>
                          <div>
                            <div className="text-default-600">Total Results</div>
                            <div className="font-semibold">{stat.totalResults}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <span className="text-4xl mb-4 block">🗄️</span>
                    <h4 className="text-lg font-semibold mb-2">No Query Data</h4>
                    <p className="text-default-600">No database queries have been monitored yet</p>
                  </div>
                )}

                {/* Optimization Recommendations */}
                {queryOptimization?.recommendations?.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold mb-3">Optimization Recommendations</h4>
                    <div className="space-y-2">
                      {queryOptimization.recommendations.map((rec, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium">{rec.description}</div>
                              <div className="text-sm text-default-600">{rec.suggestion}</div>
                            </div>
                            <Badge
                              color={getSeverityColor(rec.priority)}
                              variant="flat"
                            >
                              {rec.priority}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Tab>

            <Tab
              key="database-health"
              title={
                <div className="flex items-center space-x-2">
                  <span>💊</span>
                  <span>DB Health</span>
                </div>
              }
            >
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Database Health Monitoring</h3>

                {/* Health Status Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold mb-2">
                      <Badge
                        color={
                          databaseHealth.status === 'healthy' ? 'success' :
                          databaseHealth.status === 'unhealthy' ? 'warning' : 'danger'
                        }
                        variant="flat"
                        size="lg"
                      >
                        {databaseHealth.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-default-600">Overall Status</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-primary">
                      {Object.keys(databaseHealth.checks || {}).length}
                    </div>
                    <div className="text-sm text-default-600">Health Checks</div>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-warning">
                      {databaseHealth.recommendations?.length || 0}
                    </div>
                    <div className="text-sm text-default-600">Recommendations</div>
                  </div>
                </div>

                {/* Health Checks */}
                {Object.keys(databaseHealth.checks || {}).length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-semibold mb-3">Health Checks</h4>
                    <div className="space-y-2">
                      {Object.entries(databaseHealth.checks).map(([checkName, result]) => (
                        <div key={checkName} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <span className="capitalize">{checkName.replace(/([A-Z])/g, ' $1').trim()}</span>
                          </div>
                          <Badge
                            color={result?.status === 'healthy' ? 'success' : result?.status === 'warning' ? 'warning' : 'danger'}
                            variant="flat"
                            size="sm"
                          >
                            {result?.status || 'unknown'}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Database Optimization Recommendations */}
                {databaseHealth.recommendations?.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-semibold mb-3">Optimization Recommendations</h4>
                    <div className="space-y-3">
                      {databaseHealth.recommendations.map((rec, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">{rec.title || rec.description}</div>
                            <Badge
                              color={rec.priority === 'high' ? 'danger' : rec.priority === 'medium' ? 'warning' : 'primary'}
                              variant="flat"
                            >
                              {rec.priority}
                            </Badge>
                          </div>
                          <div className="text-sm text-default-600">
                            {rec.description || rec.suggestion}
                          </div>
                          {rec.impact && (
                            <div className="text-xs text-default-500 mt-1">
                              Impact: {rec.impact}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Health Trends */}
                {databaseHealth.trends?.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3">Health Trends</h4>
                    <div className="space-y-2">
                      {databaseHealth.trends.slice(-5).map((trend, index) => (
                        <div key={index} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div className="text-sm">
                              {new Date(trend.timestamp).toLocaleString()}
                            </div>
                            <Badge
                              color={trend.status === 'healthy' ? 'success' : trend.status === 'warning' ? 'warning' : 'danger'}
                              variant="flat"
                              size="sm"
                            >
                              {trend.status}
                            </Badge>
                          </div>
                          {trend.message && (
                            <div className="text-xs text-default-600 mt-1">
                              {trend.message}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Export Health Data */}
                <div className="mt-6 pt-6 border-t">
                  <Button
                    color="secondary"
                    variant="flat"
                    onClick={() => {
                      try {
                        const healthData = exportHealthData();
                        const blob = new Blob([JSON.stringify(healthData, null, 2)], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `database-health-${new Date().toISOString().split('T')[0]}.json`;
                        a.click();
                        URL.revokeObjectURL(url);
                      } catch (error) {
                        console.error('Failed to export health data:', error);
                      }
                    }}
                  >
                    Export Health Data
                  </Button>
                </div>
              </div>
            </Tab>
          </Tabs>
        </CardBody>
      </Card>
    </div>
  );
};

export default PerformanceMonitor;
