import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/react';

/**
 * Feature Highlights Component - Everything You Need to Succeed
 * 
 * Features:
 * - Six key feature categories with detailed benefits
 * - Grid layout with consistent visual design
 * - Clear value propositions for each feature set
 * - Professional presentation of platform capabilities
 */
const FeatureHighlights = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1
      }
    }
  };

  const featureVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: "easeOut" }
    }
  };

  // Feature categories data
  const features = [
    {
      id: 'studios',
      icon: '🏰',
      title: 'STUDIOS & PROJECTS',
      description: 'Complete team and project management',
      benefits: [
        'Team formation',
        'Project creation',
        'Mission planning',
        'Member roles',
        'Collaboration tools'
      ],
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50',
      darkBgGradient: 'from-blue-900/20 to-cyan-900/20'
    },
    {
      id: 'financial',
      icon: '💰',
      title: 'FINANCIAL & PAYMENTS',
      description: 'Transparent revenue and payment systems',
      benefits: [
        'CoG revenue model',
        'Escrow management',
        'Multiple payment methods',
        'Automated calculations',
        'Financial reporting'
      ],
      gradient: 'from-green-500 to-emerald-500',
      bgGradient: 'from-green-50 to-emerald-50',
      darkBgGradient: 'from-green-900/20 to-emerald-900/20'
    },
    {
      id: 'skills',
      icon: '🎓',
      title: 'SKILL & NETWORKING',
      description: 'Professional growth and connections',
      benefits: [
        '6-level vetting',
        'Skill verification',
        'Professional networking',
        'Learning hub',
        'Mentorship',
        'Career growth'
      ],
      gradient: 'from-purple-500 to-pink-500',
      bgGradient: 'from-purple-50 to-pink-50',
      darkBgGradient: 'from-purple-900/20 to-pink-900/20'
    },
    {
      id: 'bounty',
      icon: '🎯',
      title: 'BOUNTY & MISSIONS',
      description: 'Competitive marketplace for talent',
      benefits: [
        'Public bounties',
        'Mission tracking',
        'Skill matching',
        'Quality gates',
        'Competitive marketplace'
      ],
      gradient: 'from-orange-500 to-red-500',
      bgGradient: 'from-orange-50 to-red-50',
      darkBgGradient: 'from-orange-900/20 to-red-900/20'
    },
    {
      id: 'analytics',
      icon: '📊',
      title: 'ANALYTICS & REPORTING',
      description: 'Data-driven insights and performance',
      benefits: [
        'Real-time dashboards',
        'Performance metrics',
        'Financial insights',
        'Growth tracking'
      ],
      gradient: 'from-indigo-500 to-purple-500',
      bgGradient: 'from-indigo-50 to-purple-50',
      darkBgGradient: 'from-indigo-900/20 to-purple-900/20'
    },
    {
      id: 'security',
      icon: '🔒',
      title: 'SECURITY & COMPLIANCE',
      description: 'Enterprise-grade protection',
      benefits: [
        'End-to-end encryption',
        'GDPR compliance',
        'Audit trails',
        'Multi-factor authentication',
        'Secure payments'
      ],
      gradient: 'from-gray-500 to-slate-600',
      bgGradient: 'from-gray-50 to-slate-50',
      darkBgGradient: 'from-gray-900/20 to-slate-900/20'
    }
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 py-20">
      <motion.div
        className="max-w-7xl mx-auto px-6"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Section Header */}
        <motion.div variants={featureVariants} className="text-center mb-16">
          <h2 className="text-5xl md:text-6xl font-bold text-white mb-6">
            🛠️ Everything You Need to Succeed
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            From team formation to revenue distribution, Royaltea provides all the tools 
            you need to build and scale your creative business with complete transparency.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              variants={featureVariants}
              whileHover={{ scale: 1.02, y: -5 }}
              transition={{ duration: 0.3 }}
            >
              <Card className={`h-full bg-gradient-to-br ${feature.bgGradient} dark:${feature.darkBgGradient} border-2 border-white/10 shadow-xl hover:shadow-2xl transition-all duration-300`}>
                <CardBody className="p-8">
                  {/* Header */}
                  <div className="text-center mb-6">
                    <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${feature.gradient} text-white text-3xl mb-4`}>
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {feature.description}
                    </p>
                  </div>

                  {/* Benefits List */}
                  <div className="space-y-3">
                    {feature.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-center text-gray-700 dark:text-gray-300">
                        <span className="text-green-500 mr-3 text-sm">•</span>
                        <span className="text-sm">{benefit}</span>
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section - Platform Benefits */}
        <motion.div variants={featureVariants} className="mt-20">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Built for Scale, Designed for Fairness
            </h3>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto">
              Whether you're a solo creator or managing a 100-person team, 
              Royaltea scales with your business while maintaining complete transparency.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-6 bg-white/5 rounded-lg backdrop-blur-sm">
              <div className="text-4xl mb-3">⚡</div>
              <div className="text-2xl font-bold text-white mb-2">5 Min</div>
              <div className="text-gray-300 text-sm">Setup Time</div>
            </div>
            <div className="text-center p-6 bg-white/5 rounded-lg backdrop-blur-sm">
              <div className="text-4xl mb-3">🌍</div>
              <div className="text-2xl font-bold text-white mb-2">Global</div>
              <div className="text-gray-300 text-sm">Remote Teams</div>
            </div>
            <div className="text-center p-6 bg-white/5 rounded-lg backdrop-blur-sm">
              <div className="text-4xl mb-3">🔄</div>
              <div className="text-2xl font-bold text-white mb-2">Real-time</div>
              <div className="text-gray-300 text-sm">Sync & Updates</div>
            </div>
            <div className="text-center p-6 bg-white/5 rounded-lg backdrop-blur-sm">
              <div className="text-4xl mb-3">📈</div>
              <div className="text-2xl font-bold text-white mb-2">Unlimited</div>
              <div className="text-gray-300 text-sm">Growth Potential</div>
            </div>
          </div>
        </motion.div>

        {/* Background Decorations */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {/* Floating Elements */}
          {[...Array(15)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-3 h-3 bg-white/10 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0.1, 0.6, 0.1]
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default FeatureHighlights;
