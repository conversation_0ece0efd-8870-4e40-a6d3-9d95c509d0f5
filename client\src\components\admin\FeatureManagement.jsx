import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Button, Badge, Switch, Input } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Feature Management Component
 * 
 * Comprehensive feature flag and A/B testing interface providing:
 * - Feature flag management and deployment
 * - A/B test configuration and monitoring
 * - User segment targeting
 * - Performance impact analysis
 * - Rollback and emergency controls
 * - Feature usage analytics
 */
const FeatureManagement = ({ currentUser, onDataRefresh }) => {
  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadFeatures();
  }, []);

  const loadFeatures = async () => {
    try {
      setLoading(true);
      
      // Mock feature flags data
      setFeatures([
        {
          id: '1',
          name: 'Enhanced Analytics Dashboard',
          key: 'enhanced_analytics',
          enabled: true,
          rollout: 100,
          description: 'New analytics dashboard with real-time features'
        },
        {
          id: '2',
          name: 'AI-Powered Matching',
          key: 'ai_matching',
          enabled: false,
          rollout: 0,
          description: 'AI-based project and freelancer matching'
        },
        {
          id: '3',
          name: 'Video Calls Integration',
          key: 'video_calls',
          enabled: true,
          rollout: 50,
          description: 'Built-in video calling for client meetings'
        }
      ]);
    } catch (error) {
      console.error('Error loading features:', error);
      toast.error('Failed to load feature flags');
    } finally {
      setLoading(false);
    }
  };

  const toggleFeature = async (featureId, enabled) => {
    try {
      setFeatures(prev => prev.map(feature => 
        feature.id === featureId ? { ...feature, enabled } : feature
      ));
      
      toast.success(`Feature ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('Error toggling feature:', error);
      toast.error('Failed to update feature flag');
    }
  };

  return (
    <div className="feature-management">
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div>
              <h3 className="text-lg font-semibold">Feature Management</h3>
              <p className="text-sm text-default-600">
                Manage feature flags and A/B testing
              </p>
            </div>
            <Button color="primary" variant="flat">
              Add Feature Flag
            </Button>
          </div>
        </CardHeader>
      </Card>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Feature Flags</h3>
        </CardHeader>
        <CardBody>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table aria-label="Feature flags table">
              <TableHeader>
                <TableColumn>FEATURE</TableColumn>
                <TableColumn>STATUS</TableColumn>
                <TableColumn>ROLLOUT</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {features.map((feature) => (
                  <TableRow key={feature.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{feature.name}</div>
                        <div className="text-sm text-default-600">{feature.description}</div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <Badge color={feature.enabled ? 'success' : 'default'} variant="flat">
                        {feature.enabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      <div className="text-sm">{feature.rollout}%</div>
                    </TableCell>
                    
                    <TableCell>
                      <Switch
                        isSelected={feature.enabled}
                        onValueChange={(enabled) => toggleFeature(feature.id, enabled)}
                        size="sm"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default FeatureManagement;
