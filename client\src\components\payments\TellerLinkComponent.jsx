// TellerLinkComponent - Bank account linking interface
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Modal, 
  ModalContent, 
  ModalHeader, 
  ModalBody, 
  ModalFooter,
  Button,
  Card,
  CardBody,
  Chip,
  Divider
} from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Building2, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  CreditCard,
  Lock,
  Zap,
  Info
} from 'lucide-react';

const TellerLinkComponent = ({ isOpen, onClose, onSuccess }) => {
  const { currentUser } = useContext(UserContext);
  const [linkToken, setLinkToken] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState('intro'); // 'intro', 'linking', 'success', 'error'
  const [selectedBank, setSelectedBank] = useState(null);
  const [error, setError] = useState(null);

  // Mock bank list for demonstration
  const popularBanks = [
    { id: 'chase', name: 'Chase Bank', logo: '🏛️', supported: true },
    { id: 'bofa', name: 'Bank of America', logo: '🏛️', supported: true },
    { id: 'wells', name: 'Wells Fargo', logo: '🏛️', supported: true },
    { id: 'citi', name: 'Citibank', logo: '🏛️', supported: true },
    { id: 'usbank', name: 'U.S. Bank', logo: '🏛️', supported: true },
    { id: 'pnc', name: 'PNC Bank', logo: '🏛️', supported: true }
  ];

  useEffect(() => {
    if (isOpen && currentUser) {
      createLinkToken();
    }
  }, [isOpen, currentUser]);

  const createLinkToken = async () => {
    try {
      setIsLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (!authToken) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/.netlify/functions/teller-link/create-link-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          products: ['auth', 'transactions', 'transfer'],
          country_codes: ['US'],
          language: 'en'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create link token');
      }

      const data = await response.json();
      setLinkToken(data.link_token);
      
    } catch (error) {
      console.error('Error creating link token:', error);
      setError(error.message);
      setStep('error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBankSelection = async (bank) => {
    setSelectedBank(bank);
    setStep('linking');
    setIsLoading(true);

    try {
      // Simulate bank connection process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock successful connection
      const mockPublicToken = `public-sandbox-${Date.now()}`;
      await exchangePublicToken(mockPublicToken);
      
    } catch (error) {
      console.error('Error connecting bank:', error);
      setError(error.message);
      setStep('error');
    } finally {
      setIsLoading(false);
    }
  };

  const exchangePublicToken = async (publicToken) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      const response = await fetch('/.netlify/functions/teller-link/exchange-token', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          public_token: publicToken
        })
      });

      if (!response.ok) {
        throw new Error('Failed to exchange token');
      }

      const data = await response.json();
      
      setStep('success');
      
      // Call success callback with account data
      if (onSuccess) {
        onSuccess({
          access_token: data.access_token,
          item_id: data.item_id,
          accounts: data.accounts,
          bank: selectedBank
        });
      }
      
    } catch (error) {
      console.error('Error exchanging token:', error);
      setError(error.message);
      setStep('error');
    }
  };

  const handleClose = () => {
    setStep('intro');
    setSelectedBank(null);
    setError(null);
    setLinkToken(null);
    onClose();
  };

  const renderIntroStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Shield className="text-blue-600" size={32} />
        </div>
        <h3 className="text-xl font-semibold mb-2">Connect Your Bank Account</h3>
        <p className="text-gray-600">
          Securely link your bank account to receive payments and manage transfers
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Lock className="text-green-500" size={20} />
            <div>
              <h4 className="font-medium text-sm">Bank-Level Security</h4>
              <p className="text-xs text-gray-600">Certificate-based auth</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <Zap className="text-blue-500" size={20} />
            <div>
              <h4 className="font-medium text-sm">Instant Verification</h4>
              <p className="text-xs text-gray-600">Real-time connection</p>
            </div>
          </div>
        </Card>
      </div>

      <div className="space-y-3">
        <h4 className="font-medium">Select Your Bank</h4>
        <div className="grid grid-cols-2 gap-3">
          {popularBanks.map(bank => (
            <Button
              key={bank.id}
              variant="bordered"
              className="h-auto p-4 justify-start"
              onPress={() => handleBankSelection(bank)}
              isDisabled={!bank.supported}
            >
              <div className="flex items-center gap-3">
                <span className="text-2xl">{bank.logo}</span>
                <div className="text-left">
                  <p className="font-medium text-sm">{bank.name}</p>
                  {bank.supported ? (
                    <p className="text-xs text-green-600">Supported</p>
                  ) : (
                    <p className="text-xs text-gray-400">Coming Soon</p>
                  )}
                </div>
              </div>
            </Button>
          ))}
        </div>
        
        <Button
          variant="light"
          className="w-full"
          startContent={<Building2 size={16} />}
        >
          Search for your bank...
        </Button>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <Info className="text-blue-500 flex-shrink-0 mt-0.5" size={16} />
          <div className="text-sm">
            <p className="font-medium text-blue-900">Powered by Teller</p>
            <p className="text-blue-700">
              Your credentials are encrypted and never stored on our servers. 
              Teller provides superior developer experience with certificate-based security.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderLinkingStep = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        >
          <CreditCard className="text-blue-600" size={32} />
        </motion.div>
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-2">Connecting to {selectedBank?.name}</h3>
        <p className="text-gray-600">
          Please wait while we securely connect your account...
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex items-center justify-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
        <p className="text-sm text-gray-500">Verifying account details...</p>
      </div>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="text-green-600" size={32} />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-2">Account Connected Successfully!</h3>
        <p className="text-gray-600">
          Your {selectedBank?.name} account has been securely linked to your Royaltea account.
        </p>
      </div>

      <Card className="bg-green-50 border-green-200">
        <CardBody className="p-4">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{selectedBank?.logo}</span>
            <div className="text-left">
              <p className="font-medium">{selectedBank?.name}</p>
              <p className="text-sm text-gray-600">Checking Account ••••0000</p>
            </div>
            <Chip size="sm" color="success" variant="flat" className="ml-auto">
              Verified
            </Chip>
          </div>
        </CardBody>
      </Card>

      <div className="text-sm text-gray-600">
        <p>You can now:</p>
        <ul className="list-disc list-inside mt-2 space-y-1">
          <li>Receive payments directly to your bank account</li>
          <li>Transfer funds with low fees</li>
          <li>View real-time transaction history</li>
        </ul>
      </div>
    </div>
  );

  const renderErrorStep = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
        <AlertCircle className="text-red-600" size={32} />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold mb-2">Connection Failed</h3>
        <p className="text-gray-600 mb-4">
          We couldn't connect your bank account. Please try again.
        </p>
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-700">{error}</p>
          </div>
        )}
      </div>

      <Button
        color="primary"
        onPress={() => {
          setStep('intro');
          setError(null);
          createLinkToken();
        }}
      >
        Try Again
      </Button>
    </div>
  );

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={handleClose}
      size="2xl"
      scrollBehavior="inside"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <CreditCard size={20} className="text-primary" />
            <span>Add Payment Method</span>
          </div>
        </ModalHeader>
        
        <ModalBody className="py-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={step}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {step === 'intro' && renderIntroStep()}
              {step === 'linking' && renderLinkingStep()}
              {step === 'success' && renderSuccessStep()}
              {step === 'error' && renderErrorStep()}
            </motion.div>
          </AnimatePresence>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={handleClose}
            isDisabled={isLoading}
          >
            {step === 'success' ? 'Done' : 'Cancel'}
          </Button>
          {step === 'success' && (
            <Button 
              color="primary" 
              onPress={handleClose}
            >
              Continue
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default TellerLinkComponent;
