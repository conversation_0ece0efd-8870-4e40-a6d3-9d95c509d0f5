.wizard-navigation-container {
  margin-bottom: 2rem;
}

/* Desktop Wizard Navigation */
.wizard-navigation {
  margin-bottom: 1rem;
  background-color: hsl(var(--card));
  border-radius: 0.75rem;
  padding: 1.5rem 1rem 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.wizard-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.wizard-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  z-index: 2;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.wizard-step.clickable {
  cursor: pointer;
}

.wizard-step:hover.clickable {
  background-color: hsl(var(--accent));
  opacity: 1;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.step-title {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  text-align: center;
  transition: all 0.3s ease;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.step-connector {
  position: absolute;
  top: 20px;
  right: calc(-50% + 20px);
  width: 100%;
  height: 2px;
  background-color: hsl(var(--border));
  z-index: 0; /* Lower z-index so it appears behind the step numbers */
  transition: all 0.3s ease;
}

.step-connector.completed {
  background-color: hsl(var(--primary));
}

.wizard-step.active {
  opacity: 1;
}

.wizard-step.active .step-number {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  transform: scale(1.1);
  box-shadow: 0 4px 6px hsl(var(--primary) / 0.3);
}

.wizard-step.active .step-title {
  color: hsl(var(--foreground));
  font-weight: 600;
}

.wizard-step.completed {
  opacity: 1;
}

.wizard-step.completed .step-number {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.wizard-step.completed .step-title {
  color: hsl(var(--foreground));
}

.wizard-progress {
  height: 6px;
  background-color: hsl(var(--muted));
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: hsl(var(--primary));
  transition: width 0.5s ease;
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  from { background-position: 1rem 0; }
  to { background-position: 0 0; }
}

/* Step Tooltip */
.step-tooltip {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  padding: 0.75rem;
  border-radius: 0.5rem;
  width: 200px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.step-tooltip::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: transparent transparent hsl(var(--popover)) transparent;
}

.wizard-step:hover .step-tooltip {
  opacity: 1;
  visibility: visible;
  top: calc(100% + 10px);
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.tooltip-description {
  font-size: 0.75rem;
  opacity: 0.9;
}

/* Step Description */
.step-description-container {
  display: flex;
  align-items: center;
  background-color: hsl(var(--muted));
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  margin-top: 1rem;
  border-left: 4px solid hsl(var(--primary));
}

.step-description-icon {
  margin-right: 0.75rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-description-text {
  font-size: 0.875rem;
  color: hsl(var(--foreground));
}

/* Dark mode adjustments for step description */
.dark .step-description-container {
  background-color: hsl(var(--muted));
  border-left-color: hsl(var(--primary));
}

.dark .step-description-icon {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.dark .step-description-text {
  color: hsl(var(--foreground));
}

/* Mobile Step Indicator */
.mobile-step-indicator {
  display: none;
  background-color: hsl(var(--card));
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.current-step-info {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.step-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.step-details {
  flex: 1;
}

.step-number-mobile {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
}

.step-title-mobile {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.mobile-progress-bar {
  height: 4px;
  background-color: hsl(var(--muted));
  border-radius: 2px;
  overflow: hidden;
}

.mobile-progress {
  height: 100%;
  background-color: hsl(var(--primary));
  transition: width 0.3s ease;
}

/* Responsive styles */
@media (max-width: 992px) {
  .wizard-step .step-title {
    font-size: 0.75rem;
    max-width: 80px;
  }
}

@media (max-width: 768px) {
  .wizard-navigation {
    display: none;
  }

  .mobile-step-indicator {
    display: block;
  }

  .step-description-container {
    margin-top: 0;
  }
}

@media (max-width: 576px) {
  .wizard-steps {
    overflow-x: auto;
    padding-bottom: 1rem;
    justify-content: flex-start;
  }

  .wizard-step {
    flex: 0 0 auto;
    margin-right: 1.5rem;
  }

  .step-connector {
    width: 1.5rem;
    right: -1.5rem;
  }

  .step-title {
    max-width: 70px;
    font-size: 0.75rem;
  }

  .step-description-text {
    font-size: 0.8125rem;
  }
}

/* Dark mode adjustments - using .dark class for shadcn/ui compatibility */
.dark .wizard-navigation,
.dark .mobile-step-indicator {
  background-color: hsl(var(--card));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .step-number {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
}

.dark .step-title {
  color: hsl(var(--muted-foreground));
}

.dark .step-connector {
  background-color: hsl(var(--border));
}

.dark .wizard-step.active .step-number {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.dark .wizard-step.active .step-title {
  color: hsl(var(--foreground));
}

.dark .wizard-step.completed .step-number {
  background-color: hsl(var(--primary));
}

.dark .wizard-step.completed .step-title {
  color: hsl(var(--foreground));
}

.dark .wizard-progress {
  background-color: hsl(var(--muted));
}

.dark .step-description-container {
  background-color: hsl(var(--muted));
  border-left-color: hsl(var(--primary));
}

.dark .step-description-text {
  color: hsl(var(--muted-foreground));
}

.dark .wizard-step:hover.clickable {
  background-color: hsl(var(--accent));
}
