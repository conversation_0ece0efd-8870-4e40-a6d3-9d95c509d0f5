// EscrowManager - Escrow account management interface
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardBody, 
  Button, 
  Badge, 
  Chip, 
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Input,
  Textarea,
  Select,
  SelectItem
} from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Lock, 
  Plus, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Eye,
  Settings,
  Shield
} from 'lucide-react';

const EscrowManager = ({ onRefresh, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [escrowAccounts, setEscrowAccounts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedEscrow, setSelectedEscrow] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Create escrow form state
  const [createForm, setCreateForm] = useState({
    escrow_name: '',
    total_amount: '',
    project_id: '',
    auto_release_date: '',
    release_conditions: '',
    requires_manual_approval: true
  });

  useEffect(() => {
    if (currentUser) {
      loadEscrowAccounts();
    }
  }, [currentUser]);

  const loadEscrowAccounts = async () => {
    try {
      setIsLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      if (!authToken) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/.netlify/functions/escrow-management', {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load escrow accounts');
      }

      const data = await response.json();
      setEscrowAccounts(data.escrows || []);
      
    } catch (error) {
      console.error('Error loading escrow accounts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createEscrowAccount = async () => {
    try {
      setIsLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      const response = await fetch('/.netlify/functions/escrow-management', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...createForm,
          release_conditions: createForm.release_conditions ? 
            JSON.parse(createForm.release_conditions) : {}
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create escrow account');
      }

      const data = await response.json();
      
      setShowCreateModal(false);
      setCreateForm({
        escrow_name: '',
        total_amount: '',
        project_id: '',
        auto_release_date: '',
        release_conditions: '',
        requires_manual_approval: true
      });
      
      await loadEscrowAccounts();
      if (onRefresh) onRefresh();
      
    } catch (error) {
      console.error('Error creating escrow account:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const viewEscrowDetails = async (escrowId) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;

      const response = await fetch(`/.netlify/functions/escrow-management/${escrowId}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load escrow details');
      }

      const data = await response.json();
      setSelectedEscrow(data);
      setShowDetailsModal(true);
      
    } catch (error) {
      console.error('Error loading escrow details:', error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    const colors = {
      active: 'success',
      pending_funding: 'warning',
      released: 'default',
      disputed: 'danger'
    };
    return colors[status] || 'default';
  };

  const getStatusIcon = (status) => {
    const icons = {
      active: <Lock className="text-green-500" size={16} />,
      pending_funding: <Clock className="text-orange-500" size={16} />,
      released: <CheckCircle className="text-gray-500" size={16} />,
      disputed: <AlertTriangle className="text-red-500" size={16} />
    };
    return icons[status] || <Lock className="text-gray-500" size={16} />;
  };

  return (
    <div className={`escrow-manager space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Escrow Management</h2>
          <p className="text-gray-600">Secure fund holding until conditions are met</p>
        </div>
        <Button
          color="primary"
          startContent={<Plus size={16} />}
          onPress={() => setShowCreateModal(true)}
        >
          Create Escrow
        </Button>
      </div>

      {/* Escrow Accounts List */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-500">Loading escrow accounts...</p>
        </div>
      ) : escrowAccounts.length === 0 ? (
        <Card>
          <CardBody className="p-8 text-center">
            <Shield size={48} className="mx-auto mb-4 text-gray-300" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Escrow Accounts</h3>
            <p className="text-gray-600 mb-4">
              Create your first escrow account to securely hold funds for projects
            </p>
            <Button
              color="primary"
              startContent={<Plus size={16} />}
              onPress={() => setShowCreateModal(true)}
            >
              Create Escrow Account
            </Button>
          </CardBody>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {escrowAccounts.map((escrow) => (
            <motion.div
              key={escrow.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(escrow.status)}
                      <Chip
                        size="sm"
                        color={getStatusColor(escrow.status)}
                        variant="flat"
                      >
                        {escrow.status.replace('_', ' ')}
                      </Chip>
                    </div>
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      onPress={() => viewEscrowDetails(escrow.id)}
                    >
                      <Eye size={16} />
                    </Button>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <h3 className="font-semibold text-gray-900">{escrow.escrow_name}</h3>
                      {escrow.project && (
                        <p className="text-sm text-gray-600">
                          Project: {escrow.project.name}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Total Amount:</span>
                        <span className="font-medium">{formatCurrency(escrow.total_amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Current Balance:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(escrow.current_balance)}
                        </span>
                      </div>
                    </div>

                    {escrow.auto_release_date && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar size={14} />
                        <span>Release: {formatDate(escrow.auto_release_date)}</span>
                      </div>
                    )}

                    <div className="pt-2 border-t border-gray-100">
                      <p className="text-xs text-gray-500">
                        Created {formatDate(escrow.created_at)}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))}
        </div>
      )}

      {/* Create Escrow Modal */}
      <Modal 
        isOpen={showCreateModal} 
        onClose={() => setShowCreateModal(false)}
        size="2xl"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <Lock size={20} className="text-primary" />
              <span>Create Escrow Account</span>
            </div>
          </ModalHeader>
          
          <ModalBody className="space-y-4">
            <Input
              label="Escrow Name"
              placeholder="Enter a name for this escrow account"
              value={createForm.escrow_name}
              onChange={(e) => setCreateForm(prev => ({ ...prev, escrow_name: e.target.value }))}
            />

            <Input
              label="Total Amount"
              placeholder="0.00"
              startContent={<DollarSign size={16} />}
              type="number"
              value={createForm.total_amount}
              onChange={(e) => setCreateForm(prev => ({ ...prev, total_amount: e.target.value }))}
            />

            <Input
              label="Project ID (Optional)"
              placeholder="Enter project ID if applicable"
              value={createForm.project_id}
              onChange={(e) => setCreateForm(prev => ({ ...prev, project_id: e.target.value }))}
            />

            <Input
              label="Auto Release Date (Optional)"
              type="date"
              value={createForm.auto_release_date}
              onChange={(e) => setCreateForm(prev => ({ ...prev, auto_release_date: e.target.value }))}
            />

            <Textarea
              label="Release Conditions (JSON)"
              placeholder='{"milestone_complete": true, "client_approval": true}'
              value={createForm.release_conditions}
              onChange={(e) => setCreateForm(prev => ({ ...prev, release_conditions: e.target.value }))}
              minRows={3}
            />
          </ModalBody>
          
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={() => setShowCreateModal(false)}
            >
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={createEscrowAccount}
              isLoading={isLoading}
              isDisabled={!createForm.escrow_name || !createForm.total_amount}
            >
              Create Escrow
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Escrow Details Modal */}
      <Modal 
        isOpen={showDetailsModal} 
        onClose={() => setShowDetailsModal(false)}
        size="3xl"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <Lock size={20} className="text-primary" />
              <span>Escrow Details</span>
            </div>
          </ModalHeader>
          
          <ModalBody>
            {selectedEscrow && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold mb-2">Account Information</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Name:</span>
                        <span>{selectedEscrow.escrow_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <Chip size="sm" color={getStatusColor(selectedEscrow.status)}>
                          {selectedEscrow.status}
                        </Chip>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Amount:</span>
                        <span className="font-medium">{formatCurrency(selectedEscrow.total_amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Current Balance:</span>
                        <span className="font-medium text-green-600">
                          {formatCurrency(selectedEscrow.current_balance)}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Release Conditions</h3>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <pre className="text-xs text-gray-700 whitespace-pre-wrap">
                        {JSON.stringify(selectedEscrow.release_conditions, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>

                {selectedEscrow.releases && selectedEscrow.releases.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Release History</h3>
                    <div className="space-y-2">
                      {selectedEscrow.releases.map((release) => (
                        <div key={release.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium">{formatCurrency(release.gross_amount)}</p>
                            <p className="text-sm text-gray-600">{formatDate(release.created_at)}</p>
                          </div>
                          <Chip size="sm" color="success">Released</Chip>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={() => setShowDetailsModal(false)}
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default EscrowManager;
