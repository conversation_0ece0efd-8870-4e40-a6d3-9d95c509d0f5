import { useState, useEffect } from "react";
import axios from "axios";
import { getAuth } from "firebase/auth";
import _ from "lodash";

const Dashboard = () => {
  const [user, setUser] = useState(null);
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const auth = getAuth();
        const currentUser = auth.currentUser;
        if (!currentUser) {
          setUser("error");
          return;
        }
        // Get Firebase auth token
        const token = await currentUser.getIdToken();

        // Send token to backend
        const { data } = await axios.get("/profile", {
          headers: { Authorization: `Bearer ${token}` },
        });

        setUser(data); // User data from MongoDB
      } catch (error) {
        console.error("Error fetching user data:", error);
        // Set to "error" state if fetch fails
        setUser("error"); // Set user to "error" to handle it in the UI
      }
    };

    fetchUser();
  }, []);

  const titleCase = (str) => {
    return _.startCase(_.camelCase(str));
  };

  if (user === null) return <div>Loading...</div>;
  if (user === "error") return <div>Failed to load user data.</div>;
  return <div>Hello, {titleCase(user.displayName || user.email)}</div>; // Adjust depending on user object
};

export default Dashboard;
