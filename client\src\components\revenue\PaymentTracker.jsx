import React, { useState, useEffect, useContext } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import { formatCurrency } from '../../utils/royalty/royalty-calculator';

const PaymentTracker = ({ projectId, userId }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [payments, setPayments] = useState([]);
  const [distributions, setDistributions] = useState({});
  const [projects, setProjects] = useState({});
  const [filter, setFilter] = useState('all'); // 'all', 'pending', 'paid'
  const [sortBy, setSortBy] = useState('date'); // 'date', 'amount', 'project'
  const [sortDirection, setSortDirection] = useState('desc'); // 'asc', 'desc'

  // Fetch payments data
  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setLoading(true);
        
        // Build query
        let query = supabase
          .from('royalty_payments')
          .select(`
            *,
            distribution:royalty_distributions(
              id,
              project_id,
              revenue_id,
              distribution_date,
              total_amount,
              currency,
              status,
              calculation_method
            )
          `)
          .order('created_at', { ascending: false });
        
        // Apply filters
        if (projectId) {
          // Get all distributions for this project first
          const { data: projectDistributions, error: distributionsError } = await supabase
            .from('royalty_distributions')
            .select('id')
            .eq('project_id', projectId);
            
          if (distributionsError) throw distributionsError;
          
          const distributionIds = projectDistributions.map(d => d.id);
          if (distributionIds.length > 0) {
            query = query.in('distribution_id', distributionIds);
          } else {
            // No distributions for this project
            setPayments([]);
            setLoading(false);
            return;
          }
        }
        
        if (userId) {
          query = query.eq('recipient_id', userId);
        }
        
        if (filter !== 'all') {
          query = query.eq('status', filter);
        }
        
        // Execute query
        const { data, error } = await query;
        
        if (error) throw error;
        
        setPayments(data || []);
        
        // Get unique project IDs
        const projectIds = [...new Set(data
          .filter(p => p.distribution?.project_id)
          .map(p => p.distribution.project_id))];
        
        if (projectIds.length > 0) {
          // Fetch project data
          const { data: projectsData, error: projectsError } = await supabase
            .from('projects')
            .select('id, name, logo_url')
            .in('id', projectIds);
            
          if (projectsError) throw projectsError;
          
          // Create a map of project ID to project data
          const projectsMap = {};
          projectsData.forEach(p => {
            projectsMap[p.id] = p;
          });
          
          setProjects(projectsMap);
        }
      } catch (error) {
        console.error('Error fetching payments:', error);
        toast.error('Failed to load payment data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchPayments();
  }, [projectId, userId, filter]);

  // Handle sorting
  const sortedPayments = [...payments].sort((a, b) => {
    if (sortBy === 'date') {
      const dateA = new Date(a.distribution?.distribution_date || a.created_at);
      const dateB = new Date(b.distribution?.distribution_date || b.created_at);
      return sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
    } else if (sortBy === 'amount') {
      return sortDirection === 'asc' 
        ? parseFloat(a.amount) - parseFloat(b.amount) 
        : parseFloat(b.amount) - parseFloat(a.amount);
    } else if (sortBy === 'project') {
      const projectA = projects[a.distribution?.project_id]?.name || '';
      const projectB = projects[b.distribution?.project_id]?.name || '';
      return sortDirection === 'asc'
        ? projectA.localeCompare(projectB)
        : projectB.localeCompare(projectA);
    }
    return 0;
  });

  // Handle sort change
  const handleSortChange = (field) => {
    if (sortBy === field) {
      // Toggle direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // New field, default to descending
      setSortBy(field);
      setSortDirection('desc');
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get project name
  const getProjectName = (projectId) => {
    return projects[projectId]?.name || 'Unknown Project';
  };

  // Mark payment as paid
  const markAsPaid = async (paymentId) => {
    try {
      const { error } = await supabase
        .from('royalty_payments')
        .update({
          status: 'paid',
          payment_date: new Date().toISOString(),
          payment_method: 'manual',
          notes: 'Marked as paid manually'
        })
        .eq('id', paymentId);
        
      if (error) throw error;
      
      toast.success('Payment marked as paid');
      
      // Update local state
      setPayments(payments.map(p => 
        p.id === paymentId 
          ? { 
              ...p, 
              status: 'paid', 
              payment_date: new Date().toISOString(),
              payment_method: 'manual',
              notes: 'Marked as paid manually'
            } 
          : p
      ));
    } catch (error) {
      console.error('Error updating payment:', error);
      toast.error('Failed to update payment status');
    }
  };

  // Check if user is admin of the project
  const isProjectAdmin = (projectId) => {
    // This is a simplified check - in a real app, you'd check the user's role in the project
    return currentUser && currentUser.id === userId;
  };

  if (loading) {
    return <div className="loading-spinner">Loading payments...</div>;
  }

  if (payments.length === 0) {
    return (
      <div className="no-payments">
        <p>No royalty payments found.</p>
      </div>
    );
  }

  return (
    <div className="payment-tracker">
      <div className="payment-tracker-header">
        <h3>Royalty Payments</h3>
        
        <div className="payment-filters">
          <div className="filter-group">
            <label htmlFor="status-filter">Status:</label>
            <select
              id="status-filter"
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="status-filter"
            >
              <option value="all">All</option>
              <option value="pending">Pending</option>
              <option value="paid">Paid</option>
              <option value="failed">Failed</option>
            </select>
          </div>
          
          <div className="sort-group">
            <label>Sort by:</label>
            <div className="sort-buttons">
              <button
                className={`sort-button ${sortBy === 'date' ? 'active' : ''}`}
                onClick={() => handleSortChange('date')}
              >
                Date {sortBy === 'date' && (
                  <span className="sort-direction">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </button>
              
              <button
                className={`sort-button ${sortBy === 'amount' ? 'active' : ''}`}
                onClick={() => handleSortChange('amount')}
              >
                Amount {sortBy === 'amount' && (
                  <span className="sort-direction">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </button>
              
              <button
                className={`sort-button ${sortBy === 'project' ? 'active' : ''}`}
                onClick={() => handleSortChange('project')}
              >
                Project {sortBy === 'project' && (
                  <span className="sort-direction">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="payments-table-container">
        <table className="payments-table">
          <thead>
            <tr>
              <th>Project</th>
              <th>Distribution Date</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Payment Date</th>
              <th>Method</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedPayments.map((payment) => (
              <tr key={payment.id} className={`payment-row status-${payment.status}`}>
                <td className="project-name">
                  {payment.distribution && (
                    <div className="project-info">
                      {projects[payment.distribution.project_id]?.logo_url && (
                        <img 
                          src={projects[payment.distribution.project_id].logo_url} 
                          alt="Project Logo" 
                          className="project-logo"
                        />
                      )}
                      <span>{getProjectName(payment.distribution.project_id)}</span>
                    </div>
                  )}
                </td>
                <td className="distribution-date">
                  {payment.distribution && formatDate(payment.distribution.distribution_date)}
                </td>
                <td className="payment-amount">
                  {formatCurrency(payment.amount, payment.currency)}
                </td>
                <td className="payment-status">
                  <span className={`status-badge ${payment.status}`}>
                    {payment.status}
                  </span>
                </td>
                <td className="payment-date">
                  {payment.payment_date ? formatDate(payment.payment_date) : '-'}
                </td>
                <td className="payment-method">
                  {payment.payment_method || '-'}
                </td>
                <td className="payment-actions">
                  {payment.status === 'pending' && (
                    <>
                      {isProjectAdmin(payment.distribution?.project_id) && (
                        <button
                          className="mark-paid-button"
                          onClick={() => markAsPaid(payment.id)}
                        >
                          Mark as Paid
                        </button>
                      )}
                    </>
                  )}
                  
                  {payment.status === 'paid' && (
                    <span className="payment-reference">
                      {payment.payment_reference || 'No reference'}
                    </span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PaymentTracker;
