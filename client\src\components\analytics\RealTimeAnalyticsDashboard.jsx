import React, { useState, useEffect, useContext, useCallback } from 'react';
import { <PERSON>, Card<PERSON>ody, CardHeader, <PERSON><PERSON>, <PERSON>, Badge, Spinner } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import AnalyticsDataService from '../../services/AnalyticsDataService';

/**
 * Real-Time Analytics Dashboard
 * 
 * Features:
 * - Live data updates via WebSocket connections
 * - Real-time notifications for important events
 * - Performance monitoring with live metrics
 * - Instant data refresh and cache invalidation
 * - Mobile-optimized real-time experience
 */
const RealTimeAnalyticsDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [isConnected, setIsConnected] = useState(false);
  const [liveData, setLiveData] = useState({
    revenue: { current: 0, change: 0, trend: 'stable' },
    projects: { active: 0, completed: 0, change: 0 },
    performance: { score: 0, change: 0, trend: 'stable' },
    notifications: []
  });
  const [recentUpdates, setRecentUpdates] = useState([]);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');

  // Real-time update handler
  const handleRealTimeUpdate = useCallback((update) => {
    const timestamp = new Date().toISOString();
    
    // Add to recent updates
    setRecentUpdates(prev => [
      {
        id: Date.now(),
        type: update.type,
        message: generateUpdateMessage(update),
        timestamp,
        data: update.payload
      },
      ...prev.slice(0, 9) // Keep last 10 updates
    ]);

    // Update live data based on update type
    switch (update.type) {
      case 'financial':
        setLiveData(prev => ({
          ...prev,
          revenue: {
            ...prev.revenue,
            change: prev.revenue.change + 1,
            trend: 'up'
          }
        }));
        break;
        
      case 'projects':
        setLiveData(prev => ({
          ...prev,
          projects: {
            ...prev.projects,
            change: prev.projects.change + 1
          }
        }));
        break;
        
      case 'contributions':
        setLiveData(prev => ({
          ...prev,
          performance: {
            ...prev.performance,
            change: prev.performance.change + 1,
            trend: 'up'
          }
        }));
        break;
    }

    // Show toast notification for important updates
    if (update.type === 'financial') {
      toast.success('💰 New financial activity detected!', {
        duration: 3000,
        position: 'top-right'
      });
    }
  }, []);

  // Generate user-friendly update messages
  const generateUpdateMessage = (update) => {
    switch (update.type) {
      case 'financial':
        return 'New financial transaction recorded';
      case 'projects':
        return 'Project status updated';
      case 'contributions':
        return 'New contribution logged';
      default:
        return 'Data updated';
    }
  };

  // Initialize real-time connection
  useEffect(() => {
    if (!currentUser) return;

    const initializeRealTime = async () => {
      try {
        setConnectionStatus('connecting');
        
        // Subscribe to real-time updates
        await AnalyticsDataService.subscribeToRealTimeAnalytics(
          currentUser.id,
          handleRealTimeUpdate
        );
        
        setIsConnected(true);
        setConnectionStatus('connected');
        
        // Load initial data
        const initialData = await AnalyticsDataService.getAnalyticsOverview(
          currentUser.id,
          '7d'
        );
        
        setLiveData({
          revenue: {
            current: initialData.revenue?.total || 0,
            change: 0,
            trend: 'stable'
          },
          projects: {
            active: initialData.projects?.active || 0,
            completed: initialData.projects?.completed || 0,
            change: 0
          },
          performance: {
            score: initialData.performance?.score || 0,
            change: 0,
            trend: 'stable'
          },
          notifications: []
        });
        
      } catch (error) {
        console.error('Error initializing real-time analytics:', error);
        setConnectionStatus('error');
        toast.error('Failed to connect to real-time analytics');
      }
    };

    initializeRealTime();

    // Cleanup on unmount
    return () => {
      AnalyticsDataService.unsubscribeFromRealTimeAnalytics(currentUser.id);
      setIsConnected(false);
      setConnectionStatus('disconnected');
    };
  }, [currentUser, handleRealTimeUpdate]);

  // Connection status indicator
  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'success';
      case 'connecting': return 'warning';
      case 'error': return 'danger';
      default: return 'default';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Live';
      case 'connecting': return 'Connecting...';
      case 'error': return 'Error';
      default: return 'Offline';
    }
  };

  return (
    <div className={`real-time-analytics-dashboard ${className}`}>
      {/* Real-Time Status Header */}
      <Card className="mb-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="relative">
                <span className="text-2xl">📡</span>
                {isConnected && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
                )}
              </div>
              <div>
                <h2 className="text-xl font-bold">Real-Time Analytics</h2>
                <p className="text-sm text-default-600">
                  Live performance monitoring and instant updates
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Chip
                color={getConnectionStatusColor()}
                variant="flat"
                size="sm"
                startContent={
                  connectionStatus === 'connecting' ? (
                    <Spinner size="sm" />
                  ) : (
                    <div className={`w-2 h-2 rounded-full ${
                      connectionStatus === 'connected' ? 'bg-green-500' : 
                      connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-500'
                    }`} />
                  )
                }
              >
                {getConnectionStatusText()}
              </Chip>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Live Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Revenue Metric */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="relative overflow-hidden">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">💰</span>
                  <h3 className="font-semibold">Revenue</h3>
                </div>
                {liveData.revenue.change > 0 && (
                  <Badge color="success" variant="flat" size="sm">
                    +{liveData.revenue.change}
                  </Badge>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  ${liveData.revenue.current.toLocaleString()}
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm ${
                    liveData.revenue.trend === 'up' ? 'text-green-600' : 
                    liveData.revenue.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {liveData.revenue.trend === 'up' ? '↗️' : 
                     liveData.revenue.trend === 'down' ? '↘️' : '➡️'} 
                    Live tracking
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Projects Metric */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="relative overflow-hidden">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">🎯</span>
                  <h3 className="font-semibold">Projects</h3>
                </div>
                {liveData.projects.change > 0 && (
                  <Badge color="primary" variant="flat" size="sm">
                    +{liveData.projects.change}
                  </Badge>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-4">
                  <div>
                    <div className="text-xl font-bold">{liveData.projects.active}</div>
                    <div className="text-xs text-default-600">Active</div>
                  </div>
                  <div>
                    <div className="text-xl font-bold">{liveData.projects.completed}</div>
                    <div className="text-xs text-default-600">Completed</div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Performance Metric */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="relative overflow-hidden">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">⚡</span>
                  <h3 className="font-semibold">Performance</h3>
                </div>
                {liveData.performance.change > 0 && (
                  <Badge color="secondary" variant="flat" size="sm">
                    +{liveData.performance.change}
                  </Badge>
                )}
              </div>
              
              <div className="space-y-2">
                <div className="text-2xl font-bold">{liveData.performance.score}</div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm ${
                    liveData.performance.trend === 'up' ? 'text-green-600' : 
                    liveData.performance.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                  }`}>
                    {liveData.performance.trend === 'up' ? '📈' : 
                     liveData.performance.trend === 'down' ? '📉' : '📊'} 
                    Score
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Recent Updates Feed */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <span className="text-xl">🔔</span>
            <h3 className="font-semibold">Live Activity Feed</h3>
            <Badge color="primary" variant="flat" size="sm">
              {recentUpdates.length}
            </Badge>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          <div className="max-h-64 overflow-y-auto">
            <AnimatePresence>
              {recentUpdates.length === 0 ? (
                <div className="p-6 text-center text-default-600">
                  <span className="text-4xl mb-2 block">📊</span>
                  <p>Waiting for live updates...</p>
                  <p className="text-sm">Activity will appear here in real-time</p>
                </div>
              ) : (
                recentUpdates.map((update, index) => (
                  <motion.div
                    key={update.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                    className={`p-4 border-b border-divider ${
                      index === 0 ? 'bg-primary/5' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          update.type === 'financial' ? 'bg-green-500' :
                          update.type === 'projects' ? 'bg-blue-500' :
                          update.type === 'contributions' ? 'bg-purple-500' :
                          'bg-gray-500'
                        }`} />
                        <span className="text-sm font-medium">{update.message}</span>
                      </div>
                      <span className="text-xs text-default-600">
                        {new Date(update.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default RealTimeAnalyticsDashboard;
