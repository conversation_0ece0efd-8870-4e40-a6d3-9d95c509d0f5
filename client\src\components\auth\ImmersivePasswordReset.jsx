import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Button, Input } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';

/**
 * ImmersivePasswordReset Component
 * 
 * Full-screen immersive password reset experience
 * Follows wireframe specifications with minimal UI
 */
const ImmersivePasswordReset = ({ 
  onBackToLogin, 
  onCancel 
}) => {
  const { resetPassword } = useContext(UserContext);
  
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  // Handle password reset request
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    if (!email || !/\S+@\S+\.\S+/.test(email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    setLoading(true);

    try {
      await resetPassword(email);
      setEmailSent(true);
      toast.success('Password reset email sent! Check your inbox.');
    } catch (err) {
      console.error('Password reset error:', err);
      setError(err.message || 'Failed to send reset email. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-primary-50 to-secondary-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Back to Login button */}
      {onBackToLogin && (
        <motion.div
          className="absolute top-6 left-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onBackToLogin}
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-arrow-left text-xl mr-2"></i>
            Back to Login
          </Button>
        </motion.div>
      )}

      {/* Exit button */}
      {onCancel && (
        <motion.div
          className="absolute top-6 right-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </motion.div>
      )}

      <div className="max-w-md mx-auto w-full">
        {!emailSent ? (
          <>
            {/* Reset Password Title */}
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h1 className="text-5xl font-bold text-foreground mb-4">
                Reset Password
              </h1>
              <p className="text-lg text-default-600">
                Enter your email address and we'll send you a link to reset your password.
              </p>
            </motion.div>

            {/* Reset Form */}
            <motion.div variants={itemVariants}>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Email Input */}
                <div>
                  <Input
                    type="email"
                    label="Email Address"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => {
                      setEmail(e.target.value);
                      if (error) setError(''); // Clear error when user starts typing
                    }}
                    size="lg"
                    variant="bordered"
                    isRequired
                    isDisabled={loading}
                    classNames={{
                      input: "text-lg",
                      inputWrapper: "h-14"
                    }}
                    startContent={
                      <i className="bi bi-envelope text-default-400"></i>
                    }
                  />
                </div>

                {/* Error Message */}
                {error && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-danger-50 border border-danger-200 rounded-lg p-3"
                  >
                    <p className="text-danger text-sm">{error}</p>
                  </motion.div>
                )}

                {/* Send Reset Link Button */}
                <Button
                  type="submit"
                  size="lg"
                  className="w-full bg-primary text-white font-semibold py-4 text-lg"
                  isLoading={loading}
                  isDisabled={!email}
                >
                  {loading ? 'Sending Reset Link...' : 'Send Reset Link'}
                </Button>
              </form>
            </motion.div>

            {/* Footer Link */}
            <motion.div variants={itemVariants} className="mt-8 text-center">
              {onBackToLogin && (
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-default-600">Remember your password?</span>
                  <Button
                    variant="light"
                    onPress={onBackToLogin}
                    className="text-primary hover:text-primary-600 font-semibold"
                  >
                    Back to Login
                  </Button>
                </div>
              )}
            </motion.div>
          </>
        ) : (
          <>
            {/* Email Sent Confirmation */}
            <motion.div variants={itemVariants} className="text-center">
              <div className="mb-8">
                <div className="w-20 h-20 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <i className="bi bi-check-circle text-3xl text-success"></i>
                </div>
                <h1 className="text-4xl font-bold text-foreground mb-4">
                  Check Your Email
                </h1>
                <p className="text-lg text-default-600 mb-2">
                  We've sent a password reset link to:
                </p>
                <p className="text-lg font-semibold text-foreground mb-6">
                  {email}
                </p>
                <p className="text-default-600">
                  Click the link in the email to reset your password. 
                  If you don't see it, check your spam folder.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Button
                  size="lg"
                  variant="bordered"
                  onPress={() => {
                    setEmailSent(false);
                    setEmail('');
                    setError('');
                  }}
                  className="w-full"
                >
                  Try Different Email
                </Button>

                {onBackToLogin && (
                  <Button
                    size="lg"
                    onPress={onBackToLogin}
                    className="w-full bg-primary text-white font-semibold"
                  >
                    Back to Login
                  </Button>
                )}
              </div>

              {/* Resend Link */}
              <div className="mt-8">
                <p className="text-sm text-default-500 mb-2">
                  Didn't receive the email?
                </p>
                <Button
                  variant="light"
                  size="sm"
                  onPress={() => {
                    setEmailSent(false);
                    handleSubmit({ preventDefault: () => {} });
                  }}
                  className="text-primary hover:text-primary-600"
                >
                  Resend Reset Link
                </Button>
              </div>
            </motion.div>
          </>
        )}
      </div>
    </motion.div>
  );
};

export default ImmersivePasswordReset;
