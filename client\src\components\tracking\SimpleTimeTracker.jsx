import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Input, Textarea, Select, SelectItem, Chip } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Simple Time Tracker Component
 * 
 * A streamlined time tracking interface that follows the spatial-first design philosophy.
 * Provides immediate feedback and integrates seamlessly with the contribution system.
 */
const SimpleTimeTracker = ({ projectId = null, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // Timer state
  const [isTracking, setIsTracking] = useState(false);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [currentTask, setCurrentTask] = useState('');
  const [taskDescription, setTaskDescription] = useState('');
  const [difficulty, setDifficulty] = useState('3');
  
  // Today's entries
  const [todaysEntries, setTodaysEntries] = useState([]);
  const [totalTimeToday, setTotalTimeToday] = useState(0);

  // Update elapsed time every second when tracking
  useEffect(() => {
    let interval = null;
    if (isTracking && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime);
      }, 1000);
    } else if (!isTracking) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [isTracking, startTime]);

  // Load today's entries on mount
  useEffect(() => {
    if (currentUser) {
      loadTodaysEntries();
    }
  }, [currentUser]);

  // Load today's time entries
  const loadTodaysEntries = async () => {
    if (!currentUser) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      
      const { data, error } = await supabase
        .from('contributions')
        .select('*')
        .eq('user_id', currentUser.id)
        .gte('created_at', `${today}T00:00:00`)
        .lt('created_at', `${today}T23:59:59`)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setTodaysEntries(data || []);
      
      // Calculate total time today
      const total = (data || []).reduce((sum, entry) => sum + (entry.hours_tracked || 0), 0);
      setTotalTimeToday(total);
    } catch (error) {
      console.error('Error loading today\'s entries:', error);
    }
  };

  // Start tracking
  const startTracking = () => {
    if (!currentTask.trim()) {
      toast.error('Please enter a task description');
      return;
    }

    setIsTracking(true);
    setStartTime(Date.now());
    setElapsedTime(0);
    toast.success('Time tracking started');
  };

  // Stop tracking and save
  const stopTracking = async () => {
    if (!isTracking || !startTime) return;

    const finalElapsedTime = Date.now() - startTime;
    const hoursTracked = finalElapsedTime / (1000 * 60 * 60); // Convert to hours

    setIsTracking(false);
    setElapsedTime(finalElapsedTime);

    // Save contribution
    await saveContribution(hoursTracked);
  };

  // Save contribution to database
  const saveContribution = async (hoursTracked) => {
    if (!currentUser) {
      toast.error('You must be logged in to save contributions');
      return;
    }

    try {
      const contributionData = {
        user_id: currentUser.id,
        project_id: projectId,
        task_description: currentTask,
        description: taskDescription || currentTask,
        hours_tracked: hoursTracked,
        difficulty_rating: parseInt(difficulty),
        contribution_type: 'time_tracking',
        status: 'pending',
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('contributions')
        .insert([contributionData])
        .select()
        .single();

      if (error) throw error;

      toast.success(`Saved ${formatTime(elapsedTime)} of work`);
      
      // Reset form
      setCurrentTask('');
      setTaskDescription('');
      setElapsedTime(0);
      setStartTime(null);
      
      // Reload today's entries
      loadTodaysEntries();
    } catch (error) {
      console.error('Error saving contribution:', error);
      toast.error('Failed to save contribution');
    }
  };

  // Format time display
  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Get difficulty color
  const getDifficultyColor = (diff) => {
    const colors = {
      1: 'success', 2: 'success', 3: 'warning', 4: 'danger', 5: 'danger'
    };
    return colors[diff] || 'default';
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Active Timer */}
      <Card className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-white/10">
        <CardBody className="p-6">
          <div className="text-center mb-6">
            <motion.div
              className="text-4xl font-mono font-bold text-white mb-2"
              animate={isTracking ? { scale: [1, 1.05, 1] } : {}}
              transition={{ duration: 1, repeat: isTracking ? Infinity : 0 }}
            >
              {formatTime(elapsedTime)}
            </motion.div>
            <div className="text-white/70">
              {isTracking ? 'Currently tracking' : 'Ready to track'}
            </div>
          </div>

          <div className="space-y-4">
            <Input
              label="What are you working on?"
              placeholder="e.g., Implementing user authentication"
              value={currentTask}
              onChange={(e) => setCurrentTask(e.target.value)}
              disabled={isTracking}
              className="text-white"
            />

            <Textarea
              label="Additional details (optional)"
              placeholder="Any additional context about this task..."
              value={taskDescription}
              onChange={(e) => setTaskDescription(e.target.value)}
              disabled={isTracking}
              rows={2}
            />

            <Select
              label="Difficulty Level"
              selectedKeys={[difficulty]}
              onSelectionChange={(keys) => setDifficulty(Array.from(keys)[0])}
              disabled={isTracking}
            >
              <SelectItem key="1" value="1">1 - Very Easy</SelectItem>
              <SelectItem key="2" value="2">2 - Easy</SelectItem>
              <SelectItem key="3" value="3">3 - Medium</SelectItem>
              <SelectItem key="4" value="4">4 - Hard</SelectItem>
              <SelectItem key="5" value="5">5 - Very Hard</SelectItem>
            </Select>

            <div className="flex gap-3 justify-center">
              {!isTracking ? (
                <Button
                  onClick={startTracking}
                  className="bg-green-500 hover:bg-green-600 text-white px-8"
                  size="lg"
                >
                  ▶️ Start Tracking
                </Button>
              ) : (
                <Button
                  onClick={stopTracking}
                  className="bg-red-500 hover:bg-red-600 text-white px-8"
                  size="lg"
                >
                  ⏹️ Stop & Save
                </Button>
              )}
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Today's Summary */}
      <Card className="bg-white/5 border border-white/10">
        <CardBody className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Today's Progress</h3>
          
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {formatTime(totalTimeToday * 60 * 60 * 1000)}
              </div>
              <div className="text-white/70 text-sm">Total Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {todaysEntries.length}
              </div>
              <div className="text-white/70 text-sm">Tasks Completed</div>
            </div>
          </div>

          {/* Recent Entries */}
          <div className="space-y-3">
            <h4 className="text-lg font-medium text-white">Recent Entries</h4>
            {todaysEntries.length === 0 ? (
              <div className="text-white/50 text-center py-4">
                No entries today. Start tracking to see your progress!
              </div>
            ) : (
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {todaysEntries.slice(0, 5).map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="text-white text-sm font-medium">
                        {entry.task_description}
                      </div>
                      <div className="text-white/60 text-xs">
                        {new Date(entry.created_at).toLocaleTimeString()}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Chip
                        size="sm"
                        color={getDifficultyColor(entry.difficulty_rating)}
                        variant="flat"
                      >
                        D{entry.difficulty_rating}
                      </Chip>
                      <div className="text-white/80 text-sm font-mono">
                        {formatTime((entry.hours_tracked || 0) * 60 * 60 * 1000)}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SimpleTimeTracker;
