import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Textarea, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Financial Oversight Component - Transaction Monitoring and Financial Management
 * 
 * Features:
 * - Transaction monitoring and tracking
 * - Dispute resolution and management
 * - Fraud detection and prevention
 * - Payment issue resolution
 * - Financial analytics and reporting
 */
const FinancialOversight = ({ currentUser, className = "" }) => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [transactions, setTransactions] = useState([]);
  const [disputes, setDisputes] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedItem, setSelectedItem] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [resolutionNotes, setResolutionNotes] = useState('');

  // Financial overview data
  const [financialData, setFinancialData] = useState({
    overview: {
      totalRevenue: 245000,
      totalDisputes: 8,
      pendingPayouts: 12,
      fraudAlerts: 2,
      processingFees: 8750,
      refundRequests: 5
    },
    metrics: {
      disputeRate: 0.3,
      fraudRate: 0.08,
      averageTransactionValue: 450,
      payoutProcessingTime: 2.4,
      successRate: 99.2
    }
  });

  // Load financial data
  const loadFinancialData = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Mock transactions data
      const mockTransactions = [
        {
          id: 'txn-1',
          transactionId: 'TXN123456',
          amount: 1250.00,
          type: 'payment',
          status: 'completed',
          user: {
            name: 'John Doe',
            email: '<EMAIL>'
          },
          project: 'Alpha Development',
          paymentMethod: 'Credit Card',
          processingFee: 37.50,
          createdAt: '2025-01-16T10:30:00Z',
          completedAt: '2025-01-16T10:32:00Z'
        },
        {
          id: 'txn-2',
          transactionId: 'TXN123457',
          amount: 850.00,
          type: 'payout',
          status: 'pending',
          user: {
            name: 'Sarah Smith',
            email: '<EMAIL>'
          },
          project: 'Beta Testing',
          paymentMethod: 'Bank Transfer',
          processingFee: 25.50,
          createdAt: '2025-01-16T09:15:00Z',
          estimatedCompletion: '2025-01-17T09:15:00Z'
        },
        {
          id: 'txn-3',
          transactionId: 'TXN123458',
          amount: 2100.00,
          type: 'payment',
          status: 'failed',
          user: {
            name: 'Mike Johnson',
            email: '<EMAIL>'
          },
          project: 'Gamma Launch',
          paymentMethod: 'Credit Card',
          failureReason: 'Insufficient funds',
          createdAt: '2025-01-16T08:45:00Z',
          failedAt: '2025-01-16T08:47:00Z'
        }
      ];

      // Mock disputes data
      const mockDisputes = [
        {
          id: 'dispute-1',
          disputeId: 'DSP001',
          transactionId: 'TXN123450',
          amount: 750.00,
          reason: 'Service not delivered',
          status: 'open',
          priority: 'high',
          plaintiff: {
            name: 'Alice Brown',
            email: '<EMAIL>'
          },
          defendant: {
            name: 'Bob Wilson',
            email: '<EMAIL>'
          },
          project: 'Website Redesign',
          createdAt: '2025-01-15T14:20:00Z',
          evidence: [
            'Project timeline not met',
            'Communication issues',
            'Deliverables incomplete'
          ]
        },
        {
          id: 'dispute-2',
          disputeId: 'DSP002',
          transactionId: 'TXN123451',
          amount: 1200.00,
          reason: 'Quality issues',
          status: 'investigating',
          priority: 'medium',
          plaintiff: {
            name: 'Carol Davis',
            email: '<EMAIL>'
          },
          defendant: {
            name: 'David Lee',
            email: '<EMAIL>'
          },
          project: 'Mobile App Development',
          createdAt: '2025-01-14T11:30:00Z',
          assignedTo: 'Financial Admin',
          evidence: [
            'App crashes frequently',
            'Missing features',
            'Poor code quality'
          ]
        }
      ];

      setTransactions(mockTransactions);
      setDisputes(mockDisputes);
      
    } catch (error) {
      console.error('Error loading financial data:', error);
      toast.error('Failed to load financial data');
    } finally {
      setLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'completed': 'success',
      'pending': 'warning',
      'failed': 'danger',
      'cancelled': 'default',
      'open': 'warning',
      'investigating': 'primary',
      'resolved': 'success',
      'closed': 'default'
    };
    return colors[status] || 'default';
  };

  // Get priority color
  const getPriorityColor = (priority) => {
    const colors = {
      'high': 'danger',
      'medium': 'warning',
      'low': 'default'
    };
    return colors[priority] || 'default';
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Handle dispute action
  const handleDisputeAction = (dispute, action) => {
    setSelectedItem(dispute);
    setModalType(action);
    setResolutionNotes('');
    setShowModal(true);
  };

  // Execute dispute action
  const executeDisputeAction = async () => {
    try {
      if (!resolutionNotes.trim()) {
        toast.error('Please provide resolution notes');
        return;
      }

      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update dispute locally
      setDisputes(prevDisputes => 
        prevDisputes.map(dispute => 
          dispute.id === selectedItem.id 
            ? { 
                ...dispute, 
                status: modalType === 'resolve' ? 'resolved' : 'investigating',
                resolutionNotes: resolutionNotes,
                resolvedAt: modalType === 'resolve' ? new Date().toISOString() : undefined,
                assignedTo: currentUser?.display_name || 'Financial Admin'
              }
            : dispute
        )
      );

      toast.success(`Dispute ${modalType}d successfully`);
      setShowModal(false);
      setSelectedItem(null);
      setModalType('');
      setResolutionNotes('');
      
    } catch (error) {
      console.error('Error executing dispute action:', error);
      toast.error('Failed to execute action');
    }
  };

  // Handle transaction retry
  const handleTransactionRetry = (transaction) => {
    toast.info(`Retrying transaction ${transaction.transactionId}`);
    // Mock retry logic
    setTransactions(prevTransactions => 
      prevTransactions.map(txn => 
        txn.id === transaction.id 
          ? { ...txn, status: 'pending' }
          : txn
      )
    );
  };

  useEffect(() => {
    loadFinancialData();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">💰</span>
              <div>
                <h2 className="text-2xl font-bold">Financial Oversight</h2>
                <p className="text-default-600">Monitor transactions, disputes, and financial health</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-success">
                {formatCurrency(financialData.overview.totalRevenue)}
              </div>
              <div className="text-sm text-default-600">Total Revenue</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Financial Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">📊</span>
                <Chip color="primary" variant="flat" size="sm">Transactions</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">Success Rate</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {financialData.metrics.successRate}%
                  </div>
                </div>
                <Progress
                  value={financialData.metrics.successRate}
                  color="primary"
                  size="sm"
                />
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">⚠️</span>
                <Chip color="warning" variant="flat" size="sm">Disputes</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">Active Disputes</div>
                  <div className="text-2xl font-bold text-orange-600">
                    {financialData.overview.totalDisputes}
                  </div>
                </div>
                <div className="text-sm text-default-600">
                  Rate: {financialData.metrics.disputeRate}%
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">🚨</span>
                <Chip color="danger" variant="flat" size="sm">Fraud</Chip>
              </div>
              <div className="space-y-2">
                <div>
                  <div className="text-sm text-default-600">Fraud Alerts</div>
                  <div className="text-2xl font-bold text-purple-600">
                    {financialData.overview.fraudAlerts}
                  </div>
                </div>
                <div className="text-sm text-default-600">
                  Rate: {financialData.metrics.fraudRate}%
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <h3 className="text-lg font-semibold">Recent Transactions</h3>
            <Button
              color="primary"
              variant="flat"
              onClick={loadFinancialData}
              startContent={<span>🔄</span>}
            >
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardBody>
          <Table aria-label="Recent transactions table">
            <TableHeader>
              <TableColumn>TRANSACTION</TableColumn>
              <TableColumn>USER</TableColumn>
              <TableColumn>AMOUNT</TableColumn>
              <TableColumn>STATUS</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    <div>
                      <div className="font-semibold">{transaction.transactionId}</div>
                      <div className="text-sm text-default-600">{transaction.project}</div>
                      <div className="text-xs text-default-500">
                        {transaction.type} • {transaction.paymentMethod}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{transaction.user.name}</div>
                      <div className="text-sm text-default-600">{transaction.user.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-semibold">{formatCurrency(transaction.amount)}</div>
                      <div className="text-xs text-default-500">
                        Fee: {formatCurrency(transaction.processingFee)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Chip
                        color={getStatusColor(transaction.status)}
                        size="sm"
                        variant="flat"
                      >
                        {transaction.status}
                      </Chip>
                      <div className="text-xs text-default-500">
                        {transaction.completedAt && formatTimestamp(transaction.completedAt)}
                        {transaction.failureReason && (
                          <div className="text-danger">{transaction.failureReason}</div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="flat">
                        View
                      </Button>
                      {transaction.status === 'failed' && (
                        <Button
                          size="sm"
                          variant="flat"
                          color="warning"
                          onClick={() => handleTransactionRetry(transaction)}
                        >
                          Retry
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Active Disputes */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Active Disputes</h3>
        </CardHeader>
        <CardBody>
          <div className="space-y-4">
            {disputes.map((dispute, index) => (
              <motion.div
                key={dispute.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardBody className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h4 className="font-semibold text-lg">{dispute.disputeId}</h4>
                        <div className="text-sm text-default-600 mb-2">
                          {dispute.project} • {formatCurrency(dispute.amount)}
                        </div>
                        <div className="flex items-center gap-2">
                          <Chip
                            color={getStatusColor(dispute.status)}
                            size="sm"
                            variant="flat"
                          >
                            {dispute.status}
                          </Chip>
                          <Chip
                            color={getPriorityColor(dispute.priority)}
                            size="sm"
                            variant="flat"
                          >
                            {dispute.priority} priority
                          </Chip>
                        </div>
                      </div>
                      <div className="text-right text-sm text-default-500">
                        {formatTimestamp(dispute.createdAt)}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="text-sm font-medium">Plaintiff</label>
                        <div className="text-sm text-default-600">
                          {dispute.plaintiff.name} ({dispute.plaintiff.email})
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Defendant</label>
                        <div className="text-sm text-default-600">
                          {dispute.defendant.name} ({dispute.defendant.email})
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="text-sm font-medium">Reason</label>
                      <div className="text-sm text-default-600">{dispute.reason}</div>
                    </div>

                    <div className="mb-4">
                      <label className="text-sm font-medium">Evidence</label>
                      <ul className="text-sm text-default-600 list-disc list-inside">
                        {dispute.evidence.map((item, idx) => (
                          <li key={idx}>{item}</li>
                        ))}
                      </ul>
                    </div>

                    {dispute.status !== 'resolved' && (
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          color="primary"
                          variant="flat"
                          onClick={() => handleDisputeAction(dispute, 'investigate')}
                        >
                          Investigate
                        </Button>
                        <Button
                          size="sm"
                          color="success"
                          variant="flat"
                          onClick={() => handleDisputeAction(dispute, 'resolve')}
                        >
                          Resolve
                        </Button>
                        <Button
                          size="sm"
                          color="default"
                          variant="flat"
                          onClick={() => {
                            toast.info('Mediation interface coming soon');
                          }}
                        >
                          Mediate
                        </Button>
                      </div>
                    )}
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Dispute Action Modal */}
      <Modal 
        isOpen={showModal} 
        onClose={() => setShowModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3>{modalType === 'resolve' ? 'Resolve Dispute' : 'Investigate Dispute'}</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <p>
                  You are about to {modalType} dispute <strong>{selectedItem?.disputeId}</strong>
                </p>
                <div className="text-sm text-default-600 mt-1">
                  Amount: {formatCurrency(selectedItem?.amount || 0)}
                </div>
              </div>
              
              <Textarea
                label={modalType === 'resolve' ? 'Resolution Notes' : 'Investigation Notes'}
                placeholder={
                  modalType === 'resolve' 
                    ? 'Provide details about the resolution...'
                    : 'Document your investigation findings...'
                }
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                minRows={4}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={() => setShowModal(false)}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={executeDisputeAction}
              disabled={!resolutionNotes.trim()}
            >
              {modalType === 'resolve' ? 'Resolve Dispute' : 'Start Investigation'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default FinancialOversight;
