/**
 * Loading Monitor Utility
 * 
 * This utility provides functions to track and monitor loading operations
 * across the application. It helps with debugging loading states and
 * identifying potential issues with loading indicators.
 */

// Store active loading operations
let activeLoadingOperations = [];

// Store loading history
let loadingHistory = [];

// Maximum history entries to keep
const MAX_HISTORY_ENTRIES = 100;

/**
 * Start tracking a loading operation
 * @param {string} componentName - Name of the component initiating the loading
 * @param {string} operationName - Name of the operation (e.g., "fetchData", "processForm")
 * @param {string} reason - Reason for the loading operation
 * @returns {string} - Operation ID
 */
export const startLoadingOperation = (componentName, operationName, reason = '') => {
  const operationId = `${componentName}-${operationName}-${Date.now()}`;
  
  const operation = {
    id: operationId,
    componentName,
    operationName,
    reason,
    startTime: Date.now()
  };
  
  activeLoadingOperations.push(operation);
  
  return operationId;
};

/**
 * End tracking a loading operation
 * @param {string} operationId - ID of the operation to end
 * @param {string} result - Result of the operation ('success', 'error', 'cancelled')
 * @param {string} error - Error message if result is 'error'
 */
export const endLoadingOperation = (operationId, result = 'success', error = null) => {
  const operationIndex = activeLoadingOperations.findIndex(op => op.id === operationId);
  
  if (operationIndex === -1) {
    console.warn(`[LoadingMonitor] Operation ${operationId} not found`);
    return;
  }
  
  const operation = activeLoadingOperations[operationIndex];
  const endTime = Date.now();
  const duration = endTime - operation.startTime;
  
  // Remove from active operations
  activeLoadingOperations.splice(operationIndex, 1);
  
  // Add to history
  const historyEntry = {
    ...operation,
    endTime,
    duration,
    result,
    error,
    timestamp: endTime
  };
  
  loadingHistory.unshift(historyEntry);
  
  // Trim history if needed
  if (loadingHistory.length > MAX_HISTORY_ENTRIES) {
    loadingHistory = loadingHistory.slice(0, MAX_HISTORY_ENTRIES);
  }
};

/**
 * Get all active loading operations
 * @returns {Array} - Array of active loading operations
 */
export const getActiveLoadingOperations = () => {
  return [...activeLoadingOperations];
};

/**
 * Get loading history
 * @returns {Array} - Array of loading history entries
 */
export const getLoadingHistory = () => {
  return [...loadingHistory];
};

/**
 * Clear loading history
 */
export const clearLoadingHistory = () => {
  loadingHistory = [];
};

/**
 * Reset all loading states
 */
export const resetLoadingStates = () => {
  activeLoadingOperations = [];
  loadingHistory = [];
};

/**
 * Track a loading operation with automatic cleanup
 * @param {string} componentName - Name of the component initiating the loading
 * @param {string} operationName - Name of the operation
 * @param {Function} operation - Async function to execute
 * @param {string} reason - Reason for the loading operation
 * @returns {Promise} - Result of the operation
 */
export const trackLoadingOperation = async (componentName, operationName, operation, reason = '') => {
  const operationId = startLoadingOperation(componentName, operationName, reason);
  
  try {
    const result = await operation();
    endLoadingOperation(operationId, 'success');
    return result;
  } catch (error) {
    endLoadingOperation(operationId, 'error', error.message);
    throw error;
  }
};

export default {
  startLoadingOperation,
  endLoadingOperation,
  getActiveLoadingOperations,
  getLoadingHistory,
  clearLoadingHistory,
  resetLoadingStates,
  trackLoadingOperation
};
