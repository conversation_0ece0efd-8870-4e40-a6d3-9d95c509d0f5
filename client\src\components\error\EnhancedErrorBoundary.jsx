import React from 'react';
import { <PERSON>, Card<PERSON>ody, But<PERSON> } from '@heroui/react';
import { motion } from 'framer-motion';
import { RefreshCw, AlertTriangle, Home, Bug } from 'lucide-react';
import { ErrorState } from '../common/LoadingStates';

/**
 * Enhanced Error Boundary Component
 * 
 * Provides comprehensive error handling with:
 * - Multiple recovery strategies
 * - Error reporting
 * - User-friendly error messages
 * - Fallback UI components
 * - Error analytics
 */

class EnhancedErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error) {
    return {
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Report error to monitoring service
    this.reportError(error, errorInfo);
  }

  reportError = (error, errorInfo) => {
    const errorReport = {
      id: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      userId: this.props.userId,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      environment: import.meta.env.MODE,
      retryCount: this.state.retryCount,
      props: this.props.errorContext || {}
    };

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('Enhanced Error Boundary caught an error:', errorReport);
    }

    // Send to error reporting service (implement based on your service)
    this.sendErrorReport(errorReport);
  };

  sendErrorReport = async (errorReport) => {
    try {
      // Replace with your error reporting service
      if (this.props.onError) {
        this.props.onError(errorReport);
      }
      
      // Example: Send to analytics or error tracking service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
      isRetrying: true
    }));

    // Reset retrying state after a short delay
    setTimeout(() => {
      this.setState({ isRetrying: false });
    }, 1000);
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleReportBug = () => {
    const bugReport = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      errorId: this.state.errorId
    };
    
    // Open bug report with pre-filled data
    const bugReportUrl = `/bug/report?data=${encodeURIComponent(JSON.stringify(bugReport))}`;
    window.open(bugReportUrl, '_blank');
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback, level = 'component' } = this.props;
      
      // Use custom fallback if provided
      if (Fallback) {
        return (
          <Fallback 
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            onRetry={this.handleRetry}
            retryCount={this.state.retryCount}
          />
        );
      }

      // Component-level error (smaller, inline error)
      if (level === 'component') {
        return (
          <Card className="border-danger-200 bg-danger-50 dark:bg-danger-900/20">
            <CardBody className="p-4">
              <ErrorState
                error={this.state.error}
                onRetry={this.handleRetry}
                title="Component Error"
                description="This component encountered an error and couldn't load properly."
                className="py-4"
              />
            </CardBody>
          </Card>
        );
      }

      // Page-level error (full page error)
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="w-full max-w-2xl"
          >
            <Card className="shadow-xl">
              <CardBody className="p-8 text-center">
                {/* Error Icon */}
                <div className="text-6xl mb-6">
                  <AlertTriangle className="mx-auto text-danger" size={64} />
                </div>
                
                {/* Error Title */}
                <h1 className="text-3xl font-bold text-danger mb-4">
                  Oops! Something went wrong
                </h1>
                
                {/* Error Description */}
                <p className="text-lg text-default-600 mb-6">
                  We encountered an unexpected error. Our team has been notified and is working to fix this issue.
                </p>

                {/* Error ID */}
                <p className="text-sm text-default-500 mb-6 font-mono">
                  Error ID: {this.state.errorId}
                </p>
                
                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
                  <Button
                    color="primary"
                    variant="solid"
                    startContent={<RefreshCw size={16} />}
                    onClick={this.handleRetry}
                    isLoading={this.state.isRetrying}
                  >
                    Try Again
                  </Button>
                  
                  <Button
                    color="default"
                    variant="bordered"
                    startContent={<Home size={16} />}
                    onClick={this.handleGoHome}
                  >
                    Go Home
                  </Button>
                  
                  <Button
                    color="warning"
                    variant="light"
                    startContent={<Bug size={16} />}
                    onClick={this.handleReportBug}
                  >
                    Report Bug
                  </Button>
                </div>
                
                {/* Retry Count */}
                {this.state.retryCount > 0 && (
                  <p className="text-sm text-default-500 mb-4">
                    Retry attempts: {this.state.retryCount}
                  </p>
                )}
                
                {/* Development Error Details */}
                {import.meta.env.DEV && this.state.error && (
                  <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 mb-6 text-left">
                    <h3 className="font-semibold text-red-700 dark:text-red-300 mb-2">
                      Development Error Details:
                    </h3>
                    <div className="text-sm text-red-600 dark:text-red-400 font-mono">
                      <p className="mb-2">
                        <strong>Message:</strong> {this.state.error.message}
                      </p>
                      {this.state.error.stack && (
                        <details className="mb-2">
                          <summary className="cursor-pointer hover:text-red-700 dark:hover:text-red-300">
                            Stack Trace
                          </summary>
                          <pre className="mt-2 text-xs overflow-auto max-h-40 bg-red-100 dark:bg-red-800/30 p-2 rounded">
                            {this.state.error.stack}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default EnhancedErrorBoundary;
