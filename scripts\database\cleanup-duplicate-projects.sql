-- Cleanup duplicate projects created during testing
-- This script removes duplicate projects keeping only the most recent one per user

-- First, let's see what we have
SELECT 
    created_by,
    COUNT(*) as project_count,
    STRING_AGG(name, ', ') as project_names
FROM projects 
WHERE created_by IS NOT NULL
GROUP BY created_by
HAVING COUNT(*) > 1;

-- Delete duplicate projects, keeping only the most recent one per user
WITH ranked_projects AS (
    SELECT 
        id,
        created_by,
        name,
        created_at,
        ROW_NUMBER() OVER (
            PARTITION BY created_by 
            ORDER BY created_at DESC, updated_at DESC NULLS LAST
        ) as rn
    FROM projects
    WHERE created_by IS NOT NULL
)
DELETE FROM projects 
WHERE id IN (
    SELECT id 
    FROM ranked_projects 
    WHERE rn > 1
);

-- Show final count
SELECT 
    created_by,
    COUNT(*) as project_count,
    STRING_AGG(name, ', ') as project_names
FROM projects 
WHERE created_by IS NOT NULL
GROUP BY created_by;
