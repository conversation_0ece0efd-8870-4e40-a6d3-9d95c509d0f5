import React, { lazy } from 'react';
import SafeLazyComponentWrapper from '../../components/common/LazyComponentWrapper';

/**
 * Lazy-loaded Project Wizard Page
 * 
 * This component lazy-loads the large ProjectWizard page to improve
 * initial bundle size and page load performance.
 * 
 * Task: O3 Performance Optimization - Component Lazy Loading
 */

// Lazy load the ProjectWizard page
const ProjectWizard = lazy(() => import('./ProjectWizard'));

const LazyProjectWizard = (props) => {
  return (
    <SafeLazyComponentWrapper
      className="w-full min-h-screen"
      minHeight="100vh"
      showSpinner={true}
    >
      <ProjectWizard {...props} />
    </SafeLazyComponentWrapper>
  );
};

export default LazyProjectWizard;
