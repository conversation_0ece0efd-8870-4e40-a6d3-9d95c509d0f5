// Email Service Integration
// Integration & Services Agent: Comprehensive email notification system

const { createClient } = require('@supabase/supabase-js');
const nodemailer = require('nodemailer');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Email transporter configuration
const createEmailTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Email templates
const emailTemplates = {
  welcome: {
    subject: 'Welcome to Royaltea Platform!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Welcome to Royaltea!</h1>
        <p>Hello {{userName}},</p>
        <p>Welcome to the Royaltea platform! We're excited to have you join our community of creators, developers, and entrepreneurs.</p>
        <p>Get started by:</p>
        <ul>
          <li>Completing your profile</li>
          <li>Exploring available projects</li>
          <li>Connecting with other creators</li>
        </ul>
        <p><a href="{{platformUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Get Started</a></p>
        <p>Best regards,<br>The Royaltea Team</p>
      </div>
    `
  },
  
  payment_received: {
    subject: 'Payment Received - ${{amount}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Payment Received</h1>
        <p>Hello {{userName}},</p>
        <p>You have received a payment of <strong>${{amount}} {{currency}}</strong>.</p>
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p><strong>Transaction Details:</strong></p>
          <p>Amount: ${{amount}} {{currency}}</p>
          <p>From: {{senderName}}</p>
          <p>Description: {{description}}</p>
          <p>Date: {{transactionDate}}</p>
        </div>
        <p><a href="{{transactionUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Transaction</a></p>
        <p>Best regards,<br>The Royaltea Team</p>
      </div>
    `
  },
  
  project_invitation: {
    subject: 'Project Invitation - {{projectName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Project Invitation</h1>
        <p>Hello {{userName}},</p>
        <p>You've been invited to join the project <strong>{{projectName}}</strong>.</p>
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p><strong>Project Details:</strong></p>
          <p>Name: {{projectName}}</p>
          <p>Role: {{role}}</p>
          <p>Invited by: {{inviterName}}</p>
          <p>Description: {{projectDescription}}</p>
        </div>
        <p><a href="{{invitationUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Invitation</a></p>
        <p>Best regards,<br>The Royaltea Team</p>
      </div>
    `
  },
  
  friend_request: {
    subject: 'New Friend Request from {{senderName}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">New Friend Request</h1>
        <p>Hello {{userName}},</p>
        <p><strong>{{senderName}}</strong> has sent you a friend request on Royaltea.</p>
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p><strong>{{senderName}}</strong></p>
          <p>{{senderBio}}</p>
        </div>
        <p><a href="{{requestUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Request</a></p>
        <p>Best regards,<br>The Royaltea Team</p>
      </div>
    `
  },
  
  escrow_release: {
    subject: 'Escrow Funds Released - ${{amount}}',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Escrow Funds Released</h1>
        <p>Hello {{userName}},</p>
        <p>Escrow funds of <strong>${{amount}} {{currency}}</strong> have been released for project <strong>{{projectName}}</strong>.</p>
        <div style="background-color: #f5f5f5; padding: 15px; border-radius: 6px; margin: 20px 0;">
          <p><strong>Release Details:</strong></p>
          <p>Amount: ${{amount}} {{currency}}</p>
          <p>Project: {{projectName}}</p>
          <p>Milestone: {{milestone}}</p>
          <p>Release Date: {{releaseDate}}</p>
        </div>
        <p><a href="{{projectUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">View Project</a></p>
        <p>Best regards,<br>The Royaltea Team</p>
      </div>
    `
  },
  
  password_reset: {
    subject: 'Password Reset Request',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #4CAF50;">Password Reset</h1>
        <p>Hello {{userName}},</p>
        <p>You requested a password reset for your Royaltea account.</p>
        <p>Click the button below to reset your password. This link will expire in 1 hour.</p>
        <p><a href="{{resetUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">Reset Password</a></p>
        <p>If you didn't request this reset, please ignore this email.</p>
        <p>Best regards,<br>The Royaltea Team</p>
      </div>
    `
  }
};

// Template variable replacement
const replaceTemplateVars = (template, variables) => {
  let result = template;
  Object.keys(variables).forEach(key => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    result = result.replace(regex, variables[key] || '');
  });
  return result;
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Send email function
const sendEmail = async (emailData) => {
  try {
    const { to, template, variables, subject, html } = emailData;
    
    if (!to) {
      throw new Error('Recipient email is required');
    }
    
    let emailSubject, emailHtml;
    
    if (template && emailTemplates[template]) {
      emailSubject = replaceTemplateVars(emailTemplates[template].subject, variables || {});
      emailHtml = replaceTemplateVars(emailTemplates[template].html, variables || {});
    } else if (subject && html) {
      emailSubject = subject;
      emailHtml = html;
    } else {
      throw new Error('Either template or subject+html must be provided');
    }
    
    const transporter = createEmailTransporter();
    
    const mailOptions = {
      from: process.env.EMAIL_FROM || '<EMAIL>',
      to: to,
      subject: emailSubject,
      html: emailHtml
    };
    
    const info = await transporter.sendMail(mailOptions);
    
    // Log email sent
    await supabase
      .from('email_logs')
      .insert({
        recipient: to,
        subject: emailSubject,
        template: template,
        status: 'sent',
        message_id: info.messageId,
        variables: variables
      });
    
    return {
      success: true,
      messageId: info.messageId,
      recipient: to
    };
    
  } catch (error) {
    console.error('Send email error:', error);
    
    // Log email failure
    try {
      await supabase
        .from('email_logs')
        .insert({
          recipient: emailData.to,
          subject: emailData.subject || 'Unknown',
          template: emailData.template,
          status: 'failed',
          error_message: error.message,
          variables: emailData.variables
        });
    } catch (logError) {
      console.error('Failed to log email error:', logError);
    }
    
    throw error;
  }
};

// Send bulk emails
const sendBulkEmails = async (emailList) => {
  const results = [];
  
  for (const emailData of emailList) {
    try {
      const result = await sendEmail(emailData);
      results.push({ ...result, status: 'success' });
    } catch (error) {
      results.push({
        recipient: emailData.to,
        status: 'failed',
        error: error.message
      });
    }
  }
  
  return results;
};

// Get email templates
const getEmailTemplates = async () => {
  return Object.keys(emailTemplates).map(key => ({
    name: key,
    subject: emailTemplates[key].subject,
    variables: extractTemplateVariables(emailTemplates[key].html)
  }));
};

// Extract template variables from HTML
const extractTemplateVariables = (html) => {
  const regex = /{{(\w+)}}/g;
  const variables = [];
  let match;
  
  while ((match = regex.exec(html)) !== null) {
    if (!variables.includes(match[1])) {
      variables.push(match[1]);
    }
  }
  
  return variables;
};

// Test email configuration
const testEmailConfig = async () => {
  try {
    const transporter = createEmailTransporter();
    await transporter.verify();
    return { success: true, message: 'Email configuration is valid' };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};

    let result;

    switch (action) {
      case 'send':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        // Authenticate for single email sends
        await authenticateUser(event.headers.authorization);
        result = await sendEmail(body);
        break;

      case 'send-bulk':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        // Authenticate for bulk sends
        await authenticateUser(event.headers.authorization);
        if (!body.emails || !Array.isArray(body.emails)) {
          throw new Error('emails array is required');
        }
        result = await sendBulkEmails(body.emails);
        break;

      case 'templates':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getEmailTemplates();
        break;

      case 'test-config':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await testEmailConfig();
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Email Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
