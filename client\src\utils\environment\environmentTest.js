/**
 * Environment Configuration Test Suite
 * 
 * Tests all API connections and environment variables
 * to ensure proper setup for development
 */

import { createClient } from '@supabase/supabase-js';

// Environment validation results
export const environmentTestResults = {
  supabase: { status: 'pending', message: '', details: null },
  teller: { status: 'pending', message: '', details: null },
  analytics: { status: 'pending', message: '', details: null },
  oauth: { status: 'pending', message: '', details: null },
  overall: { status: 'pending', message: '', score: 0 }
};

/**
 * Test Supabase Connection
 */
export const testSupabaseConnection = async () => {
  try {
    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
    const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      environmentTestResults.supabase = {
        status: 'error',
        message: 'Missing Supabase environment variables',
        details: {
          hasUrl: !!supabaseUrl,
          hasKey: !!supabaseKey
        }
      };
      return false;
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test connection with a simple query
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "table not found" which is OK
      environmentTestResults.supabase = {
        status: 'error',
        message: `Supabase connection failed: ${error.message}`,
        details: { error: error.code, hint: error.hint }
      };
      return false;
    }
    
    environmentTestResults.supabase = {
      status: 'success',
      message: 'Supabase connection successful',
      details: {
        url: supabaseUrl,
        hasKey: true,
        connectionTest: 'passed'
      }
    };
    return true;
    
  } catch (error) {
    environmentTestResults.supabase = {
      status: 'error',
      message: `Supabase test failed: ${error.message}`,
      details: { error: error.toString() }
    };
    return false;
  }
};

/**
 * Test Teller Configuration
 */
export const testTellerConfiguration = async () => {
  try {
    const tellerAppId = import.meta.env.TELLER_APPLICATION_ID;
    const tellerEnv = import.meta.env.TELLER_ENVIRONMENT;
    const tellerCertPath = import.meta.env.TELLER_CERTIFICATE_PATH;
    const tellerKeyPath = import.meta.env.TELLER_PRIVATE_KEY_PATH;
    
    const hasRequiredVars = tellerAppId && tellerEnv && tellerCertPath && tellerKeyPath;
    
    if (!hasRequiredVars) {
      environmentTestResults.teller = {
        status: 'warning',
        message: 'Teller environment variables not fully configured',
        details: {
          hasAppId: !!tellerAppId,
          hasEnvironment: !!tellerEnv,
          hasCertPath: !!tellerCertPath,
          hasKeyPath: !!tellerKeyPath
        }
      };
      return false;
    }
    
    // Check if certificate files exist (we can't actually read them in browser)
    environmentTestResults.teller = {
      status: 'success',
      message: 'Teller configuration appears complete',
      details: {
        applicationId: tellerAppId,
        environment: tellerEnv,
        certificatePath: tellerCertPath,
        privateKeyPath: tellerKeyPath,
        note: 'Certificate files should be verified server-side'
      }
    };
    return true;
    
  } catch (error) {
    environmentTestResults.teller = {
      status: 'error',
      message: `Teller configuration test failed: ${error.message}`,
      details: { error: error.toString() }
    };
    return false;
  }
};

/**
 * Test Analytics Configuration
 */
export const testAnalyticsConfiguration = () => {
  try {
    const gaTrackingId = import.meta.env.VITE_GA_TRACKING_ID;
    const gaMeasurementId = import.meta.env.GA_MEASUREMENT_ID;
    
    if (!gaTrackingId) {
      environmentTestResults.analytics = {
        status: 'warning',
        message: 'Google Analytics tracking ID not configured',
        details: { hasTrackingId: false, hasMeasurementId: !!gaMeasurementId }
      };
      return false;
    }
    
    environmentTestResults.analytics = {
      status: 'success',
      message: 'Analytics configuration complete',
      details: {
        trackingId: gaTrackingId,
        measurementId: gaMeasurementId,
        note: 'Analytics will track user interactions'
      }
    };
    return true;
    
  } catch (error) {
    environmentTestResults.analytics = {
      status: 'error',
      message: `Analytics test failed: ${error.message}`,
      details: { error: error.toString() }
    };
    return false;
  }
};

/**
 * Test OAuth Configuration
 */
export const testOAuthConfiguration = () => {
  try {
    const googleClientId = import.meta.env.GOOGLE_CLIENT_ID;
    const githubClientId = import.meta.env.GITHUB_CLIENT_ID;
    
    const hasOAuth = googleClientId || githubClientId;
    
    if (!hasOAuth) {
      environmentTestResults.oauth = {
        status: 'warning',
        message: 'No OAuth providers configured',
        details: { hasGoogle: false, hasGitHub: false }
      };
      return false;
    }
    
    environmentTestResults.oauth = {
      status: 'success',
      message: 'OAuth providers configured',
      details: {
        google: !!googleClientId,
        github: !!githubClientId,
        note: 'OAuth secrets are managed in Supabase dashboard'
      }
    };
    return true;
    
  } catch (error) {
    environmentTestResults.oauth = {
      status: 'error',
      message: `OAuth test failed: ${error.message}`,
      details: { error: error.toString() }
    };
    return false;
  }
};

/**
 * Run Complete Environment Test Suite
 */
export const runEnvironmentTests = async () => {
  console.log('🧪 Running Environment Test Suite...');
  
  const results = {
    supabase: await testSupabaseConnection(),
    teller: await testTellerConfiguration(),
    analytics: testAnalyticsConfiguration(),
    oauth: testOAuthConfiguration()
  };
  
  // Calculate overall score
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  const score = Math.round((passedTests / totalTests) * 100);
  
  let overallStatus = 'error';
  let overallMessage = 'Environment setup incomplete';
  
  if (score >= 75) {
    overallStatus = 'success';
    overallMessage = 'Environment setup complete and ready for development';
  } else if (score >= 50) {
    overallStatus = 'warning';
    overallMessage = 'Environment partially configured - some features may not work';
  }
  
  environmentTestResults.overall = {
    status: overallStatus,
    message: overallMessage,
    score: score
  };
  
  // Log results
  console.log('📊 Environment Test Results:');
  console.log(`Overall Score: ${score}%`);
  console.log('Detailed Results:', environmentTestResults);
  
  return {
    results: environmentTestResults,
    score,
    passed: score >= 75
  };
};

/**
 * Get Environment Status Summary
 */
export const getEnvironmentStatus = () => {
  const criticalServices = ['supabase'];
  const importantServices = ['teller', 'oauth'];
  const optionalServices = ['analytics'];
  
  const critical = criticalServices.every(service => 
    environmentTestResults[service]?.status === 'success'
  );
  
  const important = importantServices.filter(service => 
    environmentTestResults[service]?.status === 'success'
  ).length;
  
  const optional = optionalServices.filter(service => 
    environmentTestResults[service]?.status === 'success'
  ).length;
  
  return {
    critical: critical,
    important: `${important}/${importantServices.length}`,
    optional: `${optional}/${optionalServices.length}`,
    readyForDevelopment: critical && important >= 1,
    readyForProduction: critical && important === importantServices.length
  };
};

export default {
  runEnvironmentTests,
  getEnvironmentStatus,
  environmentTestResults
};
