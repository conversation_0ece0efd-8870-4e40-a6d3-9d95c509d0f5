// Cache Manager Utility
// Integration & Services Agent: Intelligent caching for API optimization

class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.timestamps = new Map();
    this.accessCounts = new Map();
    this.maxSize = options.maxSize || 100;
    this.defaultTTL = options.defaultTTL || 5 * 60 * 1000; // 5 minutes
    this.cleanupInterval = options.cleanupInterval || 60 * 1000; // 1 minute
    
    this.startCleanupTimer();
  }

  // Set cache entry with TTL
  set(key, value, ttl = this.defaultTTL) {
    // Cleanup if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, value);
    this.timestamps.set(key, Date.now() + ttl);
    this.accessCounts.set(key, 0);
    
    return value;
  }

  // Get cache entry
  get(key) {
    if (\!this.cache.has(key)) {
      return null;
    }

    // Check if expired
    const expiry = this.timestamps.get(key);
    if (Date.now() > expiry) {
      this.delete(key);
      return null;
    }

    // Update access count
    const count = this.accessCounts.get(key) || 0;
    this.accessCounts.set(key, count + 1);

    return this.cache.get(key);
  }

  // Check if key exists and is not expired
  has(key) {
    if (\!this.cache.has(key)) {
      return false;
    }

    const expiry = this.timestamps.get(key);
    if (Date.now() > expiry) {
      this.delete(key);
      return false;
    }

    return true;
  }

  // Delete cache entry
  delete(key) {
    this.cache.delete(key);
    this.timestamps.delete(key);
    this.accessCounts.delete(key);
  }

  // Clear all cache
  clear() {
    this.cache.clear();
    this.timestamps.clear();
    this.accessCounts.clear();
  }

  // Evict least recently used entries
  evictLeastUsed() {
    const entries = Array.from(this.accessCounts.entries());
    entries.sort((a, b) => a[1] - b[1]); // Sort by access count
    
    // Remove 25% of entries
    const toRemove = Math.ceil(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      this.delete(entries[i][0]);
    }
  }

  // Start cleanup timer
  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  // Cleanup expired entries
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, expiry] of this.timestamps.entries()) {
      if (now > expiry) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`🧹 Cache cleanup: removed ${expiredKeys.length} expired entries`);
    }
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    let expired = 0;
    let active = 0;

    for (const expiry of this.timestamps.values()) {
      if (now > expiry) {
        expired++;
      } else {
        active++;
      }
    }

    return {
      total: this.cache.size,
      active,
      expired,
      maxSize: this.maxSize,
      usage: (this.cache.size / this.maxSize) * 100
    };
  }
}

// API Cache with intelligent strategies
class APICache extends CacheManager {
  constructor(options = {}) {
    super({
      maxSize: 200,
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      ...options
    });
    
    this.strategies = {
      'GET': { ttl: 5 * 60 * 1000, cacheable: true },
      'POST': { ttl: 0, cacheable: false },
      'PUT': { ttl: 0, cacheable: false },
      'DELETE': { ttl: 0, cacheable: false },
      'PATCH': { ttl: 0, cacheable: false }
    };
    
    this.endpointStrategies = new Map();
  }

  // Set caching strategy for specific endpoint
  setEndpointStrategy(endpoint, strategy) {
    this.endpointStrategies.set(endpoint, strategy);
  }

  // Generate cache key for API request
  generateCacheKey(url, method, params = {}, headers = {}) {
    const keyData = {
      url: url.split('?')[0], // Remove query params from URL
      method,
      params: this.sortObject(params),
      headers: this.sortObject(this.filterHeaders(headers))
    };
    
    return btoa(JSON.stringify(keyData));
  }

  // Sort object keys for consistent cache keys
  sortObject(obj) {
    if (\!obj || typeof obj \!== 'object') return obj;
    
    const sorted = {};
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = obj[key];
    });
    return sorted;
  }

  // Filter headers for cache key generation
  filterHeaders(headers) {
    const relevantHeaders = {};
    const includedHeaders = ['authorization', 'content-type', 'accept'];
    
    for (const [key, value] of Object.entries(headers)) {
      if (includedHeaders.includes(key.toLowerCase())) {
        relevantHeaders[key] = value;
      }
    }
    
    return relevantHeaders;
  }

  // Check if request should be cached
  shouldCache(url, method, headers = {}) {
    // Check endpoint-specific strategy
    const endpoint = url.split('?')[0];
    const endpointStrategy = this.endpointStrategies.get(endpoint);
    
    if (endpointStrategy) {
      return endpointStrategy.cacheable;
    }

    // Check method-based strategy
    const methodStrategy = this.strategies[method.toUpperCase()];
    if (\!methodStrategy || \!methodStrategy.cacheable) {
      return false;
    }

    return true;
  }

  // Cache API response
  cacheResponse(url, method, params, headers, response) {
    if (\!this.shouldCache(url, method, headers)) {
      return response;
    }

    const cacheKey = this.generateCacheKey(url, method, params, headers);
    const ttl = this.getTTL(url, method);
    
    this.set(cacheKey, {
      response: JSON.parse(JSON.stringify(response)), // Deep clone
      timestamp: Date.now(),
      url,
      method
    }, ttl);

    return response;
  }

  // Get cached response
  getCachedResponse(url, method, params = {}, headers = {}) {
    if (\!this.shouldCache(url, method, headers)) {
      return null;
    }

    const cacheKey = this.generateCacheKey(url, method, params, headers);
    const cached = this.get(cacheKey);
    
    if (cached) {
      console.log(`📦 Cache hit: ${method} ${url}`);
      return cached.response;
    }

    return null;
  }

  // Get TTL for request
  getTTL(url, method) {
    const endpoint = url.split('?')[0];
    const endpointStrategy = this.endpointStrategies.get(endpoint);
    
    if (endpointStrategy) {
      return endpointStrategy.ttl;
    }

    const methodStrategy = this.strategies[method.toUpperCase()];
    return methodStrategy ? methodStrategy.ttl : this.defaultTTL;
  }

  // Invalidate cache for specific patterns
  invalidatePattern(pattern) {
    const keysToDelete = [];
    
    for (const [key, value] of this.cache.entries()) {
      if (value.url && value.url.includes(pattern)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.delete(key));
    console.log(`🗑️ Invalidated ${keysToDelete.length} cache entries for pattern: ${pattern}`);
  }

  // Invalidate cache for specific endpoint
  invalidateEndpoint(endpoint) {
    this.invalidatePattern(endpoint);
  }
}

// Create singleton instances
export const apiCache = new APICache();

// Utility functions
export const cacheAPI = (url, method, params, headers, response) => {
  return apiCache.cacheResponse(url, method, params, headers, response);
};

export const getCachedAPI = (url, method, params, headers) => {
  return apiCache.getCachedResponse(url, method, params, headers);
};

export const invalidateCache = (pattern) => {
  apiCache.invalidatePattern(pattern);
};

export const getCacheStats = () => {
  return apiCache.getStats();
};

// Enhanced fetch with caching
export const cachedFetch = async (url, options = {}) => {
  const { method = 'GET', headers = {}, body, ...otherOptions } = options;
  const params = body ? JSON.parse(body) : {};
  
  // Try to get from cache first
  const cached = getCachedAPI(url, method, params, headers);
  if (cached) {
    return {
      ok: true,
      status: 200,
      json: async () => cached,
      clone: () => ({ json: async () => cached })
    };
  }

  // Make actual request
  const response = await fetch(url, options);
  
  // Cache successful responses
  if (response.ok) {
    const clonedResponse = response.clone();
    const data = await clonedResponse.json();
    cacheAPI(url, method, params, headers, data);
  }

  return response;
};

export default {
  APICache,
  apiCache,
  cacheAPI,
  getCachedAPI,
  invalidateCache,
  getCacheStats,
  cachedFetch
};
