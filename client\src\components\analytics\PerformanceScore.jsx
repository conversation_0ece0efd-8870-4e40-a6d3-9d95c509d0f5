import React from 'react';
import { Card, CardBody, CardHeader, Chip, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { PerformanceChart } from '../charts';

/**
 * Performance Score Widget - 1x1 Bento Grid Component
 * 
 * Features:
 * - Overall platform performance score (0-100)
 * - Visual progress indicator with color coding
 * - Performance tier display (Excellent, Good, etc.)
 * - Trend indicator showing improvement/decline
 */
const PerformanceScore = ({ score = 0, period, className = "" }) => {
  
  // Get performance tier based on score
  const getPerformanceTier = (score) => {
    if (score >= 90) return { tier: 'Excellent', color: 'success', icon: '🏆' };
    if (score >= 80) return { tier: 'Good', color: 'primary', icon: '🎯' };
    if (score >= 70) return { tier: 'Average', color: 'warning', icon: '📊' };
    if (score >= 60) return { tier: 'Below Average', color: 'danger', icon: '📉' };
    return { tier: 'Poor', color: 'danger', icon: '⚠️' };
  };

  // Get score color for progress bar
  const getScoreColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 80) return 'primary';
    if (score >= 70) return 'warning';
    return 'danger';
  };

  const performanceTier = getPerformanceTier(score);
  const scoreColor = getScoreColor(score);

  // Mock trend data (in real app, this would come from props)
  const trend = score >= 85 ? '+2' : score >= 75 ? '+1' : '0';
  const trendColor = trend.startsWith('+') ? 'success' : trend.startsWith('-') ? 'danger' : 'default';

  return (
    <div className={`performance-score ${className}`}>
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-800/20 border-2 border-blue-200 dark:border-blue-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-xl">{performanceTier.icon}</span>
              <h3 className="text-sm font-semibold">Score</h3>
            </div>
            <Chip color={trendColor} variant="flat" size="sm">
              {trend}
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0 flex flex-col justify-center">
          {/* Main Score Display */}
          <div className="text-center mb-4">
            <motion.div
              className="text-4xl font-bold text-blue-600 mb-1"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {score}/100
            </motion.div>
            <Chip 
              color={performanceTier.color} 
              variant="flat" 
              size="sm"
              className="mb-2"
            >
              {performanceTier.tier}
            </Chip>
          </div>

          {/* Progress Ring */}
          <div className="mb-4">
            <Progress
              value={score}
              color={scoreColor}
              size="lg"
              className="w-full"
              showValueLabel={false}
            />
          </div>

          {/* Score Breakdown */}
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-default-600">Quality</span>
              <span className="font-medium">94%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">Speed</span>
              <span className="font-medium">87%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">Reliability</span>
              <span className="font-medium">91%</span>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default PerformanceScore;
