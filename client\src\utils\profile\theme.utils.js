/**
 * Generate CSS variables from theme settings - disabled to use HeroUI defaults
 * @param {Object} themeSettings - The theme settings object
 * @returns {string} CSS variables as a string
 */
export const generateCssVariables = (themeSettings) => {
  // Return empty string to let HeroUI handle all styling
  return '';
};

/**
 * Apply theme to the document
 * @param {Object} themeSettings - The theme settings object
 * @param {string} customCss - Custom CSS to apply
 * @returns {void}
 */
export const applyTheme = (themeSettings, customCss = '') => {
  // Remove any existing theme style element
  const existingStyle = document.getElementById('profile-theme-style');
  if (existingStyle) {
    existingStyle.remove();
  }

  // Create a new style element
  const styleElement = document.createElement('style');
  styleElement.id = 'profile-theme-style';

  // Generate CSS variables
  const cssVariables = generateCssVariables(themeSettings);

  // Apply the theme
  styleElement.textContent = `
    :root {
      ${cssVariables}
    }

    /* Custom CSS */
    ${customCss}
  `;

  // Add the style element to the head
  document.head.appendChild(styleElement);
};

/**
 * Get font URL for Google Fonts
 * @param {Object} themeSettings - The theme settings object
 * @returns {string} Google Fonts URL
 */
export const getFontUrl = (themeSettings) => {
  if (!themeSettings || !themeSettings.fonts) return null;

  const { heading, body } = themeSettings.fonts;
  const fonts = new Set([heading, body].filter(Boolean));

  if (fonts.size === 0) return null;

  const fontString = Array.from(fonts).join('|').replace(/ /g, '+');
  return `https://fonts.googleapis.com/css2?family=${fontString}&display=swap`;
};

/**
 * Load fonts for the theme
 * @param {Object} themeSettings - The theme settings object
 * @returns {void}
 */
export const loadThemeFonts = (themeSettings) => {
  // Remove any existing font link
  const existingLink = document.getElementById('profile-theme-fonts');
  if (existingLink) {
    existingLink.remove();
  }

  // Get the font URL
  const fontUrl = getFontUrl(themeSettings);
  if (!fontUrl) return;

  // Create a new link element
  const linkElement = document.createElement('link');
  linkElement.id = 'profile-theme-fonts';
  linkElement.rel = 'stylesheet';
  linkElement.href = fontUrl;

  // Add the link element to the head
  document.head.appendChild(linkElement);
};

/**
 * Get default theme settings - disabled to use HeroUI defaults
 * @returns {Object} Default theme settings
 */
export const getDefaultThemeSettings = () => {
  return {
    theme: 'heroui-default',
    colors: {}, // Let HeroUI handle all colors
    fonts: {
      heading: 'Inter',
      body: 'Inter'
    },
    layout: 'standard'
  };
};

/**
 * Get theme settings for a specific preset theme - disabled to use HeroUI defaults
 * @param {string} themeName - The name of the preset theme
 * @returns {Object} Theme settings for the preset theme
 */
export const getPresetThemeSettings = (themeName) => {
  // Return default HeroUI theme for all presets
  return getDefaultThemeSettings();
};

/**
 * Generate a CSS color palette preview
 * @param {Object} colors - The colors object
 * @returns {Object} HTML and CSS for the color palette preview
 */
export const generateColorPalettePreview = (colors) => {
  if (!colors) return { html: '', css: '' };

  const html = `
    <div class="color-palette-preview">
      <div class="color-swatch background-color"></div>
      <div class="color-swatch primary-color"></div>
      <div class="color-swatch secondary-color"></div>
      <div class="color-swatch text-color"></div>
      <div class="color-swatch accent-color"></div>
    </div>
  `;

  const css = `
    .color-palette-preview {
      display: flex;
      gap: 8px;
    }
    .color-swatch {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      border: 1px solid #ccc;
    }
    .background-color { background-color: ${colors.background}; }
    .primary-color { background-color: ${colors.primary}; }
    .secondary-color { background-color: ${colors.secondary}; }
    .text-color { background-color: ${colors.text}; }
    .accent-color { background-color: ${colors.accent}; }
  `;

  return { html, css };
};
