import React, { useState, useEffect } from 'react';
import ContributionEntryForm from './ContributionEntryForm';
import ContributionList from './ContributionList';
import ContributionSummary from './ContributionSummary';

const ContributionManager = ({ projectId, userId = null }) => {
  const [showForm, setShowForm] = useState(false);
  const [editingContribution, setEditingContribution] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Reset form when projectId changes
  useEffect(() => {
    setShowForm(false);
    setEditingContribution(null);
  }, [projectId]);

  // Handle form submission success
  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingContribution(null);
    setRefreshKey(prev => prev + 1);
  };

  // Handle edit button click
  const handleEdit = (contribution) => {
    setEditingContribution(contribution);
    setShowForm(true);

    // Scroll to form
    setTimeout(() => {
      document.getElementById('contribution-form-section')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Handle delete success
  const handleDeleteSuccess = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="contribution-manager">
      <div className="contribution-manager-header">
        <h3 className="section-title">Contribution Tracking</h3>

        {!showForm && (
          <button
            className="btn btn-primary add-contribution-btn"
            onClick={() => setShowForm(true)}
          >
            <i className="bi bi-plus-lg"></i> Add Contribution
          </button>
        )}
      </div>

      {/* Form Section */}
      {showForm && (
        <div id="contribution-form-section" className="contribution-form-section">
          <div className="form-header">
            <h4>{editingContribution ? 'Edit Contribution' : 'Add New Contribution'}</h4>
            <button
              className="btn-close"
              onClick={() => {
                setShowForm(false);
                setEditingContribution(null);
              }}
              aria-label="Close"
            >
              <i className="bi bi-x-lg"></i>
            </button>
          </div>

          <ContributionEntryForm
            projectId={projectId}
            initialData={editingContribution}
            isEditing={!!editingContribution}
            onSuccess={handleFormSuccess}
            onCancel={() => {
              setShowForm(false);
              setEditingContribution(null);
            }}
          />
        </div>
      )}

      {/* Summary Section */}
      <div className="contribution-summary-section">
        <h4 className="section-subtitle">Summary</h4>
        <ContributionSummary
          projectId={projectId}
          userId={userId}
          key={`summary-${refreshKey}`}
        />
      </div>

      {/* List Section */}
      <div className="contribution-list-section">
        <h4 className="section-subtitle">Recent Contributions</h4>
        <ContributionList
          projectId={projectId}
          userId={userId}
          onEdit={handleEdit}
          onDelete={handleDeleteSuccess}
          key={`list-${refreshKey}`}
        />
      </div>
    </div>
  );
};

export default ContributionManager;
