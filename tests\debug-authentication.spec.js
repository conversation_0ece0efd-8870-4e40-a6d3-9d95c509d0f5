import { test, expect } from '@playwright/test';

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

test.describe('Debug Authentication', () => {
  test('Debug Authentication Process', async ({ page }) => {
    console.log('🔍 Starting authentication debug...');
    
    // Go to homepage
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take initial screenshot
    await page.screenshot({ path: 'test-results/debug-auth-initial.png', fullPage: true });
    console.log('📸 Initial page screenshot saved');
    
    // Check what's actually on the page
    const hasEmailInput = await page.locator('input[type="email"]').isVisible();
    const hasPasswordInput = await page.locator('input[type="password"]').isVisible();
    const hasSignInButton = await page.locator('text="Sign In"').isVisible();
    const hasLoginForm = hasEmailInput && hasPasswordInput;
    
    console.log(`🔐 Authentication state check:`);
    console.log(`  - Email input visible: ${hasEmailInput}`);
    console.log(`  - Password input visible: ${hasPasswordInput}`);
    console.log(`  - Sign In button visible: ${hasSignInButton}`);
    console.log(`  - Has login form: ${hasLoginForm}`);
    
    if (!hasLoginForm) {
      console.log('❌ NO LOGIN FORM FOUND - Cannot authenticate');
      
      // Check if we're already on a login page
      const currentUrl = page.url();
      console.log(`📍 Current URL: ${currentUrl}`);
      
      if (currentUrl.includes('/login')) {
        console.log('✅ Already on login page');
      } else {
        console.log('🔄 Trying to navigate to login page...');
        
        // Try clicking Sign In button
        if (hasSignInButton) {
          await page.click('text="Sign In"');
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          
          const newUrl = page.url();
          console.log(`📍 After clicking Sign In: ${newUrl}`);
          
          await page.screenshot({ path: 'test-results/debug-auth-after-signin-click.png', fullPage: true });
          
          // Check for login form again
          const nowHasEmailInput = await page.locator('input[type="email"]').isVisible();
          const nowHasPasswordInput = await page.locator('input[type="password"]').isVisible();
          console.log(`  - Email input now visible: ${nowHasEmailInput}`);
          console.log(`  - Password input now visible: ${nowHasPasswordInput}`);
          
          if (nowHasEmailInput && nowHasPasswordInput) {
            console.log('✅ Login form now available');
            
            // Try to authenticate
            console.log('📝 Attempting to fill credentials...');
            await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
            await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
            
            await page.screenshot({ path: 'test-results/debug-auth-credentials-filled.png', fullPage: true });
            
            // Look for submit button
            const submitSelectors = [
              'button[type="submit"]',
              'button:has-text("Sign In")',
              'button:has-text("Login")',
              'button:has-text("Log In")',
              'input[type="submit"]'
            ];
            
            let submitButton = null;
            for (const selector of submitSelectors) {
              const element = page.locator(selector);
              if (await element.count() > 0 && await element.isVisible()) {
                submitButton = element;
                console.log(`✅ Found submit button: ${selector}`);
                break;
              }
            }
            
            if (submitButton) {
              console.log('🔄 Clicking submit button...');
              await submitButton.click();
              await page.waitForLoadState('networkidle');
              await page.waitForTimeout(5000);
              
              const finalUrl = page.url();
              console.log(`📍 After login attempt: ${finalUrl}`);
              
              await page.screenshot({ path: 'test-results/debug-auth-after-login.png', fullPage: true });
              
              // Check if login was successful
              const stillHasLoginForm = await page.locator('input[type="email"]').isVisible();
              const hasSignInButtonAfter = await page.locator('text="Sign In"').isVisible();
              
              console.log(`🎯 Login result:`);
              console.log(`  - Still has login form: ${stillHasLoginForm}`);
              console.log(`  - Still has Sign In button: ${hasSignInButtonAfter}`);
              console.log(`  - URL changed: ${finalUrl !== newUrl}`);
              
              if (!stillHasLoginForm && !hasSignInButtonAfter) {
                console.log('✅ LOGIN SUCCESSFUL');
                
                // Test navigation after successful login
                console.log('🧭 Testing navigation after login...');
                
                const startButton = page.locator('text="Start"').first();
                if (await startButton.isVisible()) {
                  await startButton.click();
                  await page.waitForLoadState('networkidle');
                  await page.waitForTimeout(2000);
                  
                  const navUrl = page.url();
                  console.log(`📍 After clicking Start: ${navUrl}`);
                  
                  if (navUrl.includes('/start')) {
                    console.log('✅ Navigation works after authentication');
                  } else if (navUrl.includes('/login')) {
                    console.log('❌ Still redirecting to login - authentication not persistent');
                  } else {
                    console.log(`❓ Unexpected navigation result: ${navUrl}`);
                  }
                }
                
              } else {
                console.log('❌ LOGIN FAILED');
                
                // Check for error messages
                const errorSelectors = [
                  '.error',
                  '[class*="error"]',
                  'text="Invalid"',
                  'text="incorrect"',
                  'text="failed"'
                ];
                
                for (const selector of errorSelectors) {
                  const errorElement = page.locator(selector);
                  if (await errorElement.count() > 0) {
                    const errorText = await errorElement.textContent();
                    console.log(`⚠️ Error message: ${errorText}`);
                  }
                }
              }
              
            } else {
              console.log('❌ No submit button found');
            }
            
          } else {
            console.log('❌ Login form still not available after clicking Sign In');
          }
        } else {
          console.log('❌ No Sign In button to click');
        }
      }
      
      return;
    }
    
    console.log('✅ Login form found on initial page load');
    // Continue with authentication...
    
    console.log('✅ Authentication debug completed');
  });
});
