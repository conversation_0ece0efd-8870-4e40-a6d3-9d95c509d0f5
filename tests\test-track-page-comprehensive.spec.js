import { test, expect } from '@playwright/test';

test.describe('Track Page - Comprehensive Testing', () => {
  test('should load Track page without creating duplicate projects', async ({ page }) => {
    console.log('🎯 Starting comprehensive Track page test');
    
    // Navigate to the site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login
    console.log('🔐 Logging in...');
    await page.getByText('Login').click();
    await page.waitForSelector('input[type="email"]', { timeout: 10000 });
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.getByRole('button', { name: 'Sign In' }).click();
    
    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard"]', { timeout: 15000 });
    console.log('✅ Dashboard loaded');
    
    // Navigate to Track page first time
    console.log('🎯 First navigation to Track page');
    await page.getByText('Track').click();
    await page.waitForSelector('h1:has-text("Project Management Hub")', { timeout: 15000 });
    
    // Wait for project to load and capture project count
    await page.waitForTimeout(3000);
    const firstVisitProjectCount = await page.locator('[data-testid="project-overview"] .text-2xl').first().textContent();
    console.log(`📊 First visit - Active Projects: ${firstVisitProjectCount}`);
    
    // Navigate away and back to Track page
    console.log('🔄 Navigating away from Track page');
    await page.getByText('Start').click();
    await page.waitForTimeout(2000);
    
    console.log('🎯 Second navigation to Track page');
    await page.getByText('Track').click();
    await page.waitForSelector('h1:has-text("Project Management Hub")', { timeout: 15000 });
    
    // Wait for project to load and capture project count again
    await page.waitForTimeout(3000);
    const secondVisitProjectCount = await page.locator('[data-testid="project-overview"] .text-2xl').first().textContent();
    console.log(`📊 Second visit - Active Projects: ${secondVisitProjectCount}`);
    
    // Verify project count hasn't increased (no duplicate creation)
    expect(secondVisitProjectCount).toBe(firstVisitProjectCount);
    console.log('✅ No duplicate projects created');
    
    // Navigate away and back one more time to be sure
    console.log('🔄 Third navigation test');
    await page.getByText('Earn').click();
    await page.waitForTimeout(2000);
    
    console.log('🎯 Third navigation to Track page');
    await page.getByText('Track').click();
    await page.waitForSelector('h1:has-text("Project Management Hub")', { timeout: 15000 });
    
    await page.waitForTimeout(3000);
    const thirdVisitProjectCount = await page.locator('[data-testid="project-overview"] .text-2xl').first().textContent();
    console.log(`📊 Third visit - Active Projects: ${thirdVisitProjectCount}`);
    
    // Final verification
    expect(thirdVisitProjectCount).toBe(firstVisitProjectCount);
    console.log('✅ Project creation bug is fixed - no duplicates created');
    
    // Check for layout and styling
    console.log('🎨 Checking layout and styling');
    
    // Verify main components are visible
    await expect(page.locator('h1:has-text("Project Management Hub")')).toBeVisible();
    await expect(page.locator('[data-testid="project-overview"]')).toBeVisible();
    await expect(page.locator('[data-testid="quick-actions"]')).toBeVisible();
    await expect(page.locator('[data-testid="task-board"]')).toBeVisible();
    
    // Check responsive layout
    const viewport = page.viewportSize();
    console.log(`📱 Current viewport: ${viewport.width}x${viewport.height}`);
    
    // Take screenshot for visual verification
    await page.screenshot({ path: 'test-results/track-page-layout.png', fullPage: true });
    console.log('📸 Screenshot saved for layout verification');
    
    // Check for any console errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Monitor network requests for any 400 errors
    const networkErrors = [];
    page.on('response', response => {
      if (response.status() === 400) {
        networkErrors.push(`${response.status()} ${response.url()}`);
      }
    });
    
    // Wait a bit more to catch any delayed errors
    await page.waitForTimeout(5000);
    
    console.log(`🔍 Console errors found: ${consoleErrors.length}`);
    console.log(`🔍 Network 400 errors found: ${networkErrors.length}`);
    
    if (networkErrors.length > 0) {
      console.log('❌ Network errors:', networkErrors);
    }
    
    console.log('✅ Comprehensive Track page test completed');
  });
});
