/**
 * Replacement Engine - Single-Pass Variable Replacement
 * 
 * Handles all variable replacement in a single pass with comprehensive validation.
 * Supports conditional logic and ensures no unreplaced variables remain.
 */

import { ReplacementError, ErrorFactory } from './errors/AgreementErrors.js';

export class ReplacementEngine {
  constructor() {
    // Variable patterns
    this.variablePattern = /\{\{([A-Z_]+)\}\}/g;
    this.conditionalPattern = /\{\{#IF\s+([A-Z_]+)\}\}([\s\S]*?)\{\{\/IF\}\}/g;
    
    // Date formatting options
    this.dateOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
  }

  /**
   * Process template with single-pass replacement
   * @param {string} template - Template content with variables
   * @param {Object} data - Validated data for replacement
   * @returns {Promise<string>} Processed template with all variables replaced
   * @throws {ReplacementError} If replacement fails
   */
  async processTemplate(template, data) {
    try {
      let result = template;

      // Step 1: Process conditional blocks first
      result = this._processConditionals(result, data);

      // Step 2: Replace all variables in single pass
      result = this._replaceVariables(result, data);

      // Step 3: Validate no unreplaced variables remain
      this._validateNoPlaceholders(result);

      // Step 4: Final formatting and cleanup
      result = this._finalFormatting(result);

      return result;

    } catch (error) {
      if (error instanceof ReplacementError) {
        throw error;
      }
      throw new ReplacementError(
        'Template processing failed',
        null,
        [{ message: error.message, stack: error.stack }]
      );
    }
  }

  /**
   * Extract all variables from template
   * @param {string} template - Template content
   * @returns {Array} List of variables with metadata
   */
  extractVariables(template) {
    const variables = [];
    const variableSet = new Set();

    // Extract regular variables
    let match;
    const variableRegex = new RegExp(this.variablePattern.source, 'g');
    while ((match = variableRegex.exec(template)) !== null) {
      const variableName = match[1];
      if (!variableSet.has(variableName)) {
        variables.push({
          name: variableName,
          type: 'variable',
          required: true,
          pattern: `{{${variableName}}}`
        });
        variableSet.add(variableName);
      }
    }

    // Extract conditional variables
    const conditionalRegex = new RegExp(this.conditionalPattern.source, 'g');
    while ((match = conditionalRegex.exec(template)) !== null) {
      const variableName = match[1];
      if (!variableSet.has(variableName)) {
        variables.push({
          name: variableName,
          type: 'conditional',
          required: false,
          pattern: `{{#IF ${variableName}}}`
        });
        variableSet.add(variableName);
      }
    }

    return variables;
  }

  // Private methods

  /**
   * Process conditional blocks
   * @private
   */
  _processConditionals(template, data) {
    return template.replace(this.conditionalPattern, (match, condition, content) => {
      const shouldInclude = this._evaluateCondition(condition, data);
      return shouldInclude ? content : '';
    });
  }

  /**
   * Evaluate conditional expression
   * @private
   */
  _evaluateCondition(condition, data) {
    // Handle project type conditions
    if (condition.startsWith('PROJECT_TYPE_')) {
      const projectType = condition.replace('PROJECT_TYPE_', '').toLowerCase();
      return data.project.projectType === projectType;
    }

    // Handle other boolean conditions
    const value = this._resolveVariable(condition, data);
    return Boolean(value);
  }

  /**
   * Replace all variables in single pass
   * @private
   */
  _replaceVariables(template, data) {
    return template.replace(this.variablePattern, (match, variableName) => {
      try {
        const value = this._resolveVariable(variableName, data);
        if (value === undefined || value === null) {
          throw ErrorFactory.createUnreplacedVariableError(variableName);
        }
        return value;
      } catch (error) {
        throw new ReplacementError(
          `Failed to replace variable: ${variableName}`,
          variableName,
          [{ message: error.message, variable: variableName }]
        );
      }
    });
  }

  /**
   * Resolve variable value from data
   * @private
   */
  _resolveVariable(variableName, data) {
    const variableMap = {
      // Company information
      'COMPANY_NAME': data.company.name.toUpperCase(),
      'COMPANY_LEGAL_NAME': data.company.name,
      'COMPANY_STATE': data.company.state,
      'COMPANY_ADDRESS': data.company.address,
      'COMPANY_SIGNER_NAME': data.company.signerName,
      'COMPANY_SIGNER_TITLE': data.company.signerTitle,
      'COMPANY_BILLING_EMAIL': data.company.billingEmail,

      // Project information
      'PROJECT_NAME': data.project.name,
      'PROJECT_DESCRIPTION': data.project.description,
      'PROJECT_TYPE': data.project.projectType,

      // Contributor information
      'CONTRIBUTOR_NAME': data.contributor.name,
      'CONTRIBUTOR_EMAIL': data.contributor.email,
      'CONTRIBUTOR_ADDRESS': data.contributor.address || '',

      // System-generated values
      'EFFECTIVE_DATE': this._formatDate(new Date()),

      // Project type flags for conditionals
      'PROJECT_TYPE_GAME': data.project.projectType === 'game',
      'PROJECT_TYPE_SOFTWARE': data.project.projectType === 'software',
      'PROJECT_TYPE_MUSIC': data.project.projectType === 'music',
      'PROJECT_TYPE_FILM': data.project.projectType === 'film',
      'PROJECT_TYPE_ART': data.project.projectType === 'art',
      'PROJECT_TYPE_BOOK': data.project.projectType === 'book',
      'PROJECT_TYPE_APP': data.project.projectType === 'app'
    };

    const value = variableMap[variableName];
    
    if (value === undefined) {
      throw new Error(`Unknown variable: ${variableName}`);
    }

    return value;
  }

  /**
   * Validate no unreplaced placeholders remain
   * @private
   */
  _validateNoPlaceholders(content) {
    const unreplacedPatterns = [
      /\{\{[A-Z_]+\}\}/g,           // {{VARIABLE}} format
      /\{\{#IF\s+[A-Z_]+\}\}/g,    // {{#IF CONDITION}} format
      /\{\{\/IF\}\}/g,             // {{/IF}} format
      /\[[A-Z][^\]]*\]/g,          // [OLD STYLE] format
      /\[_+\]/g                    // [____] format
    ];

    const foundPlaceholders = [];

    unreplacedPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        foundPlaceholders.push(...matches);
      }
    });

    if (foundPlaceholders.length > 0) {
      throw new ReplacementError(
        `Unreplaced placeholders found: ${foundPlaceholders.join(', ')}`,
        null,
        foundPlaceholders.map(placeholder => ({
          placeholder,
          message: 'Placeholder was not replaced with actual data',
          type: 'UNREPLACED_PLACEHOLDER'
        }))
      );
    }
  }

  /**
   * Final formatting and cleanup
   * @private
   */
  _finalFormatting(content) {
    // Remove extra whitespace
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // Ensure proper spacing around headers
    content = content.replace(/^(#{1,6}\s+.+)$/gm, '\n$1\n');
    
    // Clean up any remaining formatting issues
    content = content.replace(/\n{3,}/g, '\n\n');
    
    // Trim leading/trailing whitespace
    content = content.trim();
    
    return content;
  }

  /**
   * Format date for legal documents
   * @private
   */
  _formatDate(date) {
    return date.toLocaleDateString('en-US', this.dateOptions);
  }

  /**
   * Get replacement statistics
   */
  getReplacementStats(template, data) {
    const variables = this.extractVariables(template);
    const stats = {
      totalVariables: variables.length,
      requiredVariables: variables.filter(v => v.required).length,
      conditionalVariables: variables.filter(v => v.type === 'conditional').length,
      replacementMap: {}
    };

    // Build replacement map for debugging
    variables.forEach(variable => {
      try {
        const value = this._resolveVariable(variable.name, data);
        stats.replacementMap[variable.name] = {
          value: value,
          type: typeof value,
          length: String(value).length
        };
      } catch (error) {
        stats.replacementMap[variable.name] = {
          error: error.message,
          type: 'error'
        };
      }
    });

    return stats;
  }

  /**
   * Validate template before processing
   */
  validateTemplate(template) {
    const issues = [];

    // Check for old-style placeholders
    const oldStylePatterns = [
      /\[Company[^\]]*\]/g,
      /\[Project[^\]]*\]/g,
      /\[Contributor[^\]]*\]/g,
      /\[COMPANY[^\]]*\]/g,
      /\[PROJECT[^\]]*\]/g
    ];

    oldStylePatterns.forEach(pattern => {
      const matches = template.match(pattern);
      if (matches) {
        matches.forEach(match => {
          issues.push({
            type: 'OLD_STYLE_PLACEHOLDER',
            message: `Old-style placeholder found: ${match}`,
            placeholder: match
          });
        });
      }
    });

    // Check for hardcoded content
    const forbiddenContent = [
      'City of Gamers Inc.',
      'Village of The Ages',
      'Gynell Journigan',
      '1205 43rd Street',
      'Orange County',
      '32839'
    ];

    forbiddenContent.forEach(content => {
      if (template.includes(content)) {
        issues.push({
          type: 'HARDCODED_CONTENT',
          message: `Hardcoded content found: ${content}`,
          content: content
        });
      }
    });

    return {
      valid: issues.length === 0,
      issues: issues
    };
  }
}
