// Chart Components Export Index
// Comprehensive chart library for Royaltea platform

// Analytics Charts (Task J7)
export { default as RevenueChart } from './RevenueChart';
export { default as GrowthChart } from './GrowthChart';
export { default as PerformanceChart } from './PerformanceChart';

// Revenue Charts (Task J8)
export { default as EarningsChart } from './EarningsChart';
export { default as PayoutChart } from './PayoutChart';

// Chart utilities and helpers
export const chartColors = {
  primary: '#3b82f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  secondary: '#8b5cf6',
  info: '#06b6d4'
};

export const chartTheme = {
  grid: '#e2e8f0',
  text: '#64748b',
  background: '#ffffff',
  darkBackground: '#1e293b'
};

// Common chart configurations
export const defaultChartProps = {
  height: 300,
  showLegend: true,
  responsive: true,
  animated: true
};

// Chart data formatters
export const formatCurrency = (value) => `$${value.toLocaleString()}`;
export const formatPercentage = (value) => `${value}%`;
export const formatNumber = (value) => value.toLocaleString();
