-- Create activity_feeds table for enhanced analytics
CREATE TABLE IF NOT EXISTS public.activity_feeds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    actor_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    target_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    alliance_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    conversation_id UUID,
    activity_type TEXT NOT NULL,
    activity_title TEXT NOT NULL,
    activity_description TEXT,
    view_count INTEGER DEFAULT 0,
    reaction_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_activity_feeds_actor ON public.activity_feeds(actor_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_target ON public.activity_feeds(target_user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_feeds_project ON public.activity_feeds(project_id, created_at DESC);

-- Enable RLS
ALTER TABLE public.activity_feeds ENABLE ROW LEVEL SECURITY;

-- RLS Policy
CREATE POLICY "Users can view activity feeds" ON public.activity_feeds
    FOR SELECT USING (true);

CREATE POLICY "Users can create activity feeds" ON public.activity_feeds
    FOR INSERT WITH CHECK (auth.uid() = actor_id);

-- Create activity_reactions table
CREATE TABLE IF NOT EXISTS public.activity_reactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_id UUID REFERENCES public.activity_feeds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction_type TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(activity_id, user_id, reaction_type)
);

-- Add indexes for reactions
CREATE INDEX IF NOT EXISTS idx_activity_reactions_activity ON public.activity_reactions(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_reactions_user ON public.activity_reactions(user_id);

-- Enable RLS for reactions
ALTER TABLE public.activity_reactions ENABLE ROW LEVEL SECURITY;

-- RLS policies for reactions
CREATE POLICY "Users can view reactions" ON public.activity_reactions
    FOR SELECT USING (true);

CREATE POLICY "Users can manage own reactions" ON public.activity_reactions
    FOR ALL USING (auth.uid() = user_id);
