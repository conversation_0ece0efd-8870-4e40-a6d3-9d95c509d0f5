/**
 * Agreement Validator
 * 
 * This module provides functionality for validating agreement content for legal completeness
 * and correctness. It checks for required sections, legal clauses, and proper formatting.
 */

/**
 * Required sections that must be present in all agreements
 */
export const REQUIRED_SECTIONS = {
  HEADER: {
    name: 'Header',
    pattern: /^# .+/m,
    description: 'Agreement title and header'
  },
  BACKGROUND: {
    name: 'Background',
    pattern: /## BACKGROUND/m,
    description: 'Background section explaining the purpose of the agreement'
  },
  AGREEMENT: {
    name: 'Agreement',
    pattern: /## AGREEMENT/m,
    description: 'Main agreement section'
  },
  SERVICES: {
    name: 'Services',
    pattern: /## [0-9]+\.\s*SERVICES|## SERVICES/m,
    description: 'Services section defining the work to be performed'
  },
  COMPENSATION: {
    name: 'Compensation',
    pattern: /## [0-9]+\.\s*COMPENSATION|## COMPENSATION/m,
    description: 'Compensation section defining payment terms'
  },
  INTELLECTUAL_PROPERTY: {
    name: 'Intellectual Property',
    pattern: /## [0-9]+\.\s*INTELLECTUAL PROPERTY|## INTELLECTUAL PROPERTY/m,
    description: 'Intellectual property section defining ownership of work'
  },
  CONFIDENTIALITY: {
    name: 'Confidentiality',
    pattern: /## [0-9]+\.\s*CONFIDENTIALITY|## CONFIDENTIALITY/m,
    description: 'Confidentiality section protecting sensitive information'
  },
  TERM_TERMINATION: {
    name: 'Term and Termination',
    pattern: /## [0-9]+\.\s*TERM AND TERMINATION|## TERM AND TERMINATION/m,
    description: 'Term and termination section defining agreement duration'
  },
  SIGNATURES: {
    name: 'Signatures',
    pattern: /IN WITNESS WHEREOF|COMPANY:|CONTRIBUTOR:/m,
    description: 'Signature section for parties to sign'
  },
  SCHEDULE_A: {
    name: 'Schedule A',
    pattern: /## SCHEDULE A/m,
    description: 'Schedule A defining services description'
  },
  SCHEDULE_B: {
    name: 'Schedule B',
    pattern: /## SCHEDULE B/m,
    description: 'Schedule B defining compensation details'
  },
  EXHIBIT_I: {
    name: 'Exhibit I',
    pattern: /## EXHIBIT I/m,
    description: 'Exhibit I defining project specifications'
  },
  EXHIBIT_II: {
    name: 'Exhibit II',
    pattern: /## EXHIBIT II/m,
    description: 'Exhibit II defining project roadmap'
  }
};

/**
 * Required legal clauses that must be present in all agreements
 */
export const REQUIRED_LEGAL_CLAUSES = {
  INTELLECTUAL_PROPERTY_ASSIGNMENT: {
    name: 'IP Assignment',
    pattern: /assign|assigns|assigned|assignment|transfer|transfers|transferred|convey|conveys|conveyed/i,
    section: 'INTELLECTUAL_PROPERTY',
    description: 'Clause assigning intellectual property rights to the company'
  },
  CONFIDENTIALITY_OBLIGATION: {
    name: 'Confidentiality Obligation',
    pattern: /confidential|confidentiality|non-disclosure|nondisclosure/i,
    section: 'CONFIDENTIALITY',
    description: 'Clause defining confidentiality obligations'
  },
  TERM_DURATION: {
    name: 'Term Duration',
    pattern: /shall commence|effective date|duration|period|term/i,
    section: 'TERM_TERMINATION',
    description: 'Clause defining the duration of the agreement'
  },
  TERMINATION_RIGHTS: {
    name: 'Termination Rights',
    pattern: /terminate|termination|cancel|cancellation/i,
    section: 'TERM_TERMINATION',
    description: 'Clause defining termination rights'
  },
  GOVERNING_LAW: {
    name: 'Governing Law',
    pattern: /govern|governed|governing law|jurisdiction/i,
    section: null, // Can be in any section
    description: 'Clause defining the governing law for the agreement'
  },
  ENTIRE_AGREEMENT: {
    name: 'Entire Agreement',
    pattern: /entire agreement|complete agreement|integrated agreement/i,
    section: null, // Can be in any section
    description: 'Clause stating that this is the entire agreement between the parties'
  },
  REVENUE_SHARE: {
    name: 'Revenue Share',
    pattern: /revenue share|royalty|royalties|percentage|share of revenue/i,
    section: 'COMPENSATION',
    description: 'Clause defining revenue share or royalty payments'
  }
};

/**
 * Formatting requirements for agreements
 */
export const FORMATTING_REQUIREMENTS = {
  SECTION_HEADERS: {
    name: 'Section Headers',
    pattern: /^## [A-Z0-9\s\.]+$/m,
    minOccurrences: 5,
    description: 'Section headers should be formatted as ## SECTION NAME'
  },
  SUBSECTION_HEADERS: {
    name: 'Subsection Headers',
    pattern: /^### [A-Z0-9\s\.]+$/m,
    minOccurrences: 2,
    description: 'Subsection headers should be formatted as ### SUBSECTION NAME'
  },
  NUMBERED_SECTIONS: {
    name: 'Numbered Sections',
    pattern: /^[0-9]+\.\s+/m,
    minOccurrences: 3,
    description: 'Numbered sections should be formatted as 1. SECTION NAME'
  },
  BLANK_LINES_BETWEEN_SECTIONS: {
    name: 'Blank Lines Between Sections',
    pattern: /\n\n## /g,
    minOccurrences: 3,
    description: 'There should be blank lines between sections'
  }
};

/**
 * Project-specific requirements based on project type
 */
export const PROJECT_TYPE_REQUIREMENTS = {
  game: {
    sections: ['GAME_MECHANICS', 'PLATFORMS'],
    clauses: ['ENGINE_LICENSE', 'DISTRIBUTION_RIGHTS'],
    terms: ['game', 'player', 'gameplay', 'levels']
  },
  software: {
    sections: ['SOFTWARE_REQUIREMENTS', 'USER_INTERFACE'],
    clauses: ['SOFTWARE_LICENSE', 'MAINTENANCE'],
    terms: ['software', 'user', 'interface', 'application']
  },
  music: {
    sections: ['MUSIC_COMPOSITION', 'RECORDING'],
    clauses: ['MUSIC_RIGHTS', 'PERFORMANCE_RIGHTS'],
    terms: ['music', 'track', 'recording', 'composition']
  },
  film: {
    sections: ['FILMING', 'EDITING'],
    clauses: ['FILM_RIGHTS', 'DISTRIBUTION_RIGHTS'],
    terms: ['film', 'video', 'scene', 'editing']
  }
};

/**
 * Validate an agreement for completeness and correctness
 * @param {string} agreementText - The agreement text to validate
 * @param {string} projectType - The project type (game, software, music, film)
 * @returns {Object} - Validation results
 */
export const validateAgreement = (agreementText, projectType = 'game') => {
  const results = {
    isValid: true,
    missingSections: [],
    missingClauses: [],
    formattingIssues: [],
    projectTypeIssues: [],
    warnings: [],
    score: 0,
    maxScore: 0
  };

  // Check for required sections
  Object.values(REQUIRED_SECTIONS).forEach(section => {
    results.maxScore += 10;
    if (!section.pattern.test(agreementText)) {
      results.isValid = false;
      results.missingSections.push(section);
    } else {
      results.score += 10;
    }
  });

  // Check for required legal clauses
  Object.values(REQUIRED_LEGAL_CLAUSES).forEach(clause => {
    results.maxScore += 15;
    if (!clause.pattern.test(agreementText)) {
      results.isValid = false;
      results.missingClauses.push(clause);
    } else {
      results.score += 15;
    }
  });

  // Check formatting requirements
  Object.values(FORMATTING_REQUIREMENTS).forEach(requirement => {
    results.maxScore += 5;
    const matches = agreementText.match(requirement.pattern) || [];
    if (matches.length < requirement.minOccurrences) {
      results.formattingIssues.push({
        ...requirement,
        found: matches.length,
        required: requirement.minOccurrences
      });
    } else {
      results.score += 5;
    }
  });

  // Check project-specific requirements
  if (projectType && PROJECT_TYPE_REQUIREMENTS[projectType]) {
    const typeReqs = PROJECT_TYPE_REQUIREMENTS[projectType];
    
    // Check for project-specific terms
    results.maxScore += 10;
    const termMatches = typeReqs.terms.filter(term => 
      new RegExp(`\\b${term}\\b`, 'i').test(agreementText)
    );
    
    if (termMatches.length < typeReqs.terms.length / 2) {
      results.projectTypeIssues.push({
        name: `${projectType.charAt(0).toUpperCase() + projectType.slice(1)} Terminology`,
        description: `Agreement should include ${projectType}-specific terminology`,
        found: termMatches,
        missing: typeReqs.terms.filter(term => !termMatches.includes(term))
      });
    } else {
      results.score += 10;
    }
  }

  // Check for placeholders that weren't replaced
  const placeholderPattern = /\[.*?\]|\{.*?\}|\<.*?\>/g;
  const placeholders = agreementText.match(placeholderPattern) || [];
  if (placeholders.length > 0) {
    results.warnings.push({
      name: 'Unreplaced Placeholders',
      description: 'Agreement contains placeholders that were not replaced',
      placeholders
    });
  }

  // Check for empty sections
  const emptySectionPattern = /##\s+[A-Z\s]+\s*\n\s*\n\s*##/g;
  const emptySections = agreementText.match(emptySectionPattern) || [];
  if (emptySections.length > 0) {
    results.warnings.push({
      name: 'Empty Sections',
      description: 'Agreement contains empty sections',
      count: emptySections.length
    });
  }

  // Calculate final score as a percentage
  results.scorePercentage = Math.round((results.score / results.maxScore) * 100);

  return results;
};

/**
 * Get suggestions for improving an agreement based on validation results
 * @param {Object} validationResults - The validation results from validateAgreement
 * @returns {Array<string>} - Array of improvement suggestions
 */
export const getImprovementSuggestions = (validationResults) => {
  const suggestions = [];

  // Suggestions for missing sections
  if (validationResults.missingSections.length > 0) {
    validationResults.missingSections.forEach(section => {
      suggestions.push(`Add a ${section.name} section: ${section.description}`);
    });
  }

  // Suggestions for missing clauses
  if (validationResults.missingClauses.length > 0) {
    validationResults.missingClauses.forEach(clause => {
      suggestions.push(`Add a ${clause.name} clause: ${clause.description}`);
    });
  }

  // Suggestions for formatting issues
  if (validationResults.formattingIssues.length > 0) {
    validationResults.formattingIssues.forEach(issue => {
      suggestions.push(`Improve ${issue.name} formatting: ${issue.description}. Found ${issue.found}, need at least ${issue.required}.`);
    });
  }

  // Suggestions for project-specific issues
  if (validationResults.projectTypeIssues.length > 0) {
    validationResults.projectTypeIssues.forEach(issue => {
      suggestions.push(`Enhance ${issue.name}: ${issue.description}. Missing terms: ${issue.missing.join(', ')}`);
    });
  }

  // Suggestions for warnings
  if (validationResults.warnings.length > 0) {
    validationResults.warnings.forEach(warning => {
      if (warning.name === 'Unreplaced Placeholders') {
        suggestions.push(`Replace all placeholders: ${warning.placeholders.join(', ')}`);
      } else if (warning.name === 'Empty Sections') {
        suggestions.push(`Fill in empty sections: ${warning.count} empty sections found`);
      }
    });
  }

  return suggestions;
};

export default {
  validateAgreement,
  getImprovementSuggestions,
  REQUIRED_SECTIONS,
  REQUIRED_LEGAL_CLAUSES,
  FORMATTING_REQUIREMENTS,
  PROJECT_TYPE_REQUIREMENTS
};
