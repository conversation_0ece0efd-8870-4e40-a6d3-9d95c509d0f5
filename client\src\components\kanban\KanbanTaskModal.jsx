import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Input,
  Textarea,
  Select,
  SelectItem,
  Chip
} from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  Edit3,
  Save,
  Trash2,
  User,
  Flag,
  Clock,
  Target,
  FileText
} from 'lucide-react';
import { supabase } from '../../utils/supabase/supabase.utils';

const KanbanTaskModal = ({ isOpen, onClose, onSave, onDelete, task, projectId }) => {
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    description: '',
    status: 'todo',
    assignee_id: '',
    task_type: '',
    difficulty_level: '',
    difficulty_points: 0,
    estimated_hours: 0,
    logged_hours: 0
  });
  const [contributors, setContributors] = useState([]);
  const [taskTypes, setTaskTypes] = useState([]);
  const [difficultyLevels, setDifficultyLevels] = useState([]);
  const [loading, setLoading] = useState(false);

  // Initialize form data when task changes
  useEffect(() => {
    if (task) {
      setFormData({
        id: task.id || '',
        title: task.title || '',
        description: task.description || '',
        status: task.status || 'todo',
        assignee_id: task.assignee_id || '',
        task_type: task.task_type || '',
        difficulty_level: task.difficulty_level || '',
        difficulty_points: task.difficulty_points || 0,
        estimated_hours: task.estimated_hours || 0,
        logged_hours: task.logged_hours || 0
      });
    } else {
      // Reset form for new task
      setFormData({
        id: '',
        title: '',
        description: '',
        status: 'todo',
        assignee_id: '',
        task_type: '',
        difficulty_level: '',
        difficulty_points: 0,
        estimated_hours: 0,
        logged_hours: 0
      });
    }
  }, [task]);

  // Fetch project contributors and configuration
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        setLoading(true);

        // Fetch project contributors
        let contributorsData;
        let contributorsError;

        try {
          // Try to fetch contributors with user information
          const result = await supabase
            .from('project_contributors')
            .select('user_id, users:user_id(id, display_name, avatar_url)')
            .eq('project_id', projectId);

          contributorsData = result.data;
          contributorsError = result.error;

          if (contributorsError) throw contributorsError;

          // Format contributors data
          const formattedContributors = contributorsData.map(contributor => ({
            id: contributor.user_id,
            display_name: contributor.users?.display_name || 'Unknown User',
            avatar_url: contributor.users?.avatar_url
          }));

          setContributors(formattedContributors);
        } catch (e) {
          console.log('Error fetching contributors with user join:', e);

          // Fallback: fetch just the contributors without the join
          const { data: contributorIds, error } = await supabase
            .from('project_contributors')
            .select('user_id')
            .eq('project_id', projectId);

          if (error) throw error;

          // Fetch users separately
          if (contributorIds && contributorIds.length > 0) {
            const userIds = contributorIds.map(c => c.user_id);

            const { data: usersData } = await supabase
              .from('users')
              .select('id, display_name, avatar_url')
              .in('id', userIds);

            if (usersData) {
              const formattedContributors = contributorIds.map(contributor => {
                const user = usersData.find(u => u.id === contributor.user_id);
                return {
                  id: contributor.user_id,
                  display_name: user?.display_name || 'Unknown User',
                  avatar_url: user?.avatar_url
                };
              });

              setContributors(formattedContributors);
            }
          } else {
            setContributors([]);
          }
        }

        // Fetch project contribution tracking config
        const { data: configData, error: configError } = await supabase
          .from('contribution_tracking_config')
          .select('*')
          .eq('project_id', projectId)
          .single();

        if (configError && configError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which we can handle
          throw configError;
        }

        // Set task types and difficulty levels from config
        if (configData) {
          if (configData.task_types && Array.isArray(configData.task_types)) {
            setTaskTypes(configData.task_types);
          }

          if (configData.difficulty_levels && Array.isArray(configData.difficulty_levels)) {
            setDifficultyLevels(configData.difficulty_levels);
          }
        } else {
          // Use default values if no config exists
          setTaskTypes([
            { name: 'Development', value: 'development' },
            { name: 'Design', value: 'design' },
            { name: 'Documentation', value: 'documentation' },
            { name: 'Testing', value: 'testing' },
            { name: 'Research', value: 'research' },
            { name: 'Management', value: 'management' },
            { name: 'Other', value: 'other' }
          ]);

          setDifficultyLevels([
            { name: 'Easy', value: 'easy', multiplier: 1 },
            { name: 'Medium', value: 'medium', multiplier: 2 },
            { name: 'Hard', value: 'hard', multiplier: 3 },
            { name: 'Expert', value: 'expert', multiplier: 5 }
          ]);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching project data:', error);
        setLoading(false);
      }
    };

    if (projectId && isOpen) {
      fetchProjectData();
    }
  }, [projectId, isOpen]);

  // Handle form input changes


  const statusOptions = [
    { key: 'todo', label: 'To Do', color: 'default' },
    { key: 'in_progress', label: 'In Progress', color: 'primary' },
    { key: 'review', label: 'Review', color: 'warning' },
    { key: 'done', label: 'Done', color: 'success' },
    { key: 'blocked', label: 'Blocked', color: 'danger' }
  ];

  const difficultyOptions = [
    { key: 'easy', label: 'Easy', color: 'success' },
    { key: 'medium', label: 'Medium', color: 'warning' },
    { key: 'hard', label: 'Hard', color: 'danger' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmitModal = async (e) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      toast.error('Task title is required');
      return;
    }

    try {
      await onSave(formData);
      toast.success(task ? 'Task updated successfully!' : 'Task created successfully!');
      onClose();
    } catch (error) {
      console.error('Error saving task:', error);
      toast.error('Failed to save task. Please try again.');
    }
  };

  const handleDeleteTask = async () => {
    if (!task?.id) return;

    try {
      await onDelete(task.id);
      toast.success('Task deleted successfully!');
      onClose();
    } catch (error) {
      console.error('Error deleting task:', error);
      toast.error('Failed to delete task. Please try again.');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="3xl"
      backdrop="blur"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20",
        base: "border-[#292f46] bg-[#19172c] dark:bg-[#19172c] text-[#a8b0d3]",
        header: "border-b-[1px] border-[#292f46]",
        footer: "border-t-[1px] border-[#292f46]",
        closeButton: "hover:bg-white/5 active:bg-white/10",
      }}
    >
      <ModalContent>
        <form onSubmit={handleSubmitModal}>
          <ModalHeader className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-lg bg-blue-500/20">
                <Edit3 className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white">
                  {task ? 'Edit Task' : 'Create New Task'}
                </h2>
                <p className="text-sm text-white/60">
                  {task ? 'Update task details and settings' : 'Add a new task to your project'}
                </p>
              </div>
            </div>
          </ModalHeader>

          <ModalBody className="py-6">
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
                <span className="ml-3 text-white/70">Loading task data...</span>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Task Title */}
                <Input
                  label="Task Title"
                  placeholder="Enter task title..."
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  startContent={<FileText className="w-4 h-4 text-white/40" />}
                  isRequired
                  classNames={{
                    input: "text-white",
                    label: "text-white/70"
                  }}
                />

                {/* Task Description */}
                <Textarea
                  label="Description"
                  placeholder="Describe the task in detail..."
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  minRows={3}
                  classNames={{
                    input: "text-white",
                    label: "text-white/70"
                  }}
                />

                {/* Status and Assignee Row */}
                <div className="grid grid-cols-2 gap-4">
                  <Select
                    label="Status"
                    selectedKeys={[formData.status]}
                    onSelectionChange={(keys) => handleInputChange('status', Array.from(keys)[0])}
                    classNames={{
                      trigger: "bg-white/5",
                      label: "text-white/70"
                    }}
                  >
                    {statusOptions.map((option) => (
                      <SelectItem key={option.key} value={option.key}>
                        <div className="flex items-center gap-2">
                          <Chip size="sm" color={option.color} variant="flat">
                            {option.label}
                          </Chip>
                        </div>
                      </SelectItem>
                    ))}
                  </Select>

                  <Select
                    label="Assignee"
                    selectedKeys={formData.assignee_id ? [formData.assignee_id] : []}
                    onSelectionChange={(keys) => handleInputChange('assignee_id', Array.from(keys)[0])}
                    startContent={<User className="w-4 h-4 text-white/40" />}
                    classNames={{
                      trigger: "bg-white/5",
                      label: "text-white/70"
                    }}
                  >
                    {contributors.map((contributor) => (
                      <SelectItem key={contributor.id} value={contributor.id}>
                        {contributor.display_name}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                {/* Task Type and Difficulty Row */}
                <div className="grid grid-cols-2 gap-4">
                  <Select
                    label="Task Type"
                    selectedKeys={formData.task_type ? [formData.task_type] : []}
                    onSelectionChange={(keys) => handleInputChange('task_type', Array.from(keys)[0])}
                    startContent={<Target className="w-4 h-4 text-white/40" />}
                    classNames={{
                      trigger: "bg-white/5",
                      label: "text-white/70"
                    }}
                  >
                    {taskTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </Select>

                  <Select
                    label="Difficulty Level"
                    selectedKeys={formData.difficulty_level ? [formData.difficulty_level] : []}
                    onSelectionChange={(keys) => handleInputChange('difficulty_level', Array.from(keys)[0])}
                    startContent={<Flag className="w-4 h-4 text-white/40" />}
                    classNames={{
                      trigger: "bg-white/5",
                      label: "text-white/70"
                    }}
                  >
                    {difficultyLevels.map((level) => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.name} ({level.multiplier}x)
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                {/* Points and Hours Row */}
                <div className="grid grid-cols-2 gap-4">
                  <Input
                    type="number"
                    label="Difficulty Points"
                    placeholder="0"
                    value={formData.difficulty_points?.toString() || ''}
                    onChange={(e) => handleInputChange('difficulty_points', parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.1"
                    classNames={{
                      input: "text-white",
                      label: "text-white/70"
                    }}
                  />

                  <Input
                    type="number"
                    label="Estimated Hours"
                    placeholder="0"
                    value={formData.estimated_hours?.toString() || ''}
                    onChange={(e) => handleInputChange('estimated_hours', parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.5"
                    startContent={<Clock className="w-4 h-4 text-white/40" />}
                    classNames={{
                      input: "text-white",
                      label: "text-white/70"
                    }}
                  />
                </div>

                {/* Logged Hours (if editing existing task) */}
                {task && (
                  <Input
                    type="number"
                    label="Logged Hours"
                    value={formData.logged_hours?.toString() || '0'}
                    isReadOnly
                    description="This field is automatically updated by time tracking"
                    startContent={<Clock className="w-4 h-4 text-white/40" />}
                    classNames={{
                      input: "text-white",
                      label: "text-white/70"
                    }}
                  />
                )}
              </div>
            )}
          </ModalBody>

          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={onClose}
            >
              Cancel
            </Button>
            <div className="flex gap-2">
              {task && (
                <Button
                  color="danger"
                  variant="solid"
                  onPress={handleDeleteTask}
                  startContent={<Trash2 className="w-4 h-4" />}
                >
                  Delete
                </Button>
              )}
              <Button
                color="success"
                type="submit"
                startContent={<Save className="w-4 h-4" />}
              >
                {task ? 'Update Task' : 'Create Task'}
              </Button>
            </div>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default KanbanTaskModal;
