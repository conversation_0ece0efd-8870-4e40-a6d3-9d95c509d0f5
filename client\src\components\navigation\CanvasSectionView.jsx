import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/react';

/**
 * Canvas Section View Component
 *
 * Displays detailed sections of a canvas when zoomed in.
 * Shows the different functional areas within a canvas.
 */
const CanvasSectionView = ({ canvas, onNavigateToSection, onBack }) => {
  if (!canvas || !canvas.sections) {
    return null;
  }

  // Convert sections object to array format for rendering
  const sectionsArray = Object.entries(canvas.sections).map(([key, section]) => ({
    id: key,
    title: key.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
    icon: getSectionIcon(key),
    description: getSectionDescription(key),
    color: getSectionColor(key, canvas.color),
    features: getSectionFeatures(key)
  }));

  // Helper functions to generate section metadata
  function getSectionIcon(sectionKey) {
    const iconMap = {
      'overview': '📊', 'activity': '📈', 'stats': '📋', 'notifications': '🔔',
      'list': '📝', 'detail': '🔍', 'team': '👥', 'settings': '⚙️',
      'basic-info': '📝', 'team-setup': '👥', 'revenue-model': '💰', 'review': '✅',
      'backlog': '📋', 'in-progress': '⚡', 'completed': '✅',
      'templates': '📄', 'active-agreements': '📋', 'signatures': '✍️', 'archive': '📦',
      'time-tracker': '⏱️', 'contribution-log': '📝', 'difficulty-assessment': '📊', 'submission': '📤',
      'pending-review': '⏳', 'validation-metrics': '📊', 'approval-workflow': '✅', 'feedback': '💬',
      'revenue-dashboard': '💰', 'payment-history': '📊', 'projections': '📈', 'reports': '📋',
      'calculator': '🧮', 'distribution-model': '📊', 'payment-schedule': '📅', 'history': '📜',
      'escrow-accounts': '🏦', 'release-conditions': '📋', 'transaction-log': '📊', 'disputes': '⚖️',
      'contribution-analytics': '📊', 'project-metrics': '📈', 'team-performance': '👥', 'trends': '📊',
      'predictive-analytics': '🔮', 'recommendations': '💡', 'optimization': '⚡', 'forecasting': '📈',
      'personal-info': '👤', 'skills': '🎯', 'portfolio': '💼', 'achievements': '🏆',
      'my-teams': '👥', 'invitations': '📨', 'team-creation': '➕', 'collaboration': '🤝',
      'friends': '👫', 'chat': '💬', 'activity-feed': '📰', 'community': '🌐',
      'account-settings': '⚙️', 'preferences': '🎛️', 'integrations': '🔗', 'security': '🔒',
      'inbox': '📥', 'alerts': '🚨', 'report-bug': '🐛', 'my-reports': '📋', 'status-tracker': '📊',
      'tutorials': '🎓', 'documentation': '📚', 'best-practices': '⭐', 'certification': '🏅',
      'faq': '❓', 'guides': '📖', 'support': '🆘', 'contact': '📞',
      'dashboard': '📊', 'user-management': '👥', 'system-health': '💚', 'roadmap': '🗺️',
      'database': '🗄️', 'migrations': '🔄', 'monitoring': '📡', 'logs': '📜'
    };
    return iconMap[sectionKey] || '📄';
  }

  function getSectionDescription(sectionKey) {
    const descMap = {
      'overview': 'Key metrics and insights',
      'activity': 'Recent activity feed',
      'stats': 'Quick statistics',
      'notifications': 'System notifications',
      'list': 'Browse and manage items',
      'detail': 'Detailed view and editing',
      'team': 'Team management tools',
      'settings': 'Configuration options',
      'basic-info': 'Project information',
      'team-setup': 'Configure team roles',
      'revenue-model': 'Set revenue sharing',
      'review': 'Review and launch',
      'backlog': 'Planned tasks',
      'in-progress': 'Active work',
      'completed': 'Finished tasks',
      'templates': 'Agreement templates',
      'active-agreements': 'Current agreements',
      'signatures': 'Signature management',
      'archive': 'Historical documents',
      'time-tracker': 'Track work time',
      'contribution-log': 'Log contributions',
      'difficulty-assessment': 'Assess task difficulty',
      'submission': 'Submit contributions',
      'pending-review': 'Awaiting validation',
      'validation-metrics': 'Validation statistics',
      'approval-workflow': 'Approval process',
      'feedback': 'Feedback system',
      'revenue-dashboard': 'Revenue overview',
      'payment-history': 'Payment records',
      'projections': 'Revenue forecasts',
      'reports': 'Financial reports',
      'calculator': 'Calculate royalties',
      'distribution-model': 'Revenue distribution',
      'payment-schedule': 'Payment timing',
      'history': 'Historical data',
      'escrow-accounts': 'Manage escrow',
      'release-conditions': 'Release criteria',
      'transaction-log': 'Transaction history',
      'disputes': 'Dispute resolution'
    };
    return descMap[sectionKey] || 'Manage this section';
  }

  function getSectionColor(sectionKey, defaultColor) {
    // Use variations of the canvas color for sections
    const colorVariations = [
      defaultColor,
      defaultColor.replace('500', '400'),
      defaultColor.replace('500', '600'),
      defaultColor.replace('600', '500')
    ];
    const hash = sectionKey.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return colorVariations[hash % colorVariations.length];
  }

  function getSectionFeatures(sectionKey) {
    // Return some sample features - in a real app this would come from the section definition
    return ['Feature 1', 'Feature 2', 'Feature 3'];
  }

  return (
    <motion.div
      className="relative w-full h-full flex items-center justify-center"
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.4 }}
    >
      {/* Background with canvas theme */}
      <div className={`absolute inset-0 bg-gradient-to-br ${canvas.color} opacity-20`} />

      {/* Back button */}
      <motion.button
        className="absolute top-8 left-8 z-50 px-4 py-2 bg-white/10 backdrop-blur-md rounded-lg text-white hover:bg-white/20 transition-colors"
        onClick={onBack}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.2 }}
      >
        ← Back to Overworld
      </motion.button>

      {/* Canvas title */}
      <motion.div
        className="absolute top-8 left-1/2 transform -translate-x-1/2 text-center z-40"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <div className="text-6xl mb-2">{canvas.icon}</div>
        <h1 className="text-3xl font-bold text-white mb-2">{canvas.title}</h1>
        <p className="text-white/80 text-lg">{canvas.description}</p>
      </motion.div>

      {/* Sections grid */}
      <div className="relative z-30 grid grid-cols-2 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-32">
        {sectionsArray.map((section, index) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: 0.3 + index * 0.1, duration: 0.4 }}
          >
            <Card
              className="cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
              onClick={() => onNavigateToSection && onNavigateToSection(section.id)}
            >
              <CardBody className={`
                p-6 bg-gradient-to-br ${section.color || canvas.color}
                text-white text-center relative overflow-hidden
              `}>
                {/* Background pattern */}
                <div className="absolute inset-0 opacity-20">
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1),transparent_50%)]" />
                </div>

                {/* Content */}
                <div className="relative z-10">
                  <div className="text-3xl mb-3">{section.icon}</div>
                  <h3 className="font-bold text-lg mb-2">{section.title}</h3>
                  <p className="text-sm opacity-90 mb-3">{section.description}</p>

                  {/* Features list */}
                  {section.features && section.features.length > 0 && (
                    <div className="text-xs opacity-80">
                      <div className="space-y-1">
                        {section.features.slice(0, 3).map((feature, idx) => (
                          <div key={idx} className="flex items-center justify-center gap-1">
                            <span className="w-1 h-1 bg-white/60 rounded-full"></span>
                            <span>{feature}</span>
                          </div>
                        ))}
                        {section.features.length > 3 && (
                          <div className="text-white/60">+{section.features.length - 3} more</div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Hover effect */}
                <motion.div
                  className="absolute inset-0 bg-white/10 opacity-0"
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                />
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Navigation hints */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 text-center z-40"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <p className="text-sm">🖱️ Click a section to navigate • ← Back to return to overworld</p>
      </motion.div>

      {/* Decorative elements */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Floating particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + (i % 2) * 40}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.2, 0.6, 0.2],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>
    </motion.div>
  );
};

export default CanvasSectionView;
