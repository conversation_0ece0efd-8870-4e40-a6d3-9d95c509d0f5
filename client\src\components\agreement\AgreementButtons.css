.agreement-buttons-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.agreement-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.agreement-button i {
  font-size: 1rem;
}

.agreement-button span {
  white-space: nowrap;
}

/* All button styling now handled by HeroUI components */
.agreement-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .agreement-buttons-container {
    flex-direction: column;
    align-items: stretch;
  }

  .agreement-button {
    justify-content: center;
  }
}
