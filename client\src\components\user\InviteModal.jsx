import React, { useEffect, useRef } from 'react';
import InviteUserForm from './InviteUserForm';

const InviteModal = ({ isOpen, onClose, inviteType = 'friend', projectId = null }) => {
  const modalRef = useRef(null);

  // Close when clicking outside the modal
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Close on escape key
  useEffect(() => {
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="invite-modal-overlay">
      <div ref={modalRef}>
        <InviteUserForm 
          onClose={onClose} 
          inviteType={inviteType} 
          projectId={projectId} 
        />
      </div>
    </div>
  );
};

export default InviteModal;
