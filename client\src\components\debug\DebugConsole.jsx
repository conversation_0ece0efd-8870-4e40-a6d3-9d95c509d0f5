import React from 'react';

/**
 * Debug Console Placeholder
 * Simple placeholder component to prevent build errors
 */
const DebugConsole = ({ isOpen, onClose, onCommand }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-90 text-green-400 p-4 rounded-lg font-mono text-sm max-w-md z-50">
      <div className="flex justify-between items-center mb-2">
        <span>Debug Console</span>
        <button 
          onClick={onClose}
          className="text-red-400 hover:text-red-300"
        >
          ×
        </button>
      </div>
      <div className="text-xs opacity-75">
        Debug console temporarily disabled for production build.
      </div>
    </div>
  );
};

export default DebugConsole;
