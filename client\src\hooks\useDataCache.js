import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Data Caching Hook
 * 
 * Provides caching strategies for frequently accessed project and contribution data
 * with automatic cache invalidation and memory management.
 */

// Cache storage with TTL (Time To Live)
class CacheStorage {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  set(key, value, ttl = 300000) { // Default 5 minutes
    // Clear existing timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }

    // Store value with timestamp
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    });

    // Set expiration timer
    const timer = setTimeout(() => {
      this.delete(key);
    }, ttl);

    this.timers.set(key, timer);
  }

  get(key) {
    const item = this.cache.get(key);
    
    if (!item) return null;

    // Check if expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.delete(key);
      return null;
    }

    return item.value;
  }

  delete(key) {
    this.cache.delete(key);
    
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
      this.timers.delete(key);
    }
  }

  clear() {
    this.cache.clear();
    this.timers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
  }

  has(key) {
    return this.cache.has(key) && this.get(key) !== null;
  }

  size() {
    return this.cache.size;
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    let expired = 0;
    let valid = 0;

    this.cache.forEach((item) => {
      if (now - item.timestamp > item.ttl) {
        expired++;
      } else {
        valid++;
      }
    });

    return { valid, expired, total: this.cache.size };
  }
}

// Global cache instance
const globalCache = new CacheStorage();

// Data cache hook
export const useDataCache = (options = {}) => {
  const {
    defaultTTL = 300000, // 5 minutes
    maxCacheSize = 100,
    enablePersistence = false,
    persistenceKey = 'royaltea_cache'
  } = options;

  const [cacheStats, setCacheStats] = useState({ valid: 0, expired: 0, total: 0 });

  // Update cache stats
  const updateStats = useCallback(() => {
    setCacheStats(globalCache.getStats());
  }, []);

  // Cache operations
  const setCache = useCallback((key, value, ttl = defaultTTL) => {
    // Implement cache size limit
    if (globalCache.size() >= maxCacheSize) {
      // Remove oldest entries (simple LRU)
      const entries = Array.from(globalCache.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Remove oldest 20% of entries
      const toRemove = Math.floor(entries.length * 0.2);
      for (let i = 0; i < toRemove; i++) {
        globalCache.delete(entries[i][0]);
      }
    }

    globalCache.set(key, value, ttl);
    updateStats();

    // Persist to localStorage if enabled
    if (enablePersistence) {
      try {
        const persistedData = JSON.parse(localStorage.getItem(persistenceKey) || '{}');
        persistedData[key] = {
          value,
          timestamp: Date.now(),
          ttl
        };
        localStorage.setItem(persistenceKey, JSON.stringify(persistedData));
      } catch (error) {
        console.warn('Failed to persist cache data:', error);
      }
    }
  }, [defaultTTL, maxCacheSize, enablePersistence, persistenceKey, updateStats]);

  const getCache = useCallback((key) => {
    let value = globalCache.get(key);

    // Try to load from persistence if not in memory
    if (!value && enablePersistence) {
      try {
        const persistedData = JSON.parse(localStorage.getItem(persistenceKey) || '{}');
        const item = persistedData[key];
        
        if (item && Date.now() - item.timestamp < item.ttl) {
          value = item.value;
          // Restore to memory cache
          globalCache.set(key, value, item.ttl - (Date.now() - item.timestamp));
        }
      } catch (error) {
        console.warn('Failed to load persisted cache data:', error);
      }
    }

    updateStats();
    return value;
  }, [enablePersistence, persistenceKey, updateStats]);

  const deleteCache = useCallback((key) => {
    globalCache.delete(key);
    
    if (enablePersistence) {
      try {
        const persistedData = JSON.parse(localStorage.getItem(persistenceKey) || '{}');
        delete persistedData[key];
        localStorage.setItem(persistenceKey, JSON.stringify(persistedData));
      } catch (error) {
        console.warn('Failed to remove persisted cache data:', error);
      }
    }
    
    updateStats();
  }, [enablePersistence, persistenceKey, updateStats]);

  const clearCache = useCallback(() => {
    globalCache.clear();
    
    if (enablePersistence) {
      try {
        localStorage.removeItem(persistenceKey);
      } catch (error) {
        console.warn('Failed to clear persisted cache data:', error);
      }
    }
    
    updateStats();
  }, [enablePersistence, persistenceKey, updateStats]);

  const hasCache = useCallback((key) => {
    return globalCache.has(key);
  }, []);

  // Initialize stats
  useEffect(() => {
    updateStats();
  }, [updateStats]);

  return {
    setCache,
    getCache,
    deleteCache,
    clearCache,
    hasCache,
    cacheStats
  };
};

// Cached data fetching hook
export const useCachedData = (key, fetchFunction, options = {}) => {
  const {
    ttl = 300000, // 5 minutes
    enabled = true,
    dependencies = [],
    staleWhileRevalidate = true
  } = options;

  const { getCache, setCache, hasCache } = useDataCache();
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isStale, setIsStale] = useState(false);
  const fetchRef = useRef();

  const fetchData = useCallback(async (useCache = true) => {
    if (!enabled) return;

    // Check cache first
    if (useCache && hasCache(key)) {
      const cachedData = getCache(key);
      if (cachedData) {
        setData(cachedData);
        setIsStale(false);
        return cachedData;
      }
    }

    // Prevent duplicate requests
    if (fetchRef.current) {
      return fetchRef.current;
    }

    try {
      setLoading(true);
      setError(null);

      fetchRef.current = fetchFunction();
      const result = await fetchRef.current;

      setData(result);
      setCache(key, result, ttl);
      setIsStale(false);

      return result;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
      fetchRef.current = null;
    }
  }, [key, fetchFunction, enabled, ttl, hasCache, getCache, setCache]);

  // Background revalidation for stale-while-revalidate
  const revalidate = useCallback(async () => {
    if (!staleWhileRevalidate) return;

    try {
      setIsStale(true);
      const result = await fetchData(false); // Skip cache
      setIsStale(false);
      return result;
    } catch (err) {
      console.warn('Background revalidation failed:', err);
    }
  }, [fetchData, staleWhileRevalidate]);

  // Initial load and dependency changes
  useEffect(() => {
    fetchData();
  }, [fetchData, ...dependencies]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (fetchRef.current) {
        fetchRef.current = null;
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    isStale,
    refetch: () => fetchData(false),
    revalidate
  };
};

// Optimized query hook for database operations
export const useOptimizedQuery = (queryKey, queryFunction, options = {}) => {
  const {
    enabled = true,
    refetchOnWindowFocus = false,
    refetchInterval = null,
    cacheTime = 300000, // 5 minutes
    staleTime = 60000, // 1 minute
    retry = 3,
    retryDelay = 1000
  } = options;

  const [state, setState] = useState({
    data: null,
    loading: false,
    error: null,
    isFetching: false,
    isStale: false
  });

  const { getCache, setCache } = useDataCache();
  const retryCountRef = useRef(0);
  const queryRef = useRef();

  const executeQuery = useCallback(async (skipCache = false) => {
    if (!enabled) return;

    // Check cache first
    if (!skipCache) {
      const cachedData = getCache(queryKey);
      if (cachedData) {
        setState(prev => ({
          ...prev,
          data: cachedData,
          isStale: Date.now() - cachedData.timestamp > staleTime
        }));
        return cachedData;
      }
    }

    if (queryRef.current) {
      return queryRef.current;
    }

    try {
      setState(prev => ({ ...prev, loading: true, isFetching: true, error: null }));

      queryRef.current = queryFunction();
      const result = await queryRef.current;

      const dataWithTimestamp = {
        ...result,
        timestamp: Date.now()
      };

      setState(prev => ({
        ...prev,
        data: dataWithTimestamp,
        loading: false,
        isFetching: false,
        isStale: false
      }));

      setCache(queryKey, dataWithTimestamp, cacheTime);
      retryCountRef.current = 0;

      return result;
    } catch (error) {
      if (retryCountRef.current < retry) {
        retryCountRef.current++;
        setTimeout(() => {
          executeQuery(skipCache);
        }, retryDelay * retryCountRef.current);
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          isFetching: false,
          error
        }));
      }
    } finally {
      queryRef.current = null;
    }
  }, [queryKey, queryFunction, enabled, getCache, setCache, cacheTime, staleTime, retry, retryDelay]);

  // Window focus refetch
  useEffect(() => {
    if (!refetchOnWindowFocus) return;

    const handleFocus = () => {
      if (state.isStale) {
        executeQuery(true);
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [refetchOnWindowFocus, state.isStale, executeQuery]);

  // Interval refetch
  useEffect(() => {
    if (!refetchInterval) return;

    const interval = setInterval(() => {
      executeQuery(true);
    }, refetchInterval);

    return () => clearInterval(interval);
  }, [refetchInterval, executeQuery]);

  // Initial query
  useEffect(() => {
    executeQuery();
  }, [executeQuery]);

  return {
    ...state,
    refetch: () => executeQuery(true),
    invalidate: () => {
      const { deleteCache } = useDataCache();
      deleteCache(queryKey);
      executeQuery(true);
    }
  };
};

export default useDataCache;
