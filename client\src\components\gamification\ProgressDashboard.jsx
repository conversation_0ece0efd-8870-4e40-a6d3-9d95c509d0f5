import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Progress, Chip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';

/**
 * ProgressDashboard Component
 * 
 * Enhanced progress dashboard following wireframe specifications
 * Features level display, XP tracking, skill mastery, and goal setting
 */
const ProgressDashboard = ({ 
  currentUser,
  userProgress = {},
  skills = [],
  onSetGoal,
  onViewSkillTree,
  className = ""
}) => {
  const [showGoalModal, setShowGoalModal] = useState(false);
  const [newGoal, setNewGoal] = useState('');

  // Default progress data
  const {
    currentLevel = 12,
    totalXp = 12450,
    xpToNextLevel = 3200,
    xpForNextLevel = 12800,
    monthlyXp = 890,
    skills: userSkills = [
      { name: 'React', level: 'Expert', progress: 100, color: 'primary' },
      { name: 'Node.js', level: 'Advanced', progress: 80, color: 'success' },
      { name: 'TypeScript', level: 'Intermediate', progress: 60, color: 'warning' }
    ]
  } = userProgress;

  // Calculate progress to next level
  const progressToNext = ((totalXp - (xpForNextLevel - xpToNextLevel)) / xpToNextLevel) * 100;

  // Get level title
  const getLevelTitle = (level) => {
    if (level < 5) return 'Apprentice Developer';
    if (level < 10) return 'Junior Developer';
    if (level < 15) return 'Developer';
    if (level < 20) return 'Senior Developer';
    if (level < 25) return 'Lead Developer';
    return 'Master Developer';
  };

  // Get skill level color
  const getSkillLevelColor = (level) => {
    const colors = {
      'Beginner': 'default',
      'Intermediate': 'warning',
      'Advanced': 'success',
      'Expert': 'primary',
      'Master': 'secondary'
    };
    return colors[level] || 'default';
  };

  // Handle goal setting
  const handleSetGoal = () => {
    if (newGoal.trim()) {
      if (onSetGoal) {
        onSetGoal(newGoal);
      }
      setNewGoal('');
      setShowGoalModal(false);
    }
  };

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-blue-900/20 dark:to-cyan-800/20 border-2 border-blue-200 dark:border-blue-700 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">📊</span>
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">Your Progress</h3>
            </div>
            <Chip color="primary" variant="flat" size="sm">
              Level {currentLevel}
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Level Display */}
          <div className="text-center mb-6">
            <div className="text-xl font-bold text-blue-600 mb-2">
              Level {currentLevel} {getLevelTitle(currentLevel)}
            </div>
            
            {/* Progress Bar to Next Level */}
            <div className="mb-3">
              <div className="flex justify-between text-sm text-default-600 mb-1">
                <span>Progress to Level {currentLevel + 1}</span>
                <span>{Math.round(progressToNext)}%</span>
              </div>
              <Progress 
                value={progressToNext} 
                color="primary" 
                className="w-full"
                classNames={{
                  track: "drop-shadow-md border border-default",
                  indicator: "bg-gradient-to-r from-blue-500 to-cyan-500",
                }}
              />
            </div>
            
            <div className="text-sm text-default-600">
              {(xpToNextLevel - (totalXp - (xpForNextLevel - xpToNextLevel))).toLocaleString()} XP to next level
            </div>
          </div>

          {/* XP Stats */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center">
              <div className="text-lg font-bold text-primary">{totalXp.toLocaleString()}</div>
              <div className="text-xs text-default-500">Total XP</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-success">+{monthlyXp}</div>
              <div className="text-xs text-default-500">This Month</div>
            </div>
          </div>

          {/* Skill Mastery */}
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-default-700 mb-3">Skill Mastery:</h4>
            <div className="space-y-3">
              {userSkills.map((skill, idx) => (
                <motion.div
                  key={idx}
                  className="space-y-1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: idx * 0.1 }}
                >
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{skill.name}:</span>
                    <Chip 
                      size="sm" 
                      color={getSkillLevelColor(skill.level)}
                      variant="flat"
                    >
                      {skill.level}
                    </Chip>
                  </div>
                  <Progress 
                    value={skill.progress} 
                    color={skill.color || 'primary'}
                    size="sm"
                    className="w-full"
                  />
                </motion.div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              color="primary" 
              variant="flat" 
              size="sm" 
              className="flex-1"
              onPress={() => onViewSkillTree && onViewSkillTree()}
            >
              🌳 View Skill Tree
            </Button>
            <Button 
              color="secondary" 
              variant="flat" 
              size="sm" 
              className="flex-1"
              onPress={() => setShowGoalModal(true)}
            >
              🎯 Set Goals
            </Button>
          </div>

          {/* Current Goals */}
          <div className="mt-4 p-3 bg-white/50 dark:bg-black/20 rounded-lg">
            <div className="text-xs font-semibold text-default-700 mb-2">Current Goals:</div>
            <div className="space-y-1 text-xs text-default-600">
              <div>• Reach Level 13 by month end</div>
              <div>• Master TypeScript (40% remaining)</div>
              <div>• Complete 5 more collaborations</div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Goal Setting Modal */}
      <Modal 
        isOpen={showGoalModal} 
        onClose={() => setShowGoalModal(false)}
        size="md"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">🎯 Set New Goal</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  What would you like to achieve?
                </label>
                <textarea
                  value={newGoal}
                  onChange={(e) => setNewGoal(e.target.value)}
                  className="w-full p-3 border border-default-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  rows="3"
                  placeholder="e.g., Master React by end of quarter, Complete 10 projects this month..."
                />
              </div>

              <div className="bg-primary-50 border border-primary-200 rounded-lg p-3">
                <div className="text-sm text-primary-700">
                  <strong>💡 Goal Tips:</strong>
                  <ul className="mt-1 space-y-1 text-xs">
                    <li>• Make goals specific and measurable</li>
                    <li>• Set realistic timelines</li>
                    <li>• Break large goals into smaller steps</li>
                  </ul>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-semibold mb-2">Suggested Goals:</h4>
                <div className="space-y-2">
                  {[
                    'Reach Level 15 by end of quarter',
                    'Master a new programming language',
                    'Complete 20 successful collaborations',
                    'Earn 1000 ORBs this month'
                  ].map((suggestion, idx) => (
                    <Button
                      key={idx}
                      size="sm"
                      variant="flat"
                      className="w-full justify-start text-left"
                      onPress={() => setNewGoal(suggestion)}
                    >
                      {suggestion}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={() => setShowGoalModal(false)}
            >
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleSetGoal}
              disabled={!newGoal.trim()}
            >
              🎯 Set Goal
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ProgressDashboard;
