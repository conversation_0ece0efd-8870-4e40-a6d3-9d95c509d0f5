import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Chip, Progress, Avatar, Badge } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';

/**
 * Mission Board Component - Enhanced Task Management with Bento Grid Layout
 * 
 * Features:
 * - Game-like mission terminology and visual design
 * - Bento grid layout with three-column structure
 * - Advanced filtering and search capabilities
 * - Mission claiming and assignment system
 * - Real-time updates and progress tracking
 * - Integration with existing task system
 */
const MissionBoard = ({
  projectId,
  className = "",
  filterByUser = null,
  filterByStatus = null,
  hideHeader = false,
  hideStats = false
}) => {
  const { currentUser } = useContext(UserContext);



  // State management
  const [missions, setMissions] = useState([]);
  const [filteredMissions, setFilteredMissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMission, setSelectedMission] = useState(null);
  const [showMissionModal, setShowMissionModal] = useState(false);
  
  // Filter states
  const [filters, setFilters] = useState({
    status: 'all',
    difficulty: 'all',
    type: 'all',
    assignee: 'all',
    reward: 'all'
  });
  
  // Mission statistics
  const [missionStats, setMissionStats] = useState({
    total: 0,
    available: 0,
    inProgress: 0,
    completed: 0,
    myMissions: 0
  });

  // Load missions from tasks table with enhanced data
  const loadMissions = async () => {
    try {
      setLoading(true);
      
      let query = supabase
        .from('tasks')
        .select(`
          *,
          assignee:assignee_id(id, display_name, full_name, email),
          project:project_id(id, name, description)
        `);
      
      if (projectId) {
        query = query.eq('project_id', projectId);
      }
      
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      
      // Transform tasks into missions with enhanced metadata
      const enhancedMissions = data.map(task => ({
        ...task,
        // Mission-specific enhancements
        missionType: task.task_type || 'general',
        difficultyRating: task.difficulty_level || 'medium',
        rewardOrbs: calculateOrbReward(task),
        estimatedDuration: task.estimated_hours || 0,
        skillsRequired: extractSkillsFromDescription(task.description),
        urgency: calculateUrgency(task),
        missionStatus: mapTaskStatusToMission(task.status),
        claimable: task.status === 'todo' && !task.assignee_id,
        progress: calculateProgress(task)
      }));
      
      setMissions(enhancedMissions);
      setFilteredMissions(enhancedMissions);
      updateMissionStats(enhancedMissions);
      
    } catch (error) {
      console.error('Error loading missions:', error);
      toast.error('Failed to load missions');
    } finally {
      setLoading(false);
    }
  };

  // Calculate ORB reward based on task complexity
  const calculateOrbReward = (task) => {
    const baseReward = 10;
    const difficultyMultiplier = {
      'easy': 1,
      'medium': 1.5,
      'hard': 2,
      'expert': 3
    };
    const hoursMultiplier = (task.estimated_hours || 1) * 0.5;
    
    return Math.round(baseReward * (difficultyMultiplier[task.difficulty_level] || 1.5) * hoursMultiplier);
  };

  // Extract skills from task description (simple keyword matching)
  const extractSkillsFromDescription = (description) => {
    if (!description) return [];
    
    const skillKeywords = [
      'react', 'javascript', 'typescript', 'node.js', 'python', 'design', 
      'ui/ux', 'backend', 'frontend', 'database', 'api', 'testing'
    ];
    
    const foundSkills = skillKeywords.filter(skill => 
      description.toLowerCase().includes(skill.toLowerCase())
    );
    
    return foundSkills.slice(0, 3); // Limit to 3 skills
  };

  // Calculate mission urgency
  const calculateUrgency = (task) => {
    // Simple urgency calculation based on creation date and estimated hours
    const daysSinceCreated = Math.floor((Date.now() - new Date(task.created_at)) / (1000 * 60 * 60 * 24));
    const estimatedHours = task.estimated_hours || 8;
    
    if (daysSinceCreated > 7 && estimatedHours < 4) return 'high';
    if (daysSinceCreated > 3) return 'medium';
    return 'low';
  };

  // Map task status to mission terminology
  const mapTaskStatusToMission = (status) => {
    const statusMap = {
      'todo': 'available',
      'in_progress': 'active',
      'review': 'review',
      'done': 'completed',
      'blocked': 'blocked'
    };
    return statusMap[status] || 'available';
  };

  // Calculate mission progress
  const calculateProgress = (task) => {
    if (task.status === 'done') return 100;
    if (task.status === 'review') return 90;
    if (task.status === 'in_progress') {
      // Calculate based on logged hours vs estimated hours
      const loggedHours = task.logged_hours || 0;
      const estimatedHours = task.estimated_hours || 8;
      return Math.min(Math.round((loggedHours / estimatedHours) * 80), 80);
    }
    return 0;
  };

  // Update mission statistics
  const updateMissionStats = (missionList) => {
    const stats = {
      total: missionList.length,
      available: missionList.filter(m => m.missionStatus === 'available').length,
      inProgress: missionList.filter(m => m.missionStatus === 'active').length,
      completed: missionList.filter(m => m.missionStatus === 'completed').length,
      myMissions: missionList.filter(m => m.assignee_id === currentUser?.id).length
    };
    setMissionStats(stats);
  };

  // Apply filters and search
  const applyFiltersAndSearch = () => {
    let filtered = [...missions];

    // Apply external filters first (from props)
    if (filterByUser) {
      filtered = filtered.filter(mission => mission.assignee_id === filterByUser);
    }

    if (filterByStatus) {
      const mappedStatus = mapTaskStatusToMission(filterByStatus);
      filtered = filtered.filter(mission => mission.missionStatus === mappedStatus);
    }

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(mission =>
        mission.title?.toLowerCase().includes(searchLower) ||
        mission.description?.toLowerCase().includes(searchLower) ||
        mission.skillsRequired.some(skill => skill.toLowerCase().includes(searchLower))
      );
    }

    // Apply internal status filter (only if no external filter)
    if (!filterByStatus && filters.status !== 'all') {
      filtered = filtered.filter(mission => mission.missionStatus === filters.status);
    }

    // Apply difficulty filter
    if (filters.difficulty !== 'all') {
      filtered = filtered.filter(mission => mission.difficultyRating === filters.difficulty);
    }

    // Apply type filter
    if (filters.type !== 'all') {
      filtered = filtered.filter(mission => mission.missionType === filters.type);
    }

    // Apply assignee filter (only if no external user filter)
    if (!filterByUser) {
      if (filters.assignee === 'mine') {
        filtered = filtered.filter(mission => mission.assignee_id === currentUser?.id);
      } else if (filters.assignee === 'unassigned') {
        filtered = filtered.filter(mission => !mission.assignee_id);
      }
    }

    setFilteredMissions(filtered);
  };

  // Handle mission claiming
  const claimMission = async (missionId) => {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ 
          assignee_id: currentUser.id,
          status: 'in_progress'
        })
        .eq('id', missionId);
      
      if (error) throw error;
      
      toast.success('Mission claimed successfully!');
      loadMissions(); // Reload to get updated data
      
    } catch (error) {
      console.error('Error claiming mission:', error);
      toast.error('Failed to claim mission');
    }
  };

  // Initialize component
  useEffect(() => {
    loadMissions();
  }, [projectId]);

  // Apply filters when they change
  useEffect(() => {
    applyFiltersAndSearch();
  }, [missions, searchTerm, filters]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-default-600">Loading missions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`mission-board ${className}`}>
      {/* Mission Board Header */}
      {!hideHeader && (
        <div className="mb-6">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
            🎯 Mission Board
          </h1>
          <p className="text-default-600">
            Discover and claim missions to earn ORBs and advance your skills
          </p>
        </div>
      )}

      {/* Mission Statistics Cards */}
      {!hideStats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{missionStats.total}</div>
            <div className="text-sm text-default-600">Total Missions</div>
          </CardBody>
        </Card>
        
        <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{missionStats.available}</div>
            <div className="text-sm text-default-600">Available</div>
          </CardBody>
        </Card>
        
        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{missionStats.inProgress}</div>
            <div className="text-sm text-default-600">In Progress</div>
          </CardBody>
        </Card>
        
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{missionStats.completed}</div>
            <div className="text-sm text-default-600">Completed</div>
          </CardBody>
        </Card>
        
        <Card className="bg-gradient-to-br from-pink-50 to-pink-100 dark:from-pink-900/20 dark:to-pink-800/20">
          <CardBody className="p-4 text-center">
            <div className="text-2xl font-bold text-pink-600">{missionStats.myMissions}</div>
            <div className="text-sm text-default-600">My Missions</div>
          </CardBody>
        </Card>
        </div>
      )}

      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <Input
                placeholder="Search missions by title, description, or skills..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                startContent={<span className="text-default-400">🔍</span>}
                className="w-full"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex gap-2 flex-wrap">
              <Select
                placeholder="Status"
                selectedKeys={[filters.status]}
                onSelectionChange={(keys) => setFilters({...filters, status: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Status</SelectItem>
                <SelectItem key="available">Available</SelectItem>
                <SelectItem key="active">Active</SelectItem>
                <SelectItem key="review">Review</SelectItem>
                <SelectItem key="completed">Completed</SelectItem>
              </Select>

              <Select
                placeholder="Difficulty"
                selectedKeys={[filters.difficulty]}
                onSelectionChange={(keys) => setFilters({...filters, difficulty: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All Levels</SelectItem>
                <SelectItem key="easy">Easy</SelectItem>
                <SelectItem key="medium">Medium</SelectItem>
                <SelectItem key="hard">Hard</SelectItem>
                <SelectItem key="expert">Expert</SelectItem>
              </Select>

              <Select
                placeholder="Assignee"
                selectedKeys={[filters.assignee]}
                onSelectionChange={(keys) => setFilters({...filters, assignee: Array.from(keys)[0]})}
                className="w-32"
                size="sm"
              >
                <SelectItem key="all">All</SelectItem>
                <SelectItem key="mine">My Missions</SelectItem>
                <SelectItem key="unassigned">Unassigned</SelectItem>
              </Select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Mission Grid - Bento Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AnimatePresence>
          {filteredMissions.map((mission, index) => (
            <MissionCard
              key={mission.id}
              mission={mission}
              index={index}
              onClaim={() => claimMission(mission.id)}
              onView={() => setSelectedMission(mission)}
              currentUser={currentUser}
            />
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredMissions.length === 0 && (
        <Card className="mt-8">
          <CardBody className="p-8 text-center">
            <div className="text-6xl mb-4">🎯</div>
            <h3 className="text-xl font-semibold mb-2">No missions found</h3>
            <p className="text-default-600 mb-4">
              {searchTerm || Object.values(filters).some(f => f !== 'all')
                ? 'Try adjusting your search or filters'
                : 'No missions available at the moment'
              }
            </p>
            {(searchTerm || Object.values(filters).some(f => f !== 'all')) && (
              <Button
                color="primary"
                variant="flat"
                onClick={() => {
                  setSearchTerm('');
                  setFilters({
                    status: 'all',
                    difficulty: 'all',
                    type: 'all',
                    assignee: 'all',
                    reward: 'all'
                  });
                }}
              >
                Clear Filters
              </Button>
            )}
          </CardBody>
        </Card>
      )}
    </div>
  );
};

// Mission Card Component
const MissionCard = ({ mission, index, onClaim, onView, currentUser }) => {
  const getDifficultyColor = (difficulty) => {
    const colors = {
      'easy': 'success',
      'medium': 'warning',
      'hard': 'danger',
      'expert': 'secondary'
    };
    return colors[difficulty] || 'default';
  };

  const getStatusColor = (status) => {
    const colors = {
      'available': 'success',
      'active': 'primary',
      'review': 'warning',
      'completed': 'secondary',
      'blocked': 'danger'
    };
    return colors[status] || 'default';
  };

  const getUrgencyIcon = (urgency) => {
    const icons = {
      'high': '🔥',
      'medium': '⚡',
      'low': '📅'
    };
    return icons[urgency] || '📅';
  };

  const isMyMission = mission.assignee_id === currentUser?.id;
  const canClaim = mission.claimable && !isMyMission;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      className="h-full"
    >
      <Card className="h-full hover:shadow-lg transition-shadow duration-200">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1">
              <h3 className="text-lg font-semibold line-clamp-2 mb-1">
                {mission.title}
              </h3>
              <div className="flex items-center gap-2 mb-2">
                <Chip
                  color={getStatusColor(mission.missionStatus)}
                  size="sm"
                  variant="flat"
                >
                  {mission.missionStatus}
                </Chip>
                <Chip
                  color={getDifficultyColor(mission.difficultyRating)}
                  size="sm"
                  variant="flat"
                >
                  {mission.difficultyRating}
                </Chip>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-default-500">
                {getUrgencyIcon(mission.urgency)}
              </div>
            </div>
          </div>
        </CardHeader>

        <CardBody className="pt-0">
          {/* Mission Description */}
          <p className="text-sm text-default-600 line-clamp-3 mb-3">
            {mission.description || 'No description provided'}
          </p>

          {/* Skills Required */}
          {mission.skillsRequired.length > 0 && (
            <div className="mb-3">
              <div className="text-xs text-default-500 mb-1">Skills Required:</div>
              <div className="flex flex-wrap gap-1">
                {mission.skillsRequired.map((skill, idx) => (
                  <Chip key={idx} size="sm" variant="bordered" className="text-xs">
                    {skill}
                  </Chip>
                ))}
              </div>
            </div>
          )}

          {/* Mission Metrics */}
          <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-yellow-500">💰</span>
              <span className="font-medium">{mission.rewardOrbs} ORBs</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-blue-500">⏱️</span>
              <span>{mission.estimatedDuration}h</span>
            </div>
          </div>

          {/* Progress Bar (for active missions) */}
          {mission.missionStatus === 'active' && (
            <div className="mb-3">
              <div className="flex justify-between text-xs mb-1">
                <span>Progress</span>
                <span>{mission.progress}%</span>
              </div>
              <Progress value={mission.progress} color="primary" size="sm" />
            </div>
          )}

          {/* Assignee Info */}
          {mission.assignee && (
            <div className="flex items-center gap-2 mb-3">
              <Avatar
                src={mission.assignee.avatar_url}
                name={mission.assignee.display_name || mission.assignee.full_name}
                size="sm"
              />
              <span className="text-sm text-default-600">
                {mission.assignee.display_name || mission.assignee.full_name || 'Unknown'}
              </span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto">
            <Button
              size="sm"
              variant="flat"
              onClick={onView}
              className="flex-1"
            >
              View Details
            </Button>

            {canClaim && (
              <Button
                size="sm"
                color="primary"
                onClick={onClaim}
                className="flex-1"
              >
                Claim Mission
              </Button>
            )}

            {isMyMission && mission.missionStatus === 'active' && (
              <Button
                size="sm"
                color="success"
                variant="flat"
                className="flex-1"
              >
                Continue
              </Button>
            )}
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default MissionBoard;
