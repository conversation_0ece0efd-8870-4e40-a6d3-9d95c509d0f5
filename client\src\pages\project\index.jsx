import axios from "axios";
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import moment from "moment/moment";
import LoadingAnimation from "../../components/layout/LoadingAnimation";
import { getAuth } from "firebase/auth";

const ProjectIndex = () => {
  const [projects, setProjects] = useState(null);
  const [loading, setLoading] = useState(true);
  const auth = getAuth();

  // Function to fetch projects with Firebase token
  const fetchProjects = async () => {
    setLoading(true);
    try {
      const token = await auth.currentUser?.getIdToken();
      if (!token) throw new Error("Authentication error. Please log in again.");

      const { data } = await axios.get("/project/index", {
        headers: { Authorization: `Bearer ${token}` },
      });

      setProjects(data);
    } catch (error) {
      console.error("Error fetching project data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  return (
    <>
      <div className="container mt-5">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1>Projects</h1>
          <Link to="/project/new" className="btn btn-primary btn-lg fw-bold">
            Create a Project
          </Link>
        </div>

        {/* Show loading animation while fetching */}
        {loading ? (
          <LoadingAnimation />
        ) : projects?.length > 0 ? (
          <ul className="list-group">
            {/* Header Row */}
            <li
              className="list-group-item"
              style={{
                backgroundColor: "#f8f9fa",
                color: "#495057",
                padding: "15px",
                borderRadius: "0.5rem 0.5rem 0 0",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div className="row fw-bold">
                <div className="col-md-4">Date Created</div>
                <div className="col-md-4">Project Title</div>
                <div className="col-md-4">Project ID</div>
              </div>
            </li>
            {projects.map((project) => {
              return (
                <li key={project._id} className="list-group-item">
                  <div className="row">
                    {/* Date Created */}
                    <div className="col-md-4">
                      {moment(project.dateCreated).format("l")}
                    </div>

                    {/* Project Title */}
                    <div className="col-md-4 fw-bold">{project.title}</div>

                    {/* Project Link */}
                    <div className="col-md-4">
                      <Link
                        to={`/project/${project._id}`}
                        className="text-decoration-none text-primary"
                      >
                        {project._id}
                      </Link>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        ) : (
          <p className="text-center text-muted">No projects found.</p>
        )}
      </div>
    </>
  );
};

export default ProjectIndex;
