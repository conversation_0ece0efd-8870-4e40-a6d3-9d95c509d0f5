import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>dal<PERSON>ody, 
  <PERSON>dal<PERSON>ooter,
  Button,
  Chip,
  Progress,
  Avatar,
  Card,
  CardBody,
  Textarea,
  Divider
} from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * MissionDetailsModal Component
 * 
 * Detailed mission view with full information, skill analysis, and actions
 * Follows wireframe specifications for comprehensive mission details
 */
const MissionDetailsModal = ({ 
  mission, 
  isOpen, 
  onClose, 
  onClaim, 
  onAskQuestion,
  currentUser,
  skillMatchAnalysis = null 
}) => {
  const [questionText, setQuestionText] = useState('');
  const [showQuestionForm, setShowQuestionForm] = useState(false);

  if (!mission) return null;

  // Get difficulty display with full details
  const getDifficultyDetails = (difficulty) => {
    const difficultyMap = {
      'easy': { 
        stars: '⭐⭐⭐☆☆☆☆☆☆☆', 
        color: 'success', 
        rating: '3/10',
        description: 'Beginner-friendly, minimal complexity'
      },
      'medium': { 
        stars: '⭐⭐⭐⭐⭐☆☆☆☆☆', 
        color: 'warning', 
        rating: '5/10',
        description: 'Moderate complexity, some experience needed'
      },
      'hard': { 
        stars: '⭐⭐⭐⭐⭐⭐⭐☆☆☆', 
        color: 'danger', 
        rating: '7/10',
        description: 'High complexity, advanced skills required'
      },
      'expert': { 
        stars: '⭐⭐⭐⭐⭐⭐⭐⭐⭐☆', 
        color: 'secondary', 
        rating: '9/10',
        description: 'Expert level, specialized knowledge required'
      }
    };
    return difficultyMap[difficulty] || difficultyMap['medium'];
  };

  // Get mission type details
  const getMissionTypeDetails = (type) => {
    const typeMap = {
      'bug': { icon: '🔥', name: 'Bug Fix', description: 'Fix existing issues or problems' },
      'feature': { icon: '⚡', name: 'Feature Development', description: 'Build new functionality' },
      'design': { icon: '🎨', name: 'Design Work', description: 'UI/UX design and visual assets' },
      'documentation': { icon: '📋', name: 'Documentation', description: 'Write or update documentation' },
      'testing': { icon: '🧪', name: 'Testing', description: 'Quality assurance and testing' },
      'general': { icon: '⚔️', name: 'General Task', description: 'General project work' }
    };
    return typeMap[type] || typeMap['general'];
  };

  // Get reward breakdown
  const getRewardBreakdown = () => {
    const breakdown = [];
    
    if (mission.budget_usd) {
      breakdown.push({ label: 'Fixed Payment', value: `$${mission.budget_usd}`, type: 'usd' });
    }
    
    if (mission.rewardOrbs) {
      breakdown.push({ label: 'ORB Reward', value: `${mission.rewardOrbs} ORBs`, type: 'orb' });
    }
    
    if (mission.estimatedDuration) {
      const hourlyRate = mission.budget_usd ? (mission.budget_usd / mission.estimatedDuration).toFixed(2) : null;
      if (hourlyRate) {
        breakdown.push({ label: 'Hourly Rate', value: `$${hourlyRate}/hr`, type: 'rate' });
      }
    }
    
    return breakdown;
  };

  // Handle question submission
  const handleQuestionSubmit = () => {
    if (questionText.trim()) {
      onAskQuestion(mission, questionText);
      setQuestionText('');
      setShowQuestionForm(false);
    }
  };

  const difficultyInfo = getDifficultyDetails(mission.difficultyRating);
  const typeInfo = getMissionTypeDetails(mission.missionType);
  const rewardBreakdown = getRewardBreakdown();
  const isMyMission = mission.assignee_id === currentUser?.id;
  const canClaim = mission.claimable && !isMyMission;

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="3xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{typeInfo.icon}</span>
            <div>
              <h2 className="text-xl font-bold">{mission.title}</h2>
              <p className="text-sm text-default-500">{typeInfo.name} • {mission.project?.name || 'General Mission'}</p>
            </div>
          </div>
        </ModalHeader>

        <ModalBody>
          {/* Mission Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* Difficulty Card */}
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-lg mb-2">{difficultyInfo.stars}</div>
                <div className="font-semibold text-sm">{difficultyInfo.rating} Difficulty</div>
                <div className="text-xs text-default-500">{difficultyInfo.description}</div>
              </CardBody>
            </Card>

            {/* Timeline Card */}
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl mb-2">⏰</div>
                <div className="font-semibold text-sm">
                  {mission.estimatedDuration ? `${mission.estimatedDuration} hours` : 'Flexible'}
                </div>
                <div className="text-xs text-default-500">Estimated duration</div>
              </CardBody>
            </Card>

            {/* Status Card */}
            <Card>
              <CardBody className="p-4 text-center">
                <div className="text-2xl mb-2">
                  {mission.missionStatus === 'available' ? '🟢' : 
                   mission.missionStatus === 'active' ? '🟡' : 
                   mission.missionStatus === 'completed' ? '🟢' : '🔴'}
                </div>
                <div className="font-semibold text-sm capitalize">{mission.missionStatus}</div>
                <div className="text-xs text-default-500">Current status</div>
              </CardBody>
            </Card>
          </div>

          {/* Reward Breakdown */}
          {rewardBreakdown.length > 0 && (
            <Card className="mb-6">
              <CardBody className="p-4">
                <h3 className="font-semibold mb-3">💰 Reward Breakdown</h3>
                <div className="space-y-2">
                  {rewardBreakdown.map((reward, idx) => (
                    <div key={idx} className="flex justify-between items-center">
                      <span className="text-sm text-default-600">{reward.label}</span>
                      <span className={`font-semibold ${
                        reward.type === 'usd' ? 'text-success' : 
                        reward.type === 'orb' ? 'text-primary' : 'text-default-700'
                      }`}>
                        {reward.value}
                      </span>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Skills Required */}
          {mission.skillsRequired && mission.skillsRequired.length > 0 && (
            <Card className="mb-6">
              <CardBody className="p-4">
                <h3 className="font-semibold mb-3">🎯 Skills Required</h3>
                <div className="flex flex-wrap gap-2">
                  {mission.skillsRequired.map((skill, idx) => (
                    <Chip key={idx} color="primary" variant="flat">
                      {skill}
                    </Chip>
                  ))}
                </div>
                {skillMatchAnalysis && (
                  <div className="mt-3 p-3 bg-success-50 rounded-lg">
                    <div className="text-sm font-medium text-success-700">
                      🎯 Skill Match: {skillMatchAnalysis.score}%
                    </div>
                    <div className="text-xs text-success-600 mt-1">
                      {skillMatchAnalysis.matchedSkills.length} of {mission.skillsRequired.length} skills match your profile
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          )}

          {/* Mission Description */}
          <Card className="mb-6">
            <CardBody className="p-4">
              <h3 className="font-semibold mb-3">📋 Mission Description</h3>
              <div className="text-sm text-default-700 whitespace-pre-wrap">
                {mission.description || 'No detailed description provided.'}
              </div>
            </CardBody>
          </Card>

          {/* Assignee Information */}
          {mission.assignee && (
            <Card className="mb-6">
              <CardBody className="p-4">
                <h3 className="font-semibold mb-3">👤 Assigned To</h3>
                <div className="flex items-center space-x-3">
                  <Avatar 
                    name={mission.assignee.display_name || mission.assignee.full_name} 
                    size="md"
                  />
                  <div>
                    <div className="font-medium">
                      {mission.assignee.display_name || mission.assignee.full_name || 'Unknown User'}
                    </div>
                    <div className="text-sm text-default-500">
                      Rating: 4.8★ • 23 missions completed
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          )}

          {/* Progress (for active missions) */}
          {mission.missionStatus === 'active' && mission.progress > 0 && (
            <Card className="mb-6">
              <CardBody className="p-4">
                <h3 className="font-semibold mb-3">📊 Progress</h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Completion</span>
                    <span>{mission.progress}%</span>
                  </div>
                  <Progress value={mission.progress} color="primary" />
                  {mission.logged_hours && mission.estimated_hours && (
                    <div className="flex justify-between text-sm text-default-500">
                      <span>Time logged</span>
                      <span>{mission.logged_hours}h / {mission.estimated_hours}h</span>
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          )}

          {/* Question Form */}
          {showQuestionForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Card className="mb-6">
                <CardBody className="p-4">
                  <h3 className="font-semibold mb-3">💬 Ask a Question</h3>
                  <Textarea
                    placeholder="What would you like to know about this mission?"
                    value={questionText}
                    onChange={(e) => setQuestionText(e.target.value)}
                    minRows={3}
                    className="mb-3"
                  />
                  <div className="flex gap-2">
                    <Button size="sm" color="primary" onPress={handleQuestionSubmit}>
                      Send Question
                    </Button>
                    <Button size="sm" variant="flat" onPress={() => setShowQuestionForm(false)}>
                      Cancel
                    </Button>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          )}
        </ModalBody>

        <ModalFooter>
          <div className="flex gap-2 w-full">
            {!showQuestionForm && (
              <Button 
                variant="flat" 
                onPress={() => setShowQuestionForm(true)}
                startContent={<i className="bi bi-chat-dots"></i>}
              >
                Ask Question
              </Button>
            )}
            
            {canClaim && (
              <Button 
                color="primary" 
                onPress={() => {
                  onClaim(mission.id);
                  onClose();
                }}
                className="flex-1"
              >
                Claim This Mission
              </Button>
            )}
            
            {isMyMission && (
              <Button 
                color="success" 
                variant="flat"
                className="flex-1"
              >
                Continue Working
              </Button>
            )}
            
            <Button variant="light" onPress={onClose}>
              Close
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default MissionDetailsModal;
