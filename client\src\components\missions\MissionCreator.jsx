import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Input, Textarea, Select, SelectItem } from '@heroui/react';

const MissionCreator = ({ onClose, onMissionCreated }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    mission_type: 'skill_development',
    difficulty_level: 'medium',
    difficulty_points: 25,
    estimated_hours: 1,
    is_public: true
  });

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // TODO: Implement mission creation API call
      console.log('Creating mission:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onMissionCreated();
      onClose();
    } catch (error) {
      console.error('Error creating mission:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal isOpen={true} onClose={onClose} size="2xl">
      <ModalContent>
        <ModalHeader>
          <h3>Create New Mission</h3>
        </ModalHeader>
        <ModalBody>
          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              label="Mission Title"
              placeholder="Enter mission title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
            />

            <Textarea
              label="Description"
              placeholder="Describe the mission objectives and requirements"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={4}
              required
            />

            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Mission Type"
                value={formData.mission_type}
                onChange={(e) => setFormData({ ...formData, mission_type: e.target.value })}
              >
                <SelectItem key="skill_development" value="skill_development">📚 Skill Development</SelectItem>
                <SelectItem key="collaboration" value="collaboration">🤝 Collaboration</SelectItem>
                <SelectItem key="achievement" value="achievement">🏆 Achievement</SelectItem>
                <SelectItem key="exploration" value="exploration">🔍 Exploration</SelectItem>
                <SelectItem key="social" value="social">👥 Social</SelectItem>
              </Select>

              <Select
                label="Difficulty Level"
                value={formData.difficulty_level}
                onChange={(e) => setFormData({ ...formData, difficulty_level: e.target.value })}
              >
                <SelectItem key="easy" value="easy">Easy</SelectItem>
                <SelectItem key="medium" value="medium">Medium</SelectItem>
                <SelectItem key="hard" value="hard">Hard</SelectItem>
                <SelectItem key="expert" value="expert">Expert</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Input
                type="number"
                label="Points Reward"
                value={formData.difficulty_points.toString()}
                onChange={(e) => setFormData({ ...formData, difficulty_points: parseInt(e.target.value) || 0 })}
                min="1"
                max="1000"
              />

              <Input
                type="number"
                label="Estimated Hours"
                value={formData.estimated_hours.toString()}
                onChange={(e) => setFormData({ ...formData, estimated_hours: parseInt(e.target.value) || 1 })}
                min="1"
                max="100"
              />
            </div>
          </form>
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            color="primary" 
            onClick={handleSubmit}
            loading={loading}
            disabled={!formData.title.trim() || !formData.description.trim()}
          >
            Create Mission
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default MissionCreator;
