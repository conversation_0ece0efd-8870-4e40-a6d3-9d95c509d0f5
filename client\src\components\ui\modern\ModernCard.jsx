import React from 'react';
import {
  <PERSON>,
  CardHeader,
  Card<PERSON><PERSON>,
  CardFooter
} from '../heroui';

/**
 * ModernCard Component
 *
 * A modern card component that uses shadcn/ui's Card components.
 * This component is designed to be a drop-in replacement for Bootstrap cards.
 *
 * @param {Object} props - Component props
 * @param {string} [props.title] - Card title
 * @param {string} [props.subtitle] - Card subtitle/description
 * @param {React.ReactNode} [props.header] - Custom header content
 * @param {React.ReactNode} props.children - Card content
 * @param {React.ReactNode} [props.footer] - Card footer content
 * @param {string} [props.className] - Additional CSS classes for the card
 * @param {string} [props.headerClassName] - Additional CSS classes for the header
 * @param {string} [props.bodyClassName] - Additional CSS classes for the body
 * @param {string} [props.footerClassName] - Additional CSS classes for the footer
 * @returns {React.ReactElement} - ModernCard component
 */
const ModernCard = ({
  title,
  subtitle,
  header,
  children,
  footer,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  ...props
}) => {

  return (
    <Card className={className} {...props}>
      {(title || subtitle || header) && (
        <CardHeader className={headerClassName}>
          {header || (
            <>
              {title && <h3 className="text-lg font-semibold">{title}</h3>}
              {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
            </>
          )}
        </CardHeader>
      )}

      <CardBody className={bodyClassName}>
        {children}
      </CardBody>

      {footer && (
        <CardFooter className={footerClassName}>
          {footer}
        </CardFooter>
      )}
    </Card>
  );
};

export default ModernCard;
