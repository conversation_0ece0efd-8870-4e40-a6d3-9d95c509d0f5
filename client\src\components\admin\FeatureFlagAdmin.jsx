import React from 'react';

import { useFeatureFlags } from '../../contexts/feature-flags.context';

/**
 * FeatureFlagAdmin Component
 *
 * An admin panel for managing feature flags during development and testing.
 * This component allows developers to toggle features on and off.
 */
const FeatureFlagAdmin = () => {
  const {
    flags,
    loading,
    toggleFeature,
    resetFeatures
  } = useFeatureFlags();

  if (loading) {
    return <div>Loading feature flags...</div>;
  }

  // Feature flag descriptions
  const flagDescriptions = {
    'new-ui': 'Modern UI components using Tailwind CSS and shadcn/ui',
    'tremor-charts': 'Enhanced data visualization with Tremor',
    'tanstack-query': 'Improved API management with TanStack Query',
    'new-forms': 'Enhanced form handling with React Hook Form and Zod',
    'new-auth': 'Improved authentication with <PERSON>'
  };

  return (
    <Card className="mb-4">
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h5 className="mb-0">Feature Flags</h5>
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={resetFeatures}
        >
          Reset All
        </Button>
      </Card.Header>
      <Card.Body>
        <Alert variant="info">
          Use these toggles to enable or disable new features during development and testing.
          Changes will be saved to your browser and persist across sessions.
        </Alert>

        <div className="mt-3">
          {Object.keys(flags).map(flagName => (
            <Form.Check
              key={flagName}
              type="switch"
              id={`feature-flag-${flagName}`}
              label={
                <div>
                  <strong>{flagName}</strong>
                  <div className="text-muted small">
                    {flagDescriptions[flagName] || 'No description available'}
                  </div>
                </div>
              }
              checked={flags[flagName]}
              onChange={() => toggleFeature(flagName)}
              className="mb-3"
            />
          ))}
        </div>
      </Card.Body>
      <Card.Footer className="text-muted">
        <small>
          Feature flags are stored in your browser's local storage and in your user preferences if you're logged in.
        </small>
      </Card.Footer>
    </Card>
  );
};

export default FeatureFlagAdmin;
