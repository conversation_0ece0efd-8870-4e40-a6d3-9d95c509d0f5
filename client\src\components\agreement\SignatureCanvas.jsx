import React, { useRef, useState, useEffect } from 'react';
import SignaturePad from 'react-signature-canvas';

/**
 * SignatureCanvas Component
 * 
 * A component for capturing digital signatures using a canvas.
 * Supports drawing with mouse/touch, clearing, and saving the signature.
 */
const SignatureCanvas = ({ onSave, initialValue = null }) => {
  const sigCanvas = useRef(null);
  const [isEmpty, setIsEmpty] = useState(true);
  const [isTypedSignature, setIsTypedSignature] = useState(false);
  const [typedName, setTypedName] = useState('');
  const [selectedFont, setSelectedFont] = useState('Indie Flower');

  // Available signature fonts
  const signatureFonts = [
    { name: 'Indie Flower', label: 'Handwritten' },
    { name: 'Pacifico', label: 'Flowing' },
    { name: 'Sacramento', label: 'Elegant' },
    { name: 'Caveat', label: 'Casual' },
    { name: 'Dancing Script', label: 'Formal' }
  ];

  // Load fonts
  useEffect(() => {
    // Add Google Fonts link if not already present
    if (!document.getElementById('signature-fonts')) {
      const link = document.createElement('link');
      link.id = 'signature-fonts';
      link.rel = 'stylesheet';
      link.href = 'https://fonts.googleapis.com/css2?family=Indie+Flower&family=Pacifico&family=Sacramento&family=Caveat&family=Dancing+Script&display=swap';
      document.head.appendChild(link);
    }
  }, []);

  // Set initial value if provided
  useEffect(() => {
    if (initialValue && sigCanvas.current) {
      // If it's a data URL, load it into the canvas
      if (typeof initialValue === 'string' && initialValue.startsWith('data:')) {
        const img = new Image();
        img.onload = () => {
          const ctx = sigCanvas.current.getCanvas().getContext('2d');
          ctx.drawImage(img, 0, 0);
          setIsEmpty(false);
        };
        img.src = initialValue;
      }
    }
  }, [initialValue]);

  // Clear the signature canvas
  const clearSignature = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
    }
  };

  // Save the signature as a data URL
  const saveSignature = () => {
    if (isTypedSignature) {
      // For typed signatures, create a canvas with the typed name
      const canvas = document.createElement('canvas');
      canvas.width = 500;
      canvas.height = 200;
      const ctx = canvas.getContext('2d');
      
      // Set up the canvas
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Configure text style
      ctx.fillStyle = 'black';
      ctx.font = `48px "${selectedFont}"`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Draw the text
      ctx.fillText(typedName, canvas.width / 2, canvas.height / 2);
      
      // Convert to data URL and save
      const dataURL = canvas.toDataURL('image/png');
      onSave(dataURL);
    } else if (sigCanvas.current && !isEmpty) {
      // For drawn signatures, get the data URL from the signature pad
      const dataURL = sigCanvas.current.toDataURL('image/png');
      onSave(dataURL);
    }
  };

  // Toggle between drawn and typed signature
  const toggleSignatureType = () => {
    setIsTypedSignature(!isTypedSignature);
    if (sigCanvas.current) {
      sigCanvas.current.clear();
    }
    setIsEmpty(true);
    setTypedName('');
  };

  // Check if signature pad is empty after each stroke
  const handleEndStroke = () => {
    if (sigCanvas.current) {
      setIsEmpty(sigCanvas.current.isEmpty());
    }
  };

  return (
    <div className="signature-canvas-container">
      <div className="signature-type-toggle">
        <button
          type="button"
          className={`toggle-button ${!isTypedSignature ? 'active' : ''}`}
          onClick={() => setIsTypedSignature(false)}
        >
          Draw Signature
        </button>
        <button
          type="button"
          className={`toggle-button ${isTypedSignature ? 'active' : ''}`}
          onClick={() => setIsTypedSignature(true)}
        >
          Type Signature
        </button>
      </div>

      {isTypedSignature ? (
        <div className="typed-signature-container">
          <div className="font-selector">
            <label htmlFor="signatureFont">Font Style:</label>
            <select
              id="signatureFont"
              value={selectedFont}
              onChange={(e) => setSelectedFont(e.target.value)}
              className="font-select"
            >
              {signatureFonts.map((font) => (
                <option key={font.name} value={font.name} style={{ fontFamily: font.name }}>
                  {font.label}
                </option>
              ))}
            </select>
          </div>
          <div className="typed-signature-preview" style={{ fontFamily: selectedFont }}>
            {typedName || 'Your Signature'}
          </div>
          <input
            type="text"
            value={typedName}
            onChange={(e) => {
              setTypedName(e.target.value);
              setIsEmpty(e.target.value.trim() === '');
            }}
            placeholder="Type your signature here"
            className="typed-signature-input"
          />
        </div>
      ) : (
        <div className="drawn-signature-container">
          <SignaturePad
            ref={sigCanvas}
            canvasProps={{
              className: 'signature-pad',
              width: 500,
              height: 200
            }}
            onEnd={handleEndStroke}
          />
          <div className="signature-instructions">
            <p>Sign using your mouse or touch screen</p>
          </div>
        </div>
      )}

      <div className="signature-actions">
        <button
          type="button"
          className="clear-button"
          onClick={clearSignature}
          disabled={isEmpty}
        >
          Clear
        </button>
        <button
          type="button"
          className="save-button"
          onClick={saveSignature}
          disabled={isEmpty}
        >
          Save Signature
        </button>
      </div>
    </div>
  );
};

export default SignatureCanvas;
