import React, { useState } from 'react';
import { toast } from 'react-hot-toast';

const RoadmapPhaseEditor = ({ phase, onSave, onCancel }) => {
  const [title, setTitle] = useState(phase.title || '');
  const [timeframe, setTimeframe] = useState(phase.timeframe || '');
  const [sections, setSections] = useState(phase.sections || []);
  const [expandedSections, setExpandedSections] = useState({});

  // Toggle section expansion
  const toggleSection = (sectionId) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Update section title
  const updateSectionTitle = (sectionId, newTitle) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { ...section, title: newTitle } 
          : section
      )
    );
  };

  // Update task text
  const updateTaskText = (sectionId, taskId, newText) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              tasks: section.tasks.map(task => 
                task.id === taskId 
                  ? { ...task, text: newText } 
                  : task
              ) 
            } 
          : section
      )
    );
  };

  // Toggle task completion
  const toggleTaskCompletion = (sectionId, taskId) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              tasks: section.tasks.map(task => 
                task.id === taskId 
                  ? { ...task, completed: !task.completed } 
                  : task
              ) 
            } 
          : section
      )
    );
  };

  // Add a new task to a section
  const addTask = (sectionId) => {
    const section = sections.find(s => s.id === sectionId);
    if (!section) return;

    // Generate a new task ID based on section ID and next number
    const taskNumber = section.tasks.length + 1;
    const newTaskId = `${sectionId}.${taskNumber}`;
    
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              tasks: [
                ...section.tasks, 
                { id: newTaskId, text: 'New task', completed: false }
              ] 
            } 
          : section
      )
    );
  };

  // Add a new section
  const addSection = () => {
    // Generate a new section ID
    const sectionNumber = sections.length + 1;
    const newSectionId = `${phase.id}.${sectionNumber}`;
    
    const newSection = {
      id: newSectionId,
      title: 'New Section',
      tasks: []
    };
    
    setSections(prev => [...prev, newSection]);
    
    // Auto-expand the new section
    setExpandedSections(prev => ({
      ...prev,
      [newSectionId]: true
    }));
  };

  // Remove a task
  const removeTask = (sectionId, taskId) => {
    setSections(prev => 
      prev.map(section => 
        section.id === sectionId 
          ? { 
              ...section, 
              tasks: section.tasks.filter(task => task.id !== taskId)
            } 
          : section
      )
    );
  };

  // Remove a section
  const removeSection = (sectionId) => {
    setSections(prev => prev.filter(section => section.id !== sectionId));
  };

  // Handle save
  const handleSave = () => {
    if (!title.trim()) {
      toast.error('Phase title cannot be empty');
      return;
    }

    const updatedPhase = {
      ...phase,
      title,
      timeframe,
      sections
    };

    onSave(updatedPhase);
  };

  return (
    <div className="roadmap-phase-editor">
      <div className="editor-header">
        <h2>Edit Phase {phase.id}</h2>
      </div>
      
      <div className="editor-form">
        <div className="form-group">
          <label>Phase Title</label>
          <input 
            type="text" 
            value={title} 
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Phase title"
          />
        </div>
        
        <div className="form-group">
          <label>Timeframe</label>
          <input 
            type="text" 
            value={timeframe} 
            onChange={(e) => setTimeframe(e.target.value)}
            placeholder="e.g., Q3 2024, In Progress, etc."
          />
        </div>
        
        <div className="sections-container">
          <div className="sections-header">
            <h3>Sections</h3>
            <button 
              type="button" 
              className="add-section-btn"
              onClick={addSection}
            >
              Add Section
            </button>
          </div>
          
          {sections.map(section => (
            <div key={section.id} className="section-item">
              <div 
                className="section-header"
                onClick={() => toggleSection(section.id)}
              >
                <div className="section-title-container">
                  <span className="section-id">{section.id}</span>
                  <input 
                    type="text" 
                    value={section.title} 
                    onChange={(e) => updateSectionTitle(section.id, e.target.value)}
                    onClick={(e) => e.stopPropagation()}
                    placeholder="Section title"
                  />
                </div>
                <div className="section-actions">
                  <button 
                    type="button" 
                    className="remove-section-btn"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (window.confirm(`Are you sure you want to remove section ${section.id}?`)) {
                        removeSection(section.id);
                      }
                    }}
                  >
                    Remove
                  </button>
                  <span className={`expand-icon ${expandedSections[section.id] ? 'expanded' : ''}`}>
                    ▼
                  </span>
                </div>
              </div>
              
              {expandedSections[section.id] && (
                <div className="section-content">
                  <div className="tasks-header">
                    <h4>Tasks</h4>
                    <button 
                      type="button" 
                      className="add-task-btn"
                      onClick={() => addTask(section.id)}
                    >
                      Add Task
                    </button>
                  </div>
                  
                  <ul className="tasks-list">
                    {section.tasks.map(task => (
                      <li key={task.id} className="task-item">
                        <div className="task-checkbox">
                          <input 
                            type="checkbox" 
                            checked={task.completed} 
                            onChange={() => toggleTaskCompletion(section.id, task.id)}
                          />
                        </div>
                        <div className="task-content">
                          <span className="task-id">{task.id}</span>
                          <input 
                            type="text" 
                            value={task.text} 
                            onChange={(e) => updateTaskText(section.id, task.id, e.target.value)}
                            className={task.completed ? 'completed' : ''}
                            placeholder="Task description"
                          />
                        </div>
                        <button 
                          type="button" 
                          className="remove-task-btn"
                          onClick={() => removeTask(section.id, task.id)}
                        >
                          ×
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      
      <div className="editor-actions">
        <button 
          type="button" 
          className="cancel-btn"
          onClick={onCancel}
        >
          Cancel
        </button>
        <button 
          type="button" 
          className="save-btn"
          onClick={handleSave}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default RoadmapPhaseEditor;
