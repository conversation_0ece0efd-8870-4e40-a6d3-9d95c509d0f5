import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ooter, Button, Input, Textarea, Select, SelectItem, Chip, Switch } from '@heroui/react';

/**
 * Bounty Posting Modal Component - Create New Bounty Interface
 * 
 * Features:
 * - Comprehensive bounty creation form
 * - Skill requirements and difficulty setting
 * - Payment structure configuration (fixed vs milestone)
 * - Application requirements and filters
 * - Preview and validation before posting
 */
const BountyPostingModal = ({ isOpen, onClose, onSubmit, currentUser }) => {
  const [bountyData, setBountyData] = useState({
    title: '',
    description: '',
    category: '',
    subcategory: '',
    value: '',
    paymentType: 'fixed',
    timeline: '',
    difficulty: 5,
    skillsRequired: [],
    requirements: {
      portfolio: false,
      skillVerification: false,
      certification: false,
      minRating: 0,
      experience: 'any'
    },
    milestones: [
      { name: 'Project Start', percentage: 50, amount: 0 },
      { name: 'Final Delivery', percentage: 50, amount: 0 }
    ],
    featured: false,
    urgent: false,
    maxApplications: 10
  });

  const [currentSkill, setCurrentSkill] = useState('');
  const [errors, setErrors] = useState({});

  // Skill suggestions
  const skillSuggestions = [
    'React', 'Vue.js', 'Angular', 'Node.js', 'Python', 'JavaScript', 'TypeScript',
    'UI/UX Design', 'Figma', 'Adobe Creative Suite', 'Photoshop', 'Illustrator',
    'Mobile Development', 'React Native', 'Flutter', 'iOS', 'Android',
    'Backend Development', 'APIs', 'Database', 'MongoDB', 'PostgreSQL',
    'DevOps', 'AWS', 'Docker', 'Kubernetes', 'CI/CD',
    'AI/ML', 'Data Science', 'Machine Learning', 'TensorFlow', 'PyTorch',
    'Blockchain', 'Solidity', 'Smart Contracts', 'Web3',
    'Testing', 'QA', 'Automation', 'Security', 'Penetration Testing'
  ];

  // Categories and subcategories
  const categories = {
    development: ['Frontend', 'Backend', 'Full-stack', 'Mobile', 'Blockchain', 'AI/ML'],
    design: ['UI/UX', 'Graphic Design', 'Brand Identity', 'Web Design', 'Mobile Design'],
    testing: ['QA Testing', 'Automation', 'Security Audit', 'Performance Testing'],
    writing: ['Technical Writing', 'Content Creation', 'Documentation', 'Copywriting'],
    marketing: ['Digital Marketing', 'SEO', 'Social Media', 'Content Strategy']
  };

  // Add skill to requirements
  const addSkill = () => {
    if (currentSkill.trim() && !bountyData.skillsRequired.includes(currentSkill.trim())) {
      setBountyData(prev => ({
        ...prev,
        skillsRequired: [...prev.skillsRequired, currentSkill.trim()]
      }));
      setCurrentSkill('');
    }
  };

  // Remove skill from requirements
  const removeSkill = (skillToRemove) => {
    setBountyData(prev => ({
      ...prev,
      skillsRequired: prev.skillsRequired.filter(skill => skill !== skillToRemove)
    }));
  };

  // Update milestone amounts when total value changes
  React.useEffect(() => {
    if (bountyData.value && bountyData.paymentType === 'milestone') {
      const totalValue = parseFloat(bountyData.value);
      setBountyData(prev => ({
        ...prev,
        milestones: prev.milestones.map(milestone => ({
          ...milestone,
          amount: Math.round((milestone.percentage / 100) * totalValue)
        }))
      }));
    }
  }, [bountyData.value, bountyData.paymentType]);

  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!bountyData.title.trim()) newErrors.title = 'Title is required';
    if (!bountyData.description.trim()) newErrors.description = 'Description is required';
    if (!bountyData.category) newErrors.category = 'Category is required';
    if (!bountyData.value || parseFloat(bountyData.value) <= 0) newErrors.value = 'Valid budget is required';
    if (!bountyData.timeline.trim()) newErrors.timeline = 'Timeline is required';
    if (bountyData.skillsRequired.length === 0) newErrors.skills = 'At least one skill is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;
    
    const submissionData = {
      ...bountyData,
      value: parseFloat(bountyData.value),
      postedAt: new Date(),
      poster: {
        id: currentUser?.id || 'current-user',
        name: currentUser?.display_name || 'Current User',
        rating: 4.8,
        reviewCount: 23,
        verified: true
      },
      applicantCount: 0,
      viewCount: 0,
      deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      tags: bountyData.skillsRequired.slice(0, 3)
    };
    
    onSubmit(submissionData);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Post New Bounty</h2>
          <p className="text-default-600 font-normal">Create a high-value bounty to attract top talent</p>
        </ModalHeader>
        
        <ModalBody>
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Basic Information</h3>
              
              <div>
                <label className="block text-sm font-medium mb-2">Bounty Title *</label>
                <Input
                  placeholder="e.g., AI-Powered Analytics Dashboard"
                  value={bountyData.title}
                  onChange={(e) => setBountyData(prev => ({ ...prev, title: e.target.value }))}
                  isInvalid={!!errors.title}
                  errorMessage={errors.title}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description *</label>
                <Textarea
                  placeholder="Provide a detailed description of the work required, deliverables, and any specific requirements..."
                  value={bountyData.description}
                  onChange={(e) => setBountyData(prev => ({ ...prev, description: e.target.value }))}
                  minRows={4}
                  isInvalid={!!errors.description}
                  errorMessage={errors.description}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Category *</label>
                  <Select
                    placeholder="Select category"
                    selectedKeys={bountyData.category ? [bountyData.category] : []}
                    onSelectionChange={(keys) => setBountyData(prev => ({ 
                      ...prev, 
                      category: Array.from(keys)[0] || '',
                      subcategory: '' // Reset subcategory when category changes
                    }))}
                    isInvalid={!!errors.category}
                    errorMessage={errors.category}
                  >
                    {Object.keys(categories).map((category) => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Subcategory</label>
                  <Select
                    placeholder="Select subcategory"
                    selectedKeys={bountyData.subcategory ? [bountyData.subcategory] : []}
                    onSelectionChange={(keys) => setBountyData(prev => ({ ...prev, subcategory: Array.from(keys)[0] || '' }))}
                    isDisabled={!bountyData.category}
                  >
                    {bountyData.category && categories[bountyData.category]?.map((subcategory) => (
                      <SelectItem key={subcategory.toLowerCase().replace(/\s+/g, '-')} value={subcategory}>
                        {subcategory}
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </div>
            </div>

            {/* Budget and Timeline */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Budget & Timeline</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Budget (USD) *</label>
                  <Input
                    type="number"
                    placeholder="5000"
                    value={bountyData.value}
                    onChange={(e) => setBountyData(prev => ({ ...prev, value: e.target.value }))}
                    startContent="$"
                    isInvalid={!!errors.value}
                    errorMessage={errors.value}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Payment Type</label>
                  <Select
                    selectedKeys={[bountyData.paymentType]}
                    onSelectionChange={(keys) => setBountyData(prev => ({ ...prev, paymentType: Array.from(keys)[0] }))}
                  >
                    <SelectItem key="fixed">Fixed Price</SelectItem>
                    <SelectItem key="milestone">Milestone-based</SelectItem>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Timeline *</label>
                  <Input
                    placeholder="e.g., 4 weeks, 2 months"
                    value={bountyData.timeline}
                    onChange={(e) => setBountyData(prev => ({ ...prev, timeline: e.target.value }))}
                    isInvalid={!!errors.timeline}
                    errorMessage={errors.timeline}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Difficulty Level: {bountyData.difficulty}/10</label>
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={bountyData.difficulty}
                  onChange={(e) => setBountyData(prev => ({ ...prev, difficulty: parseInt(e.target.value) }))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-default-500 mt-1">
                  <span>Beginner</span>
                  <span>Expert</span>
                </div>
              </div>
            </div>

            {/* Skills Required */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Skills Required</h3>
              
              <div className="flex gap-2">
                <Input
                  placeholder="Add required skill"
                  value={currentSkill}
                  onChange={(e) => setCurrentSkill(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addSkill()}
                  className="flex-1"
                />
                <Button color="primary" onClick={addSkill}>
                  Add
                </Button>
              </div>

              {errors.skills && (
                <p className="text-red-500 text-sm">{errors.skills}</p>
              )}

              <div className="flex flex-wrap gap-2">
                {bountyData.skillsRequired.map((skill, index) => (
                  <Chip
                    key={index}
                    onClose={() => removeSkill(skill)}
                    variant="flat"
                    color="primary"
                  >
                    {skill}
                  </Chip>
                ))}
              </div>

              <div className="text-sm text-default-500">
                <p className="mb-2">Suggested skills:</p>
                <div className="flex flex-wrap gap-1">
                  {skillSuggestions.slice(0, 10).map((skill) => (
                    <button
                      key={skill}
                      onClick={() => setCurrentSkill(skill)}
                      className="text-xs bg-default-100 hover:bg-default-200 dark:bg-default-800 dark:hover:bg-default-700 px-2 py-1 rounded transition-colors"
                    >
                      {skill}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Application Requirements */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Application Requirements</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <Switch
                    isSelected={bountyData.requirements.portfolio}
                    onValueChange={(value) => setBountyData(prev => ({
                      ...prev,
                      requirements: { ...prev.requirements, portfolio: value }
                    }))}
                  >
                    Require Portfolio
                  </Switch>
                  
                  <Switch
                    isSelected={bountyData.requirements.skillVerification}
                    onValueChange={(value) => setBountyData(prev => ({
                      ...prev,
                      requirements: { ...prev.requirements, skillVerification: value }
                    }))}
                  >
                    Require Skill Verification
                  </Switch>
                  
                  <Switch
                    isSelected={bountyData.requirements.certification}
                    onValueChange={(value) => setBountyData(prev => ({
                      ...prev,
                      requirements: { ...prev.requirements, certification: value }
                    }))}
                  >
                    Require Certification
                  </Switch>
                </div>

                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium mb-2">Minimum Rating</label>
                    <Select
                      selectedKeys={[bountyData.requirements.minRating.toString()]}
                      onSelectionChange={(keys) => setBountyData(prev => ({
                        ...prev,
                        requirements: { ...prev.requirements, minRating: parseFloat(Array.from(keys)[0]) }
                      }))}
                    >
                      <SelectItem key="0">No minimum</SelectItem>
                      <SelectItem key="3.0">3.0+ stars</SelectItem>
                      <SelectItem key="4.0">4.0+ stars</SelectItem>
                      <SelectItem key="4.5">4.5+ stars</SelectItem>
                    </Select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Experience Level</label>
                    <Select
                      selectedKeys={[bountyData.requirements.experience]}
                      onSelectionChange={(keys) => setBountyData(prev => ({
                        ...prev,
                        requirements: { ...prev.requirements, experience: Array.from(keys)[0] }
                      }))}
                    >
                      <SelectItem key="any">Any level</SelectItem>
                      <SelectItem key="beginner">Beginner</SelectItem>
                      <SelectItem key="intermediate">Intermediate</SelectItem>
                      <SelectItem key="advanced">Advanced</SelectItem>
                      <SelectItem key="expert">Expert</SelectItem>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Options */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Additional Options</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Maximum Applications</label>
                  <Input
                    type="number"
                    value={bountyData.maxApplications}
                    onChange={(e) => setBountyData(prev => ({ ...prev, maxApplications: parseInt(e.target.value) || 10 }))}
                    min="1"
                    max="50"
                  />
                </div>

                <div className="space-y-3">
                  <Switch
                    isSelected={bountyData.featured}
                    onValueChange={(value) => setBountyData(prev => ({ ...prev, featured: value }))}
                  >
                    Featured Listing (+$50)
                  </Switch>
                  
                  <Switch
                    isSelected={bountyData.urgent}
                    onValueChange={(value) => setBountyData(prev => ({ ...prev, urgent: value }))}
                  >
                    Mark as Urgent
                  </Switch>
                </div>
              </div>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Cancel
          </Button>
          <Button color="primary" onPress={handleSubmit}>
            Post Bounty
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default BountyPostingModal;
