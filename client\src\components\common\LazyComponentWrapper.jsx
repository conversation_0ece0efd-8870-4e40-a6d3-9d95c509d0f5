import React, { Suspense } from 'react';
import { Spinner } from '@heroui/react';

/**
 * Lazy Component Wrapper - Performance Optimization
 * 
 * This wrapper provides a consistent loading experience for lazy-loaded components
 * with proper error boundaries and fallback UI.
 * 
 * Task: O3 Performance Optimization - Component Lazy Loading
 */

const LazyComponentWrapper = ({ 
  children, 
  fallback = null, 
  className = "",
  minHeight = "200px",
  showSpinner = true 
}) => {
  // Default fallback component
  const defaultFallback = (
    <div 
      className={`flex items-center justify-center ${className}`}
      style={{ minHeight }}
    >
      {showSpinner && (
        <div className="flex flex-col items-center gap-3">
          <Spinner size="lg" color="primary" />
          <div className="text-sm text-default-600">Loading component...</div>
        </div>
      )}
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  );
};

// Error Boundary for lazy components
class LazyComponentErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Lazy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-danger text-lg mb-2">⚠️ Component Error</div>
          <div className="text-sm text-default-600 mb-4">
            Failed to load component. Please refresh the page.
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Refresh Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Combined wrapper with error boundary
const SafeLazyComponentWrapper = ({ children, ...props }) => {
  return (
    <LazyComponentErrorBoundary>
      <LazyComponentWrapper {...props}>
        {children}
      </LazyComponentWrapper>
    </LazyComponentErrorBoundary>
  );
};

export default SafeLazyComponentWrapper;
export { LazyComponentWrapper, LazyComponentErrorBoundary };
