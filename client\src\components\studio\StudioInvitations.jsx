import React, { useState, useEffect, useContext } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Textarea, Badge } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';

const StudioInvitations = () => {
  const { id: studioId } = useParams();
  const { currentUser } = useContext(UserContext);
  
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [inviteForm, setInviteForm] = useState({
    email: '',
    role: 'member',
    collaboration_type: 'studio_member',
    engagement_duration: 'permanent',
    message: ''
  });

  useEffect(() => {
    if (currentUser && studioId) {
      loadInvitations();
    }
  }, [currentUser, studioId]);

  const loadInvitations = async () => {
    try {
      setLoading(true);
      
      // TODO: Replace with actual API call
      const response = await fetch(`/api/studios/${studioId}/invitations`, {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setInvitations(data.data || []);
      } else {
        throw new Error('Failed to load invitations');
      }
    } catch (error) {
      console.error('Error loading invitations:', error);
      toast.error('Failed to load invitations');
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvitation = async (e) => {
    e.preventDefault();
    
    try {
      const response = await fetch(`/api/studios/${studioId}/invitations`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(inviteForm)
      });
      
      if (response.ok) {
        toast.success('Invitation sent successfully!');
        setShowInviteForm(false);
        setInviteForm({
          email: '',
          role: 'member',
          collaboration_type: 'studio_member',
          engagement_duration: 'permanent',
          message: ''
        });
        loadInvitations();
      } else {
        throw new Error('Failed to send invitation');
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to send invitation');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'accepted': return 'success';
      case 'declined': return 'danger';
      case 'expired': return 'default';
      default: return 'default';
    }
  };

  const getCollaborationTypeIcon = (type) => {
    switch (type) {
      case 'studio_member': return '👥';
      case 'contractor': return '🤝';
      case 'specialist': return '⭐';
      default: return '👤';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading invitations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Studio Invitations</h1>
          <p className="text-gray-600">Manage invitations to join your studio</p>
        </div>
        
        <Button
          onClick={() => setShowInviteForm(true)}
          color="primary"
          disabled={showInviteForm}
        >
          Send Invitation
        </Button>
      </div>

      {/* Invite Form */}
      {showInviteForm && (
        <Card className="mb-6">
          <CardHeader>
            <h3 className="text-lg font-semibold">Send Studio Invitation</h3>
          </CardHeader>
          <CardBody>
            <form onSubmit={handleSendInvitation} className="space-y-4">
              <Input
                label="Email Address"
                type="email"
                value={inviteForm.email}
                onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
                required
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Select
                  label="Role"
                  value={inviteForm.role}
                  onChange={(e) => setInviteForm({ ...inviteForm, role: e.target.value })}
                >
                  <SelectItem key="member" value="member">Member</SelectItem>
                  <SelectItem key="admin" value="admin">Admin</SelectItem>
                  <SelectItem key="contributor" value="contributor">Contributor</SelectItem>
                </Select>

                <Select
                  label="Collaboration Type"
                  value={inviteForm.collaboration_type}
                  onChange={(e) => setInviteForm({ ...inviteForm, collaboration_type: e.target.value })}
                >
                  <SelectItem key="studio_member" value="studio_member">👥 Studio Member</SelectItem>
                  <SelectItem key="contractor" value="contractor">🤝 Contractor</SelectItem>
                  <SelectItem key="specialist" value="specialist">⭐ Specialist</SelectItem>
                </Select>

                <Select
                  label="Engagement Duration"
                  value={inviteForm.engagement_duration}
                  onChange={(e) => setInviteForm({ ...inviteForm, engagement_duration: e.target.value })}
                >
                  <SelectItem key="permanent" value="permanent">Permanent</SelectItem>
                  <SelectItem key="project_based" value="project_based">Project Based</SelectItem>
                  <SelectItem key="one_off" value="one_off">One Off</SelectItem>
                </Select>
              </div>

              <Textarea
                label="Personal Message (Optional)"
                placeholder="Add a personal message to the invitation..."
                value={inviteForm.message}
                onChange={(e) => setInviteForm({ ...inviteForm, message: e.target.value })}
                rows={3}
              />

              <div className="flex justify-end space-x-3">
                <Button
                  variant="flat"
                  onClick={() => setShowInviteForm(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  disabled={!inviteForm.email.trim()}
                >
                  Send Invitation
                </Button>
              </div>
            </form>
          </CardBody>
        </Card>
      )}

      {/* Invitations List */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Sent Invitations</h3>
        </CardHeader>
        <CardBody>
          {invitations.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-4">📧</div>
              <p className="text-gray-600 mb-4">No invitations sent yet</p>
              <Button
                onClick={() => setShowInviteForm(true)}
                color="primary"
                variant="flat"
              >
                Send First Invitation
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {invitations.map((invitation) => (
                <div
                  key={invitation.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {getCollaborationTypeIcon(invitation.collaboration_type)}
                    </div>
                    <div>
                      <p className="font-medium">{invitation.invited_user_email || invitation.email}</p>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <span>{invitation.role}</span>
                        <span>•</span>
                        <span>{invitation.collaboration_type?.replace('_', ' ')}</span>
                        <span>•</span>
                        <span>{invitation.engagement_duration}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Badge 
                      color={getStatusColor(invitation.status)} 
                      variant="flat"
                    >
                      {invitation.status}
                    </Badge>
                    <div className="text-sm text-gray-500">
                      {new Date(invitation.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>
    </div>
  );
};

export default StudioInvitations;
