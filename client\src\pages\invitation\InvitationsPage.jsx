import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import FriendRequestItem from '../../components/invitation/FriendRequestItem';
import ProjectInvitationItem from '../../components/invitation/ProjectInvitationItem';

const InvitationsPage = () => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('all');
  const [friendRequests, setFriendRequests] = useState([]);
  const [projectInvitations, setProjectInvitations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch friend requests
  const fetchFriendRequests = async () => {
    if (!currentUser) return;

    try {
      const { data, error } = await supabase
        .from('friend_requests')
        .select(`
          *,
          sender:users!friend_requests_sender_id_fkey(id, display_name, avatar_url)
        `)
        .eq('recipient_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setFriendRequests(data || []);
    } catch (error) {
      console.error('Error fetching friend requests:', error);
    }
  };

  // Fetch project invitations from project_contributors table
  const fetchProjectInvitations = async () => {
    if (!currentUser) return;

    try {
      // Get pending project invitations from project_contributors table
      const { data, error } = await supabase
        .from('project_contributors')
        .select(`
          *,
          project:projects(id, name),
          inviter:users(id, display_name, avatar_url)
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data to match the expected format for ProjectInvitationItem
      const transformedData = data.map(item => ({
        id: item.id,
        project_id: item.project_id,
        invited_by: item.inviter?.id,
        invited_user_id: item.user_id,
        invited_email: item.email,
        message: '',
        status: item.status,
        created_at: item.created_at,
        updated_at: item.updated_at,
        project: item.project,
        inviter: item.inviter,
        metadata: {
          display_name: item.display_name,
          role: item.role,
          permission_level: item.permission_level,
          is_admin: item.is_admin
        }
      }));

      setProjectInvitations(transformedData || []);
    } catch (error) {
      console.error('Error fetching project invitations:', error);
    }
  };

  // Fetch all invitations
  const fetchInvitations = async () => {
    setIsLoading(true);

    try {
      await Promise.all([
        fetchFriendRequests(),
        fetchProjectInvitations()
      ]);
    } catch (error) {
      console.error('Error fetching invitations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchInvitations();
  }, [currentUser]);

  // Handle friend request update
  const handleFriendRequestUpdate = (requestId, status) => {
    setFriendRequests(prev =>
      prev.map(request =>
        request.id === requestId
          ? { ...request, status }
          : request
      )
    );
  };

  // Handle project invitation update
  const handleProjectInvitationUpdate = (invitationId, status) => {
    setProjectInvitations(prev =>
      prev.map(invitation =>
        invitation.id === invitationId
          ? { ...invitation, status }
          : invitation
      )
    );
  };

  // Filter invitations based on active tab
  const getFilteredInvitations = () => {
    let filteredFriendRequests = [];
    let filteredProjectInvitations = [];

    if (activeTab === 'all' || activeTab === 'friends') {
      filteredFriendRequests = friendRequests;
    }

    if (activeTab === 'all' || activeTab === 'projects') {
      filteredProjectInvitations = projectInvitations;
    }

    if (activeTab === 'pending') {
      filteredFriendRequests = friendRequests.filter(request => request.status === 'pending');
      filteredProjectInvitations = projectInvitations.filter(invitation => invitation.status === 'pending');
    }

    return {
      friendRequests: filteredFriendRequests,
      projectInvitations: filteredProjectInvitations
    };
  };

  const { friendRequests: filteredFriendRequests, projectInvitations: filteredProjectInvitations } = getFilteredInvitations();

  // Combine and sort all invitations by date
  const allInvitations = [...filteredFriendRequests, ...filteredProjectInvitations]
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

  // Count pending invitations
  const pendingCount = friendRequests.filter(r => r.status === 'pending').length +
                      projectInvitations.filter(i => i.status === 'pending').length;

  return (
    <div className="invitations-page">
      <div className="invitations-page-header">
        <h1>Invitations</h1>
        <p>Manage your friend requests and project invitations</p>
      </div>

      <div className="invitations-tabs">
        <div
          className={`invitations-tab ${activeTab === 'all' ? 'active' : ''}`}
          onClick={() => setActiveTab('all')}
        >
          All
        </div>
        <div
          className={`invitations-tab ${activeTab === 'pending' ? 'active' : ''}`}
          onClick={() => setActiveTab('pending')}
        >
          Pending ({pendingCount})
        </div>
        <div
          className={`invitations-tab ${activeTab === 'friends' ? 'active' : ''}`}
          onClick={() => setActiveTab('friends')}
        >
          Friends ({friendRequests.length})
        </div>
        <div
          className={`invitations-tab ${activeTab === 'projects' ? 'active' : ''}`}
          onClick={() => setActiveTab('projects')}
        >
          Projects ({projectInvitations.length})
        </div>
      </div>

      <div className="invitations-list">
        {isLoading ? (
          <div className="loading-spinner">Loading...</div>
        ) : allInvitations.length === 0 ? (
          <div className="empty-invitations">
            <i className="bi bi-envelope-slash"></i>
            <h3>No invitations</h3>
            <p>You don't have any invitations at the moment.</p>
          </div>
        ) : (
          allInvitations.map(invitation => {
            // Determine if it's a friend request or project invitation
            if (invitation.sender_id) {
              return (
                <FriendRequestItem
                  key={`friend-${invitation.id}`}
                  request={invitation}
                  onUpdate={handleFriendRequestUpdate}
                />
              );
            } else {
              return (
                <ProjectInvitationItem
                  key={`project-${invitation.id}`}
                  invitation={invitation}
                  onUpdate={handleProjectInvitationUpdate}
                />
              );
            }
          })
        )}
      </div>
    </div>
  );
};

export default InvitationsPage;
