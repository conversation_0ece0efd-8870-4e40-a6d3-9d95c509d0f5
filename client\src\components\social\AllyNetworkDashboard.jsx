// AllyNetworkDashboard - Comprehensive social networking dashboard
// Implements the complete social system with bento grid layout
import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button, Badge, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Users, 
  Search, 
  Filter, 
  UserPlus, 
  MessageCircle, 
  Award, 
  TrendingUp,
  Star,
  MapPin,
  Briefcase,
  Clock,
  CheckCircle,
  XCircle,
  Send
} from 'lucide-react';

const AllyNetworkDashboard = () => {
  const { currentUser } = useContext(UserContext);
  const [allies, setAllies] = useState([]);
  const [suggestedAllies, setSuggestedAllies] = useState([]);
  const [allyRequests, setAllyRequests] = useState([]);
  const [networkStats, setNetworkStats] = useState({
    totalAllies: 0,
    pendingRequests: 0,
    endorsements: 0,
    networkScore: 0
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [filterSkill, setFilterSkill] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedUser, setSelectedUser] = useState(null);
  const [requestMessage, setRequestMessage] = useState('');

  useEffect(() => {
    if (currentUser) {
      loadNetworkData();
    }
  }, [currentUser]);

  const loadNetworkData = async () => {
    try {
      setIsLoading(true);
      await Promise.all([
        loadAllies(),
        loadSuggestedAllies(),
        loadAllyRequests(),
        loadNetworkStats()
      ]);
    } catch (error) {
      console.error('Error loading network data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadAllies = async () => {
    try {
      const { data, error } = await supabase
        .from('user_allies')
        .select(`
          *,
          ally:ally_id (
            id,
            email,
            user_metadata
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('status', 'accepted');

      if (error) throw error;
      setAllies(data || []);
    } catch (error) {
      console.error('Error loading allies:', error);
    }
  };

  const loadSuggestedAllies = async () => {
    try {
      // Mock suggested allies for now - in production this would use AI recommendations
      const mockSuggestions = [
        {
          id: 'user1',
          email: '<EMAIL>',
          user_metadata: {
            display_name: 'Sarah Chen',
            avatar_url: null,
            skills: ['React', 'Node.js', 'TypeScript'],
            location: 'San Francisco, CA',
            experience_level: 'Senior'
          },
          compatibility_score: 95,
          mutual_connections: 3
        },
        {
          id: 'user2',
          email: '<EMAIL>',
          user_metadata: {
            display_name: 'Mike Rodriguez',
            avatar_url: null,
            skills: ['UI/UX', 'Figma', 'Design Systems'],
            location: 'Austin, TX',
            experience_level: 'Mid-level'
          },
          compatibility_score: 87,
          mutual_connections: 1
        }
      ];
      setSuggestedAllies(mockSuggestions);
    } catch (error) {
      console.error('Error loading suggested allies:', error);
    }
  };

  const loadAllyRequests = async () => {
    try {
      const { data, error } = await supabase
        .from('user_allies')
        .select(`
          *,
          requester:user_id (
            id,
            email,
            user_metadata
          )
        `)
        .eq('ally_id', currentUser.id)
        .eq('status', 'pending');

      if (error) throw error;
      setAllyRequests(data || []);
    } catch (error) {
      console.error('Error loading ally requests:', error);
    }
  };

  const loadNetworkStats = async () => {
    try {
      // Load network analytics
      const { data: networkData, error } = await supabase
        .from('network_analytics')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (networkData && networkData.length > 0) {
        const stats = networkData[0];
        setNetworkStats({
          totalAllies: stats.total_connections || 0,
          pendingRequests: allyRequests.length,
          endorsements: stats.endorsements_received || 0,
          networkScore: Math.round(stats.network_score || 0)
        });
      }
    } catch (error) {
      console.error('Error loading network stats:', error);
    }
  };

  const sendAllyRequest = async (targetUserId) => {
    try {
      const { error } = await supabase
        .from('user_allies')
        .insert({
          user_id: currentUser.id,
          ally_id: targetUserId,
          status: 'pending',
          message: requestMessage || 'Would like to connect!'
        });

      if (error) throw error;

      // Remove from suggested allies
      setSuggestedAllies(prev => prev.filter(user => user.id !== targetUserId));
      setRequestMessage('');
      onClose();
      
      console.log('Ally request sent successfully');
    } catch (error) {
      console.error('Error sending ally request:', error);
    }
  };

  const respondToRequest = async (requestId, response) => {
    try {
      const { error } = await supabase
        .from('user_allies')
        .update({ status: response })
        .eq('id', requestId);

      if (error) throw error;

      // Reload data
      await loadNetworkData();
      
      console.log(`Request ${response} successfully`);
    } catch (error) {
      console.error('Error responding to request:', error);
    }
  };

  const openRequestModal = (user) => {
    setSelectedUser(user);
    setRequestMessage(`Hi ${user.user_metadata?.display_name || user.email}, I'd like to connect with you on Royaltea!`);
    onOpen();
  };

  const getNetworkLevel = (score) => {
    if (score >= 80) return { level: 'Expert', color: 'text-purple-600', bg: 'bg-purple-100' };
    if (score >= 60) return { level: 'Advanced', color: 'text-blue-600', bg: 'bg-blue-100' };
    if (score >= 40) return { level: 'Intermediate', color: 'text-green-600', bg: 'bg-green-100' };
    if (score >= 20) return { level: 'Developing', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { level: 'Beginner', color: 'text-gray-600', bg: 'bg-gray-100' };
  };

  const networkLevel = getNetworkLevel(networkStats.networkScore);

  if (!currentUser) {
    return null;
  }

  return (
    <div className="ally-network-dashboard space-y-6">
      {/* Bento Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        
        {/* Network Overview - 2x2 */}
        <Card className="lg:col-span-2 lg:row-span-2">
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold">Network Overview</h3>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${networkLevel.bg} ${networkLevel.color}`}>
                {networkLevel.level}
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{networkStats.totalAllies}</div>
                <div className="text-sm text-gray-600">Total Allies</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{networkStats.endorsements}</div>
                <div className="text-sm text-gray-600">Endorsements</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{networkStats.networkScore}</div>
                <div className="text-sm text-gray-600">Network Score</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{networkStats.pendingRequests}</div>
                <div className="text-sm text-gray-600">Pending</div>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Network Growth</span>
                <span className="text-green-600">+12% this month</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '68%' }}></div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Quick Stats - 1x1 each */}
        <Card>
          <CardBody className="p-4 text-center">
            <Users className="mx-auto mb-2 text-blue-500" size={32} />
            <div className="text-xl font-bold">{allies.length}</div>
            <div className="text-sm text-gray-600">Active Allies</div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4 text-center">
            <TrendingUp className="mx-auto mb-2 text-green-500" size={32} />
            <div className="text-xl font-bold">+5</div>
            <div className="text-sm text-gray-600">This Week</div>
          </CardBody>
        </Card>
      </div>

      {/* Search and Filters - 4x1 */}
      <Card>
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Search allies by name or skills..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<Search size={18} />}
              className="flex-1"
            />
            <Select
              placeholder="Filter by skill"
              value={filterSkill}
              onChange={(e) => setFilterSkill(e.target.value)}
              className="md:w-48"
            >
              <SelectItem key="react" value="react">React</SelectItem>
              <SelectItem key="nodejs" value="nodejs">Node.js</SelectItem>
              <SelectItem key="python" value="python">Python</SelectItem>
              <SelectItem key="design" value="design">Design</SelectItem>
            </Select>
            <Button color="primary" startContent={<Filter size={18} />}>
              Apply Filters
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Suggested Allies - 6x2 */}
      <Card>
        <CardBody className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Suggested Allies</h3>
            <Badge content={suggestedAllies.length} color="primary" />
          </div>

          {suggestedAllies.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <UserPlus size={32} className="mx-auto mb-2 opacity-50" />
              <p>No suggestions available</p>
              <p className="text-sm">Complete your profile to get better recommendations</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {suggestedAllies.map((user) => (
                <motion.div
                  key={user.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold">{user.user_metadata?.display_name || user.email}</h4>
                      <p className="text-sm text-gray-600">{user.user_metadata?.experience_level}</p>
                      {user.user_metadata?.location && (
                        <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                          <MapPin size={12} />
                          <span>{user.user_metadata.location}</span>
                        </div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-green-600">{user.compatibility_score}%</div>
                      <div className="text-xs text-gray-500">match</div>
                    </div>
                  </div>

                  {user.user_metadata?.skills && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {user.user_metadata.skills.slice(0, 3).map((skill, index) => (
                        <Badge key={index} size="sm" variant="flat">{skill}</Badge>
                      ))}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      {user.mutual_connections} mutual connections
                    </div>
                    <Button
                      size="sm"
                      color="primary"
                      onPress={() => openRequestModal(user)}
                      startContent={<UserPlus size={14} />}
                    >
                      Connect
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Ally Requests - 4x1 */}
      {allyRequests.length > 0 && (
        <Card>
          <CardBody className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Ally Requests</h3>
              <Badge content={allyRequests.length} color="warning" />
            </div>

            <div className="space-y-3">
              {allyRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <Users size={20} className="text-gray-500" />
                    </div>
                    <div>
                      <h4 className="font-medium">
                        {request.requester?.user_metadata?.display_name || request.requester?.email}
                      </h4>
                      <p className="text-sm text-gray-600">{request.message}</p>
                      <div className="flex items-center gap-1 text-xs text-gray-500 mt-1">
                        <Clock size={12} />
                        <span>{new Date(request.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      color="success"
                      onPress={() => respondToRequest(request.id, 'accepted')}
                      startContent={<CheckCircle size={14} />}
                    >
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      variant="light"
                      onPress={() => respondToRequest(request.id, 'declined')}
                      startContent={<XCircle size={14} />}
                    >
                      Decline
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Current Allies - 6x2 */}
      <Card>
        <CardBody className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Your Allies</h3>
            <Badge content={allies.length} color="success" />
          </div>

          {allies.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Users size={32} className="mx-auto mb-2 opacity-50" />
              <p>No allies yet</p>
              <p className="text-sm">Start connecting with other professionals</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {allies.map((ally) => (
                <motion.div
                  key={ally.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold">
                        {ally.ally?.user_metadata?.display_name || ally.ally?.email}
                      </h4>
                      <p className="text-sm text-gray-600">
                        Connected {new Date(ally.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="light"
                      startContent={<MessageCircle size={14} />}
                    >
                      Message
                    </Button>
                    <Button
                      size="sm"
                      variant="light"
                      startContent={<Award size={14} />}
                    >
                      Endorse
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </CardBody>
      </Card>

      {/* Connection Request Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="md">
        <ModalContent>
          <ModalHeader>
            <h3>Send Connection Request</h3>
          </ModalHeader>
          <ModalBody>
            {selectedUser && (
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users size={24} className="text-gray-500" />
                  </div>
                  <div>
                    <h4 className="font-semibold">
                      {selectedUser.user_metadata?.display_name || selectedUser.email}
                    </h4>
                    <p className="text-sm text-gray-600">{selectedUser.user_metadata?.experience_level}</p>
                  </div>
                </div>

                <textarea
                  value={requestMessage}
                  onChange={(e) => setRequestMessage(e.target.value)}
                  placeholder="Add a personal message..."
                  className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={() => selectedUser && sendAllyRequest(selectedUser.id)}
              startContent={<Send size={16} />}
            >
              Send Request
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AllyNetworkDashboard;
