import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import DatePicker from 'react-datepicker';
import { toast } from 'react-hot-toast';
import FileUpload from '../../components/common/FileUpload';
import { logProjectActivity } from '../../utils/activity-logger';

const ContributionEntryForm = ({ projectId, onSuccess, onCancel, initialData = null }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [project, setProject] = useState(null);
  const [milestones, setMilestones] = useState([]);
  const [taskTypes, setTaskTypes] = useState([]);
  const [categories, setCategories] = useState([]);
  const [difficultyLevels, setDifficultyLevels] = useState([1, 2, 3, 5, 8]);
  const [isEditing, setIsEditing] = useState(false);

  const [formData, setFormData] = useState({
    project_id: projectId, // Explicitly set project_id in initial state
    task_name: '',
    task_type: '',
    category: '',
    difficulty: 3,
    hours_spent: 1,
    description: '',
    milestone_id: null, // Use null instead of empty string for UUID field
    date_performed: new Date(),
    status: 'pending',
    validation_status: 'pending', // Add validation status
    has_attachments: false,
    attachments_data: [],
    tags: []
  });

  // State for file attachments
  const [attachmentFiles, setAttachmentFiles] = useState([]);

  // Update project_id in formData when projectId prop changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      project_id: projectId
    }));
  }, [projectId]);

  // Fetch project data, milestones, and contribution tracking config
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch project data
        let projectData;
        let configData;

        try {
          // Try to fetch project with contribution_tracking_config
          const { data, error } = await supabase
            .from('projects')
            .select('*, contribution_tracking_config(*)')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          projectData = data;
          configData = data.contribution_tracking_config;
        } catch (e) {
          console.log('Error fetching project with config:', e);

          // Fallback: fetch just the project
          const { data, error } = await supabase
            .from('projects')
            .select('*')
            .eq('id', projectId)
            .single();

          if (error) throw error;
          projectData = data;

          // Fetch config separately
          try {
            const { data: configResult, error: configError } = await supabase
              .from('contribution_tracking_config')
              .select('*')
              .eq('project_id', projectId)
              .single();

            if (!configError) {
              configData = configResult;
            }
          } catch (configErr) {
            console.log('Error fetching config separately:', configErr);
          }
        }

        setProject(projectData);

        // Use default config if none exists
        const defaultConfig = {
          task_types: [
            {name: "Development", value: "development"},
            {name: "Design", value: "design"},
            {name: "Documentation", value: "documentation"},
            {name: "Testing", value: "testing"},
            {name: "Research", value: "research"},
            {name: "Management", value: "management"},
            {name: "Other", value: "other"}
          ],
          categories: [
            {name: "Frontend", value: "frontend"},
            {name: "Backend", value: "backend"},
            {name: "Database", value: "database"},
            {name: "UI/UX", value: "ui-ux"},
            {name: "DevOps", value: "devops"},
            {name: "QA", value: "qa"},
            {name: "Content", value: "content"},
            {name: "Other", value: "other"}
          ],
          difficulty_levels: [1, 2, 3, 5, 8]
        };

        // Process task types
        let processedTaskTypes = defaultConfig.task_types;
        if (configData && configData.task_types && Array.isArray(configData.task_types)) {
          if (configData.task_types.length > 0) {
            // Check if task_types is an array of objects or strings
            if (typeof configData.task_types[0] === 'object') {
              processedTaskTypes = configData.task_types;
            } else if (typeof configData.task_types[0] === 'string') {
              // Convert strings to objects
              processedTaskTypes = configData.task_types.map(type => ({ name: type, value: type }));
            }
          }
        }
        setTaskTypes(processedTaskTypes);

        // Process categories
        let processedCategories = defaultConfig.categories;
        if (configData && configData.categories && Array.isArray(configData.categories)) {
          if (configData.categories.length > 0) {
            // Check if categories is an array of objects or strings
            if (typeof configData.categories[0] === 'object') {
              processedCategories = configData.categories;
            } else if (typeof configData.categories[0] === 'string') {
              // Convert strings to objects
              processedCategories = configData.categories.map(cat => ({ name: cat, value: cat }));
            }
          }
        }
        setCategories(processedCategories);

        // Process difficulty levels
        let processedDifficultyLevels = defaultConfig.difficulty_levels;
        if (configData) {
          // Check for difficulty_scale first, then difficulty_levels
          const difficultyData = configData.difficulty_scale || configData.difficulty_levels;
          if (difficultyData && Array.isArray(difficultyData)) {
            if (difficultyData.length > 0) {
              // Check if it's an array of numbers or objects
              if (typeof difficultyData[0] === 'number') {
                processedDifficultyLevels = difficultyData;
              } else if (typeof difficultyData[0] === 'object' && difficultyData[0].multiplier) {
                // Extract multipliers from objects
                processedDifficultyLevels = difficultyData.map(level => level.multiplier);
              }
            }
          }
        }
        setDifficultyLevels(processedDifficultyLevels);

        // Fetch milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('milestones')
          .select('id, name, status')
          .eq('project_id', projectId)
          .order('deadline', { ascending: true });

        if (milestonesError) throw milestonesError;

        setMilestones(milestonesData || []);

        // If editing, set initial form data
        if (initialData) {
          setIsEditing(true);

          // Handle attachments if present
          if (initialData.has_attachments && initialData.attachments_data) {
            setAttachmentFiles(initialData.attachments_data);
          }

          setFormData({
            ...initialData,
            date_performed: initialData.date_performed ? new Date(initialData.date_performed) : new Date(),
            attachments_data: initialData.attachments_data || [],
            has_attachments: initialData.has_attachments || false
          });
        }
      } catch (error) {
        console.error('Error fetching project data:', error);
        toast.error('Failed to load project data');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectData();
  }, [projectId, initialData]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle date change
  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date_performed: date
    }));
  };

  // Handle task type selection
  const handleTaskTypeChange = (e) => {
    const selectedTaskTypeValue = e.target.value;

    // Find the selected task type and get its default difficulty
    const taskType = taskTypes.find(type =>
      (type.value || type.name) === selectedTaskTypeValue
    );

    setFormData(prev => ({
      ...prev,
      task_type: selectedTaskTypeValue,
      // Update difficulty if task type has a default difficulty
      ...(taskType && taskType.difficulty && { difficulty: taskType.difficulty })
    }));
  };

  // Handle file upload complete
  const handleFileUploadComplete = (files) => {
    console.log('File upload complete. Files:', files);

    // Make sure files is an array
    const filesArray = Array.isArray(files) ? files : [];
    setAttachmentFiles(filesArray);

    // Check if there are any files
    const hasFiles = filesArray.length > 0;

    // Update form data with attachment information
    setFormData(prev => ({
      ...prev,
      has_attachments: hasFiles,
      attachments_data: filesArray
    }));

    // Log the updated form data for debugging
    console.log('Updated form data with attachments:', {
      has_attachments: hasFiles,
      attachments_count: filesArray.length,
      attachments_data: filesArray
    });

    // Show success message
    if (hasFiles) {
      toast.success(`${filesArray.length} file(s) attached successfully`);
    }
  };

  // Handle file upload error
  const handleFileUploadError = (error) => {
    toast.error(`File upload error: ${error.message}`);
  };


  // Handle tag input
  const handleTagKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      const value = e.target.value.trim();
      if (value && !formData.tags.includes(value)) {
        setFormData(prev => ({
          ...prev,
          tags: [...prev.tags, value]
        }));
        e.target.value = '';
      }
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (index) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter((_, i) => i !== index)
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      toast.error('You must be logged in to submit a contribution');
      return;
    }

    if (!projectId) {
      toast.error('Project ID is required');
      return;
    }

    try {
      setLoading(true);

      // Ensure project_id is set correctly
      if (!formData.project_id) {
        console.log('Project ID missing in form data, setting it explicitly');
      }

      // Process form data to ensure proper types
      // Check if attachments_data is valid
      const hasAttachments = Array.isArray(formData.attachments_data) && formData.attachments_data.length > 0;

      console.log('Form submission - has attachments:', hasAttachments);
      console.log('Form submission - attachments data:', formData.attachments_data);

      // Make sure attachments_data is properly formatted
      let processedAttachmentsData = [];
      if (hasAttachments) {
        // Ensure each attachment has the required fields
        processedAttachmentsData = formData.attachments_data.map(file => ({
          name: file.name || 'unnamed-file',
          size: file.size || 0,
          type: file.type || 'application/octet-stream',
          url: file.url || '',
          path: file.path || '',
          uploaded_at: file.uploaded_at || new Date().toISOString()
        }));

        console.log('Processed attachments data:', processedAttachmentsData);
      }

      // Create a clean contribution data object
      const contributionData = {
        project_id: projectId,
        user_id: currentUser.id,
        task_name: formData.task_name,
        task_type: formData.task_type,
        category: formData.category,
        difficulty: parseInt(formData.difficulty),
        hours_spent: parseFloat(formData.hours_spent),
        description: formData.description,
        date_performed: formData.date_performed,
        status: formData.status,
        validation_status: formData.validation_status || 'pending',
        tags: formData.tags || [],
        // Attachment data
        has_attachments: hasAttachments,
        attachments_data: processedAttachmentsData,
        // Convert empty string milestone_id to null to avoid UUID validation error
        milestone_id: formData.milestone_id === '' ? null : formData.milestone_id
      };

      // Log the data being submitted for debugging
      console.log('Submitting contribution with data:', contributionData);

      let result;

      if (isEditing) {
        // Update existing contribution
        console.log('Updating existing contribution with ID:', initialData.id);
        const { data, error } = await supabase
          .from('contributions')
          .update(contributionData)
          .eq('id', initialData.id)
          .select('*, milestone:milestones(name, status)')
          .single();

        if (error) {
          console.error('Error updating contribution:', error);
          throw error;
        }

        console.log('Contribution updated successfully. Result:', data);
        result = data;
        toast.success('Contribution updated successfully');

        // Log activity for contribution update
        await logProjectActivity(
          projectId,
          currentUser.id,
          'contribution_updated',
          {
            contribution_id: data.id,
            task: data.task_name,
            hours: data.hours_spent,
            difficulty: data.difficulty
          }
        );
      } else {
        // Create new contribution
        console.log('Creating new contribution with data:', {
          ...contributionData,
          has_attachments: contributionData.has_attachments,
          attachments_count: contributionData.attachments_data.length
        });

        const { data, error } = await supabase
          .from('contributions')
          .insert([contributionData])
          .select('*, milestone:milestones(name, status)')
          .single();

        if (error) {
          console.error('Error creating contribution:', error);
          throw error;
        }

        console.log('Contribution created successfully. Result:', data);
        result = data;
        toast.success('Contribution added successfully');

        // Log activity for new contribution
        await logProjectActivity(
          projectId,
          currentUser.id,
          'contribution_added',
          {
            contribution_id: data.id,
            task: data.task_name,
            hours: data.hours_spent,
            difficulty: data.difficulty
          }
        );
      }

      // Reset form
      setFormData({
        project_id: projectId, // Keep the project_id
        task_name: '',
        task_type: '',
        category: '',
        difficulty: 3,
        hours_spent: 1,
        description: '',
        milestone_id: null, // Use null instead of empty string
        date_performed: new Date(),
        status: 'pending',
        validation_status: 'pending', // Add validation status
        has_attachments: false,
        attachments_data: [],
        tags: []
      });

      // Reset attachment files
      setAttachmentFiles([]);

      // Call success callback
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('Error submitting contribution:', error);
      toast.error('Failed to submit contribution');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !project) {
    return <div className="loading-spinner">Loading...</div>;
  }

  return (
    <div className="contribution-entry-form">
      <form onSubmit={handleSubmit}>
        <div className="form-grid">
          {/* Task Name */}
          <div className="form-group">
            <label htmlFor="task_name">Task Name*</label>
            <input
              type="text"
              id="task_name"
              name="task_name"
              value={formData.task_name}
              onChange={handleInputChange}
              className="form-control"
              placeholder="Brief name of the task"
              required
            />
          </div>

          {/* Task Type */}
          <div className="form-group">
            <label htmlFor="task_type">Task Type*</label>
            <select
              id="task_type"
              name="task_type"
              value={formData.task_type}
              onChange={handleTaskTypeChange}
              className="form-control"
              required
            >
              <option value="">Select Task Type</option>
              {taskTypes.map((type, index) => (
                <option key={index} value={type.value || type.name}>
                  {type.name} {type.difficulty ? `(Difficulty: ${type.difficulty})` : ''}
                </option>
              ))}
            </select>
          </div>

          {/* Category */}
          <div className="form-group">
            <label htmlFor="category">Category*</label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="">Select Category</option>
              {categories.map((category, index) => (
                <option key={index} value={typeof category === 'object' ? (category.value || category.name) : category}>
                  {typeof category === 'object' ? category.name : category}
                </option>
              ))}
            </select>
          </div>

          {/* Difficulty */}
          <div className="form-group">
            <label htmlFor="difficulty">Difficulty*</label>
            <select
              id="difficulty"
              name="difficulty"
              value={formData.difficulty}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              {difficultyLevels.map((level) => (
                <option key={level} value={level}>
                  {level}
                </option>
              ))}
            </select>
          </div>

          {/* Hours Spent */}
          <div className="form-group">
            <label htmlFor="hours_spent">Hours Spent*</label>
            <input
              type="number"
              id="hours_spent"
              name="hours_spent"
              value={formData.hours_spent}
              onChange={handleInputChange}
              className="form-control"
              min="0.1"
              step="0.1"
              required
            />
          </div>

          {/* Date Performed */}
          <div className="form-group">
            <label htmlFor="date_performed">Date Performed*</label>
            <DatePicker
              id="date_performed"
              selected={formData.date_performed}
              onChange={handleDateChange}
              className="form-control"
              dateFormat="MMMM d, yyyy"
              maxDate={new Date()}
              required
            />
          </div>

          {/* Milestone */}
          <div className="form-group">
            <label htmlFor="milestone_id">Milestone</label>
            <select
              id="milestone_id"
              name="milestone_id"
              value={formData.milestone_id}
              onChange={handleInputChange}
              className="form-control"
            >
              <option value="">Select Milestone (Optional)</option>
              {milestones.map((milestone) => (
                <option key={milestone.id} value={milestone.id}>
                  {milestone.name} ({milestone.status})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Description */}
        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="form-control"
            rows="4"
            placeholder="Describe what you did in detail"
          />
        </div>

        {/* File Attachments */}
        <div className="form-group">
          <label>Attachments</label>
          <FileUpload
            bucketName="contribution-attachments"
            folderPath={`projects/${projectId}/contributions`}
            onUploadComplete={handleFileUploadComplete}
            onUploadError={handleFileUploadError}
            maxFiles={5}
            maxFileSize={10}
            allowedFileTypes={['image/*', 'application/pdf', '.doc', '.docx', '.txt']}
            existingFiles={attachmentFiles}
          />
        </div>


        {/* Tags */}
        <div className="form-group">
          <label htmlFor="tags">Tags</label>
          <div className="tags-input-container">
            <input
              type="text"
              id="tags-input"
              className="form-control"
              placeholder="Add tags (press Enter after each tag)"
              onKeyDown={handleTagKeyDown}
            />
            <div className="tags-list">
              {(formData.tags || []).map((tag, index) => (
                <span key={index} className="tag-item">
                  {tag}
                  <button
                    type="button"
                    className="tag-remove-btn"
                    onClick={() => handleRemoveTag(index)}
                  >
                    &times;
                  </button>
                </span>
              ))}
            </div>
          </div>
        </div>


        {/* Form Actions */}
        <div className="form-actions">
          {onCancel && (
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span className="ms-2">Saving...</span>
              </>
            ) : (
              isEditing ? 'Update Contribution' : 'Add Contribution'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ContributionEntryForm;
