// FloatingChatPanel - Simplified chat interface
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Badge } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import socialService from '../../services/socialService';
import { MessageCircle, X, Send, Minimize2 } from 'lucide-react';

const FloatingChatPanel = ({ isMinimized = false, onToggleMinimize }) => {
  const { currentUser } = useContext(UserContext);
  const [conversations, setConversations] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [newMessage, setNewMessage] = useState('');

  useEffect(() => {
    if (currentUser) {
      loadConversations();
    }
  }, [currentUser]);

  const loadConversations = async () => {
    try {
      const convos = await socialService.getConversations();
      setConversations(convos);
      
      const totalUnread = convos.reduce((sum, convo) => sum + convo.unread_count, 0);
      setUnreadCount(totalUnread);
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  if (isMinimized) {
    return (
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="fixed bottom-4 right-4 z-50"
      >
        <Button
          isIconOnly
          color="primary"
          size="lg"
          className="relative shadow-lg"
          onPress={() => onToggleMinimize(false)}
        >
          <MessageCircle size={24} />
          {unreadCount > 0 && (
            <Badge
              content={unreadCount > 99 ? '99+' : unreadCount}
              color="danger"
              className="absolute -top-2 -right-2"
            />
          )}
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ x: 400, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: 400, opacity: 0 }}
      className="fixed bottom-4 right-4 z-50"
    >
      <Card className="w-80 h-96 bg-white shadow-xl border">
        <CardBody className="p-0 flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center gap-2">
              <MessageCircle size={20} className="text-primary" />
              <h3 className="font-semibold">Messages</h3>
              {unreadCount > 0 && (
                <Badge content={unreadCount} color="danger" size="sm" />
              )}
            </div>
            <Button
              isIconOnly
              size="sm"
              variant="light"
              onPress={() => onToggleMinimize(true)}
            >
              <X size={16} />
            </Button>
          </div>

          {/* Conversations */}
          <div className="flex-1 overflow-y-auto p-2">
            {conversations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MessageCircle size={32} className="mx-auto mb-2 opacity-50" />
                <p className="text-sm">No conversations yet</p>
              </div>
            ) : (
              <div className="space-y-2">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.user_id}
                    className="p-3 hover:bg-gray-50 rounded-lg cursor-pointer"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-sm">{conversation.user_name}</p>
                        <p className="text-xs text-gray-600 truncate">
                          {conversation.messages[0]?.content || 'No messages'}
                        </p>
                      </div>
                      {conversation.unread_count > 0 && (
                        <Badge content={conversation.unread_count} color="primary" size="sm" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Quick message input */}
          <div className="p-3 border-t">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Quick message..."
                className="flex-1 px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20"
              />
              <Button
                isIconOnly
                size="sm"
                color="primary"
                isDisabled={!newMessage.trim()}
              >
                <Send size={14} />
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default FloatingChatPanel;
