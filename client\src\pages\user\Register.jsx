import { useState } from "react";
import {
  createAuthUserWithEmailAndPassword,
  signInWithGooglePopup,
} from "../../../utils/firebase/firebase.utils";
import axios from "axios";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";

const Register = () => {
  const navigate = useNavigate();
  const [data, setData] = useState({
    displayName: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  // GOOGLE SIGN IN
  const signInWithGoogle = async () => {
    try {
      console.log("Signing in with google...");
      const { user } = await signInWithGooglePopup();

      // Get Firebase authentication token
      const token = await user.getIdToken();
      console.log("Token: ", token);

      // Send user data and token to backend for MongoDB storage
      const { data: response } = await axios.post(
        "/register",
        {
          displayName: user.displayName,
          email: user.email,
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      console.log("Sent token.");

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success("Google sign-in successful! Welcome!");
        navigate("/user/index");
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      toast.error("Google sign-in failed. Please try again.");
    }
  };

  // find a way to communicate the invalid displayname error
  const registerUser = async (e) => {
    e.preventDefault();
    console.log("Registering...");

    const { displayName, email, password, confirmPassword } = data;

    // Validate displayName (3-15 characters, only letters, numbers, underscores, spaces)
    const displayNameRegex = /^[\w\s]{3,15}$/;
    if (!displayNameRegex.test(displayName)) {
      toast.error(
        "Invalid Username: Must be 3-15 characters and contain only letters, numbers, underscores, and spaces."
      );
      return;
    }
    console.log("Display Name", displayName);
    console.log("And the result is ", displayNameRegex.test(displayName));

    if (password !== confirmPassword) {
      alert("Passwords don't match!");
      return;
    }
    try {
      const { user } = await createAuthUserWithEmailAndPassword(
        email,
        password,
        displayName
      );

      // Get Firebase token
      const token = await user.getIdToken();

      // Send user data and token to backend
      const { data: response } = await axios.post(
        "/register",
        { displayName, email },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.error) {
        toast.error(response.error);
      } else {
        setData({
          displayName: "",
          email: "",
          password: "",
          confirmPassword: "",
        });
        toast.success("Registration successful! Welcome!");
        navigate("/user/index");
      }
    } catch (error) {
      console.error("Firebase registration error:", error);
      if (error.code === "auth/email-already-in-use") {
        toast.error("Email is already in use.");
      } else if (error.code === "auth/weak-password") {
        toast.error("Password should be at least 6 characters.");
      } else if (error.code === "auth/invalid-email") {
        toast.error("Invalid email format.");
      } else {
        toast.error("Registration failed. Please try again.");
      }
    }
  };

  return (
    <div className="container mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6" style={{ maxWidth: "500px" }}>
          <div className="card shadow-sm border-0 rounded-lg">
            <div className="card-body p-4 mx-auto">
              <h3 className="text-center mb-4">Create an Account</h3>
              <form onSubmit={registerUser} className="text-start">
                <div className="mb-3">
                  <label htmlFor="displayName" className="form-label">
                    Username
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    id="displayName"
                    value={data.displayName}
                    autoComplete="new-username"
                    onChange={(e) =>
                      setData({ ...data, displayName: e.target.value })
                    }
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    Email
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    value={data.email}
                    autoComplete="new-email"
                    onChange={(e) =>
                      setData({ ...data, email: e.target.value })
                    }
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="password" className="form-label">
                    Password
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    value={data.password}
                    autoComplete="new-password"
                    onChange={(e) =>
                      setData({ ...data, password: e.target.value })
                    }
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="confirm-password" className="form-label">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="confirm-password"
                    value={data.confirmPassword}
                    autoComplete="new-password"
                    onChange={(e) =>
                      setData({ ...data, confirmPassword: e.target.value })
                    }
                  />
                </div>
                <div className="">
                  <button
                    type="submit"
                    className="btn btn-dark btn-sm w-50  w-100 mx-auto d-block fw-semibold py-2 mt-4"
                  >
                    Sign Up
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary btn-sm w-50  w-100 mx-auto d-block fw-semibold py-2 mt-2"
                    onClick={signInWithGoogle}
                  >
                    Sign in with Google
                  </button>
                </div>
                <div className="mt-3 text-end">
                  Already have an account? <Link to="/login">Login</Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
