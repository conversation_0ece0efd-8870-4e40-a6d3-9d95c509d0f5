/**
 * API Caching Utility for Performance Optimization
 * 
 * This utility provides intelligent caching for API requests to improve
 * application performance and reduce server load.
 * 
 * Task: O3 Performance Optimization - API Caching
 */

class APICache {
  constructor(maxSize = 100, ttl = 5 * 60 * 1000) { // 5 minutes default TTL
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  generateKey(url, params = {}) {
    return `${url}_${JSON.stringify(params)}`;
  }

  set(key, data) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get(key) {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  clear() {
    this.cache.clear();
  }

  has(key) {
    const cached = this.cache.get(key);
    if (!cached) return false;
    
    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  // Get cache statistics
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      ttl: this.ttl,
      keys: Array.from(this.cache.keys())
    };
  }

  // Remove expired entries
  cleanup() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

export const apiCache = new APICache();

// Cached fetch function
export const cachedFetch = async (url, options = {}, cacheKey = null) => {
  const key = cacheKey || apiCache.generateKey(url, options);
  
  // Check cache first
  const cached = apiCache.get(key);
  if (cached) {
    console.log(`[Cache Hit] ${key}`);
    return cached;
  }
  
  // Fetch and cache
  try {
    console.log(`[Cache Miss] Fetching ${url}`);
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (response.ok) {
      apiCache.set(key, data);
    }
    
    return data;
  } catch (error) {
    console.error('API fetch error:', error);
    throw error;
  }
};

// Request deduplication
const pendingRequests = new Map();

export const deduplicatedFetch = async (url, options = {}) => {
  const key = apiCache.generateKey(url, options);
  
  // Check if request is already pending
  if (pendingRequests.has(key)) {
    console.log(`[Request Deduplication] Waiting for pending request: ${key}`);
    return pendingRequests.get(key);
  }
  
  // Check cache
  const cached = apiCache.get(key);
  if (cached) {
    return cached;
  }
  
  // Make request and store promise
  const requestPromise = fetch(url, options)
    .then(response => response.json())
    .then(data => {
      apiCache.set(key, data);
      pendingRequests.delete(key);
      return data;
    })
    .catch(error => {
      pendingRequests.delete(key);
      throw error;
    });
  
  pendingRequests.set(key, requestPromise);
  return requestPromise;
};

// Batch request utility
export const batchRequests = async (requests, batchSize = 5) => {
  const results = [];
  
  for (let i = 0; i < requests.length; i += batchSize) {
    const batch = requests.slice(i, i + batchSize);
    const batchResults = await Promise.allSettled(
      batch.map(request => cachedFetch(request.url, request.options, request.cacheKey))
    );
    results.push(...batchResults);
  }
  
  return results;
};

// Cache warming utility
export const warmCache = async (urls) => {
  console.log(`[Cache Warming] Warming cache for ${urls.length} URLs`);
  
  const requests = urls.map(url => ({ url }));
  await batchRequests(requests, 3); // Smaller batch size for warming
  
  console.log('[Cache Warming] Complete');
};

// Periodic cache cleanup
setInterval(() => {
  apiCache.cleanup();
}, 60000); // Cleanup every minute

export default { 
  apiCache, 
  cachedFetch, 
  deduplicatedFetch, 
  batchRequests, 
  warmCache 
};
