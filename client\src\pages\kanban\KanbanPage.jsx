import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import KanbanBoard from '../../components/kanban/KanbanBoard';
import LoadingAnimation from '../../components/layout/LoadingAnimation';

const KanbanPage = () => {
  const { id: projectId } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userRole, setUserRole] = useState(null);

  // Fetch project data and check user permissions
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!currentUser) {
        setError('You must be logged in to view this page.');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;

        setProject(projectData);

        // Check user's role in the project
        const { data: contributorData, error: contributorError } = await supabase
          .from('project_contributors')
          .select('is_admin')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .single();

        if (contributorError && contributorError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" error, which means user is not a contributor
          throw contributorError;
        }

        if (contributorData) {
          setUserRole(contributorData.is_admin ? 'admin' : 'contributor');
        } else {
          // Check if project is public
          if (projectData.is_public) {
            setUserRole('viewer');
          } else {
            setError('You do not have permission to view this project.');
            navigate('/projects');
          }
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching project data:', error);
        setError('Failed to load project data. Please try again later.');
        setLoading(false);
      }
    };

    fetchProjectData();
  }, [projectId, currentUser, navigate]);

  if (loading) {
    return <LoadingAnimation message="Loading project tasks..." />;
  }

  if (error) {
    return (
      <div className="kanban-page-error">
        <h2>Error</h2>
        <p>{error}</p>
        <div className="d-flex gap-2">
          <button className="btn btn-primary" onClick={() => navigate('/projects')}>
            Back to Projects
          </button>
          <button className="btn btn-secondary" onClick={() => navigate(`/project/${projectId}`)}>
            Back to Project
          </button>
          <button className="btn btn-info" onClick={() => window.location.reload()}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="kanban-page">
      <div className="kanban-page-header">
        <h1>{project.name} - Tasks</h1>
        <div className="kanban-page-actions">
          <button
            className="btn btn-secondary"
            onClick={() => navigate(`/project/${projectId}`)}
          >
            Back to Project
          </button>
        </div>
      </div>

      <KanbanBoard projectId={projectId} />
    </div>
  );
};

export default KanbanPage;
