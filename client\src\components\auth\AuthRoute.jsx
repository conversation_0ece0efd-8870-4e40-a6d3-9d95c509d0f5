import React, { useContext, useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import SimpleLoading from '../layout/SimpleLoading';

/**
 * AuthRoute Component
 *
 * A wrapper component that handles authentication state and redirects
 * unauthenticated users to the login page.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The component to render if authenticated
 * @returns {React.ReactNode} - The rendered component or redirect
 */
const AuthRoute = ({ children }) => {
  const { currentUser, isLoading } = useContext(UserContext);
  const location = useLocation();
  const [localLoading, setLocalLoading] = useState(true);

  // Add a small delay to ensure auth state is properly checked
  useEffect(() => {
    console.log('🔍 AUTH ROUTE: Auth state -', isLoading ? 'loading' : (currentUser ? 'authenticated' : 'not authenticated'));
    console.log('🔍 AUTH ROUTE: Current path:', location.pathname);

    // If auth is still loading, keep local loading true
    if (isLoading) {
      console.log('🔍 AUTH ROUTE: Auth is loading, keeping local loading true');
      setLocalLoading(true);
      return;
    }

    // Add a small delay to ensure auth state is stable
    console.log('🔍 AUTH ROUTE: Auth loading complete, setting timer to stabilize state');
    const timer = setTimeout(() => {
      console.log('🔍 AUTH ROUTE: Timer complete, setting local loading to false');
      setLocalLoading(false);
    }, 300);

    return () => {
      console.log('🔍 AUTH ROUTE: Cleaning up timer');
      clearTimeout(timer);
    };
  }, [isLoading, currentUser, location.pathname]);

  // Show loading indicator while checking authentication
  if (isLoading || localLoading) {
    console.log('🔍 AUTH ROUTE: Showing loading indicator, isLoading:', isLoading, 'localLoading:', localLoading);
    return <SimpleLoading text="AUTH ROUTE: Checking authentication..." fullPage={true} />;
  }

  // If not authenticated, redirect to login with the return URL
  if (!currentUser) {
    console.log('🔍 AUTH ROUTE: Redirecting to login from', location.pathname);
    // Store the current location so we can redirect back after login
    return <Navigate to="/login" state={{ from: location.pathname }} replace />;
  }

  // If authenticated, render the children
  console.log('🔍 AUTH ROUTE: User authenticated, rendering protected content for', location.pathname);
  return children;
};

export default AuthRoute;
