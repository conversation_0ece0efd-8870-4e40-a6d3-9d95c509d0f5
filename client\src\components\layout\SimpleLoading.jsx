import React from 'react';
import { cn } from '../../lib/utils';

/**
 * SimpleLoading Component
 *
 * A minimal, reliable loading indicator without any complex focus detection
 * or visibility tracking. This is designed to be simple and reliable.
 */
const SimpleLoading = ({ text = 'Loading...', size = 'medium', fullPage = false }) => {
  // Determine the size class based on the size prop
  const spinnerSize =
    size === 'small' ? 'w-5 h-5 border-2' :
    size === 'large' ? 'w-16 h-16 border-4' : 'w-10 h-10 border-3';

  // Container classes
  const containerClass = cn(
    'flex flex-col items-center justify-center',
    fullPage ? 'fixed inset-0 bg-white/80 dark:bg-black/80 z-50' : 'p-5'
  );

  return (
    <div className={containerClass} data-testid="simple-loading">
      <div className={cn(
        spinnerSize,
        'rounded-full border-gray-200 border-t-primary animate-spin'
      )}></div>
      {text && <div className="mt-3 text-sm text-gray-600 dark:text-gray-300">{text}</div>}
    </div>
  );
};

export default SimpleLoading;
