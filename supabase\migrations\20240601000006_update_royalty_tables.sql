-- Update royalty tables to match the new schema while preserving existing data

-- Check if the revenue_distribution table exists
DO $$
DECLARE
    revenue_distribution_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'revenue_distribution'
    ) INTO revenue_distribution_exists;

    IF revenue_distribution_exists THEN
        RAISE NOTICE 'Revenue distribution table exists, will update schema';
    ELSE
        RAISE NOTICE 'Revenue distribution table does not exist, will create new tables';
    END IF;
END $$;

-- Create royalty_distributions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.royalty_distributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    revenue_id UUID REFERENCES public.revenue_entries(id) ON DELETE SET NULL,
    distribution_date DATE NOT NULL,
    total_amount DECIMAL(12, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'calculated', 'approved', 'distributed', 'cancelled')),
    calculation_method TEXT NOT NULL,
    calculation_data JSONB DEFAULT '{}'::jsonb,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    approved_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    approved_at TIMESTAMP WITH TIME ZONE,
    notes TEXT
);

-- Create royalty_payments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.royalty_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    distribution_id UUID NOT NULL REFERENCES public.royalty_distributions(id) ON DELETE CASCADE,
    recipient_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    amount DECIMAL(12, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'failed')),
    payment_method TEXT,
    payment_reference TEXT,
    payment_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    notes TEXT
);

-- Migrate data from revenue_distribution table to royalty_distributions and royalty_payments if it exists
DO $$
DECLARE
    revenue_distribution_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'revenue_distribution'
    ) INTO revenue_distribution_exists;

    IF revenue_distribution_exists THEN
        -- Check if there's data to migrate
        IF EXISTS (SELECT 1 FROM public.revenue_distribution LIMIT 1) THEN
            -- Group distributions by revenue_id to create royalty_distributions records
            INSERT INTO public.royalty_distributions (
                project_id,
                revenue_id,
                distribution_date,
                total_amount,
                currency,
                status,
                calculation_method,
                calculation_data,
                created_by,
                created_at,
                updated_at,
                notes
            )
            SELECT
                rd.project_id,
                rd.revenue_id,
                COALESCE(r.date_received, CURRENT_DATE),
                SUM(rd.amount),
                rd.currency,
                CASE
                    WHEN rd.status = 'pending' THEN 'calculated'
                    WHEN rd.status = 'approved' THEN 'approved'
                    WHEN rd.status = 'paid' THEN 'distributed'
                    ELSE 'pending'
                END,
                rd.calculation_method,
                jsonb_build_object(
                    'original_distribution', jsonb_agg(jsonb_build_object(
                        'contributor_id', rd.contributor_id,
                        'user_id', rd.user_id,
                        'amount', rd.amount,
                        'percentage', rd.percentage,
                        'calculation_details', rd.calculation_details
                    ))
                ),
                MIN(rd.created_by),
                MIN(rd.created_at),
                MIN(rd.updated_at),
                'Migrated from legacy revenue_distribution table'
            FROM
                public.revenue_distribution rd
            LEFT JOIN
                public.revenue r ON rd.revenue_id = r.id
            GROUP BY
                rd.project_id, rd.revenue_id, r.date_received, rd.currency, rd.status, rd.calculation_method;

            -- Create royalty_payments records for each distribution
            INSERT INTO public.royalty_payments (
                distribution_id,
                recipient_id,
                amount,
                currency,
                status,
                created_at,
                updated_at,
                notes
            )
            SELECT
                rd.id,
                old_rd.user_id,
                old_rd.amount,
                old_rd.currency,
                CASE
                    WHEN old_rd.status = 'pending' THEN 'pending'
                    WHEN old_rd.status = 'approved' THEN 'pending'
                    WHEN old_rd.status = 'paid' THEN 'paid'
                    ELSE 'pending'
                END,
                old_rd.created_at,
                old_rd.updated_at,
                'Migrated from legacy revenue_distribution table'
            FROM
                public.revenue_distribution old_rd
            JOIN
                public.royalty_distributions rd ON rd.revenue_id = old_rd.revenue_id AND rd.project_id = old_rd.project_id;

            RAISE NOTICE 'Data migration from revenue_distribution completed';
        ELSE
            RAISE NOTICE 'No data to migrate from revenue_distribution table';
        END IF;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_royalty_distributions_project_id ON public.royalty_distributions(project_id);
CREATE INDEX IF NOT EXISTS idx_royalty_distributions_revenue_id ON public.royalty_distributions(revenue_id);
CREATE INDEX IF NOT EXISTS idx_royalty_distributions_status ON public.royalty_distributions(status);
CREATE INDEX IF NOT EXISTS idx_royalty_payments_distribution_id ON public.royalty_payments(distribution_id);
CREATE INDEX IF NOT EXISTS idx_royalty_payments_recipient_id ON public.royalty_payments(recipient_id);
CREATE INDEX IF NOT EXISTS idx_royalty_payments_status ON public.royalty_payments(status);

-- Set up Row Level Security (RLS)
-- Royalty Distributions
ALTER TABLE public.royalty_distributions ENABLE ROW LEVEL SECURITY;

-- Project members can view royalty distributions
DROP POLICY IF EXISTS "Project members can view royalty distributions" ON public.royalty_distributions;
CREATE POLICY "Project members can view royalty distributions"
ON public.royalty_distributions FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = royalty_distributions.project_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
    )
);

-- Project admins can create, update, or delete royalty distributions
DROP POLICY IF EXISTS "Project admins can manage royalty distributions" ON public.royalty_distributions;
CREATE POLICY "Project admins can manage royalty distributions"
ON public.royalty_distributions FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.project_contributors
        WHERE project_contributors.project_id = royalty_distributions.project_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Royalty Payments
ALTER TABLE public.royalty_payments ENABLE ROW LEVEL SECURITY;

-- Users can view their own royalty payments
DROP POLICY IF EXISTS "Users can view their own royalty payments" ON public.royalty_payments;
CREATE POLICY "Users can view their own royalty payments"
ON public.royalty_payments FOR SELECT
USING (
    recipient_id = auth.uid()
);

-- Project admins can view all royalty payments for their projects
DROP POLICY IF EXISTS "Project admins can view all royalty payments" ON public.royalty_payments;
CREATE POLICY "Project admins can view all royalty payments"
ON public.royalty_payments FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.royalty_distributions
        JOIN public.project_contributors ON project_contributors.project_id = royalty_distributions.project_id
        WHERE royalty_distributions.id = royalty_payments.distribution_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Project admins can create, update, or delete royalty payments
DROP POLICY IF EXISTS "Project admins can manage royalty payments" ON public.royalty_payments;
CREATE POLICY "Project admins can manage royalty payments"
ON public.royalty_payments FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.royalty_distributions
        JOIN public.project_contributors ON project_contributors.project_id = royalty_distributions.project_id
        WHERE royalty_distributions.id = royalty_payments.distribution_id
        AND project_contributors.user_id = auth.uid()
        AND project_contributors.status = 'active'
        AND (
            project_contributors.permission_level = 'Owner' OR
            project_contributors.permission_level = 'Admin'
        )
    )
);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to update the updated_at column
DROP TRIGGER IF EXISTS update_royalty_distributions_updated_at ON public.royalty_distributions;
CREATE TRIGGER update_royalty_distributions_updated_at
BEFORE UPDATE ON public.royalty_distributions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_royalty_payments_updated_at ON public.royalty_payments;
CREATE TRIGGER update_royalty_payments_updated_at
BEFORE UPDATE ON public.royalty_payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to log royalty activities
CREATE OR REPLACE FUNCTION log_royalty_activity()
RETURNS TRIGGER AS $$
DECLARE
    project_id_val UUID;
    activity_type_val TEXT;
    activity_data_val JSONB;
BEGIN
    IF TG_TABLE_NAME = 'royalty_distributions' THEN
        project_id_val := NEW.project_id;

        IF TG_OP = 'INSERT' THEN
            activity_type_val := 'royalty_distribution_created';
            activity_data_val := jsonb_build_object(
                'distribution_id', NEW.id,
                'total_amount', NEW.total_amount,
                'currency', NEW.currency,
                'distribution_date', NEW.distribution_date,
                'calculation_method', NEW.calculation_method
            );
        ELSIF TG_OP = 'UPDATE' THEN
            activity_type_val := 'royalty_distribution_updated';
            activity_data_val := jsonb_build_object(
                'distribution_id', NEW.id,
                'total_amount', NEW.total_amount,
                'currency', NEW.currency,
                'distribution_date', NEW.distribution_date,
                'status', NEW.status
            );
        END IF;
    ELSIF TG_TABLE_NAME = 'royalty_payments' THEN
        -- Get project_id from the distribution
        SELECT d.project_id INTO project_id_val
        FROM public.royalty_distributions d
        WHERE d.id = NEW.distribution_id;

        IF TG_OP = 'INSERT' THEN
            activity_type_val := 'royalty_payment_created';
            activity_data_val := jsonb_build_object(
                'payment_id', NEW.id,
                'distribution_id', NEW.distribution_id,
                'recipient_id', NEW.recipient_id,
                'amount', NEW.amount,
                'currency', NEW.currency
            );
        ELSIF TG_OP = 'UPDATE' AND NEW.status != OLD.status THEN
            activity_type_val := 'royalty_payment_status_changed';
            activity_data_val := jsonb_build_object(
                'payment_id', NEW.id,
                'distribution_id', NEW.distribution_id,
                'recipient_id', NEW.recipient_id,
                'amount', NEW.amount,
                'currency', NEW.currency,
                'old_status', OLD.status,
                'new_status', NEW.status
            );
        END IF;
    END IF;

    -- Log the activity if we have a valid activity type
    IF activity_type_val IS NOT NULL AND project_id_val IS NOT NULL THEN
        INSERT INTO public.project_activities (
            project_id,
            user_id,
            activity_type,
            activity_data
        ) VALUES (
            project_id_val,
            COALESCE(NEW.created_by, auth.uid()),
            activity_type_val,
            activity_data_val
        );
    END IF;

    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create triggers to log royalty activities
DROP TRIGGER IF EXISTS log_royalty_distributions_activity ON public.royalty_distributions;
CREATE TRIGGER log_royalty_distributions_activity
AFTER INSERT OR UPDATE ON public.royalty_distributions
FOR EACH ROW
EXECUTE FUNCTION log_royalty_activity();

DROP TRIGGER IF EXISTS log_royalty_payments_activity ON public.royalty_payments;
CREATE TRIGGER log_royalty_payments_activity
AFTER INSERT OR UPDATE ON public.royalty_payments
FOR EACH ROW
EXECUTE FUNCTION log_royalty_activity();

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.royalty_distributions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.royalty_payments TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
