import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Progress, Chip } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { 
  Clock, 
  Play, 
  Pause, 
  Square,
  BarChart3,
  Calendar,
  Target,
  Timer
} from 'lucide-react';

/**
 * Enhanced Time Tracking Panel Component
 * 
 * Features:
 * - Per-task time tracking
 * - Active task display
 * - Daily/weekly summaries
 * - Time logged per project breakdown
 */
const TimeTrackingPanel = ({ 
  activeTask = null,
  activeTimerTaskId = null,
  onStopTimer,
  isVisible = true,
  className = "" 
}) => {
  const { currentUser } = useContext(UserContext);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [todayStats, setTodayStats] = useState({
    totalTime: 0,
    tasksWorked: 0,
    projectBreakdown: []
  });
  const [weekStats, setWeekStats] = useState({
    totalTime: 0,
    dailyAverage: 0,
    productivity: 0
  });

  // Update elapsed time for active timer
  useEffect(() => {
    let interval = null;
    if (activeTimerTaskId && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime);
      }, 1000);
    } else if (!activeTimerTaskId) {
      setElapsedTime(0);
    }
    return () => clearInterval(interval);
  }, [activeTimerTaskId, startTime]);

  // Set start time when timer becomes active
  useEffect(() => {
    if (activeTimerTaskId && !startTime) {
      setStartTime(Date.now());
    } else if (!activeTimerTaskId) {
      setStartTime(null);
      setElapsedTime(0);
    }
  }, [activeTimerTaskId]);

  // Load time tracking statistics
  useEffect(() => {
    if (currentUser) {
      loadTimeStats();
    }
  }, [currentUser]);

  const loadTimeStats = async () => {
    try {
      // Get today's stats
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const { data: todayEntries, error: todayError } = await supabase
        .from('time_entries')
        .select(`
          *,
          tasks (
            title,
            project_id,
            projects (name)
          )
        `)
        .eq('user_id', currentUser.id)
        .gte('created_at', today.toISOString())
        .lt('created_at', tomorrow.toISOString());

      if (todayError) throw todayError;

      // Calculate today's stats
      const totalMinutesToday = todayEntries?.reduce((sum, entry) => sum + (entry.minutes_logged || 0), 0) || 0;
      const uniqueTasks = new Set(todayEntries?.map(entry => entry.task_id)).size;
      
      // Project breakdown
      const projectBreakdown = {};
      todayEntries?.forEach(entry => {
        const projectName = entry.tasks?.projects?.name || 'No Project';
        if (!projectBreakdown[projectName]) {
          projectBreakdown[projectName] = 0;
        }
        projectBreakdown[projectName] += entry.minutes_logged || 0;
      });

      setTodayStats({
        totalTime: totalMinutesToday,
        tasksWorked: uniqueTasks,
        projectBreakdown: Object.entries(projectBreakdown).map(([name, minutes]) => ({
          name,
          minutes,
          percentage: totalMinutesToday > 0 ? Math.round((minutes / totalMinutesToday) * 100) : 0
        }))
      });

      // Get week stats
      const weekStart = new Date(today);
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());

      const { data: weekEntries, error: weekError } = await supabase
        .from('time_entries')
        .select('minutes_logged, created_at')
        .eq('user_id', currentUser.id)
        .gte('created_at', weekStart.toISOString());

      if (weekError) throw weekError;

      const totalMinutesWeek = weekEntries?.reduce((sum, entry) => sum + (entry.minutes_logged || 0), 0) || 0;
      const dailyAverage = Math.round(totalMinutesWeek / 7);
      const productivity = totalMinutesWeek > 0 ? Math.min(Math.round((totalMinutesWeek / (40 * 60)) * 100), 100) : 0;

      setWeekStats({
        totalTime: totalMinutesWeek,
        dailyAverage,
        productivity
      });

    } catch (error) {
      console.error('Error loading time stats:', error);
    }
  };

  const formatTime = (milliseconds) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatMinutes = (minutes) => {
    if (!minutes || minutes === 0) return '0m';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  };

  const handleStopTimer = async () => {
    if (activeTimerTaskId && onStopTimer) {
      await onStopTimer(activeTimerTaskId);
      // Reload stats after stopping timer
      setTimeout(loadTimeStats, 1000);
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.3 }}
      className={`space-y-4 ${className}`}
    >
      {/* Active Timer */}
      <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border border-green-500/20">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Timer className="w-5 h-5 text-green-400" />
            <h3 className="text-lg font-semibold text-white">Active Timer</h3>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          {activeTimerTaskId && activeTask ? (
            <div className="space-y-4">
              <div className="text-center">
                <motion.div
                  className="text-3xl font-mono font-bold text-green-400 mb-2"
                  animate={{ scale: [1, 1.05, 1] }}
                  transition={{ duration: 1, repeat: Infinity }}
                >
                  {formatTime(elapsedTime)}
                </motion.div>
                <div className="text-white/70 text-sm">Currently tracking</div>
              </div>

              <div className="bg-white/5 rounded-lg p-3">
                <div className="font-medium text-white mb-1">{activeTask.title}</div>
                <div className="text-sm text-white/60">{activeTask.description || 'No description'}</div>
              </div>

              <Button
                color="danger"
                variant="flat"
                onClick={handleStopTimer}
                startContent={<Square className="w-4 h-4" />}
                className="w-full"
              >
                Stop Timer
              </Button>
            </div>
          ) : (
            <div className="text-center py-4">
              <Clock className="w-8 h-8 text-white/40 mx-auto mb-2" />
              <div className="text-white/60">No active timer</div>
              <div className="text-sm text-white/40">Start a timer from any task card</div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Today's Summary */}
      <Card className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">Today's Summary</h3>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-white/70">Total Time</span>
              <span className="font-semibold text-white">{formatMinutes(todayStats.totalTime)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/70">Tasks Worked</span>
              <span className="font-semibold text-white">{todayStats.tasksWorked}</span>
            </div>

            {todayStats.projectBreakdown.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-white/80">Project Breakdown</div>
                {todayStats.projectBreakdown.map((project, index) => (
                  <div key={index} className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-white/60">{project.name}</span>
                      <span className="text-white/80">{formatMinutes(project.minutes)}</span>
                    </div>
                    <Progress
                      value={project.percentage}
                      color="primary"
                      size="sm"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Week Summary */}
      <Card className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <BarChart3 className="w-5 h-5 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">This Week</h3>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-white/70">Total Time</span>
              <span className="font-semibold text-white">{formatMinutes(weekStats.totalTime)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-white/70">Daily Average</span>
              <span className="font-semibold text-white">{formatMinutes(weekStats.dailyAverage)}</span>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-white/70">Productivity</span>
                <Chip 
                  size="sm" 
                  color={weekStats.productivity >= 70 ? 'success' : weekStats.productivity >= 40 ? 'warning' : 'danger'}
                  variant="flat"
                >
                  {weekStats.productivity}%
                </Chip>
              </div>
              <Progress
                value={weekStats.productivity}
                color={weekStats.productivity >= 70 ? 'success' : weekStats.productivity >= 40 ? 'warning' : 'danger'}
                size="sm"
              />
            </div>
          </div>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default TimeTrackingPanel;
