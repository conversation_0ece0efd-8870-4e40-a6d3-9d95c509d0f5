import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import MilestoneForm from './MilestoneForm';
import MilestoneList from './MilestoneList';
import MilestoneProgress from './MilestoneProgress';
import TimelineVisualization from './TimelineVisualization';
import MilestoneCompletionTracker from './MilestoneCompletionTracker';

const MilestoneManager = ({ projectId }) => {
  const { currentUser } = useContext(UserContext);
  const [showForm, setShowForm] = useState(false);
  const [editingMilestone, setEditingMilestone] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [userRole, setUserRole] = useState(null);
  const [milestones, setMilestones] = useState([]);
  const [projectData, setProjectData] = useState(null);
  const [selectedMilestone, setSelectedMilestone] = useState(null);
  const [viewMode, setViewMode] = useState('progress'); // 'progress', 'timeline', 'completion'

  // Check user's role in the project
  useEffect(() => {
    const checkUserRole = async () => {
      if (!currentUser || !projectId) return;

      try {
        const { data, error } = await supabase
          .from('project_contributors')
          .select('is_admin')
          .eq('project_id', projectId)
          .eq('user_id', currentUser.id)
          .single();

        if (!error && data) {
          setUserRole(data.is_admin ? 'admin' : 'contributor');
        } else {
          setUserRole('viewer');
        }
      } catch (error) {
        console.error('Error checking user role:', error);
        setUserRole('viewer');
      }
    };

    checkUserRole();
  }, [currentUser, projectId]);

  // Fetch milestones and project data
  useEffect(() => {
    const fetchData = async () => {
      if (!projectId) return;

      try {
        // Fetch milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', projectId)
          .order('created_at', { ascending: true });

        if (milestonesError) throw milestonesError;
        setMilestones(milestonesData || []);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;
        setProjectData(projectData);

      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, [projectId, refreshKey]);

  // Reset form when projectId changes
  useEffect(() => {
    setShowForm(false);
    setEditingMilestone(null);
    setSelectedMilestone(null);
    setViewMode('progress');
  }, [projectId]);

  // Handle form submission success
  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingMilestone(null);
    setRefreshKey(prev => prev + 1);
  };

  // Handle edit button click
  const handleEdit = (milestone) => {
    setEditingMilestone(milestone);
    setShowForm(true);

    // Scroll to form
    setTimeout(() => {
      document.getElementById('milestone-form-section')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Handle delete success
  const handleDeleteSuccess = () => {
    setRefreshKey(prev => prev + 1);
    if (selectedMilestone) {
      setSelectedMilestone(null);
    }
  };

  // Handle milestone selection
  const handleMilestoneSelect = (milestone) => {
    setSelectedMilestone(milestone);
    setViewMode('completion');

    // Scroll to completion tracker
    setTimeout(() => {
      document.getElementById('milestone-completion-section')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Handle milestone update from completion tracker
  const handleMilestoneUpdate = (updatedMilestone) => {
    // Update milestone in local state
    setMilestones(prev => prev.map(m =>
      m.id === updatedMilestone.id ? updatedMilestone : m
    ));

    // Update selected milestone
    setSelectedMilestone(updatedMilestone);

    // Refresh data
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="milestone-manager">
      <div className="milestone-manager-header">
        <h3 className="section-title">Project Milestones</h3>

        <div className="milestone-actions">
          {/* View Mode Selector */}
          <div className="view-mode-selector">
            <button
              className={`view-mode-button ${viewMode === 'progress' ? 'active' : ''}`}
              onClick={() => setViewMode('progress')}
            >
              <i className="bi bi-pie-chart"></i> Progress
            </button>
            <button
              className={`view-mode-button ${viewMode === 'timeline' ? 'active' : ''}`}
              onClick={() => setViewMode('timeline')}
            >
              <i className="bi bi-calendar-range"></i> Timeline
            </button>
            {selectedMilestone && (
              <button
                className={`view-mode-button ${viewMode === 'completion' ? 'active' : ''}`}
                onClick={() => setViewMode('completion')}
              >
                <i className="bi bi-check-square"></i> Completion
              </button>
            )}
          </div>

          {userRole === 'admin' && !showForm && (
            <button
              className="btn btn-primary add-milestone-btn"
              onClick={() => setShowForm(true)}
            >
              <i className="bi bi-plus-lg"></i> Add Milestone
            </button>
          )}
        </div>
      </div>

      {/* Progress Section */}
      {viewMode === 'progress' && (
        <div className="milestone-progress-section">
          <MilestoneProgress
            projectId={projectId}
            key={`progress-${refreshKey}`}
          />
        </div>
      )}

      {/* Timeline Section */}
      {viewMode === 'timeline' && (
        <div className="milestone-timeline-section">
          <TimelineVisualization
            projectId={projectId}
            milestones={milestones}
            projectStartDate={projectData?.launch_date}
            projectEndDate={null}
            key={`timeline-${refreshKey}`}
          />
        </div>
      )}

      {/* Completion Tracker Section */}
      {viewMode === 'completion' && selectedMilestone && (
        <div id="milestone-completion-section" className="milestone-completion-section">
          <MilestoneCompletionTracker
            milestone={selectedMilestone}
            onUpdate={handleMilestoneUpdate}
            key={`completion-${refreshKey}-${selectedMilestone.id}`}
          />
        </div>
      )}

      {/* Form Section */}
      {showForm && userRole === 'admin' && (
        <div id="milestone-form-section" className="milestone-form-section">
          <div className="form-header">
            <h4>{editingMilestone ? 'Edit Milestone' : 'Add New Milestone'}</h4>
            <button
              className="btn-close"
              onClick={() => {
                setShowForm(false);
                setEditingMilestone(null);
              }}
              aria-label="Close"
            >
              <i className="bi bi-x-lg"></i>
            </button>
          </div>

          <MilestoneForm
            projectId={projectId}
            initialData={editingMilestone}
            isEditing={!!editingMilestone}
            onSuccess={handleFormSuccess}
          />
        </div>
      )}

      {/* List Section */}
      <div className="milestone-list-section">
        <h4 className="section-subtitle">All Milestones</h4>
        <MilestoneList
          projectId={projectId}
          onEdit={userRole === 'admin' ? handleEdit : null}
          onDelete={handleDeleteSuccess}
          onSelect={handleMilestoneSelect}
          key={`list-${refreshKey}`}
        />
      </div>
    </div>
  );
};

export default MilestoneManager;
