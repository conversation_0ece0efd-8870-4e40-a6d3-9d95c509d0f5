import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
// Production-ready roadmap tracker without hardcoded data
import RoadmapPhaseEditor from './RoadmapPhaseEditor';

const DirectSupabaseRoadmapTracker = () => {
  const { currentUser } = useContext(UserContext);
  const [phases, setPhases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [editingPhase, setEditingPhase] = useState(null);
  const [showEditor, setShowEditor] = useState(false);

  // Check if user is admin and load data
  useEffect(() => {
    const checkAdminAndLoadData = async () => {
      if (!currentUser) {
        setLoading(false);
        return;
      }

      try {
        // Check if user is admin
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (userError) {
          console.error('Error checking admin status:', userError);
          setIsAdmin(false);
        } else {
          setIsAdmin(userData?.is_admin || false);
        }

        // Load roadmap data from Supabase
        try {
          console.log('Attempting to load roadmap data from Supabase...');

          const { data: roadmapEntries, error: roadmapError } = await supabase
            .from('roadmap')
            .select('*')
            .order('created_at', { ascending: false })
            .limit(1);

          if (roadmapError) {
            throw new Error(`Supabase error: ${roadmapError.message}`);
          }

          console.log('Roadmap entries from Supabase:', roadmapEntries);

          if (roadmapEntries && roadmapEntries.length > 0) {
            if (roadmapEntries[0].data) {
              console.log('Valid roadmap data found in Supabase');

              // Validate that the data is in the expected format
              if (Array.isArray(roadmapEntries[0].data)) {
                console.log('Data is an array, as expected');
                setPhases(roadmapEntries[0].data);
                toast.success('Loaded roadmap data from database');
                return; // Exit early if successful
              } else {
                console.error('Data is not an array:', roadmapEntries[0].data);
                throw new Error('Invalid data format: expected an array');
              }
            } else {
              console.error('No data property in roadmap entry:', roadmapEntries[0]);
              throw new Error('Invalid data format: missing data property');
            }
          } else {
            console.log('No roadmap entries found in Supabase');
            throw new Error('No roadmap data found in database');
          }
        } catch (supabaseError) {
          console.error('Error loading roadmap data from Supabase:', supabaseError);
          toast.error('Failed to load roadmap data from database');

          // Initialize with empty roadmap if database fails
          console.log('Database load failed, initializing empty roadmap');
          setPhases([]);
          toast.info('Roadmap data will be loaded from database when available');
        }
      } catch (error) {
        console.error('Error in initialization:', error);
        setPhases([]);
      } finally {
        setLoading(false);
      }
    };

    checkAdminAndLoadData();
  }, [currentUser]);

  // Save to Supabase whenever phases change
  useEffect(() => {
    // Skip saving on initial load
    if (loading) return;

    // Only save to Supabase if user is admin
    if (isAdmin) {
      const saveToSupabase = async () => {
        try {
          setSaving(true);
          console.log('Saving roadmap data to Supabase...');

          // Validate data before saving
          if (!Array.isArray(phases)) {
            console.error('Invalid data format: phases is not an array');
            toast.error('Cannot save: Invalid data format');
            setSaving(false);
            return;
          }

          // First, try to upsert (update or insert) instead of delete and insert
          try {
            console.log('Attempting to upsert roadmap data...');

            // Get existing roadmap entries
            const { data: existingEntries, error: fetchError } = await supabase
              .from('roadmap')
              .select('id')
              .limit(1);

            if (fetchError) {
              console.error('Error fetching existing roadmap entries:', fetchError);
              throw new Error(`Fetch error: ${fetchError.message}`);
            }

            if (existingEntries && existingEntries.length > 0) {
              // Update existing entry
              console.log('Updating existing roadmap entry:', existingEntries[0].id);
              const { data, error: updateError } = await supabase
                .from('roadmap')
                .update({
                  data: phases,
                  updated_by: currentUser.id
                  // Don't set updated_at - let Supabase handle it or use a trigger
                })
                .eq('id', existingEntries[0].id)
                .select();

              if (updateError) {
                console.error('Error updating roadmap data:', updateError);
                throw new Error(`Update error: ${updateError.message}`);
              }

              console.log('Updated roadmap data successfully:', data);
              toast.success('Saved to database');
            } else {
              // Insert new entry
              console.log('No existing roadmap entry, creating new one...');
              const { data, error: insertError } = await supabase
                .from('roadmap')
                .insert({
                  data: phases,
                  updated_by: currentUser.id
                })
                .select();

              if (insertError) {
                console.error('Error inserting roadmap data:', insertError);
                throw new Error(`Insert error: ${insertError.message}`);
              }

              console.log('Inserted roadmap data successfully:', data);
              toast.success('Saved to database');
            }
          } catch (upsertError) {
            console.error('Error during upsert operation:', upsertError);
            toast.error(`Failed to save to database: ${upsertError.message}`);
          }
        } catch (error) {
          console.error('Error saving data to Supabase:', error);
          toast.error(`Error saving to database: ${error.message}`);
        } finally {
          setSaving(false);
        }
      };

      // Debounce Supabase saves to avoid too many requests
      const saveTimeout = setTimeout(() => {
        saveToSupabase();
      }, 2000);

      return () => clearTimeout(saveTimeout);
    }
  }, [phases, loading, isAdmin, currentUser]);

  // Calculate overall progress stats
  const calculateStats = () => {
    let totalTasks = 0;
    let completedTasks = 0;

    // Check if phases is defined and is an array
    if (Array.isArray(phases) && phases.length > 0) {
      phases.forEach(phase => {
        if (phase && Array.isArray(phase.sections)) {
          phase.sections.forEach(section => {
            if (section && Array.isArray(section.tasks)) {
              totalTasks += section.tasks.length;
              completedTasks += section.tasks.filter(task => task.completed).length;
            }
          });
        }
      });
    }

    return {
      totalTasks,
      completedTasks,
      progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    };
  };

  const stats = calculateStats();

  // Toggle task completion status
  const toggleTaskCompletion = (phaseId, sectionId, taskId) => {
    setPhases(prevPhases => {
      if (!Array.isArray(prevPhases)) {
        console.error('prevPhases is not an array:', prevPhases);
        return prevPhases;
      }

      return prevPhases.map(phase => {
        if (!phase) return phase;

        if (phase.id === phaseId) {
          if (!Array.isArray(phase.sections)) {
            console.error('phase.sections is not an array:', phase);
            return phase;
          }

          return {
            ...phase,
            sections: phase.sections.map(section => {
              if (!section) return section;

              if (section.id === sectionId) {
                if (!Array.isArray(section.tasks)) {
                  console.error('section.tasks is not an array:', section);
                  return section;
                }

                return {
                  ...section,
                  tasks: section.tasks.map(task => {
                    if (!task) return task;

                    if (task.id === taskId) {
                      return {
                        ...task,
                        completed: !task.completed
                      };
                    }
                    return task;
                  })
                };
              }
              return section;
            })
          };
        }
        return phase;
      });
    });
  };

  // Toggle phase expansion
  const togglePhaseExpansion = (phaseId) => {
    setPhases(prevPhases => {
      if (!Array.isArray(prevPhases)) {
        console.error('prevPhases is not an array:', prevPhases);
        return prevPhases;
      }

      return prevPhases.map(phase => {
        if (!phase) return phase;

        if (phase.id === phaseId) {
          return {
            ...phase,
            expanded: !phase.expanded
          };
        }
        return phase;
      });
    });
  };

  // Calculate section progress
  const calculateSectionProgress = (section) => {
    if (!section || !Array.isArray(section.tasks)) {
      return 0;
    }
    const total = section.tasks.length;
    const completed = section.tasks.filter(task => task.completed).length;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  // Calculate phase progress
  const calculatePhaseProgress = (phase) => {
    let totalTasks = 0;
    let completedTasks = 0;

    if (phase && Array.isArray(phase.sections)) {
      phase.sections.forEach(section => {
        if (section && Array.isArray(section.tasks)) {
          totalTasks += section.tasks.length;
          completedTasks += section.tasks.filter(task => task.completed).length;
        }
      });
    }

    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  };

  // Clear all progress (reset all tasks to uncompleted)
  const resetProgress = () => {
    if (confirm('Are you sure you want to reset all progress? This cannot be undone.')) {
      setPhases(prevPhases => {
        if (!Array.isArray(prevPhases)) {
          console.error('prevPhases is not an array:', prevPhases);
          return prevPhases;
        }

        return prevPhases.map(phase => {
          if (!phase || !Array.isArray(phase.sections)) {
            return phase;
          }

          return {
            ...phase,
            sections: phase.sections.map(section => {
              if (!section || !Array.isArray(section.tasks)) {
                return section;
              }

              return {
                ...section,
                tasks: section.tasks.map(task => {
                  if (!task) return task;

                  return {
                    ...task,
                    completed: false
                  };
                })
              };
            })
          };
        });
      });
      toast.success('Progress reset successfully');
    }
  };

  // Initialize roadmap from database
  const initializeRoadmap = () => {
    if (confirm('Initialize roadmap from database? This will load the latest roadmap structure.')) {
      // Initialize with empty structure - data should come from database
      const emptyData = [];

      // Create a map of task IDs to completion status from current data
      const completionMap = {};
      phases.forEach(phase => {
        phase.sections.forEach(section => {
          section.tasks.forEach(task => {
            completionMap[`${phase.id}-${section.id}-${task.id}`] = task.completed;
          });
        });
      });

      setPhases(emptyData);
      toast.success('Roadmap initialized - please load data from database');
    }
  };

  // Export data function
  const exportData = () => {
    const dataStr = JSON.stringify(phases, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = 'royaltea-roadmap-data.json';

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    toast.success('Data exported successfully');
  };

  // Import data function
  const importData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const importedData = JSON.parse(event.target.result);
          if (confirm('Are you sure you want to import this data? Current progress will be replaced.')) {
            setPhases(importedData);
            toast.success('Data imported successfully');
          }
        } catch (error) {
          toast.error('Error importing data. Please check the file format.');
          console.error('Import error:', error);
        }
      };
      reader.readAsText(file);
    };

    input.click();
  };

  // Start editing a phase
  const startEditingPhase = (phase) => {
    if (!isAdmin) {
      toast.error('You need admin privileges to edit phases');
      return;
    }

    setEditingPhase(phase);
    setShowEditor(true);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingPhase(null);
    setShowEditor(false);
  };

  // Save edited phase
  const saveEditedPhase = (updatedPhase) => {
    setPhases(prevPhases =>
      prevPhases.map(phase =>
        phase.id === updatedPhase.id ? updatedPhase : phase
      )
    );

    setEditingPhase(null);
    setShowEditor(false);
    toast.success(`Phase ${updatedPhase.id} updated successfully`);
  };

  if (loading) {
    return (
      <div className="roadmap-loading">
        <div className="roadmap-spinner"></div>
      </div>
    );
  }

  return (
    <div className="roadmap-container">
      {showEditor && editingPhase ? (
        <RoadmapPhaseEditor
          phase={editingPhase}
          onSave={saveEditedPhase}
          onCancel={cancelEditing}
        />
      ) : (
        <div className="roadmap-wrapper">
          <div className="roadmap-header">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h1 className="roadmap-title">Royaltea MVP Development Tracker</h1>
                {saving && (
                  <span className="roadmap-saving">Saving changes...</span>
                )}
                {isAdmin ? (
                  <div className="mt-2 text-sm text-green-600">
                    Admin mode: Changes are automatically saved to the database
                  </div>
                ) : (
                  <div className="mt-2 text-sm text-red-500">
                    You don't have admin privileges. Changes will not be saved.
                  </div>
                )}
              </div>
              <div className="roadmap-buttons">
                <button
                  onClick={exportData}
                  className="roadmap-button roadmap-button-blue"
                >
                  Export Data
                </button>
                <button
                  onClick={importData}
                  className="roadmap-button roadmap-button-green"
                >
                  Import Data
                </button>
                <button
                  onClick={initializeRoadmap}
                  className="roadmap-button roadmap-button-purple"
                >
                  Initialize Roadmap
                </button>
                <button
                  onClick={resetProgress}
                  className="roadmap-button roadmap-button-red"
                >
                  Reset All
                </button>
              </div>
            </div>

          <div className="roadmap-progress">
            <div className="roadmap-progress-header">
              <h2 className="roadmap-progress-title">Overall Progress</h2>
              <span className="roadmap-progress-percentage">{stats.progressPercentage}%</span>
            </div>
            <div className="roadmap-progress-bar-bg">
              <div
                className="roadmap-progress-bar"
                style={{ width: `${stats.progressPercentage}%` }}
              ></div>
            </div>
            <div className="roadmap-progress-stats">
              {stats.completedTasks} of {stats.totalTasks} tasks completed
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {phases.map((phase) => (
            <div key={phase.id} className="roadmap-phase">
              <div
                className="roadmap-phase-header"
                onClick={() => togglePhaseExpansion(phase.id)}
              >
                <div className="roadmap-phase-title">
                  <span className={`roadmap-phase-title-text ${calculatePhaseProgress(phase) === 100 ? 'completed' : ''}`}>
                    Phase {phase.id}: {phase.title}
                  </span>
                  <span className="roadmap-phase-timeframe">({phase.timeframe})</span>
                </div>
                <div className="roadmap-phase-progress">
                  <div className="roadmap-phase-progress-bar-container">
                    <div className="roadmap-phase-progress-bar-bg">
                      <div
                        className={`roadmap-phase-progress-bar ${calculatePhaseProgress(phase) === 100 ? 'completed' : 'in-progress'}`}
                        style={{ width: `${calculatePhaseProgress(phase)}%` }}
                      ></div>
                    </div>
                    <span className="roadmap-phase-progress-text">{calculatePhaseProgress(phase)}%</span>
                  </div>
                  {isAdmin && (
                    <button
                      className="roadmap-phase-edit-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        startEditingPhase(phase);
                      }}
                      title="Edit Phase"
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                      </svg>
                    </button>
                  )}
                  <svg
                    className={`roadmap-phase-chevron ${phase.expanded ? 'expanded' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              {phase.expanded && (
                <div className="roadmap-phase-content">
                  {phase.sections.map((section) => (
                    <div key={section.id} className="roadmap-section">
                      <div className="roadmap-section-header">
                        <h3 className="roadmap-section-title">{section.id} {section.title}</h3>
                        <div className="roadmap-section-progress">
                          <div className="roadmap-section-progress-bar-bg">
                            <div
                              className={`roadmap-section-progress-bar ${calculateSectionProgress(section) === 100 ? 'completed' : 'in-progress'}`}
                              style={{ width: `${calculateSectionProgress(section)}%` }}
                            ></div>
                          </div>
                          <span className="roadmap-section-progress-text">{calculateSectionProgress(section)}%</span>
                        </div>
                      </div>

                      <ul className="roadmap-tasks">
                        {section.tasks.map((task) => (
                          <li key={task.id} className="roadmap-task">
                            <input
                              type="checkbox"
                              checked={task.completed}
                              onChange={() => toggleTaskCompletion(phase.id, section.id, task.id)}
                              className="roadmap-task-checkbox"
                            />
                            <span className={`roadmap-task-text ${task.completed ? 'completed' : ''}`}>
                              {task.text}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="roadmap-footer">
          <p>Developed for City of Gamers - Royaltea Project</p>
          {isAdmin ? (
            <p>Progress is saved automatically to the database</p>
          ) : (
            <p>You must be an admin to save changes to the database</p>
          )}
        </div>
      </div>
      )}
    </div>
  );
};

// Production-ready roadmap tracker - data comes from database
// Note: Roadmap data should be managed through the admin interface
// and stored in the Supabase database. This component loads data
// from the 'roadmap_phases' table.


export default DirectSupabaseRoadmapTracker;
