import React, { useState } from 'react';
// import { <PERSON>dal, Button, Form, Spinner, Alert, Tabs, Tab } from 'react-bootstrap';
import { addSkillVerification } from '../../utils/skills/skills.utils';

const VerificationForm = ({ show, onHide, userSkill, onSave }) => {
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('self');
  
  const [formData, setFormData] = useState({
    verificationType: 'self_assessment',
    source: '',
    description: '',
    completionDate: '',
    url: '',
    fileUpload: null
  });

  const handleInputChange = (e) => {
    const { name, value, files } = e.target;
    
    if (name === 'fileUpload' && files && files.length > 0) {
      setFormData({ ...formData, fileUpload: files[0] });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    
    // Set the verification type based on the selected tab
    const verificationTypes = {
      self: 'self_assessment',
      learning: 'learning_platform',
      project: 'project_evidence',
      peer: 'peer_endorsement',
      challenge: 'challenge'
    };
    
    setFormData({
      ...formData,
      verificationType: verificationTypes[tab],
      source: '',
      description: '',
      completionDate: '',
      url: '',
      fileUpload: null
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      setError(null);
      
      // Prepare verification data
      const verificationData = {
        source: formData.source,
        data: {
          description: formData.description,
          completion_date: formData.completionDate,
          url: formData.url
        }
      };
      
      // Add the verification
      const result = await addSkillVerification(
        userSkill.id,
        formData.verificationType,
        verificationData
      );
      
      if (onSave) {
        onSave(result);
      }
      
      onHide();
    } catch (error) {
      console.error('Error adding verification:', error);
      setError(error.message || 'Failed to add verification. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} centered backdrop="static" size="lg">
      <Modal.Header closeButton>
        <Modal.Title>Add Skill Verification</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <div className="skill-info mb-4">
          <h4>{userSkill?.skill?.name}</h4>
          {userSkill?.skill?.micro_skill && (
            <p className="text-muted mb-0">{userSkill.skill.micro_skill}</p>
          )}
          <p className="small text-muted mt-2">
            Adding verification evidence helps increase your skill's verification level and credibility.
          </p>
        </div>
        
        <Tabs
          activeKey={activeTab}
          onSelect={handleTabChange}
          className="mb-4 verification-tabs"
        >
          <Tab eventKey="self" title="Self Assessment">
            <Form>
              <p className="verification-tab-description">
                Self-assessment is the basic level of verification. Describe your experience with this skill.
              </p>
              
              <Form.Group className="mb-3">
                <Form.Label>Description of Experience</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Describe your experience with this skill..."
                  required
                />
              </Form.Group>
            </Form>
          </Tab>
          
          <Tab eventKey="learning" title="Learning Platform">
            <Form>
              <p className="verification-tab-description">
                Add verification from learning platforms like LinkedIn Learning, Coursera, Unreal Learning, etc.
              </p>
              
              <Form.Group className="mb-3">
                <Form.Label>Learning Platform</Form.Label>
                <Form.Select
                  name="source"
                  value={formData.source}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select a platform</option>
                  <option value="LinkedIn Learning">LinkedIn Learning</option>
                  <option value="Coursera">Coursera</option>
                  <option value="Unreal Learning">Unreal Learning</option>
                  <option value="Unity Learn">Unity Learn</option>
                  <option value="Udemy">Udemy</option>
                  <option value="edX">edX</option>
                  <option value="Other">Other</option>
                </Form.Select>
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Course/Certificate Name</Form.Label>
                <Form.Control
                  type="text"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter the course or certificate name"
                  required
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Completion Date</Form.Label>
                <Form.Control
                  type="date"
                  name="completionDate"
                  value={formData.completionDate}
                  onChange={handleInputChange}
                  required
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Certificate URL (Optional)</Form.Label>
                <Form.Control
                  type="url"
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  placeholder="https://example.com/certificate"
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Upload Certificate (Optional)</Form.Label>
                <Form.Control
                  type="file"
                  name="fileUpload"
                  onChange={handleInputChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                <Form.Text className="text-muted">
                  Accepted formats: PDF, JPG, PNG (max 5MB)
                </Form.Text>
              </Form.Group>
            </Form>
          </Tab>
          
          <Tab eventKey="project" title="Project Evidence">
            <Form>
              <p className="verification-tab-description">
                Link to projects that demonstrate your skill, either on this platform or external repositories.
              </p>
              
              <Form.Group className="mb-3">
                <Form.Label>Project Type</Form.Label>
                <Form.Select
                  name="source"
                  value={formData.source}
                  onChange={handleInputChange}
                  required
                >
                  <option value="">Select a project type</option>
                  <option value="Royaltea Project">Royaltea Project</option>
                  <option value="GitHub Repository">GitHub Repository</option>
                  <option value="GitLab Repository">GitLab Repository</option>
                  <option value="Portfolio">Portfolio</option>
                  <option value="Other">Other</option>
                </Form.Select>
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Project Name/Description</Form.Label>
                <Form.Control
                  type="text"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter the project name or description"
                  required
                />
              </Form.Group>
              
              <Form.Group className="mb-3">
                <Form.Label>Project URL</Form.Label>
                <Form.Control
                  type="url"
                  name="url"
                  value={formData.url}
                  onChange={handleInputChange}
                  placeholder="https://github.com/username/repository"
                  required
                />
              </Form.Group>
            </Form>
          </Tab>
          
          <Tab eventKey="peer" title="Peer Endorsement">
            <Form>
              <p className="verification-tab-description">
                Request endorsements from peers who can verify your skill level.
              </p>
              
              <div className="alert alert-info">
                <i className="bi bi-info-circle-fill me-2"></i>
                Peer endorsement functionality will be available soon. This will allow you to request skill verification from colleagues and industry professionals.
              </div>
            </Form>
          </Tab>
          
          <Tab eventKey="challenge" title="Skill Challenge">
            <Form>
              <p className="verification-tab-description">
                Complete skill-specific challenges to demonstrate your proficiency.
              </p>
              
              <div className="alert alert-info">
                <i className="bi bi-info-circle-fill me-2"></i>
                Skill challenges will be available soon. These time-constrained assessments will test your practical knowledge and provide objective verification of your skills.
              </div>
            </Form>
          </Tab>
        </Tabs>
        
        {error && (
          <Alert variant="danger" className="mt-3">{error}</Alert>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide} disabled={saving}>
          Cancel
        </Button>
        <Button 
          variant="primary" 
          onClick={handleSubmit} 
          disabled={saving || (activeTab === 'peer' || activeTab === 'challenge')}
        >
          {saving ? (
            <>
              <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-2" />
              Saving...
            </>
          ) : (
            'Add Verification'
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default VerificationForm;
