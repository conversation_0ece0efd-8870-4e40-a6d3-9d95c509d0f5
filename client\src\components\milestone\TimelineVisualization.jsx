import React, { useState, useEffect } from 'react';
import { format, differenceInDays, addDays, isBefore, isAfter, parseISO } from 'date-fns';

const TimelineVisualization = ({ projectId, milestones, projectStartDate, projectEndDate }) => {
  const [timelineData, setTimelineData] = useState([]);
  const [timeScale, setTimeScale] = useState('months'); // 'days', 'weeks', 'months'
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [totalDuration, setTotalDuration] = useState(0);
  const [timelineWidth, setTimelineWidth] = useState(0);
  const [showDependencies, setShowDependencies] = useState(true);

  // Process milestones and project dates
  useEffect(() => {
    if (!milestones || milestones.length === 0) return;

    // Determine start and end dates
    let start = projectStartDate ? new Date(projectStartDate) : null;
    let end = projectEndDate ? new Date(projectEndDate) : null;

    // If project dates are not provided, calculate from milestones
    if (!start || !end) {
      const milestoneDates = milestones
        .filter(m => m.deadline)
        .map(m => new Date(m.deadline));

      if (milestoneDates.length > 0) {
        if (!start) {
          start = new Date(Math.min(...milestoneDates));
          // Set start date to 2 weeks before first milestone
          start = addDays(start, -14);
        }

        if (!end) {
          end = new Date(Math.max(...milestoneDates));
          // Set end date to 2 weeks after last milestone
          end = addDays(end, 14);
        }
      } else {
        // Default dates if no milestones have deadlines
        start = new Date();
        end = addDays(start, 90); // 3 months project by default
      }
    }

    setStartDate(start);
    setEndDate(end);

    // Calculate total duration in days
    const duration = differenceInDays(end, start) + 1;
    setTotalDuration(duration);

    // Set timeline width based on scale
    let width;
    switch (timeScale) {
      case 'days':
        width = duration * 40; // 40px per day
        break;
      case 'weeks':
        width = (duration / 7) * 100; // 100px per week
        break;
      case 'months':
      default:
        width = (duration / 30) * 200; // 200px per month
        break;
    }
    setTimelineWidth(Math.max(width, 800)); // Minimum width of 800px

    // Process milestones for timeline
    const processedMilestones = milestones.map(milestone => {
      const milestoneDate = milestone.deadline ? new Date(milestone.deadline) : null;
      let position = 0;
      
      if (milestoneDate) {
        // Calculate position as percentage of total duration
        const daysPassed = differenceInDays(milestoneDate, start);
        position = (daysPassed / duration) * 100;
      }

      return {
        ...milestone,
        date: milestoneDate,
        position: position,
        isOverdue: milestoneDate && isBefore(milestoneDate, new Date()) && milestone.status !== 'completed',
        dependencies: milestone.dependencies || []
      };
    });

    setTimelineData(processedMilestones);
  }, [milestones, projectStartDate, projectEndDate, timeScale]);

  // Generate time markers based on scale
  const generateTimeMarkers = () => {
    if (!startDate || !endDate) return [];

    const markers = [];
    let current = new Date(startDate);
    const end = new Date(endDate);

    switch (timeScale) {
      case 'days':
        // Generate marker for each day
        while (isBefore(current, end) || current.getDate() === end.getDate()) {
          const daysPassed = differenceInDays(current, startDate);
          const position = (daysPassed / totalDuration) * 100;
          
          markers.push({
            date: new Date(current),
            label: format(current, 'MMM d'),
            position: position
          });
          
          current = addDays(current, 1);
        }
        break;
        
      case 'weeks':
        // Generate marker for each week
        while (isBefore(current, end)) {
          const daysPassed = differenceInDays(current, startDate);
          const position = (daysPassed / totalDuration) * 100;
          
          markers.push({
            date: new Date(current),
            label: format(current, 'MMM d'),
            position: position
          });
          
          current = addDays(current, 7);
        }
        break;
        
      case 'months':
      default:
        // Generate marker for each month
        while (isBefore(current, end) || current.getMonth() === end.getMonth()) {
          const daysPassed = differenceInDays(current, startDate);
          const position = (daysPassed / totalDuration) * 100;
          
          markers.push({
            date: new Date(current),
            label: format(current, 'MMM yyyy'),
            position: position
          });
          
          // Move to first day of next month
          current = new Date(current.getFullYear(), current.getMonth() + 1, 1);
        }
        break;
    }

    return markers;
  };

  // Get status color
  const getStatusColor = (status, isOverdue) => {
    if (isOverdue) return 'var(--danger-color)';
    
    switch (status) {
      case 'completed': return 'var(--success-color)';
      case 'in-progress': return 'var(--primary-color)';
      case 'pending': return 'var(--info-color)';
      case 'blocked': return 'var(--danger-color)';
      default: return 'var(--text-secondary)';
    }
  };

  // Render dependency lines
  const renderDependencyLines = () => {
    if (!showDependencies) return null;
    
    const lines = [];
    const milestoneMap = {};
    
    // Create a map of milestone IDs to their positions
    timelineData.forEach(milestone => {
      milestoneMap[milestone.id] = {
        position: milestone.position,
        status: milestone.status
      };
    });
    
    // Create dependency lines
    timelineData.forEach(milestone => {
      if (milestone.dependencies && milestone.dependencies.length > 0) {
        milestone.dependencies.forEach(depId => {
          if (milestoneMap[depId]) {
            const fromPosition = milestoneMap[depId].position;
            const toPosition = milestone.position;
            const isCompleted = milestoneMap[depId].status === 'completed';
            
            lines.push(
              <line
                key={`${depId}-${milestone.id}`}
                x1={`${fromPosition}%`}
                y1="50%"
                x2={`${toPosition}%`}
                y2="50%"
                className={`dependency-line ${isCompleted ? 'completed' : ''}`}
              />
            );
          }
        });
      }
    });
    
    return (
      <svg className="dependency-lines-container" style={{ width: '100%', height: '100%' }}>
        {lines}
      </svg>
    );
  };

  if (!startDate || !endDate || timelineData.length === 0) {
    return <div className="timeline-visualization-empty">No timeline data available</div>;
  }

  const timeMarkers = generateTimeMarkers();

  return (
    <div className="timeline-visualization-container">
      <div className="timeline-controls">
        <h3>Project Timeline</h3>
        
        <div className="timeline-scale-controls">
          <span>Scale:</span>
          <div className="scale-buttons">
            <button 
              className={`scale-button ${timeScale === 'days' ? 'active' : ''}`}
              onClick={() => setTimeScale('days')}
            >
              Days
            </button>
            <button 
              className={`scale-button ${timeScale === 'weeks' ? 'active' : ''}`}
              onClick={() => setTimeScale('weeks')}
            >
              Weeks
            </button>
            <button 
              className={`scale-button ${timeScale === 'months' ? 'active' : ''}`}
              onClick={() => setTimeScale('months')}
            >
              Months
            </button>
          </div>
          
          <label className="dependency-toggle">
            <input 
              type="checkbox" 
              checked={showDependencies} 
              onChange={() => setShowDependencies(!showDependencies)}
            />
            <span>Show Dependencies</span>
          </label>
        </div>
      </div>
      
      <div className="timeline-scroll-container">
        <div className="timeline-wrapper" style={{ width: `${timelineWidth}px` }}>
          {/* Time markers */}
          <div className="timeline-markers">
            {timeMarkers.map((marker, index) => (
              <div 
                key={index}
                className="time-marker"
                style={{ left: `${marker.position}%` }}
              >
                <div className="marker-line"></div>
                <div className="marker-label">{marker.label}</div>
              </div>
            ))}
          </div>
          
          {/* Timeline track */}
          <div className="timeline-track">
            {/* Project start and end indicators */}
            <div className="project-endpoint start" style={{ left: '0%' }}>
              <div className="endpoint-marker"></div>
              <div className="endpoint-label">Project Start</div>
              <div className="endpoint-date">{format(startDate, 'MMM d, yyyy')}</div>
            </div>
            
            <div className="project-endpoint end" style={{ left: '100%' }}>
              <div className="endpoint-marker"></div>
              <div className="endpoint-label">Project End</div>
              <div className="endpoint-date">{format(endDate, 'MMM d, yyyy')}</div>
            </div>
            
            {/* Today marker */}
            {isAfter(new Date(), startDate) && isBefore(new Date(), endDate) && (
              <div 
                className="today-marker"
                style={{ 
                  left: `${(differenceInDays(new Date(), startDate) / totalDuration) * 100}%` 
                }}
              >
                <div className="today-line"></div>
                <div className="today-label">Today</div>
              </div>
            )}
            
            {/* Dependency lines */}
            {renderDependencyLines()}
            
            {/* Milestone markers */}
            {timelineData.map((milestone, index) => (
              <div 
                key={milestone.id || index}
                className={`milestone-marker ${milestone.status} ${milestone.isOverdue ? 'overdue' : ''}`}
                style={{ 
                  left: `${milestone.position}%`,
                  '--milestone-color': getStatusColor(milestone.status, milestone.isOverdue)
                }}
                title={milestone.name}
              >
                <div className="milestone-dot"></div>
                <div className="milestone-label">
                  <div className="milestone-name">{milestone.name}</div>
                  {milestone.date && (
                    <div className="milestone-date">{format(milestone.date, 'MMM d, yyyy')}</div>
                  )}
                  <div className="milestone-status">{milestone.status}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TimelineVisualization;
