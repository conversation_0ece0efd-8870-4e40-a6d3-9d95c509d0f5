import React, { useState, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Navigate } from 'react-router-dom';
import BugReportForm from '../../components/bug/BugReportForm';
import KnownBugsList from '../../components/bugs/KnownBugsList';
import { Button, Card, CardBody, CardHeader, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { Bug, CheckCircle, Plus, ArrowLeft } from 'lucide-react';

const BugReportPage = () => {
  const { currentUser } = useContext(UserContext);
  const [showReportForm, setShowReportForm] = useState(false);
  const [reportSuccess, setReportSuccess] = useState(false);

  // Handle successful bug report submission
  const handleReportSuccess = () => {
    setShowReportForm(false);
    setReportSuccess(true);

    // Reset success message after 5 seconds
    setTimeout(() => {
      setReportSuccess(false);
    }, 5000);
  };

  // If not logged in, redirect to login
  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-default-100 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-none shadow-lg">
            <CardBody className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                    <Bug size={24} className="text-primary" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                      Bug Reports & Known Issues
                    </h1>
                    <p className="text-default-600 mt-1">Help us improve Royaltea by reporting bugs and checking known issues</p>
                  </div>
                </div>
                {!showReportForm && (
                  <Button
                    color="primary"
                    onClick={() => setShowReportForm(true)}
                    startContent={<Plus size={18} />}
                    className="min-w-32"
                  >
                    Report a Bug
                  </Button>
                )}
                {showReportForm && (
                  <Button
                    variant="bordered"
                    onClick={() => setShowReportForm(false)}
                    startContent={<ArrowLeft size={18} />}
                    className="min-w-32"
                  >
                    Back to List
                  </Button>
                )}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Success Message */}
        {reportSuccess && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-6"
          >
            <Card className="border-success/20 bg-success/10">
              <CardBody className="p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-success/20 flex items-center justify-center">
                    <CheckCircle size={20} className="text-success" />
                  </div>
                  <div>
                    <p className="font-medium text-success">Bug Report Submitted!</p>
                    <p className="text-sm text-success/80">Thank you for your bug report! Our team will review it soon.</p>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          {showReportForm ? (
            <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                    <Bug size={20} className="text-primary" />
                  </div>
                  <h2 className="text-xl font-semibold text-foreground">Report a Bug</h2>
                </div>
              </CardHeader>
              <CardBody>
                <BugReportForm
                  onSuccess={handleReportSuccess}
                  onCancel={() => setShowReportForm(false)}
                />
              </CardBody>
            </Card>
          ) : (
            <KnownBugsList />
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default BugReportPage;
