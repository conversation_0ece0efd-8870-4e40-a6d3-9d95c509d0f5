import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import { But<PERSON>, Card, CardBody, Drawer, DrawerContent, DrawerHeader, DrawerBody } from '@heroui/react';

/**
 * Mobile Navigation Component
 * 
 * Provides enhanced mobile responsiveness for the experimental navigation system.
 * Includes touch gestures, mobile-optimized layouts, and responsive design patterns.
 */

const MobileNavigation = ({ 
  currentUser, 
  currentCanvas, 
  onCanvasChange,
  className = "" 
}) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Navigation items
  const navigationItems = [
    {
      key: 'start',
      title: 'Start',
      icon: '🚀',
      description: 'Create projects',
      route: '/start',
      color: 'primary'
    },
    {
      key: 'track',
      title: 'Track',
      icon: '📊',
      description: 'Log contributions',
      route: '/contributions',
      color: 'secondary'
    },
    {
      key: 'earn',
      title: 'Earn',
      icon: '💰',
      description: 'View earnings',
      route: '/earn',
      color: 'success'
    },
    {
      key: 'learn',
      title: 'Learn',
      icon: '📚',
      description: 'Resources',
      route: '/learn',
      color: 'warning'
    }
  ];

  // Touch gesture handlers
  const handleTouchStart = (e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      // Swipe left - next section
      navigateToNext();
    }
    
    if (isRightSwipe) {
      // Swipe right - previous section or open drawer
      if (touchStart < 50) {
        setIsDrawerOpen(true);
      } else {
        navigateToPrevious();
      }
    }
  };

  // Navigation helpers
  const navigateToNext = () => {
    const currentIndex = navigationItems.findIndex(item => 
      location.pathname.startsWith(item.route)
    );
    
    if (currentIndex < navigationItems.length - 1) {
      navigate(navigationItems[currentIndex + 1].route);
    }
  };

  const navigateToPrevious = () => {
    const currentIndex = navigationItems.findIndex(item => 
      location.pathname.startsWith(item.route)
    );
    
    if (currentIndex > 0) {
      navigate(navigationItems[currentIndex - 1].route);
    }
  };

  // Get current navigation item
  const getCurrentItem = () => {
    return navigationItems.find(item => 
      location.pathname.startsWith(item.route)
    ) || navigationItems[0];
  };

  const currentItem = getCurrentItem();

  if (!isMobile) {
    return null; // Only render on mobile
  }

  return (
    <div className={className}>
      {/* Mobile Header */}
      <div className="fixed top-0 left-0 right-0 z-40 bg-background/80 backdrop-blur-sm border-b border-divider">
        <div className="flex items-center justify-between p-4">
          <Button
            isIconOnly
            variant="light"
            onClick={() => setIsDrawerOpen(true)}
          >
            ☰
          </Button>
          
          <div className="flex items-center gap-2">
            <span className="text-2xl">{currentItem.icon}</span>
            <span className="font-semibold">{currentItem.title}</span>
          </div>
          
          <Button
            isIconOnly
            variant="light"
            onClick={() => navigate('/profile')}
          >
            👤
          </Button>
        </div>
      </div>

      {/* Mobile Content Area with Touch Gestures */}
      <div
        className="pt-16 min-h-screen"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Swipe Indicators */}
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-30">
          <div className="flex items-center gap-2 bg-background/80 backdrop-blur-sm rounded-full px-4 py-2 border border-divider">
            {navigationItems.map((item, index) => (
              <div
                key={item.key}
                className={`w-2 h-2 rounded-full transition-colors ${
                  item.key === currentItem.key ? 'bg-primary' : 'bg-default-300'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Quick Actions Bar */}
        <div className="fixed bottom-20 left-0 right-0 z-30 px-4">
          <Card className="bg-background/80 backdrop-blur-sm">
            <CardBody className="p-3">
              <div className="flex items-center justify-around">
                <Button
                  size="sm"
                  variant="flat"
                  onClick={() => navigate('/track')}
                  className="flex-1 mx-1"
                >
                  ⏱️ Track
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onClick={() => navigate('/project/wizard')}
                  className="flex-1 mx-1"
                >
                  ➕ Create
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onClick={() => navigate('/earn')}
                  className="flex-1 mx-1"
                >
                  💰 Earn
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Navigation Drawer */}
      <Drawer 
        isOpen={isDrawerOpen} 
        onClose={() => setIsDrawerOpen(false)}
        placement="left"
        size="sm"
      >
        <DrawerContent>
          <DrawerHeader>
            <div className="flex items-center gap-2">
              <span className="text-2xl">👑</span>
              <span className="font-bold">Royaltea</span>
            </div>
          </DrawerHeader>
          <DrawerBody>
            <div className="space-y-4">
              {/* Main Navigation */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Main Navigation
                </h3>
                <div className="space-y-2">
                  {navigationItems.map((item) => (
                    <motion.div
                      key={item.key}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        variant={currentItem.key === item.key ? 'solid' : 'light'}
                        color={item.color}
                        onClick={() => {
                          navigate(item.route);
                          setIsDrawerOpen(false);
                        }}
                        className="w-full justify-start"
                      >
                        <span className="text-lg mr-3">{item.icon}</span>
                        <div className="text-left">
                          <div className="font-medium">{item.title}</div>
                          <div className="text-xs opacity-70">{item.description}</div>
                        </div>
                      </Button>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Quick Actions
                </h3>
                <div className="space-y-2">
                  <Button
                    variant="flat"
                    onClick={() => {
                      navigate('/project/wizard');
                      setIsDrawerOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">🚀</span>
                    Create Project
                  </Button>
                  <Button
                    variant="flat"
                    onClick={() => {
                      navigate('/contributions');
                      setIsDrawerOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">⏱️</span>
                    Start Tracking
                  </Button>
                  <Button
                    variant="flat"
                    onClick={() => {
                      navigate('/analytics/contributions');
                      setIsDrawerOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">📊</span>
                    View Analytics
                  </Button>
                </div>
              </div>

              {/* Settings */}
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3">
                  Settings
                </h3>
                <div className="space-y-2">
                  <Button
                    variant="flat"
                    onClick={() => {
                      navigate('/profile');
                      setIsDrawerOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">👤</span>
                    Profile
                  </Button>
                  <Button
                    variant="flat"
                    onClick={() => {
                      navigate('/settings');
                      setIsDrawerOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">⚙️</span>
                    Settings
                  </Button>
                  <Button
                    variant="flat"
                    onClick={() => {
                      navigate('/help');
                      setIsDrawerOpen(false);
                    }}
                    className="w-full justify-start"
                  >
                    <span className="text-lg mr-3">❓</span>
                    Help
                  </Button>
                </div>
              </div>
            </div>
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* Touch Gesture Hints */}
      <AnimatePresence>
        {isMobile && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="fixed top-20 left-4 right-4 z-20 pointer-events-none"
          >
            <Card className="bg-primary/10 border border-primary/20">
              <CardBody className="p-3">
                <div className="text-center text-sm text-primary">
                  <div className="flex items-center justify-center gap-4">
                    <span>← Swipe for menu</span>
                    <span>Swipe to navigate →</span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MobileNavigation;
