import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Textarea, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Badge, Chip, Image, Select, SelectItem, Switch } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Portfolio Management Component
 * 
 * Comprehensive portfolio management interface providing:
 * - Portfolio item creation and editing
 * - Media upload and management
 * - Project categorization and tagging
 * - Portfolio organization and ordering
 * - Visibility and privacy controls
 * - Portfolio analytics and insights
 */
const PortfolioManagement = ({ userId, isOwnProfile = false }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [portfolioItems, setPortfolioItems] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [newItem, setNewItem] = useState({
    title: '',
    description: '',
    category: '',
    technologies: [],
    project_url: '',
    github_url: '',
    image_url: '',
    is_featured: false,
    is_public: true,
    display_order: 0
  });

  // Portfolio categories
  const categories = [
    { key: 'web-development', label: 'Web Development', icon: '🌐' },
    { key: 'mobile-development', label: 'Mobile Development', icon: '📱' },
    { key: 'design', label: 'Design & UI/UX', icon: '🎨' },
    { key: 'data-science', label: 'Data Science', icon: '📊' },
    { key: 'ai-ml', label: 'AI & Machine Learning', icon: '🤖' },
    { key: 'blockchain', label: 'Blockchain', icon: '⛓️' },
    { key: 'game-development', label: 'Game Development', icon: '🎮' },
    { key: 'devops', label: 'DevOps & Infrastructure', icon: '⚙️' },
    { key: 'other', label: 'Other', icon: '📁' }
  ];

  // Load portfolio data
  useEffect(() => {
    if (userId) {
      loadPortfolioData();
    }
  }, [userId]);

  const loadPortfolioData = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('portfolio_items')
        .select('*')
        .eq('user_id', userId)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) throw error;

      setPortfolioItems(data || []);
    } catch (error) {
      console.error('Error loading portfolio data:', error);
      toast.error('Failed to load portfolio data');
    } finally {
      setLoading(false);
    }
  };

  // Add new portfolio item
  const handleAddItem = async () => {
    if (!newItem.title.trim()) {
      toast.error('Please enter a project title');
      return;
    }

    try {
      const itemData = {
        user_id: userId,
        title: newItem.title.trim(),
        description: newItem.description.trim(),
        category: newItem.category || 'other',
        technologies: newItem.technologies,
        project_url: newItem.project_url.trim(),
        github_url: newItem.github_url.trim(),
        image_url: newItem.image_url.trim(),
        is_featured: newItem.is_featured,
        is_public: newItem.is_public,
        display_order: portfolioItems.length,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('portfolio_items')
        .insert(itemData)
        .select()
        .single();

      if (error) throw error;

      setPortfolioItems(prev => [...prev, data]);
      setShowAddModal(false);
      resetNewItem();

      toast.success('Portfolio item added successfully');
    } catch (error) {
      console.error('Error adding portfolio item:', error);
      toast.error('Failed to add portfolio item');
    }
  };

  // Update portfolio item
  const handleUpdateItem = async () => {
    if (!editingItem || !editingItem.title.trim()) {
      toast.error('Please enter a project title');
      return;
    }

    try {
      const { error } = await supabase
        .from('portfolio_items')
        .update({
          title: editingItem.title.trim(),
          description: editingItem.description.trim(),
          category: editingItem.category,
          technologies: editingItem.technologies,
          project_url: editingItem.project_url.trim(),
          github_url: editingItem.github_url.trim(),
          image_url: editingItem.image_url.trim(),
          is_featured: editingItem.is_featured,
          is_public: editingItem.is_public
        })
        .eq('id', editingItem.id);

      if (error) throw error;

      setPortfolioItems(prev => prev.map(item => 
        item.id === editingItem.id ? editingItem : item
      ));

      setEditingItem(null);
      toast.success('Portfolio item updated successfully');
    } catch (error) {
      console.error('Error updating portfolio item:', error);
      toast.error('Failed to update portfolio item');
    }
  };

  // Delete portfolio item
  const handleDeleteItem = async (itemId) => {
    if (!confirm('Are you sure you want to delete this portfolio item?')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('portfolio_items')
        .delete()
        .eq('id', itemId);

      if (error) throw error;

      setPortfolioItems(prev => prev.filter(item => item.id !== itemId));
      toast.success('Portfolio item deleted successfully');
    } catch (error) {
      console.error('Error deleting portfolio item:', error);
      toast.error('Failed to delete portfolio item');
    }
  };

  // Toggle featured status
  const handleToggleFeatured = async (itemId, isFeatured) => {
    try {
      const { error } = await supabase
        .from('portfolio_items')
        .update({ is_featured: isFeatured })
        .eq('id', itemId);

      if (error) throw error;

      setPortfolioItems(prev => prev.map(item => 
        item.id === itemId ? { ...item, is_featured: isFeatured } : item
      ));

      toast.success(`Portfolio item ${isFeatured ? 'featured' : 'unfeatured'}`);
    } catch (error) {
      console.error('Error updating featured status:', error);
      toast.error('Failed to update featured status');
    }
  };

  // Reset new item form
  const resetNewItem = () => {
    setNewItem({
      title: '',
      description: '',
      category: '',
      technologies: [],
      project_url: '',
      github_url: '',
      image_url: '',
      is_featured: false,
      is_public: true,
      display_order: 0
    });
  };

  // Get category info
  const getCategoryInfo = (categoryKey) => {
    return categories.find(cat => cat.key === categoryKey) || categories[categories.length - 1];
  };

  // Format date
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="portfolio-management space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div>
              <h2 className="text-xl font-bold">Portfolio</h2>
              <p className="text-sm text-default-600">
                Showcase your best work and projects
              </p>
            </div>
            
            {isOwnProfile && (
              <Button
                color="primary"
                onPress={() => setShowAddModal(true)}
                startContent={<span>➕</span>}
              >
                Add Project
              </Button>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Portfolio Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-primary mb-1">
                {portfolioItems.length}
              </div>
              <div className="text-sm text-default-600">Total Projects</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-warning mb-1">
                {portfolioItems.filter(item => item.is_featured).length}
              </div>
              <div className="text-sm text-default-600">Featured</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-success mb-1">
                {portfolioItems.filter(item => item.is_public).length}
              </div>
              <div className="text-sm text-default-600">Public</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-secondary mb-1">
                {new Set(portfolioItems.map(item => item.category)).size}
              </div>
              <div className="text-sm text-default-600">Categories</div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Portfolio Items Grid */}
      {portfolioItems.length === 0 ? (
        <Card>
          <CardBody className="text-center py-8">
            <span className="text-4xl mb-4 block">📁</span>
            <h3 className="text-lg font-semibold mb-2">No Portfolio Items Yet</h3>
            <p className="text-default-600 mb-4">
              Start showcasing your work by adding your first project
            </p>
            {isOwnProfile && (
              <Button
                color="primary"
                onPress={() => setShowAddModal(true)}
              >
                Add Your First Project
              </Button>
            )}
          </CardBody>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {portfolioItems.map((item, index) => {
              const categoryInfo = getCategoryInfo(item.category);
              
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card className="h-full">
                    <CardBody className="p-0">
                      {/* Project Image */}
                      {item.image_url && (
                        <div className="relative">
                          <Image
                            src={item.image_url}
                            alt={item.title}
                            className="w-full h-48 object-cover"
                          />
                          {item.is_featured && (
                            <Badge
                              color="warning"
                              variant="flat"
                              className="absolute top-2 right-2"
                            >
                              ⭐ Featured
                            </Badge>
                          )}
                        </div>
                      )}
                      
                      <div className="p-4">
                        {/* Project Header */}
                        <div className="flex items-start justify-between mb-2">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg line-clamp-1">{item.title}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <span className="text-sm">{categoryInfo.icon}</span>
                              <span className="text-sm text-default-600">{categoryInfo.label}</span>
                            </div>
                          </div>
                          
                          {isOwnProfile && (
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="light"
                                onClick={() => setEditingItem(item)}
                              >
                                ✏️
                              </Button>
                              <Button
                                size="sm"
                                color="danger"
                                variant="light"
                                onClick={() => handleDeleteItem(item.id)}
                              >
                                🗑️
                              </Button>
                            </div>
                          )}
                        </div>
                        
                        {/* Project Description */}
                        <p className="text-sm text-default-600 line-clamp-2 mb-3">
                          {item.description}
                        </p>
                        
                        {/* Technologies */}
                        {item.technologies && item.technologies.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {item.technologies.slice(0, 3).map((tech, techIndex) => (
                              <Chip key={techIndex} size="sm" variant="flat">
                                {tech}
                              </Chip>
                            ))}
                            {item.technologies.length > 3 && (
                              <Chip size="sm" variant="flat" color="default">
                                +{item.technologies.length - 3}
                              </Chip>
                            )}
                          </div>
                        )}
                        
                        {/* Project Links */}
                        <div className="flex gap-2 mb-3">
                          {item.project_url && (
                            <Button
                              size="sm"
                              variant="flat"
                              color="primary"
                              as="a"
                              href={item.project_url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              🔗 Live Demo
                            </Button>
                          )}
                          {item.github_url && (
                            <Button
                              size="sm"
                              variant="flat"
                              color="default"
                              as="a"
                              href={item.github_url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              📂 Code
                            </Button>
                          )}
                        </div>
                        
                        {/* Project Footer */}
                        <div className="flex items-center justify-between text-xs text-default-500">
                          <span>Added {formatDate(item.created_at)}</span>
                          {isOwnProfile && (
                            <Switch
                              size="sm"
                              isSelected={item.is_featured}
                              onValueChange={(featured) => handleToggleFeatured(item.id, featured)}
                            >
                              Featured
                            </Switch>
                          )}
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>
      )}

      {/* Add/Edit Portfolio Item Modal */}
      <Modal
        isOpen={showAddModal || editingItem !== null}
        onClose={() => {
          setShowAddModal(false);
          setEditingItem(null);
          resetNewItem();
        }}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">📁</span>
              <span>{editingItem ? 'Edit Project' : 'Add New Project'}</span>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Project Title"
                placeholder="Enter project title"
                value={editingItem ? editingItem.title : newItem.title}
                onChange={(e) => {
                  const value = e.target.value;
                  if (editingItem) {
                    setEditingItem(prev => ({ ...prev, title: value }));
                  } else {
                    setNewItem(prev => ({ ...prev, title: value }));
                  }
                }}
              />
              
              <Textarea
                label="Description"
                placeholder="Describe your project"
                value={editingItem ? editingItem.description : newItem.description}
                onChange={(e) => {
                  const value = e.target.value;
                  if (editingItem) {
                    setEditingItem(prev => ({ ...prev, description: value }));
                  } else {
                    setNewItem(prev => ({ ...prev, description: value }));
                  }
                }}
                minRows={3}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Category"
                  selectedKeys={[editingItem ? editingItem.category : newItem.category]}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0];
                    if (editingItem) {
                      setEditingItem(prev => ({ ...prev, category: value }));
                    } else {
                      setNewItem(prev => ({ ...prev, category: value }));
                    }
                  }}
                >
                  {categories.map(category => (
                    <SelectItem key={category.key} startContent={category.icon}>
                      {category.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Input
                  label="Image URL"
                  placeholder="https://example.com/image.jpg"
                  value={editingItem ? editingItem.image_url : newItem.image_url}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (editingItem) {
                      setEditingItem(prev => ({ ...prev, image_url: value }));
                    } else {
                      setNewItem(prev => ({ ...prev, image_url: value }));
                    }
                  }}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Live Demo URL"
                  placeholder="https://example.com"
                  value={editingItem ? editingItem.project_url : newItem.project_url}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (editingItem) {
                      setEditingItem(prev => ({ ...prev, project_url: value }));
                    } else {
                      setNewItem(prev => ({ ...prev, project_url: value }));
                    }
                  }}
                />
                
                <Input
                  label="GitHub URL"
                  placeholder="https://github.com/username/repo"
                  value={editingItem ? editingItem.github_url : newItem.github_url}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (editingItem) {
                      setEditingItem(prev => ({ ...prev, github_url: value }));
                    } else {
                      setNewItem(prev => ({ ...prev, github_url: value }));
                    }
                  }}
                />
              </div>
              
              <div className="flex gap-4">
                <Switch
                  isSelected={editingItem ? editingItem.is_featured : newItem.is_featured}
                  onValueChange={(value) => {
                    if (editingItem) {
                      setEditingItem(prev => ({ ...prev, is_featured: value }));
                    } else {
                      setNewItem(prev => ({ ...prev, is_featured: value }));
                    }
                  }}
                >
                  Featured Project
                </Switch>
                
                <Switch
                  isSelected={editingItem ? editingItem.is_public : newItem.is_public}
                  onValueChange={(value) => {
                    if (editingItem) {
                      setEditingItem(prev => ({ ...prev, is_public: value }));
                    } else {
                      setNewItem(prev => ({ ...prev, is_public: value }));
                    }
                  }}
                >
                  Public Visibility
                </Switch>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={() => {
                setShowAddModal(false);
                setEditingItem(null);
                resetNewItem();
              }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={editingItem ? handleUpdateItem : handleAddItem}
              isDisabled={editingItem ? !editingItem.title.trim() : !newItem.title.trim()}
            >
              {editingItem ? 'Update Project' : 'Add Project'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default PortfolioManagement;
