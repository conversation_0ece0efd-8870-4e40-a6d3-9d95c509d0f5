// Analytics Service API
// Integration & Services Agent: Comprehensive analytics and reporting system

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Track analytics event
const trackEvent = async (user, eventData) => {
  try {
    const {
      event_type,
      event_category,
      event_data = {},
      value,
      metadata = {},
      session_id
    } = eventData;

    if (!event_type || !event_category) {
      throw new Error('event_type and event_category are required');
    }

    const { data: event, error } = await supabase
      .from('analytics_events')
      .insert({
        user_id: user.id,
        event_type,
        event_category,
        event_data,
        value,
        metadata,
        session_id
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to track event: ${error.message}`);
    }

    return {
      success: true,
      event_id: event.id,
      message: 'Event tracked successfully'
    };

  } catch (error) {
    console.error('Track event error:', error);
    throw error;
  }
};

// Get user dashboard data
const getDashboardData = async (user, queryParams) => {
  try {
    const timePeriod = queryParams.get('period') || 'last_30_days';
    const startDate = getStartDateForPeriod(timePeriod);
    const endDate = new Date().toISOString().split('T')[0];

    // Get performance metrics
    const { data: performanceMetrics, error: perfError } = await supabase
      .from('performance_metrics')
      .select('*')
      .eq('user_id', user.id)
      .gte('period_start', startDate)
      .lte('period_end', endDate)
      .order('calculated_at', { ascending: false });

    if (perfError) {
      console.error('Failed to fetch performance metrics:', perfError);
    }

    // Get financial summary
    const { data: financialSummary, error: finError } = await supabase
      .from('financial_summaries')
      .select('*')
      .eq('user_id', user.id)
      .gte('period_start', startDate)
      .lte('period_end', endDate)
      .order('period_start', { ascending: false })
      .limit(1)
      .single();

    if (finError && finError.code !== 'PGRST116') {
      console.error('Failed to fetch financial summary:', finError);
    }

    // Get recent events
    const { data: recentEvents, error: eventsError } = await supabase
      .from('analytics_events')
      .select('*')
      .eq('user_id', user.id)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })
      .limit(50);

    if (eventsError) {
      console.error('Failed to fetch recent events:', eventsError);
    }

    // Calculate key metrics
    const keyMetrics = calculateKeyMetrics(performanceMetrics, financialSummary, recentEvents);

    return {
      key_metrics: keyMetrics,
      performance_metrics: performanceMetrics || [],
      financial_summary: financialSummary || null,
      recent_events: recentEvents || [],
      period: {
        start_date: startDate,
        end_date: endDate,
        period_type: timePeriod
      }
    };

  } catch (error) {
    console.error('Get dashboard data error:', error);
    throw error;
  }
};

// Get financial analytics
const getFinancialAnalytics = async (user, queryParams) => {
  try {
    const timePeriod = queryParams.get('period') || 'last_6_months';
    const periodType = queryParams.get('period_type') || 'monthly';
    const startDate = getStartDateForPeriod(timePeriod);
    const endDate = new Date().toISOString().split('T')[0];

    // Get financial summaries for the period
    const { data: summaries, error } = await supabase
      .from('financial_summaries')
      .select('*')
      .eq('user_id', user.id)
      .eq('period_type', periodType)
      .gte('period_start', startDate)
      .lte('period_end', endDate)
      .order('period_start', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch financial analytics: ${error.message}`);
    }

    // Calculate trends and insights
    const analytics = calculateFinancialAnalytics(summaries || []);

    return {
      summaries: summaries || [],
      analytics,
      period: {
        start_date: startDate,
        end_date: endDate,
        period_type: periodType
      }
    };

  } catch (error) {
    console.error('Get financial analytics error:', error);
    throw error;
  }
};

// Get project analytics
const getProjectAnalytics = async (user, queryParams) => {
  try {
    const projectId = queryParams.get('project_id');
    const limit = Math.min(parseInt(queryParams.get('limit')) || 10, 50);

    let query = supabase
      .from('project_analytics')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (projectId) {
      query = query.eq('project_id', projectId);
    } else {
      query = query.limit(limit);
    }

    const { data: projects, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch project analytics: ${error.message}`);
    }

    // Calculate project insights
    const insights = calculateProjectInsights(projects || []);

    return {
      projects: projects || [],
      insights,
      total_projects: projects?.length || 0
    };

  } catch (error) {
    console.error('Get project analytics error:', error);
    throw error;
  }
};

// Get performance trends
const getPerformanceTrends = async (user, queryParams) => {
  try {
    const metricType = queryParams.get('metric_type') || 'success_rate';
    const periodType = queryParams.get('period_type') || 'monthly';
    const timePeriod = queryParams.get('period') || 'last_12_months';
    const startDate = getStartDateForPeriod(timePeriod);

    const { data: metrics, error } = await supabase
      .from('performance_metrics')
      .select('*')
      .eq('user_id', user.id)
      .eq('metric_type', metricType)
      .eq('period_type', periodType)
      .gte('period_start', startDate)
      .order('period_start', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch performance trends: ${error.message}`);
    }

    // Calculate trend analysis
    const trendAnalysis = calculateTrendAnalysis(metrics || []);

    return {
      metrics: metrics || [],
      trend_analysis: trendAnalysis,
      metric_type: metricType,
      period_type: periodType
    };

  } catch (error) {
    console.error('Get performance trends error:', error);
    throw error;
  }
};

// Helper functions
const getStartDateForPeriod = (period) => {
  const now = new Date();
  const startDate = new Date(now);

  switch (period) {
    case 'last_7_days':
      startDate.setDate(now.getDate() - 7);
      break;
    case 'last_30_days':
      startDate.setDate(now.getDate() - 30);
      break;
    case 'last_3_months':
      startDate.setMonth(now.getMonth() - 3);
      break;
    case 'last_6_months':
      startDate.setMonth(now.getMonth() - 6);
      break;
    case 'last_12_months':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    case 'this_year':
      startDate.setMonth(0, 1);
      break;
    default:
      startDate.setDate(now.getDate() - 30);
  }

  return startDate.toISOString().split('T')[0];
};

const calculateKeyMetrics = (performanceMetrics, financialSummary, recentEvents) => {
  const metrics = {
    success_rate: 0,
    avg_rating: 0,
    total_revenue: 0,
    revenue_growth: 0,
    project_count: 0,
    active_streak: 0
  };

  // Calculate from performance metrics
  if (performanceMetrics && performanceMetrics.length > 0) {
    const successRate = performanceMetrics.find(m => m.metric_type === 'success_rate');
    const avgRating = performanceMetrics.find(m => m.metric_type === 'avg_rating');
    
    if (successRate) metrics.success_rate = parseFloat(successRate.metric_value);
    if (avgRating) metrics.avg_rating = parseFloat(avgRating.metric_value);
  }

  // Calculate from financial summary
  if (financialSummary) {
    metrics.total_revenue = parseFloat(financialSummary.total_revenue) || 0;
    metrics.revenue_growth = parseFloat(financialSummary.revenue_growth_rate) || 0;
    metrics.project_count = financialSummary.project_count || 0;
  }

  // Calculate activity streak from events
  if (recentEvents && recentEvents.length > 0) {
    metrics.active_streak = calculateActivityStreak(recentEvents);
  }

  return metrics;
};

const calculateFinancialAnalytics = (summaries) => {
  if (!summaries || summaries.length === 0) {
    return {
      total_revenue: 0,
      total_profit: 0,
      avg_monthly_revenue: 0,
      growth_trend: 'stable',
      best_month: null,
      revenue_breakdown: {}
    };
  }

  const totalRevenue = summaries.reduce((sum, s) => sum + (parseFloat(s.total_revenue) || 0), 0);
  const totalProfit = summaries.reduce((sum, s) => sum + (parseFloat(s.net_profit) || 0), 0);
  const avgMonthlyRevenue = totalRevenue / summaries.length;

  // Find best performing month
  const bestMonth = summaries.reduce((best, current) => {
    return (parseFloat(current.total_revenue) || 0) > (parseFloat(best.total_revenue) || 0) ? current : best;
  }, summaries[0]);

  // Calculate growth trend
  const recentRevenue = summaries.slice(-3).reduce((sum, s) => sum + (parseFloat(s.total_revenue) || 0), 0);
  const earlierRevenue = summaries.slice(0, 3).reduce((sum, s) => sum + (parseFloat(s.total_revenue) || 0), 0);
  const growthTrend = recentRevenue > earlierRevenue ? 'growing' : recentRevenue < earlierRevenue ? 'declining' : 'stable';

  return {
    total_revenue: totalRevenue,
    total_profit: totalProfit,
    avg_monthly_revenue: avgMonthlyRevenue,
    growth_trend: growthTrend,
    best_month: bestMonth,
    revenue_breakdown: calculateRevenueBreakdown(summaries)
  };
};

const calculateProjectInsights = (projects) => {
  if (!projects || projects.length === 0) {
    return {
      avg_timeline_efficiency: 0,
      avg_budget_efficiency: 0,
      success_rate: 0,
      avg_rating: 0,
      common_technologies: [],
      improvement_areas: []
    };
  }

  const completedProjects = projects.filter(p => p.project_status === 'completed');
  
  const avgTimelineEfficiency = completedProjects.reduce((sum, p) => sum + (parseFloat(p.timeline_efficiency) || 0), 0) / completedProjects.length;
  const avgBudgetEfficiency = completedProjects.reduce((sum, p) => sum + (parseFloat(p.budget_efficiency) || 0), 0) / completedProjects.length;
  const successRate = (completedProjects.filter(p => p.success_indicator).length / projects.length) * 100;
  const avgRating = completedProjects.reduce((sum, p) => sum + (parseFloat(p.final_rating) || 0), 0) / completedProjects.length;

  // Find common technologies
  const techCounts = {};
  projects.forEach(p => {
    if (p.technology_stack) {
      p.technology_stack.forEach(tech => {
        techCounts[tech] = (techCounts[tech] || 0) + 1;
      });
    }
  });
  const commonTechnologies = Object.entries(techCounts)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([tech, count]) => ({ technology: tech, count }));

  return {
    avg_timeline_efficiency: avgTimelineEfficiency,
    avg_budget_efficiency: avgBudgetEfficiency,
    success_rate: successRate,
    avg_rating: avgRating,
    common_technologies: commonTechnologies,
    improvement_areas: identifyImprovementAreas(projects)
  };
};

const calculateTrendAnalysis = (metrics) => {
  if (!metrics || metrics.length < 2) {
    return {
      trend_direction: 'insufficient_data',
      trend_strength: 0,
      latest_value: 0,
      change_percentage: 0
    };
  }

  const values = metrics.map(m => parseFloat(m.metric_value));
  const latest = values[values.length - 1];
  const previous = values[values.length - 2];
  const changePercentage = previous !== 0 ? ((latest - previous) / previous) * 100 : 0;

  // Simple trend calculation
  const trendDirection = latest > previous ? 'increasing' : latest < previous ? 'decreasing' : 'stable';
  const trendStrength = Math.abs(changePercentage);

  return {
    trend_direction: trendDirection,
    trend_strength: trendStrength,
    latest_value: latest,
    change_percentage: changePercentage,
    data_points: values.length
  };
};

const calculateActivityStreak = (events) => {
  // Simple streak calculation based on daily activity
  const today = new Date();
  let streak = 0;
  
  for (let i = 0; i < 30; i++) {
    const checkDate = new Date(today);
    checkDate.setDate(today.getDate() - i);
    const dateStr = checkDate.toISOString().split('T')[0];
    
    const hasActivity = events.some(event => 
      event.created_at.startsWith(dateStr)
    );
    
    if (hasActivity) {
      streak++;
    } else if (i > 0) {
      break; // Break streak if no activity found
    }
  }
  
  return streak;
};

const calculateRevenueBreakdown = (summaries) => {
  const breakdown = {
    project_revenue: 0,
    commission_revenue: 0,
    bonus_revenue: 0,
    other_revenue: 0
  };

  summaries.forEach(summary => {
    breakdown.project_revenue += parseFloat(summary.project_revenue) || 0;
    breakdown.commission_revenue += parseFloat(summary.commission_revenue) || 0;
    breakdown.bonus_revenue += parseFloat(summary.bonus_revenue) || 0;
    breakdown.other_revenue += parseFloat(summary.other_revenue) || 0;
  });

  return breakdown;
};

const identifyImprovementAreas = (projects) => {
  const areas = [];
  
  const completedProjects = projects.filter(p => p.project_status === 'completed');
  if (completedProjects.length === 0) return areas;

  const avgTimelineEfficiency = completedProjects.reduce((sum, p) => sum + (parseFloat(p.timeline_efficiency) || 0), 0) / completedProjects.length;
  const avgBudgetEfficiency = completedProjects.reduce((sum, p) => sum + (parseFloat(p.budget_efficiency) || 0), 0) / completedProjects.length;
  const avgCommunicationRating = completedProjects.reduce((sum, p) => sum + (parseFloat(p.communication_rating) || 0), 0) / completedProjects.length;

  if (avgTimelineEfficiency < 90) {
    areas.push({
      area: 'Timeline Management',
      current_score: avgTimelineEfficiency,
      suggestion: 'Focus on better project planning and milestone tracking'
    });
  }

  if (avgBudgetEfficiency < 95) {
    areas.push({
      area: 'Budget Management',
      current_score: avgBudgetEfficiency,
      suggestion: 'Improve cost estimation and expense tracking'
    });
  }

  if (avgCommunicationRating < 4.5) {
    areas.push({
      area: 'Communication',
      current_score: avgCommunicationRating,
      suggestion: 'Enhance client communication and regular updates'
    });
  }

  return areas;
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'track':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await trackEvent(user, body);
        break;

      case 'dashboard':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getDashboardData(user, queryParams);
        break;

      case 'financial':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getFinancialAnalytics(user, queryParams);
        break;

      case 'projects':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getProjectAnalytics(user, queryParams);
        break;

      case 'trends':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await getPerformanceTrends(user, queryParams);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Analytics Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
