import React, { useContext, useState, useRef, useEffect } from 'react';
import { ThemeContext } from '../../contexts/theme.context';

// Import sound effects
const cricketSoundUrl = '/sounds/cricket-night.mp3';
const roosterSoundUrl = '/sounds/rooster-morning.mp3';

const ThemeToggle = ({ className = '' }) => {
  const { theme, toggleTheme, soundEnabled } = useContext(ThemeContext);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationType, setAnimationType] = useState(null);
  const buttonRef = useRef(null);
  const audioRef = useRef(null);

  // Create audio elements for sound effects
  useEffect(() => {
    audioRef.current = new Audio();
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const handleToggle = () => {
    // Determine which animation and sound to play based on current theme
    const newAnimationType = theme === 'light' ? 'to-dark' : 'to-light';
    setAnimationType(newAnimationType);
    setIsAnimating(true);

    // Play the appropriate sound effect if sound is enabled
    if (audioRef.current && soundEnabled) {
      audioRef.current.src = theme === 'light' ? cricketSoundUrl : roosterSoundUrl;
      audioRef.current.volume = 0.3; // Set volume to 30%

      // Play sound with fade out
      audioRef.current.play().then(() => {
        // Set the duration to half the original length
        const fadeStartTime = audioRef.current.duration / 4; // Start fading at 1/4 of the original duration
        const fadeEndTime = audioRef.current.duration / 2; // End at 1/2 of the original duration

        // Set up a timer to start fading out
        const fadeTimer = setTimeout(() => {
          // Start fade out
          const fadeInterval = setInterval(() => {
            if (audioRef.current) {
              if (audioRef.current.volume > 0.02) {
                audioRef.current.volume -= 0.02; // Gradually reduce volume
              } else {
                audioRef.current.pause(); // Stop when volume is very low
                clearInterval(fadeInterval);
              }
            } else {
              clearInterval(fadeInterval);
            }
          }, 50);

          // Set a timeout to ensure the audio stops after the fade end time
          setTimeout(() => {
            if (audioRef.current) {
              audioRef.current.pause();
            }
            clearInterval(fadeInterval);
          }, (fadeEndTime - fadeStartTime) * 1000);

        }, fadeStartTime * 1000);

        // Clean up timer if audio ends early
        audioRef.current.onended = () => {
          clearTimeout(fadeTimer);
        };
      }).catch(e => console.error('Error playing sound:', e));
    }

    // Create animation elements
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      if (theme === 'light') {
        // Create falling stars animation for dark mode
        createFallingStars(centerX, centerY);
      } else {
        // Create sunrise animation for light mode
        createSunriseAnimation(centerX, centerY);
      }
    }

    // Toggle the theme after a short delay to allow animation to start
    setTimeout(() => {
      toggleTheme();
    }, 100);

    // Reset animation state after animation completes
    setTimeout(() => {
      setIsAnimating(false);
    }, 2000);
  };

  // Function to create falling stars animation
  const createFallingStars = (x, y) => {
    const container = document.createElement('div');
    container.className = 'stars-container';
    container.style.position = 'fixed';
    container.style.left = '0';
    container.style.top = '0';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.pointerEvents = 'none';
    container.style.zIndex = '9999';
    document.body.appendChild(container);

    // Create multiple stars
    for (let i = 0; i < 12; i++) {
      const star = document.createElement('div');
      star.className = 'falling-star';

      // Random position around the button
      const angle = Math.random() * Math.PI * 2;
      const distance = 20 + Math.random() * 30;
      const startX = x + Math.cos(angle) * distance;
      const startY = y + Math.sin(angle) * distance;

      // Random size
      const size = 3 + Math.random() * 7;

      // Set star styles
      star.style.width = `${size}px`;
      star.style.height = `${size}px`;
      star.style.left = `${startX}px`;
      star.style.top = `${startY}px`;
      star.style.opacity = '0';
      star.style.backgroundColor = '#fff';
      star.style.borderRadius = '50%';
      star.style.position = 'absolute';
      star.style.boxShadow = '0 0 10px 2px rgba(255, 255, 255, 0.8)';
      star.style.transition = 'all 1.5s cubic-bezier(0.05, 0.5, 0.5, 1)';

      // Add to container
      container.appendChild(star);

      // Animate with a slight delay for each star
      setTimeout(() => {
        star.style.opacity = '1';
        star.style.transform = `translate(${Math.random() * 100 - 50}px, ${100 + Math.random() * 100}px) scale(0.1)`;
      }, i * 50);
    }

    // Remove the container after animation completes
    setTimeout(() => {
      document.body.removeChild(container);
    }, 2000);
  };

  // Function to create sunrise animation with birds
  const createSunriseAnimation = (x, y) => {
    const container = document.createElement('div');
    container.className = 'sunrise-container';
    container.style.position = 'fixed';
    container.style.left = '0';
    container.style.top = '0';
    container.style.width = '100%';
    container.style.height = '100%';
    container.style.pointerEvents = 'none';
    container.style.zIndex = '9999';
    document.body.appendChild(container);

    // Create sun element
    const sun = document.createElement('div');
    sun.className = 'sunrise-sun';
    sun.style.position = 'absolute';
    sun.style.width = '60px';
    sun.style.height = '60px';
    sun.style.borderRadius = '50%';
    sun.style.background = 'radial-gradient(circle, #ffdb57 30%, #ff9d3f 70%)';
    sun.style.boxShadow = '0 0 40px 20px rgba(255, 180, 0, 0.3)';
    sun.style.left = `${x - 30}px`;
    sun.style.top = `${y + 50}px`;
    sun.style.opacity = '0';
    sun.style.transform = 'scale(0.2)';
    sun.style.transition = 'all 1.5s cubic-bezier(0.2, 0.8, 0.2, 1)';
    container.appendChild(sun);

    // Create rays
    for (let i = 0; i < 8; i++) {
      const ray = document.createElement('div');
      ray.className = 'sunrise-ray';
      ray.style.position = 'absolute';
      ray.style.width = '2px';
      ray.style.height = '20px';
      ray.style.backgroundColor = '#ffdb57';
      ray.style.left = `${x}px`;
      ray.style.top = `${y}px`;
      ray.style.transformOrigin = '0 0';
      ray.style.transform = `rotate(${i * 45}deg) translate(30px, 0)`;
      ray.style.opacity = '0';
      ray.style.transition = 'all 1.5s cubic-bezier(0.2, 0.8, 0.2, 1)';
      container.appendChild(ray);

      setTimeout(() => {
        ray.style.opacity = '1';
        ray.style.height = '40px';
      }, 300 + i * 50);
    }

    // Create flying birds
    const createBird = (delay) => {
      // Bird container
      const bird = document.createElement('div');
      bird.className = 'flying-bird';
      bird.style.position = 'absolute';

      // Random position around the sun
      const startX = x - 100 - Math.random() * 100;
      const startY = y - 20 - Math.random() * 60;

      // Random size
      const size = 8 + Math.random() * 6;

      // Set bird styles
      bird.style.left = `${startX}px`;
      bird.style.top = `${startY}px`;
      bird.style.fontSize = `${size}px`;
      bird.style.opacity = '0';
      bird.style.transition = 'all 2s cubic-bezier(0.2, 0.8, 0.2, 1)';
      bird.style.transform = 'scale(0.5)';
      bird.innerHTML = '🐦'; // Bird emoji

      container.appendChild(bird);

      // Animate bird
      setTimeout(() => {
        bird.style.opacity = '0.8';
        bird.style.transform = 'scale(1)';
        bird.style.left = `${startX + 300 + Math.random() * 200}px`;
        bird.style.top = `${startY - 50 - Math.random() * 100}px`;
      }, delay);
    };

    // Create multiple birds with different delays
    for (let i = 0; i < 5; i++) {
      createBird(500 + i * 200);
    }

    // Animate sun rising
    setTimeout(() => {
      sun.style.opacity = '1';
      sun.style.transform = 'translateY(-80px) scale(1)';
    }, 100);

    // Remove the container after animation completes
    setTimeout(() => {
      document.body.removeChild(container);
    }, 2000);
  };

  return (
    <button
      ref={buttonRef}
      className={`flex items-center justify-center p-2 rounded-full transition-all duration-200 hover:bg-muted focus:outline-none focus:ring-2 focus:ring-ring text-foreground ${className} ${isAnimating ? `animate-pulse` : ''}`}
      onClick={handleToggle}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {theme === 'light' ? (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-moon-stars" viewBox="0 0 16 16">
          <path d="M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278z"/>
          <path d="M10.794 3.148a.217.217 0 0 1 .412 0l.387 1.162c.173.518.579.924 1.097 1.097l1.162.387a.217.217 0 0 1 0 .412l-1.162.387a1.734 1.734 0 0 0-1.097 1.097l-.387 1.162a.217.217 0 0 1-.412 0l-.387-1.162A1.734 1.734 0 0 0 9.31 6.593l-1.162-.387a.217.217 0 0 1 0-.412l1.162-.387a1.734 1.734 0 0 0 1.097-1.097l.387-1.162zM13.863.099a.145.145 0 0 1 .274 0l.258.774c.115.346.386.617.732.732l.774.258a.145.145 0 0 1 0 .274l-.774.258a1.156 1.156 0 0 0-.732.732l-.258.774a.145.145 0 0 1-.274 0l-.258-.774a1.156 1.156 0 0 0-.732-.732l-.774-.258a.145.145 0 0 1 0-.274l.774-.258c.346-.115.617-.386.732-.732L13.863.1z"/>
        </svg>
      ) : (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" className="bi bi-sun" viewBox="0 0 16 16">
          <path d="M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z"/>
        </svg>
      )}
    </button>
  );
};

export default ThemeToggle;
