import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { format } from 'date-fns';

/**
 * Generate a PDF from an HTML element
 * @param {HTMLElement} element - The HTML element to convert to PDF
 * @param {string} filename - The filename for the PDF
 * @param {Object} options - Additional options for PDF generation
 * @returns {Promise<Blob>} - A promise that resolves with the PDF blob
 */
export const generatePDFFromElement = async (element, filename, options = {}) => {
  if (!element) {
    throw new Error('Element is required for PDF generation');
  }

  const defaultOptions = {
    scale: 2, // Higher scale for better quality
    useCORS: true, // Enable CORS for images
    logging: false,
    letterRendering: true,
    allowTaint: false,
    backgroundColor: '#ffffff'
  };

  const pdfOptions = {
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
    ...options
  };

  try {
    // Create a PDF directly from HTML content instead of using canvas
    const pdf = new jsPDF(pdfOptions);

    // Get the width and height of the PDF page
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Return the PDF as a blob
    return new Promise((resolve) => {
      // Add HTML content directly to the PDF
      // This is a simpler approach that avoids image format issues
      pdf.html(element, {
        callback: function(pdf) {
          // Do NOT automatically save the PDF - this was causing unwanted downloads
          // The caller should handle saving if needed
          const blob = pdf.output('blob');
          resolve(blob);
        },
        x: 10,
        y: 10,
        width: pdfWidth - 20,
        windowWidth: 800
      });
    });
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

/**
 * Generate a PDF from agreement text
 * @param {string} agreementText - The markdown text of the agreement
 * @param {Object} metadata - Metadata for the agreement (project name, date, etc.)
 * @param {Object} options - Additional options for PDF generation
 * @returns {Promise<Blob>} - A promise that resolves with the PDF blob
 */
export const generateAgreementPDF = async (agreementText, metadata = {}, options = {}) => {
  if (!agreementText) {
    throw new Error('Agreement text is required for PDF generation');
  }

  // Create a temporary container for the agreement
  const container = document.createElement('div');
  container.className = 'agreement-pdf-container';
  container.style.width = '210mm'; // A4 width
  container.style.padding = '20mm';
  container.style.boxSizing = 'border-box';
  container.style.fontFamily = 'Arial, sans-serif';
  container.style.fontSize = '12pt';
  container.style.lineHeight = '1.5';
  container.style.color = '#000';
  container.style.backgroundColor = '#fff';

  // Add styles for exhibits and other special sections
  const style = document.createElement('style');
  style.textContent = `
    .agreement-pdf-container h1 { font-size: 18pt; margin-top: 20px; margin-bottom: 10px; }
    .agreement-pdf-container h2 { font-size: 16pt; margin-top: 16px; margin-bottom: 8px; }
    .agreement-pdf-container h3 { font-size: 14pt; margin-top: 14px; margin-bottom: 7px; }
    .agreement-pdf-container .exhibit-section { margin-left: 10px; margin-bottom: 20px; }
    .agreement-pdf-container .exhibit-section strong { display: block; margin-top: 10px; }
    .agreement-pdf-container .sub-list { margin-left: 20px; list-style-type: lower-alpha; }
    .agreement-pdf-container ol { margin-bottom: 10px; }
    .agreement-pdf-container ul { margin-bottom: 10px; }
    .agreement-pdf-container li { margin-bottom: 5px; }
  `;
  container.appendChild(style);

  // Add header with metadata
  const header = document.createElement('div');
  header.style.marginBottom = '20mm';
  header.style.textAlign = 'center';

  const title = document.createElement('h1');
  title.textContent = metadata.title || 'Contributor Agreement';
  title.style.fontSize = '18pt';
  title.style.marginBottom = '5mm';

  const projectName = document.createElement('h2');
  projectName.textContent = metadata.projectName || '';
  projectName.style.fontSize = '16pt';
  projectName.style.marginBottom = '5mm';

  const dateElement = document.createElement('p');
  dateElement.textContent = `Date: ${metadata.date || format(new Date(), 'MMMM d, yyyy')}`;
  dateElement.style.fontSize = '12pt';

  header.appendChild(title);
  header.appendChild(projectName);
  header.appendChild(dateElement);

  // Add agreement content
  const content = document.createElement('div');
  content.innerHTML = typeof agreementText === 'string' ?
    agreementText.replace(/\\n/g, '<br>').replace(/\\t/g, '&nbsp;&nbsp;&nbsp;&nbsp;') :
    'Agreement text not available';

  // Add signature section if provided
  if (metadata.signature) {
    const signatureSection = document.createElement('div');
    signatureSection.style.marginTop = '30mm';
    signatureSection.style.borderTop = '1px solid #000';
    signatureSection.style.paddingTop = '10mm';

    const signatureTitle = document.createElement('h3');
    signatureTitle.textContent = 'Signature';
    signatureTitle.style.fontSize = '14pt';
    signatureTitle.style.marginBottom = '10mm';

    const signatureInfo = document.createElement('div');
    signatureInfo.style.display = 'flex';
    signatureInfo.style.justifyContent = 'space-between';

    const signatureLeft = document.createElement('div');
    signatureLeft.style.width = '45%';

    const signatureName = document.createElement('p');
    signatureName.textContent = `Name: ${metadata.signature.name || ''}`;
    signatureName.style.marginBottom = '5mm';

    const signatureDate = document.createElement('p');
    signatureDate.textContent = `Date: ${metadata.signature.date || ''}`;

    signatureLeft.appendChild(signatureName);
    signatureLeft.appendChild(signatureDate);

    const signatureRight = document.createElement('div');
    signatureRight.style.width = '45%';

    const signatureImage = document.createElement('div');
    signatureImage.style.borderBottom = '1px solid #000';
    signatureImage.style.height = '20mm';
    signatureImage.style.marginBottom = '5mm';

    if (metadata.signature.image) {
      const img = document.createElement('img');
      img.src = metadata.signature.image;
      img.style.maxHeight = '20mm';
      img.style.maxWidth = '100%';
      signatureImage.appendChild(img);
    }

    const signatureLabel = document.createElement('p');
    signatureLabel.textContent = 'Signature';
    signatureLabel.style.textAlign = 'center';

    signatureRight.appendChild(signatureImage);
    signatureRight.appendChild(signatureLabel);

    signatureInfo.appendChild(signatureLeft);
    signatureInfo.appendChild(signatureRight);

    signatureSection.appendChild(signatureTitle);
    signatureSection.appendChild(signatureInfo);

    content.appendChild(signatureSection);
  }

  // Add footer with page number
  const footer = document.createElement('div');
  footer.style.marginTop = '20mm';
  footer.style.textAlign = 'center';
  footer.style.fontSize = '10pt';
  footer.style.color = '#666';
  footer.textContent = 'Page 1 of 1';

  // Assemble the document
  container.appendChild(header);
  container.appendChild(content);
  container.appendChild(footer);

  // Append to document temporarily (needed for html2canvas)
  document.body.appendChild(container);

  try {
    // Create a promise to handle the PDF generation
    return new Promise((resolve, reject) => {
      try {
        // Create a PDF directly
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4',
          ...options
        });

        // Get the width and height of the PDF page
        const pdfWidth = pdf.internal.pageSize.getWidth();

        // Add HTML content directly to the PDF
        pdf.html(container, {
          callback: function(pdf) {
            // Clean up
            document.body.removeChild(container);

            // Do NOT automatically save the PDF - this was causing unwanted downloads
            // Only return the blob for controlled download via the download button

            // Return the PDF as a blob
            const blob = pdf.output('blob');
            resolve(blob);
          },
          x: 10,
          y: 10,
          width: pdfWidth - 20,
          windowWidth: 800
        });
      } catch (error) {
        // Clean up on error
        if (document.body.contains(container)) {
          document.body.removeChild(container);
        }
        reject(error);
      }
    });
  } catch (error) {
    // Clean up on error
    if (document.body.contains(container)) {
      document.body.removeChild(container);
    }
    throw error;
  }
};

/**
 * Save a PDF blob to the user's device
 * @param {Blob} pdfBlob - The PDF blob to save
 * @param {string} filename - The filename for the PDF
 */
export const savePDFToDevice = (pdfBlob, filename = 'document.pdf') => {
  // This function is only called when the user explicitly clicks the download button
  const url = URL.createObjectURL(pdfBlob);
  const link = document.createElement('a');
  link.href = url;

  // Sanitize filename to ensure it's valid
  // Remove any @ symbols at the beginning and replace invalid characters with underscores
  const sanitizedFilename = filename
    .replace(/^@+/, '')
    .replace(/[<>:"/\\|?*]/g, '_');

  link.download = sanitizedFilename;
  link.click();

  // Clean up
  setTimeout(() => {
    URL.revokeObjectURL(url);
  }, 100);
};

/**
 * Convert markdown to HTML for PDF generation
 * @param {string} markdown - The markdown text to convert
 * @returns {string} - The HTML representation of the markdown
 */
export const markdownToHTML = (markdown) => {
  if (!markdown) return '';

  // Pre-process the markdown to handle special sections like exhibits
  let processedMarkdown = markdown;

  // Process exhibits and schedules sections
  const exhibitRegex = /## (EXHIBIT [IV]+|SCHEDULE [A-Z])([\s\S]*?)(?=## |$)/g;
  processedMarkdown = processedMarkdown.replace(exhibitRegex, (match, title, content) => {
    // Add special class for exhibit sections
    return `## ${title}\n<div class="exhibit-section">${content}</div>\n`;
  });

  // Process numbered lists with nested items (common in legal documents)
  // This handles formats like "1. Main item" followed by "   a. Sub-item"
  const nestedListRegex = /^(\s*\d+\.\s+.*$)(\n\s+[a-z]\.\s+.*$)+/gm;
  processedMarkdown = processedMarkdown.replace(nestedListRegex, (match) => {
    // Split the match into lines
    const lines = match.split('\n');
    const mainItem = lines[0].trim();
    const subItems = lines.slice(1).map(line => line.trim());

    // Format as a nested list
    let result = `${mainItem}\n<ul class="sub-list">`;
    subItems.forEach(item => {
      result += `\n<li>${item}</li>`;
    });
    result += '\n</ul>';

    return result;
  });

  // Simple markdown to HTML conversion
  return processedMarkdown
    // Headers
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Italic
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    // Lists - improved to handle nested lists better
    .replace(/^\s*\* (.*$)/gm, '<ul><li>$1</li></ul>')
    .replace(/^\s*\d+\. (.*$)/gm, '<ol><li>$1</li></ol>')
    // Links
    .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>')
    // Special handling for exhibit sections
    .replace(/<div class="exhibit-section">([\s\S]*?)<\/div>/g, (match, content) => {
      // Process the content within exhibit sections
      return `<div class="exhibit-section">${content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/^\s*\* (.*$)/gm, '<ul><li>$1</li></ul>')
        .replace(/^\s*\d+\. (.*$)/gm, '<ol><li>$1</li></ol>')
      }</div>`;
    })
    // Paragraphs - but don't wrap content that's already in HTML tags
    .replace(/^\s*(\n)?([^\n<]+)(\n)?$/gm, function(m) {
      return /^<(\/)?(h\d|ul|ol|li|blockquote|pre|img|div)/.test(m) ? m : '<p>'+m+'</p>';
    })
    // Fix duplicate tags
    .replace(/<\/ul>\s*<ul>/g, '')
    .replace(/<\/ol>\s*<ol>/g, '')
    // Line breaks - but preserve line breaks in exhibit sections
    .replace(/\n(?!<\/div>)/g, '<br>');
};

export default {
  generatePDFFromElement,
  generateAgreementPDF,
  savePDFToDevice,
  markdownToHTML
};
