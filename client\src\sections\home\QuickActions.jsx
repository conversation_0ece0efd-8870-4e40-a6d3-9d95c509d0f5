import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardBody, CardHeader, Button } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Quick Actions Section
 * 
 * Provides quick access to common actions and workflows.
 * Part of the Home canvas in the experimental navigation system.
 */
const QuickActions = () => {
  const navigate = useNavigate();

  // Quick action items
  const quickActions = [
    {
      title: 'Start New Project',
      description: 'Create a new project and set up your team',
      icon: '🚀',
      color: 'from-blue-500 to-purple-600',
      action: () => navigate('/project/wizard'),
      category: 'Create'
    },
    {
      title: 'Track Contribution',
      description: 'Log your work and track time spent',
      icon: '⏱️',
      color: 'from-green-500 to-emerald-600',
      action: () => navigate('/track'),
      category: 'Track'
    },
    {
      title: 'View Analytics',
      description: 'See insights and performance metrics',
      icon: '📊',
      color: 'from-orange-500 to-red-600',
      action: () => navigate('/analytics/contributions'),
      category: 'Analyze'
    },
    {
      title: 'Manage Revenue',
      description: 'Track earnings and royalty distributions',
      icon: '💰',
      color: 'from-yellow-500 to-orange-500',
      action: () => navigate('/earn'),
      category: 'Earn'
    },
    {
      title: 'Browse Projects',
      description: 'Explore and join existing projects',
      icon: '📁',
      color: 'from-cyan-500 to-blue-600',
      action: () => navigate('/projects'),
      category: 'Explore'
    },
    {
      title: 'Learning Center',
      description: 'Tutorials and best practices',
      icon: '🎓',
      color: 'from-indigo-500 to-purple-600',
      action: () => navigate('/learn'),
      category: 'Learn'
    }
  ];

  // Group actions by category
  const groupedActions = quickActions.reduce((groups, action) => {
    const category = action.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(action);
    return groups;
  }, {});

  return (
    <div className="p-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500 flex items-center justify-center">
                <span className="text-xl">⚡</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Quick Actions</h2>
                <p className="text-white/60 text-sm">Jump into your most common workflows</p>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            <div className="space-y-6">
              {Object.entries(groupedActions).map(([category, actions], categoryIndex) => (
                <motion.div
                  key={category}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 * categoryIndex }}
                >
                  <h3 className="text-white/80 text-sm font-medium mb-3 uppercase tracking-wider">
                    {category}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {actions.map((action, actionIndex) => (
                      <motion.div
                        key={action.title}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ 
                          duration: 0.3, 
                          delay: 0.1 * categoryIndex + 0.05 * actionIndex 
                        }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Card 
                          className="bg-white/5 hover:bg-white/10 border-white/10 hover:border-white/20 transition-all duration-300 cursor-pointer group"
                          onClick={action.action}
                        >
                          <CardBody className="p-4">
                            <div className="flex items-start gap-4">
                              {/* Action Icon */}
                              <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${action.color} flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300`}>
                                <span className="text-xl">{action.icon}</span>
                              </div>

                              {/* Action Details */}
                              <div className="flex-1 min-w-0">
                                <h4 className="text-white font-medium mb-1 group-hover:text-white/90 transition-colors">
                                  {action.title}
                                </h4>
                                <p className="text-white/60 text-sm leading-relaxed">
                                  {action.description}
                                </p>
                              </div>

                              {/* Arrow Indicator */}
                              <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <svg 
                                  className="w-5 h-5 text-white/60" 
                                  fill="none" 
                                  stroke="currentColor" 
                                  viewBox="0 0 24 24"
                                >
                                  <path 
                                    strokeLinecap="round" 
                                    strokeLinejoin="round" 
                                    strokeWidth={2} 
                                    d="M9 5l7 7-7 7" 
                                  />
                                </svg>
                              </div>
                            </div>
                          </CardBody>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Additional Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="mt-8 pt-6 border-t border-white/10"
            >
              <h3 className="text-white/80 text-sm font-medium mb-4 uppercase tracking-wider">
                Quick Links
              </h3>
              <div className="flex flex-wrap gap-2">
                <Button
                  size="sm"
                  variant="flat"
                  className="bg-white/10 text-white hover:bg-white/20"
                  onClick={() => navigate('/settings')}
                >
                  Settings
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  className="bg-white/10 text-white hover:bg-white/20"
                  onClick={() => navigate('/profile')}
                >
                  Profile
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  className="bg-white/10 text-white hover:bg-white/20"
                  onClick={() => navigate('/notifications')}
                >
                  Notifications
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  className="bg-white/10 text-white hover:bg-white/20"
                  onClick={() => navigate('/bugs')}
                >
                  Report Bug
                </Button>
              </div>
            </motion.div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default QuickActions;
