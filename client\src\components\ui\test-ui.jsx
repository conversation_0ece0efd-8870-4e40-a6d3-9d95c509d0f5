import React from 'react';
import { <PERSON><PERSON>, <PERSON>, CardB<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, Input } from './heroui';

/**
 * TestUI Component
 *
 * A component to test shadcn/ui styling
 *
 * @returns {React.ReactElement} TestUI component
 */
const TestUI = () => {
  return (
    <div className="p-4" style={{ maxWidth: '1200px', margin: '0 auto' }}>
      <h1 className="text-2xl font-bold mb-4">UI Component Test</h1>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem', marginBottom: '2rem' }}>
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Card Title</h3>
            <p className="text-sm text-muted-foreground">Card Description</p>
          </CardHeader>
          <CardBody>
            <p>This is a test card to verify HeroUI styling.</p>
          </CardBody>
          <CardFooter>
            <Button>Primary Button</Button>
            <Button variant="bordered" style={{ marginLeft: '0.5rem' }}>Outline Button</Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Form Elements</h3>
            <p className="text-sm text-muted-foreground">Test form controls</p>
          </CardHeader>
          <CardBody>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.25rem' }}>Name</label>
                <Input placeholder="Enter your name" />
              </div>
              <div>
                <label style={{ display: 'block', fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.25rem' }}>Email</label>
                <Input type="email" placeholder="Enter your email" />
              </div>
            </div>
          </CardBody>
          <CardFooter>
            <Button variant="flat">Cancel</Button>
            <Button style={{ marginLeft: '0.5rem' }}>Submit</Button>
          </CardFooter>
        </Card>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem' }}>Button Variants</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
          <Button variant="solid">Solid</Button>
          <Button variant="faded">Faded</Button>
          <Button variant="bordered">Bordered</Button>
          <Button variant="light">Light</Button>
          <Button variant="flat">Flat</Button>
          <Button variant="ghost">Ghost</Button>
        </div>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem' }}>Button Sizes</h2>
        <div style={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: '0.5rem' }}>
          <Button size="md">Medium</Button>
          <Button size="sm">Small</Button>
          <Button size="lg">Large</Button>
          <Button isIconOnly>
            <span>+</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TestUI;
