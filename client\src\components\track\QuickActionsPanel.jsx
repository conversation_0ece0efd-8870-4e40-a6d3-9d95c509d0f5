import React from 'react';
import { Card, CardBody, Button, Toolt<PERSON> } from '@heroui/react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  UserPlus, 
  Target, 
  BarChart3
} from 'lucide-react';

/**
 * Quick Actions Panel Component
 * 
 * 4-button grid for common actions:
 * - Create Task
 * - Invite Collaborator
 * - Check Mission Board (links to Earn section)
 * - Project Analytics
 */
const QuickActionsPanel = ({
  onCreateTask,
  onInviteCollaborator,
  onProjectAnalytics,
  currentProject,
  className = ""
}) => {
  const navigate = useNavigate();

  const handleMissionBoard = () => {
    navigate('/earn');
  };

  const actions = [
    {
      id: 'create-task',
      title: 'Create Task',
      description: currentProject ? `Add a new task to ${currentProject.name}` : 'Add a new task to your project',
      icon: Plus,
      onClick: onCreateTask,
      color: 'success',
      bgGradient: 'from-green-500/20 to-emerald-500/20',
      hoverGradient: 'hover:from-green-500/30 hover:to-emerald-500/30'
    },
    {
      id: 'invite-collaborator',
      title: 'Invite Collaborator',
      description: currentProject ? `Add team members to ${currentProject.name}` : 'Add team members to your project',
      icon: UserPlus,
      onClick: onInviteCollaborator,
      color: 'primary',
      bgGradient: 'from-blue-500/20 to-cyan-500/20',
      hoverGradient: 'hover:from-blue-500/30 hover:to-cyan-500/30'
    },
    {
      id: 'mission-board',
      title: 'Check Mission Board',
      description: 'Browse available missions and bounties',
      icon: Target,
      onClick: handleMissionBoard,
      color: 'warning',
      bgGradient: 'from-yellow-500/20 to-amber-500/20',
      hoverGradient: 'hover:from-yellow-500/30 hover:to-amber-500/30 hover:shadow-lg hover:shadow-yellow-500/20',
      special: true, // This is the mission board link
      badge: 'New Missions Available!'
    },
    {
      id: 'project-analytics',
      title: 'Project Analytics',
      description: currentProject ? `View insights for ${currentProject.name}` : 'View detailed project insights',
      icon: BarChart3,
      onClick: onProjectAnalytics,
      color: 'secondary',
      bgGradient: 'from-purple-500/20 to-pink-500/20',
      hoverGradient: 'hover:from-purple-500/30 hover:to-pink-500/30'
    }
  ];

  return (
    <Card className={`bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-white/10 ${className}`}>
      <CardBody className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-white">Quick Actions</h3>
          <p className="text-sm text-white/60">Common project management tasks</p>
        </div>

        <div className="grid grid-cols-1 gap-3">
          {actions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Tooltip
                content={action.description}
                placement="top"
                classNames={{
                  base: "py-2 px-4 shadow-xl text-black",
                  content: "py-2 px-4 shadow-xl text-black bg-gradient-to-br from-white to-neutral-400"
                }}
              >
                <Button
                  onClick={action.onClick}
                  className={
                    `w-full h-auto min-h-[80px] p-3 flex flex-col items-center justify-center gap-2 bg-gradient-to-br ${action.bgGradient} ${action.hoverGradient} transition-all duration-200 relative ` +
                    (action.special
                      ? 'border border-yellow-500/50 hover:border-yellow-400/70 ring-2 ring-yellow-500/30 shadow-lg shadow-yellow-500/10'
                      : 'border border-white/10 hover:border-white/20')
                  }
                  variant="light"
                >
                  {/* Icon Container - Fixed size and positioning */}
                  <div className={
                    action.special
                      ? 'w-10 h-10 rounded-full bg-yellow-500/20 text-yellow-400 flex items-center justify-center flex-shrink-0'
                      : `w-10 h-10 rounded-full bg-${action.color}-500/20 text-${action.color}-400 flex items-center justify-center flex-shrink-0`
                  }>
                    <action.icon className="w-5 h-5" />
                  </div>

                  {/* Title Only - Clean and minimal */}
                  <div className={
                    action.special
                      ? 'font-semibold text-sm text-yellow-200 leading-tight text-center'
                      : 'font-semibold text-sm text-white leading-tight text-center'
                  }>
                    {action.title}
                  </div>

                  {/* Special Badge - Better positioning */}
                  {action.special && (
                    <div className="absolute -top-1 -right-1 z-10">
                      <div className="bg-yellow-500 text-black text-xs px-1.5 py-0.5 rounded-full font-semibold animate-pulse">
                        NEW
                      </div>
                    </div>
                  )}
                </Button>
              </Tooltip>
            </motion.div>
          ))}
        </div>

        <div className="mt-4 pt-4 border-t border-white/10">
          <p className="text-xs text-white/50 text-center">
            💡 Tip: Use the Mission Board to find additional revenue opportunities
          </p>
        </div>
      </CardBody>
    </Card>
  );
};

export default QuickActionsPanel;
