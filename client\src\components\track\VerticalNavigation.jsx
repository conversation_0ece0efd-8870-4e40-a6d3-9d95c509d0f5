import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON> } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Search,
  Package,
  Download,
  FileText,
  Calendar,
  Activity,
  HelpCircle,
  Bell,
  Settings,
  Upload,
  Link,
  BarChart3,
  Share2,
  Archive,
  Timer,
  Target
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Vertical Navigation Component for Track Page
 * 
 * Implements the two vertical navigation bars:
 * - Left: Helper functions (Add Task, Quick Filter, etc.)
 * - Right: Settings & Tools (Notifications, Settings, etc.)
 */
const VerticalNavigation = ({ 
  onAddTask, 
  onQuickFilter, 
  onBulkActions, 
  onImportTasks,
  onViewTemplates,
  onTeamCalendar,
  onActivityFeed,
  onHelp,
  onNotifications,
  onProjectSettings,
  onExportData,
  onIntegrations,
  onTimeTrackingToggle,
  onAnalytics,
  onShareProject,
  onArchive,
  notificationCount = 0,
  isTimeTrackingVisible = false,
  className = ""
}) => {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(true);

  // Hide on mobile
  useEffect(() => {
    const handleResize = () => {
      setIsVisible(window.innerWidth >= 768);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Left navigation actions (Helper Functions)
  const leftActions = [
    {
      id: 'add-task',
      icon: Plus,
      label: 'Add New Task',
      onClick: onAddTask,
      color: 'success'
    },
    {
      id: 'mission-board',
      icon: Target,
      label: 'Browse Mission Board',
      onClick: () => navigate('/earn'),
      color: 'warning',
      special: true
    },
    {
      id: 'quick-filter',
      icon: Search,
      label: 'Quick Filter',
      onClick: onQuickFilter,
      color: 'primary'
    },
    {
      id: 'bulk-actions',
      icon: Package,
      label: 'Bulk Actions',
      onClick: onBulkActions,
      color: 'secondary'
    },
    {
      id: 'import-tasks',
      icon: Download,
      label: 'Import Tasks',
      onClick: onImportTasks,
      color: 'warning'
    },
    {
      id: 'view-templates',
      icon: FileText,
      label: 'View Templates',
      onClick: onViewTemplates,
      color: 'default'
    },
    {
      id: 'team-calendar',
      icon: Calendar,
      label: 'Team Calendar',
      onClick: onTeamCalendar,
      color: 'primary'
    },
    {
      id: 'activity-feed',
      icon: Activity,
      label: 'Activity Feed',
      onClick: onActivityFeed,
      color: 'secondary'
    },
    {
      id: 'help',
      icon: HelpCircle,
      label: 'Help & Tutorials',
      onClick: onHelp,
      color: 'default'
    }
  ];

  // Right navigation actions (Settings & Tools)
  const rightActions = [
    {
      id: 'notifications',
      icon: Bell,
      label: 'Notifications',
      onClick: onNotifications,
      color: 'danger',
      badge: notificationCount
    },
    {
      id: 'project-settings',
      icon: Settings,
      label: 'Project Settings',
      onClick: onProjectSettings,
      color: 'default'
    },
    {
      id: 'export-data',
      icon: Upload,
      label: 'Export Data',
      onClick: onExportData,
      color: 'primary'
    },
    {
      id: 'integrations',
      icon: Link,
      label: 'Integrations',
      onClick: onIntegrations,
      color: 'secondary'
    },
    {
      id: 'time-tracking',
      icon: Timer,
      label: 'Time Tracking Panel',
      onClick: onTimeTrackingToggle,
      color: isTimeTrackingVisible ? 'success' : 'warning'
    },
    {
      id: 'analytics',
      icon: BarChart3,
      label: 'Analytics',
      onClick: onAnalytics,
      color: 'primary'
    },
    {
      id: 'share-project',
      icon: Share2,
      label: 'Share Project',
      onClick: onShareProject,
      color: 'secondary'
    },
    {
      id: 'archive',
      icon: Archive,
      label: 'Archive',
      onClick: onArchive,
      color: 'default'
    }
  ];

  if (!isVisible) return null;

  return (
    <>
      {/* Left Navigation Bar - Helper Functions */}
      <motion.div
        initial={{ x: -60, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="hidden md:flex fixed left-0 top-0 h-full w-15 bg-black/20 backdrop-blur-md border-r border-white/10 flex-col items-center py-6 space-y-4 z-40"
      >
        {leftActions.map((action, index) => (
          <motion.div
            key={action.id}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="relative"
          >
            <Tooltip content={action.label} placement="right">
              <Button
                isIconOnly
                variant="light"
                color={action.color}
                onClick={action.onClick}
                className={`
                  w-12 h-12 rounded-lg transition-all duration-200 relative
                  ${action.special
                    ? 'bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/50 shadow-lg shadow-yellow-500/20'
                    : 'hover:bg-white/10'
                  }
                `}
              >
                <action.icon className={`w-5 h-5 ${action.special ? 'text-yellow-400' : ''}`} />
                {action.special && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                )}
              </Button>
            </Tooltip>
          </motion.div>
        ))}
      </motion.div>

      {/* Right Navigation Bar - Settings & Tools */}
      <motion.div
        initial={{ x: 60, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
        className="hidden md:flex fixed right-0 top-0 h-full w-15 bg-black/20 backdrop-blur-md border-l border-white/10 flex-col items-center py-6 space-y-4 z-40"
      >
        {rightActions.map((action, index) => (
          <motion.div
            key={action.id}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="relative"
          >
            <Tooltip content={action.label} placement="left">
              <Button
                isIconOnly
                variant="light"
                color={action.color}
                onClick={action.onClick}
                className="w-12 h-12 rounded-lg hover:bg-white/10 transition-all duration-200 relative"
              >
                <action.icon className="w-5 h-5" />
                {action.badge && action.badge > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {action.badge > 99 ? '99+' : action.badge}
                  </span>
                )}
              </Button>
            </Tooltip>
          </motion.div>
        ))}
      </motion.div>
    </>
  );
};

export default VerticalNavigation;
