// Enhanced Authentication & Security Middleware
// Authentication & Security Agent: Comprehensive security middleware for API endpoints
// Created: January 16, 2025

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map();

// Security headers
const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://hqqlrrqvjcetoxbdjgzx.supabase.co;"
};

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Max-Age': '86400'
};

// Combine all headers
const defaultHeaders = {
  ...securityHeaders,
  ...corsHeaders,
  'Content-Type': 'application/json'
};

/**
 * Rate limiting middleware
 */
function rateLimit(maxRequests = 100, windowMs = 60000) {
  return (req) => {
    const key = req.ip || req.headers['x-forwarded-for'] || 'unknown';
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    if (rateLimitStore.has(key)) {
      const requests = rateLimitStore.get(key).filter(time => time > windowStart);
      rateLimitStore.set(key, requests);
    } else {
      rateLimitStore.set(key, []);
    }
    
    const requests = rateLimitStore.get(key);
    
    if (requests.length >= maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: windowStart + windowMs
      };
    }
    
    requests.push(now);
    rateLimitStore.set(key, requests);
    
    return {
      allowed: true,
      remaining: maxRequests - requests.length,
      resetTime: windowStart + windowMs
    };
  };
}

/**
 * Extract user from JWT token
 */
async function getUserFromToken(token) {
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      return null;
    }
    
    // Get additional user data from users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();
    
    if (userError && userError.code !== 'PGRST116') {
      console.error('Error fetching user data:', userError);
      return user; // Return basic user if extended data fails
    }
    
    return {
      ...user,
      ...userData
    };
  } catch (error) {
    console.error('Error extracting user from token:', error);
    return null;
  }
}

/**
 * Log security event
 */
async function logSecurityEvent(eventType, severity, userId, description, eventData = {}, riskScore = null) {
  try {
    await supabase
      .from('security_events')
      .insert([{
        event_type: eventType,
        severity: severity,
        user_id: userId,
        event_description: description,
        event_data: eventData,
        risk_score: riskScore
      }]);
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}

/**
 * Check if user has required admin role
 */
function hasAdminRole(user, requiredRole) {
  if (!user || !user.is_admin) {
    return false;
  }
  
  // Super admin has all permissions
  if (user.admin_role === 'super_admin') {
    return true;
  }
  
  // Check specific role
  if (user.admin_role === requiredRole) {
    return true;
  }
  
  // For backward compatibility, allow general admin access
  return user.is_admin === true;
}

/**
 * Main authentication middleware
 */
function authMiddleware(options = {}) {
  const {
    requireAuth = true,
    requireAdmin = false,
    adminRole = null,
    rateLimit: rateLimitOptions = { maxRequests: 100, windowMs: 60000 },
    allowAnonymous = false
  } = options;
  
  const rateLimiter = rateLimit(rateLimitOptions.maxRequests, rateLimitOptions.windowMs);
  
  return async (event, context) => {
    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: defaultHeaders,
        body: ''
      };
    }
    
    // Apply rate limiting
    const rateLimitResult = rateLimiter({
      ip: event.headers['x-forwarded-for'] || event.headers['client-ip'] || 'unknown',
      headers: event.headers
    });
    
    if (!rateLimitResult.allowed) {
      await logSecurityEvent(
        'rate_limit_exceeded',
        'warning',
        null,
        'Rate limit exceeded',
        {
          ip: event.headers['x-forwarded-for'],
          user_agent: event.headers['user-agent'],
          path: event.path
        },
        30
      );
      
      return {
        statusCode: 429,
        headers: {
          ...defaultHeaders,
          'X-RateLimit-Limit': rateLimitOptions.maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(rateLimitResult.resetTime / 1000).toString()
        },
        body: JSON.stringify({
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please try again later.'
        })
      };
    }
    
    // Add rate limit headers
    const responseHeaders = {
      ...defaultHeaders,
      'X-RateLimit-Limit': rateLimitOptions.maxRequests.toString(),
      'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(rateLimitResult.resetTime / 1000).toString()
    };
    
    let user = null;
    
    // Extract and validate authentication if required
    if (requireAuth || !allowAnonymous) {
      const authHeader = event.headers.authorization;
      
      if (!authHeader) {
        await logSecurityEvent(
          'unauthorized_access_attempt',
          'warning',
          null,
          'Missing authorization header',
          {
            ip: event.headers['x-forwarded-for'],
            user_agent: event.headers['user-agent'],
            path: event.path
          },
          20
        );
        
        return {
          statusCode: 401,
          headers: responseHeaders,
          body: JSON.stringify({
            error: 'Unauthorized',
            message: 'Authorization header required'
          })
        };
      }
      
      const token = authHeader.replace('Bearer ', '');
      user = await getUserFromToken(token);
      
      if (!user) {
        await logSecurityEvent(
          'invalid_token',
          'warning',
          null,
          'Invalid or expired token',
          {
            ip: event.headers['x-forwarded-for'],
            user_agent: event.headers['user-agent'],
            path: event.path
          },
          40
        );
        
        return {
          statusCode: 401,
          headers: responseHeaders,
          body: JSON.stringify({
            error: 'Unauthorized',
            message: 'Invalid or expired token'
          })
        };
      }
      
      // Check if user account is active
      if (user.status !== 'active') {
        await logSecurityEvent(
          'suspended_user_access_attempt',
          'warning',
          user.id,
          `User with status '${user.status}' attempted to access API`,
          {
            ip: event.headers['x-forwarded-for'],
            user_agent: event.headers['user-agent'],
            path: event.path,
            user_status: user.status,
            suspension_reason: user.suspension_reason
          },
          60
        );
        
        return {
          statusCode: 403,
          headers: responseHeaders,
          body: JSON.stringify({
            error: 'Account suspended',
            message: user.suspension_reason || 'Your account has been suspended'
          })
        };
      }
    }
    
    // Check admin requirements
    if (requireAdmin && user) {
      if (!hasAdminRole(user, adminRole)) {
        await logSecurityEvent(
          'unauthorized_admin_access',
          'error',
          user.id,
          `User attempted to access admin endpoint without proper role`,
          {
            ip: event.headers['x-forwarded-for'],
            user_agent: event.headers['user-agent'],
            path: event.path,
            required_role: adminRole,
            user_role: user.admin_role,
            is_admin: user.is_admin
          },
          70
        );
        
        return {
          statusCode: 403,
          headers: responseHeaders,
          body: JSON.stringify({
            error: 'Forbidden',
            message: 'Admin access required'
          })
        };
      }
    }
    
    // Add user to event context
    event.user = user;
    event.headers = { ...event.headers, ...responseHeaders };
    
    return null; // Continue to handler
  };
}

module.exports = {
  authMiddleware,
  getUserFromToken,
  logSecurityEvent,
  hasAdminRole,
  defaultHeaders,
  securityHeaders,
  corsHeaders
};
