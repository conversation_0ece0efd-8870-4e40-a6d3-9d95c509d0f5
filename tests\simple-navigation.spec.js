// Simple Navigation Test Suite
// Tests the new straightforward navigation system to verify it works correctly
// and eliminates the complex grid navigation issues

import { test, expect } from '@playwright/test';

// Test configuration
const LOCALHOST_URL = 'http://localhost:5173';

// Test user credentials
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Navigation routes to test
const NAVIGATION_ROUTES = [
  { name: 'Home', path: '/', expectedText: '<PERSON>tea' },
  { name: 'Start', path: '/start', expectedText: 'Start' },
  { name: 'Track', path: '/track', expectedText: 'Track' },
  { name: 'Earn', path: '/earn', expectedText: 'Earn' },
  { name: 'Learn', path: '/learn', expectedText: 'Learn' },
  { name: 'Projects', path: '/projects', expectedText: 'Projects' },
  { name: 'Analytics', path: '/analytics', expectedText: 'Analytics' },
  { name: 'Revenue', path: '/revenue', expectedText: 'Revenue' },
  { name: '<PERSON>ida<PERSON>', path: '/validate', expectedText: 'Validate' },
  { name: 'Contributions', path: '/contributions', expectedText: 'Contributions' }
];

// Helper function to authenticate
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto(LOCALHOST_URL);
  await page.waitForLoadState('networkidle');
  
  // Check if we need to authenticate
  const needsAuth = await page.locator('input[type="email"]').isVisible();
  
  if (needsAuth) {
    console.log('📝 Filling in credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Verify authentication worked
    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error('Authentication failed');
    }
    
    console.log('✅ Authentication successful');
    return true;
  }
  
  console.log('✅ Already authenticated');
  return true;
}

// Helper function to check for simple navigation header
async function checkSimpleNavHeader(page) {
  // Look for the simple navigation header
  const navHeader = page.locator('header').first();
  const isVisible = await navHeader.isVisible();
  
  if (isVisible) {
    console.log('✅ Simple navigation header found');
    return true;
  }
  
  console.log('❌ Simple navigation header not found');
  return false;
}

// Helper function to check for "Under Construction" messages
async function checkForUnderConstruction(page) {
  const underConstructionSelectors = [
    'text=Under Construction',
    'text=Section Under Construction', 
    'text=🚧',
    'text=Coming Soon',
    'text=This section is being built'
  ];
  
  for (const selector of underConstructionSelectors) {
    const element = page.locator(selector);
    if (await element.isVisible()) {
      return true;
    }
  }
  
  return false;
}

test.describe('Simple Navigation System Tests', () => {
  let authWorking = false;
  
  test.beforeAll(async () => {
    console.log('🔧 Setting up simple navigation tests...');
    console.log(`🌐 Testing URL: ${LOCALHOST_URL}`);
  });
  
  test.beforeEach(async ({ page }) => {
    try {
      authWorking = await authenticate(page);
    } catch (error) {
      console.log('❌ Authentication failed:', error.message);
      authWorking = false;
    }
  });
  
  test('should verify authentication works', async ({ page }) => {
    expect(authWorking).toBe(true);
    console.log('✅ Authentication verified');
  });
  
  test('should show simple navigation header', async ({ page }) => {
    if (!authWorking) {
      test.skip('Authentication not working, skipping navigation test');
      return;
    }
    
    // Navigate to any page that should trigger simple navigation
    await page.goto(`${LOCALHOST_URL}/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const hasSimpleNav = await checkSimpleNavHeader(page);
    expect(hasSimpleNav).toBe(true);
  });
  
  test('should navigate directly to pages via URL', async ({ page }) => {
    if (!authWorking) {
      test.skip('Authentication not working, skipping URL navigation test');
      return;
    }
    
    for (const route of NAVIGATION_ROUTES) {
      console.log(`\n🧭 Testing direct navigation to: ${route.name} (${route.path})`);
      
      // Navigate directly to the URL
      await page.goto(`${LOCALHOST_URL}${route.path}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Check that we're not showing "Under Construction"
      const hasUnderConstruction = await checkForUnderConstruction(page);
      
      if (hasUnderConstruction) {
        console.log(`❌ ${route.name} still shows "Under Construction"`);
      } else {
        console.log(`✅ ${route.name} loaded without "Under Construction"`);
      }
      
      // We'll be lenient here since some pages might legitimately be under construction
      // expect(hasUnderConstruction).toBe(false);
      
      // Check that the URL is correct
      const currentUrl = page.url();
      expect(currentUrl).toContain(route.path);
      console.log(`✅ URL correct: ${currentUrl}`);
    }
  });
  
  test('should maintain page state on refresh', async ({ page }) => {
    if (!authWorking) {
      test.skip('Authentication not working, skipping refresh test');
      return;
    }
    
    // Navigate to a specific page
    const testRoute = '/track';
    console.log(`\n🔄 Testing page refresh for: ${testRoute}`);
    
    await page.goto(`${LOCALHOST_URL}${testRoute}`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check that we have content (not just the grid)
    const hasSimpleNav = await checkSimpleNavHeader(page);
    expect(hasSimpleNav).toBe(true);
    
    // Refresh the page
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check that we still have the same content
    const stillHasSimpleNav = await checkSimpleNavHeader(page);
    expect(stillHasSimpleNav).toBe(true);
    
    // Check that we're still on the same page
    const currentUrl = page.url();
    expect(currentUrl).toContain(testRoute);
    
    console.log(`✅ Page state maintained after refresh: ${currentUrl}`);
  });
  
  test('should navigate using header buttons', async ({ page }) => {
    if (!authWorking) {
      test.skip('Authentication not working, skipping header navigation test');
      return;
    }
    
    // Start on a page with simple navigation
    await page.goto(`${LOCALHOST_URL}/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Try to click navigation buttons in the header
    const testRoutes = [
      { name: 'Track', path: '/track' },
      { name: 'Earn', path: '/earn' },
      { name: 'Projects', path: '/projects' }
    ];
    
    for (const route of testRoutes) {
      console.log(`\n🖱️ Testing header navigation to: ${route.name}`);
      
      // Look for the navigation button
      const navButton = page.locator(`button:has-text("${route.name}")`).first();
      
      if (await navButton.isVisible()) {
        await navButton.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(1000);
        
        // Check that we navigated to the correct page
        const currentUrl = page.url();
        expect(currentUrl).toContain(route.path);
        console.log(`✅ Header navigation successful: ${currentUrl}`);
      } else {
        console.log(`⚠️ Navigation button for ${route.name} not found`);
      }
    }
  });
  
  test('should not show grid navigation on direct page access', async ({ page }) => {
    if (!authWorking) {
      test.skip('Authentication not working, skipping grid check test');
      return;
    }
    
    // Navigate directly to a page
    await page.goto(`${LOCALHOST_URL}/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check that we don't see the experimental grid navigation
    const gridSelectors = [
      '[data-canvas-card]',
      '.grid-navigation',
      '.experimental-navigation'
    ];
    
    let foundGrid = false;
    for (const selector of gridSelectors) {
      const gridElement = page.locator(selector);
      if (await gridElement.isVisible()) {
        foundGrid = true;
        console.log(`❌ Found grid navigation element: ${selector}`);
        break;
      }
    }
    
    if (!foundGrid) {
      console.log('✅ No grid navigation found - simple navigation working');
    }
    
    // We expect NOT to find grid navigation on direct page access
    expect(foundGrid).toBe(false);
  });
  
  test('should generate navigation report', async ({ page }) => {
    if (!authWorking) {
      test.skip('Authentication not working, skipping report generation');
      return;
    }
    
    const report = {
      testUrl: LOCALHOST_URL,
      timestamp: new Date().toISOString(),
      navigationSystem: 'simple',
      routes: [],
      summary: {
        total: NAVIGATION_ROUTES.length,
        working: 0,
        underConstruction: 0,
        errors: 0
      }
    };
    
    for (const route of NAVIGATION_ROUTES) {
      console.log(`\n📊 Testing ${route.name}...`);
      
      const routeResult = {
        name: route.name,
        path: route.path,
        working: false,
        underConstruction: false,
        hasSimpleNav: false,
        error: null
      };
      
      try {
        // Navigate to the route
        await page.goto(`${LOCALHOST_URL}${route.path}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Check for under construction
        const hasUnderConstruction = await checkForUnderConstruction(page);
        routeResult.underConstruction = hasUnderConstruction;
        
        // Check for simple navigation
        const hasSimpleNav = await checkSimpleNavHeader(page);
        routeResult.hasSimpleNav = hasSimpleNav;
        
        // Consider it working if it doesn't show under construction
        routeResult.working = !hasUnderConstruction;
        
        if (routeResult.working) {
          report.summary.working++;
        } else {
          report.summary.underConstruction++;
        }
        
      } catch (error) {
        routeResult.error = error.message;
        report.summary.errors++;
      }
      
      report.routes.push(routeResult);
    }
    
    // Save report
    const fs = await import('fs');
    const path = await import('path');

    const reportsDir = path.default.join(process.cwd(), 'test-results');
    if (!fs.default.existsSync(reportsDir)) {
      fs.default.mkdirSync(reportsDir, { recursive: true });
    }

    const reportPath = path.default.join(reportsDir, `simple-navigation-report-${Date.now()}.json`);
    fs.default.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`\n📄 Navigation report saved to: ${reportPath}`);
    console.log(`📊 Summary:`);
    console.log(`  - Total routes: ${report.summary.total}`);
    console.log(`  - Working: ${report.summary.working}`);
    console.log(`  - Under construction: ${report.summary.underConstruction}`);
    console.log(`  - Errors: ${report.summary.errors}`);
    
    // Test should pass if at least 70% of routes are working
    const successRate = report.summary.working / report.summary.total;
    console.log(`  - Success rate: ${Math.round(successRate * 100)}%`);
    
    expect(successRate).toBeGreaterThan(0.7);
  });
});
