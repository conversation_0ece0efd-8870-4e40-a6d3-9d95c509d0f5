import React from 'react';
import { Input } from '../heroui';
import { cn } from '../../../lib/utils';

/**
 * ModernFormGroup Component
 *
 * A modern form group component that wraps form controls with a label.
 *
 * @param {Object} props - Component props
 * @param {string} props.id - Input ID
 * @param {string} props.label - Input label
 * @param {React.ReactNode} props.children - Form control
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.labelClassName] - Additional CSS classes for the label
 * @param {string} [props.helpText] - Help text to display below the input
 * @returns {React.ReactElement} - ModernFormGroup component
 */
export const ModernFormGroup = ({
  id,
  label,
  children,
  className = '',
  labelClassName = '',
  helpText,
  ...props
}) => {

  return (
    <div className={cn('mb-4', className)} {...props}>
      <label
        htmlFor={id}
        className={cn('block mb-2 text-sm font-medium', labelClassName)}
      >
        {label}
      </label>
      {children}
      {helpText && (
        <p className="mt-2 text-sm text-slate-500">
          {helpText}
        </p>
      )}
    </div>
  );
};

/**
 * ModernInput Component
 *
 * A modern input component that uses shadcn/ui's Input component.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.type] - Input type
 * @returns {React.ReactElement} - ModernInput component
 */
export const ModernInput = ({
  className = '',
  type = 'text',
  ...props
}) => {

  return (
    <Input
      type={type}
      className={className}
      {...props}
    />
  );
};

/**
 * ModernTextarea Component
 *
 * A modern textarea component.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @returns {React.ReactElement} - ModernTextarea component
 */
export const ModernTextarea = ({
  className = '',
  ...props
}) => {

  return (
    <textarea
      className={cn(
        'flex min-h-[80px] w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-slate-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:placeholder:text-slate-400 dark:focus-visible:ring-slate-300',
        className
      )}
      {...props}
    />
  );
};

/**
 * ModernSelect Component
 *
 * A modern select component.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {React.ReactNode} props.children - Select options
 * @returns {React.ReactElement} - ModernSelect component
 */
export const ModernSelect = ({
  className = '',
  children,
  ...props
}) => {

  return (
    <select
      className={cn(
        'flex h-10 w-full rounded-md border border-slate-200 bg-white px-3 py-2 text-sm ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-950 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-800 dark:bg-slate-950 dark:ring-offset-slate-950 dark:focus-visible:ring-slate-300',
        className
      )}
      {...props}
    >
      {children}
    </select>
  );
};

/**
 * ModernCheckbox Component
 *
 * A modern checkbox component.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.label] - Checkbox label
 * @returns {React.ReactElement} - ModernCheckbox component
 */
export const ModernCheckbox = ({
  className = '',
  label,
  id,
  ...props
}) => {

  return (
    <div className="flex items-center space-x-2">
      <input
        type="checkbox"
        id={id}
        className={cn(
          'h-4 w-4 rounded border-slate-300 text-slate-900 focus:ring-slate-950 dark:border-slate-700 dark:text-slate-50 dark:focus:ring-slate-300',
          className
        )}
        {...props}
      />
      {label && (
        <label
          htmlFor={id}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
        </label>
      )}
    </div>
  );
};

/**
 * ModernRadio Component
 *
 * A modern radio component.
 *
 * @param {Object} props - Component props
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.label] - Radio label
 * @returns {React.ReactElement} - ModernRadio component
 */
export const ModernRadio = ({
  className = '',
  label,
  id,
  ...props
}) => {

  return (
    <div className="flex items-center space-x-2">
      <input
        type="radio"
        id={id}
        className={cn(
          'h-4 w-4 border-slate-300 text-slate-900 focus:ring-slate-950 dark:border-slate-700 dark:text-slate-50 dark:focus:ring-slate-300',
          className
        )}
        {...props}
      />
      {label && (
        <label
          htmlFor={id}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
        </label>
      )}
    </div>
  );
};
