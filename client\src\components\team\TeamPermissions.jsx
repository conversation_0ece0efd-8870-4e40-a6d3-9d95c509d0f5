import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { supabase } from '../../utils/supabase/supabase.utils';

/**
 * TeamPermissions component for managing role-based permissions
 * @param {Object} props - Component props
 * @param {string} props.teamId - The team ID
 * @param {boolean} props.isOwner - Whether the current user is the team owner
 * @param {Function} props.onPermissionsUpdated - Callback when permissions are updated
 */
const TeamPermissions = ({ teamId, isOwner, onPermissionsUpdated }) => {
  const [loading, setLoading] = useState(true);
  const [permissions, setPermissions] = useState({});
  const [editMode, setEditMode] = useState(false);
  const [originalPermissions, setOriginalPermissions] = useState({});

  // Define permission categories and their specific permissions
  const permissionStructure = {
    projects: {
      title: 'Project Permissions',
      permissions: {
        create_projects: 'Create new projects',
        edit_projects: 'Edit project details',
        delete_projects: 'Delete projects',
        manage_project_members: 'Manage project members',
      }
    },
    members: {
      title: 'Team Member Permissions',
      permissions: {
        invite_members: 'Invite new members',
        remove_members: 'Remove members',
        change_member_roles: 'Change member roles',
      }
    },
    agreements: {
      title: 'Agreement Permissions',
      permissions: {
        generate_agreements: 'Generate contributor agreements',
        sign_agreements: 'Sign agreements on behalf of team',
        view_all_agreements: 'View all team agreements',
      }
    },
    finances: {
      title: 'Financial Permissions',
      permissions: {
        view_finances: 'View financial information',
        manage_payments: 'Manage payments and distributions',
        edit_revenue_settings: 'Edit revenue settings',
      }
    }
  };

  // Define default permissions for each role
  const defaultRolePermissions = {
    owner: {
      // Owners have all permissions by default
      create_projects: true,
      edit_projects: true,
      delete_projects: true,
      manage_project_members: true,
      invite_members: true,
      remove_members: true,
      change_member_roles: true,
      generate_agreements: true,
      sign_agreements: true,
      view_all_agreements: true,
      view_finances: true,
      manage_payments: true,
      edit_revenue_settings: true,
    },
    admin: {
      // Admins have most permissions except critical ones
      create_projects: true,
      edit_projects: true,
      delete_projects: false,
      manage_project_members: true,
      invite_members: true,
      remove_members: true,
      change_member_roles: false,
      generate_agreements: true,
      sign_agreements: true,
      view_all_agreements: true,
      view_finances: true,
      manage_payments: false,
      edit_revenue_settings: false,
    },
    member: {
      // Regular members have limited permissions
      create_projects: false,
      edit_projects: false,
      delete_projects: false,
      manage_project_members: false,
      invite_members: false,
      remove_members: false,
      change_member_roles: false,
      generate_agreements: false,
      sign_agreements: false,
      view_all_agreements: false,
      view_finances: false,
      manage_payments: false,
      edit_revenue_settings: false,
    }
  };

  // Fetch team permissions on component mount
  useEffect(() => {
    if (teamId) {
      fetchTeamPermissions();
    }
  }, [teamId]);

  // Fetch team permissions from the database
  const fetchTeamPermissions = async () => {
    try {
      setLoading(true);

      // Get team permissions from the database
      const { data, error } = await supabase
        .from('team_permissions')
        .select('*')
        .eq('team_id', teamId)
        .single();

      if (error) {
        // If no permissions exist yet, create default permissions
        if (error.code === 'PGRST116') {
          setPermissions({
            admin: { ...defaultRolePermissions.admin },
            member: { ...defaultRolePermissions.member }
          });
        } else {
          throw error;
        }
      } else {
        // Use existing permissions or fall back to defaults
        setPermissions({
          admin: data.admin_permissions || { ...defaultRolePermissions.admin },
          member: data.member_permissions || { ...defaultRolePermissions.member }
        });
      }

      // Store original permissions for cancel functionality
      setOriginalPermissions(JSON.parse(JSON.stringify(permissions)));
    } catch (error) {
      console.error('Error fetching team permissions:', error);
      toast.error('Failed to load team permissions');
    } finally {
      setLoading(false);
    }
  };

  // Save permissions to the database
  const savePermissions = async () => {
    if (!isOwner) {
      toast.error('Only team owners can update permissions');
      return;
    }

    try {
      setLoading(true);

      // Check if permissions already exist
      const { data, error: checkError } = await supabase
        .from('team_permissions')
        .select('id')
        .eq('team_id', teamId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        throw checkError;
      }

      let result;
      if (data) {
        // Update existing permissions
        result = await supabase
          .from('team_permissions')
          .update({
            admin_permissions: permissions.admin,
            member_permissions: permissions.member,
            updated_at: new Date()
          })
          .eq('id', data.id);
      } else {
        // Insert new permissions
        result = await supabase
          .from('team_permissions')
          .insert({
            team_id: teamId,
            admin_permissions: permissions.admin,
            member_permissions: permissions.member
          });
      }

      if (result.error) throw result.error;

      toast.success('Team permissions updated successfully');
      setEditMode(false);
      setOriginalPermissions(JSON.parse(JSON.stringify(permissions)));

      // Call the callback if provided
      if (onPermissionsUpdated) {
        onPermissionsUpdated();
      }
    } catch (error) {
      console.error('Error saving team permissions:', error);
      toast.error('Failed to update team permissions');
    } finally {
      setLoading(false);
    }
  };

  // Handle permission toggle
  const handlePermissionToggle = (role, permissionKey) => {
    setPermissions(prev => ({
      ...prev,
      [role]: {
        ...prev[role],
        [permissionKey]: !prev[role][permissionKey]
      }
    }));
  };

  // Cancel editing and revert to original permissions
  const cancelEditing = () => {
    setPermissions(JSON.parse(JSON.stringify(originalPermissions)));
    setEditMode(false);
  };

  if (loading) {
    return <div className="permissions-loading">Loading permissions...</div>;
  }

  return (
    <div className="team-permissions-container">
      <div className="permissions-header">
        <h3>Role Permissions</h3>
        {!editMode && isOwner && (
          <button
            className="edit-permissions-button"
            onClick={() => setEditMode(true)}
          >
            Edit Permissions
          </button>
        )}
      </div>

      <div className="permissions-description">
        <p>
          Define what members with different roles can do within the team.
          Owner permissions cannot be modified and include all permissions by default.
        </p>
      </div>

      <div className="permissions-table-container">
        <table className="permissions-table">
          <thead>
            <tr>
              <th className="permission-name">Permission</th>
              <th className="role-header owner">Owner</th>
              <th className="role-header admin">Admin</th>
              <th className="role-header member">Member</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(permissionStructure).map(([category, { title, permissions: categoryPermissions }]) => (
              <React.Fragment key={category}>
                <tr className="category-header">
                  <td colSpan="4">{title}</td>
                </tr>
                {Object.entries(categoryPermissions).map(([permKey, permLabel]) => (
                  <tr key={permKey} className="permission-row">
                    <td className="permission-label">{permLabel}</td>
                    <td className="permission-value owner">
                      <span className="permission-always-granted">
                        <i className="bi bi-check-lg"></i>
                      </span>
                    </td>
                    <td className="permission-value admin">
                      {editMode ? (
                        <label className="permission-toggle">
                          <input
                            type="checkbox"
                            checked={permissions.admin[permKey] || false}
                            onChange={() => handlePermissionToggle('admin', permKey)}
                          />
                          <span className="toggle-slider"></span>
                        </label>
                      ) : (
                        <span className={permissions.admin[permKey] ? 'permission-granted' : 'permission-denied'}>
                          {permissions.admin[permKey] ? (
                            <i className="bi bi-check-lg"></i>
                          ) : (
                            <i className="bi bi-x-lg"></i>
                          )}
                        </span>
                      )}
                    </td>
                    <td className="permission-value member">
                      {editMode ? (
                        <label className="permission-toggle">
                          <input
                            type="checkbox"
                            checked={permissions.member[permKey] || false}
                            onChange={() => handlePermissionToggle('member', permKey)}
                          />
                          <span className="toggle-slider"></span>
                        </label>
                      ) : (
                        <span className={permissions.member[permKey] ? 'permission-granted' : 'permission-denied'}>
                          {permissions.member[permKey] ? (
                            <i className="bi bi-check-lg"></i>
                          ) : (
                            <i className="bi bi-x-lg"></i>
                          )}
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {editMode && (
        <div className="permissions-actions">
          <button
            className="save-permissions-button"
            onClick={savePermissions}
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Permissions'}
          </button>
          <button
            className="cancel-permissions-button"
            onClick={cancelEditing}
            disabled={loading}
          >
            Cancel
          </button>
        </div>
      )}
    </div>
  );
};

export default TeamPermissions;
