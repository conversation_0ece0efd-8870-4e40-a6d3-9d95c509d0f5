import React from 'react';
import { Link } from 'react-router-dom';

const ProfileSidebar = ({ 
  profile, 
  topCollaborators = [], 
  topSkills = [],
  isOwnProfile = false,
  onEditSection = null
}) => {
  const {
    bio,
    location,
    website,
    social_links = {}
  } = profile || {};

  // Format website URL for display
  const formatWebsiteUrl = (url) => {
    if (!url) return '';
    return url.replace(/^https?:\/\/(www\.)?/, '').replace(/\/$/, '');
  };

  // Get website display text
  const websiteDisplay = formatWebsiteUrl(website);

  // Social media icons mapping
  const socialIcons = {
    github: 'bi-github',
    twitter: 'bi-twitter-x',
    linkedin: 'bi-linkedin',
    instagram: 'bi-instagram',
    facebook: 'bi-facebook',
    youtube: 'bi-youtube',
    twitch: 'bi-twitch',
    discord: 'bi-discord',
    itchio: 'bi-controller'
  };

  return (
    <div className="profile-sidebar">
      {/* About Me Section */}
      <div className="sidebar-section">
        <div className="section-header">
          <h3>About Me</h3>
          {isOwnProfile && onEditSection && (
            <button 
              className="edit-section-btn"
              onClick={() => onEditSection('about')}
              title="Edit About Me"
            >
              <i className="bi bi-pencil"></i>
            </button>
          )}
        </div>
        
        <div className="about-content">
          {bio ? (
            <p className="bio-text">{bio}</p>
          ) : (
            <p className="empty-bio">No bio provided</p>
          )}
          
          {location && (
            <div className="location-info">
              <i className="bi bi-geo-alt"></i>
              <span>{location}</span>
            </div>
          )}
          
          {website && (
            <div className="website-info">
              <i className="bi bi-globe"></i>
              <a href={website} target="_blank" rel="noopener noreferrer">
                {websiteDisplay}
              </a>
            </div>
          )}
        </div>
      </div>
      
      {/* Connect Section */}
      {(Object.values(social_links).some(link => link) || (isOwnProfile && onEditSection)) && (
        <div className="sidebar-section">
          <div className="section-header">
            <h3>Connect</h3>
            {isOwnProfile && onEditSection && (
              <button 
                className="edit-section-btn"
                onClick={() => onEditSection('social')}
                title="Edit Social Links"
              >
                <i className="bi bi-pencil"></i>
              </button>
            )}
          </div>
          
          <div className="social-links">
            {Object.entries(social_links).map(([platform, url]) => {
              if (!url) return null;
              
              return (
                <a 
                  key={platform}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  title={platform.charAt(0).toUpperCase() + platform.slice(1)}
                >
                  <i className={`bi ${socialIcons[platform] || 'bi-link'}`}></i>
                </a>
              );
            })}
            
            {!Object.values(social_links).some(link => link) && isOwnProfile && (
              <p className="empty-social">Add your social media links</p>
            )}
          </div>
        </div>
      )}
      
      {/* Skills Section */}
      {(topSkills.length > 0 || (isOwnProfile && onEditSection)) && (
        <div className="sidebar-section">
          <div className="section-header">
            <h3>Skills</h3>
            {isOwnProfile && onEditSection && (
              <button 
                className="edit-section-btn"
                onClick={() => onEditSection('skills')}
                title="Edit Skills"
              >
                <i className="bi bi-pencil"></i>
              </button>
            )}
          </div>
          
          <div className="skills-list">
            {topSkills.length > 0 ? (
              topSkills.map((skill) => (
                <div key={skill.id} className="skill-item">
                  <div className="skill-header">
                    <span className="skill-name">{skill.name}</span>
                    <span className="skill-level">{skill.proficiency_score}%</span>
                  </div>
                  <div className="skill-bar-container">
                    <div 
                      className="skill-bar" 
                      style={{ width: `${skill.proficiency_score}%` }}
                    ></div>
                  </div>
                  <div className="skill-verification">
                    <i className={`bi ${skill.verification_icon}`}></i>
                    <span>{skill.verification_label}</span>
                  </div>
                </div>
              ))
            ) : (
              <p className="empty-skills">No skills added yet</p>
            )}
            
            <Link to={`/profile/${profile?.id}/skills`} className="view-all-link">
              View all skills
            </Link>
          </div>
        </div>
      )}
      
      {/* Top Collaborators Section */}
      {(topCollaborators.length > 0 || (isOwnProfile && onEditSection)) && (
        <div className="sidebar-section">
          <div className="section-header">
            <h3>Top Collaborators</h3>
            {isOwnProfile && onEditSection && (
              <button 
                className="edit-section-btn"
                onClick={() => onEditSection('collaborators')}
                title="Edit Collaborators"
              >
                <i className="bi bi-pencil"></i>
              </button>
            )}
          </div>
          
          <div className="collaborators-grid">
            {topCollaborators.length > 0 ? (
              topCollaborators.map((collab) => (
                <Link 
                  key={collab.id}
                  to={`/profile/${collab.collaborator.id}`}
                  className="collaborator-item"
                >
                  <div className="collaborator-avatar">
                    <img 
                      src={collab.collaborator.avatar_url || '/default-avatar-specs.png'} 
                      alt={collab.collaborator.display_name} 
                    />
                  </div>
                  <span className="collaborator-name">
                    {collab.collaborator.display_name}
                  </span>
                </Link>
              ))
            ) : (
              <p className="empty-collaborators">No collaborators added yet</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileSidebar;
