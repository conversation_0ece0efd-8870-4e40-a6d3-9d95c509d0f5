import { supabase } from './supabase/supabase.utils';

/**
 * Log a project activity
 * @param {string} projectId - The project ID
 * @param {string} userId - The user ID
 * @param {string} activityType - The type of activity
 * @param {Object} activityData - Additional data about the activity
 * @param {boolean} isPublic - Whether the activity is public
 * @returns {Promise} - The result of the insert operation
 */
export const logProjectActivity = async (
  projectId,
  userId,
  activityType,
  activityData = {},
  isPublic = true
) => {
  try {
    // Check if project_activities table exists
    const { count, error: countError } = await supabase
      .from('project_activities')
      .select('*', { count: 'exact', head: true });

    if (countError && countError.code === '42P01') {
      // Table doesn't exist, create it
      console.log('project_activities table does not exist, creating it...');

      // Since we can't create tables directly from the client,
      // we'll log this and return. The table should be created
      // through migrations or server-side scripts.
      console.error('project_activities table does not exist. Please run the migration script.');
      return { error: 'Table does not exist' };
    } else if (countError) {
      console.error('Error checking project_activities table:', countError);
      return { error: countError };
    }

    // Insert the activity
    const { data, error } = await supabase
      .from('project_activities')
      .insert([
        {
          project_id: projectId,
          user_id: userId,
          activity_type: activityType,
          activity_data: activityData,
          is_public: isPublic,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Error logging project activity:', error);
      return { error };
    }

    return { data };
  } catch (error) {
    console.error('Unexpected error logging project activity:', error);
    return { error };
  }
};

/**
 * Get project activities
 * @param {string} projectId - The project ID
 * @param {number} limit - The maximum number of activities to return
 * @param {number} page - The page number for pagination
 * @returns {Promise} - The result of the select operation
 */
export const getProjectActivities = async (projectId, limit = 20, page = 1) => {
  try {
    // Check if project_activities table exists
    const { count, error: countError } = await supabase
      .from('project_activities')
      .select('*', { count: 'exact', head: true });

    if (countError && countError.code === '42P01') {
      // Table doesn't exist
      console.error('project_activities table does not exist. Please run the migration script.');
      return { data: [], error: 'Table does not exist' };
    } else if (countError) {
      console.error('Error checking project_activities table:', countError);
      return { error: countError };
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Try to get activities with user details using the view
    let data, error;
    try {
      const result = await supabase
        .from('project_activities_with_users')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      data = result.data;
      error = result.error;

      // If we got data, format it to match the expected structure
      if (data && !error) {
        data = data.map(item => ({
          ...item,
          user: {
            id: item.user_id,
            display_name: item.user_display_name,
            avatar_url: item.user_avatar_url
          }
        }));
      }
    } catch (viewError) {
      console.warn('Error using project_activities_with_users view, falling back to direct query:', viewError);

      // Fall back to direct query if the view doesn't exist
      const result = await supabase
        .from('project_activities')
        .select('*')
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      data = result.data;
      error = result.error;
    }

    if (error) {
      console.error('Error getting project activities:', error);
      return { error };
    }

    return { data };
  } catch (error) {
    console.error('Unexpected error getting project activities:', error);
    return { error };
  }
};

/**
 * Format activity for display
 * @param {Object} activity - The activity object
 * @returns {Object} - Formatted activity data
 */
export const formatActivity = (activity) => {
  const { activity_type, activity_data, created_at, user } = activity;
  const timestamp = new Date(created_at);

  // Create a default user object if user is missing or incomplete
  const defaultUser = {
    id: activity.user_id || 'unknown',
    display_name: (user && user.display_name) ||
                  activity.user_display_name ||
                  'Unknown User',
    avatar_url: (user && user.avatar_url) ||
                activity.user_avatar_url ||
                null
  };

  // Default formatted data
  let formattedData = {
    title: 'Unknown Activity',
    description: '',
    icon: 'bi-question-circle',
    color: 'secondary',
    timestamp,
    user: user || defaultUser
  };

  // Format based on activity type
  switch (activity_type) {
    case 'project_created':
      formattedData = {
        ...formattedData,
        title: 'Project Created',
        description: `Created project "${activity_data.project_name || 'Unnamed Project'}"`,
        icon: 'bi-plus-circle',
        color: 'success'
      };
      break;

    case 'project_updated':
      formattedData = {
        ...formattedData,
        title: 'Project Updated',
        description: `Updated project details`,
        icon: 'bi-pencil',
        color: 'primary'
      };
      break;

    case 'contributor_added':
      formattedData = {
        ...formattedData,
        title: 'Contributor Added',
        description: `Added ${activity_data.contributor_name || 'a contributor'} to the project`,
        icon: 'bi-person-plus',
        color: 'success'
      };
      break;

    case 'contributor_removed':
      formattedData = {
        ...formattedData,
        title: 'Contributor Removed',
        description: `Removed ${activity_data.contributor_name || 'a contributor'} from the project`,
        icon: 'bi-person-dash',
        color: 'danger'
      };
      break;

    case 'contribution_added':
      formattedData = {
        ...formattedData,
        title: 'Contribution Recorded',
        description: `Recorded ${activity_data.hours || 'time'} for ${activity_data.task || 'a task'}`,
        icon: 'bi-clock-history',
        color: 'info'
      };
      break;

    case 'milestone_created':
      formattedData = {
        ...formattedData,
        title: 'Milestone Created',
        description: `Created milestone "${activity_data.milestone_name || 'Unnamed Milestone'}"`,
        icon: 'bi-flag',
        color: 'primary'
      };
      break;

    case 'milestone_completed':
      formattedData = {
        ...formattedData,
        title: 'Milestone Completed',
        description: `Completed milestone "${activity_data.milestone_name || 'Unnamed Milestone'}"`,
        icon: 'bi-check-circle',
        color: 'success'
      };
      break;

    case 'agreement_created':
      formattedData = {
        ...formattedData,
        title: 'Agreement Created',
        description: `Created a contributor agreement`,
        icon: 'bi-file-earmark-text',
        color: 'primary'
      };
      break;

    case 'agreement_signed':
      formattedData = {
        ...formattedData,
        title: 'Agreement Signed',
        description: `Signed a contributor agreement`,
        icon: 'bi-pen',
        color: 'success'
      };
      break;

    case 'agreement_updated':
      formattedData = {
        ...formattedData,
        title: 'Agreement Updated',
        description: `Updated a contributor agreement to version ${activity_data.version || '?'}`,
        icon: 'bi-file-earmark-diff',
        color: 'warning'
      };
      break;

    case 'revenue_added':
      formattedData = {
        ...formattedData,
        title: 'Revenue Added',
        description: `Added ${activity_data.amount || 'revenue'} from ${activity_data.source || 'a source'}`,
        icon: 'bi-cash-coin',
        color: 'success'
      };
      break;

    case 'task_created':
      formattedData = {
        ...formattedData,
        title: 'Task Created',
        description: `Created task "${activity_data.task_name || 'Unnamed Task'}"`,
        icon: 'bi-list-check',
        color: 'primary'
      };
      break;

    case 'task_completed':
      formattedData = {
        ...formattedData,
        title: 'Task Completed',
        description: `Completed task "${activity_data.task_name || 'Unnamed Task'}"`,
        icon: 'bi-check2-square',
        color: 'success'
      };
      break;

    case 'contribution_updated':
      formattedData = {
        ...formattedData,
        title: 'Contribution Updated',
        description: `Updated contribution for "${activity_data.task || 'a task'}"`,
        icon: 'bi-pencil-square',
        color: 'primary'
      };
      break;

    case 'contribution_validated':
      const validationStatus = activity_data.status || 'unknown';
      const statusText = validationStatus === 'approved' ? 'Approved' :
                         validationStatus === 'rejected' ? 'Rejected' :
                         validationStatus === 'pending_changes' ? 'Requested changes for' : 'Validated';

      formattedData = {
        ...formattedData,
        title: 'Contribution Validated',
        description: `${statusText} a contribution`,
        icon: validationStatus === 'approved' ? 'bi-check-circle' :
              validationStatus === 'rejected' ? 'bi-x-circle' :
              validationStatus === 'pending_changes' ? 'bi-pencil-square' : 'bi-clipboard-check',
        color: validationStatus === 'approved' ? 'success' :
               validationStatus === 'rejected' ? 'danger' :
               validationStatus === 'pending_changes' ? 'warning' : 'info'
      };
      break;

    case 'bulk_contributions_added':
      formattedData = {
        ...formattedData,
        title: 'Bulk Contributions Added',
        description: `Added ${activity_data.count || 'multiple'} contributions for "${activity_data.task || 'a task'}"`,
        icon: 'bi-collection',
        color: 'info'
      };
      break;

    case 'bulk_contributions_validated':
      const bulkStatus = activity_data.status || 'unknown';
      const bulkStatusText = bulkStatus === 'approved' ? 'Approved' :
                            bulkStatus === 'rejected' ? 'Rejected' :
                            bulkStatus === 'pending_changes' ? 'Requested changes for' : 'Validated';

      formattedData = {
        ...formattedData,
        title: 'Bulk Validation',
        description: `${bulkStatusText} ${activity_data.count || 'multiple'} contributions`,
        icon: bulkStatus === 'approved' ? 'bi-check-all' :
              bulkStatus === 'rejected' ? 'bi-x-circle' :
              bulkStatus === 'pending_changes' ? 'bi-pencil-square' : 'bi-clipboard-check',
        color: bulkStatus === 'approved' ? 'success' :
               bulkStatus === 'rejected' ? 'danger' :
               bulkStatus === 'pending_changes' ? 'warning' : 'info'
      };
      break;

    default:
      // Use default values
      break;
  }

  return formattedData;
};
