import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * ORBWallet Component
 * 
 * Enhanced ORB wallet following wireframe specifications
 * Features balance display, recent transactions, and cash out functionality
 */
const ORBWallet = ({ 
  currentUser,
  orbBalance = 0,
  recentTransactions = [],
  onCashOut,
  onViewHistory,
  className = ""
}) => {
  const [showCashOutModal, setShowCashOutModal] = useState(false);
  const [cashOutAmount, setCashOutAmount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [orbToUsdRate] = useState(0.10); // $0.10 per ORB

  // Calculate USDC equivalent
  const usdcValue = (orbBalance * orbToUsdRate).toFixed(2);

  // Handle cash out process
  const handleCashOut = async () => {
    if (cashOutAmount <= 0 || cashOutAmount > orbBalance) {
      toast.error('Invalid cash out amount');
      return;
    }

    setLoading(true);
    try {
      // Implement ORB cash out through payment system
      const cashOutData = {
        amount: cashOutAmount,
        currency: 'ORB',
        target_currency: 'USDC',
        conversion_rate: orbToUsdRate,
        target_amount: (cashOutAmount * orbToUsdRate).toFixed(2),
        processing_fee: (cashOutAmount * orbToUsdRate * 0.05).toFixed(2), // 5% fee
        net_amount: (cashOutAmount * orbToUsdRate * 0.95).toFixed(2)
      };

      // In production, this would call the payment API
      // const response = await fetch('/api/orb/cash-out', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(cashOutData)
      // });

      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast.success(`Successfully initiated cash out of ${cashOutAmount} ORBs for $${cashOutData.net_amount} USDC (after fees)`);
      setShowCashOutModal(false);
      setCashOutAmount(0);

      if (onCashOut) {
        onCashOut(cashOutAmount);
      }
    } catch (error) {
      console.error('Cash out error:', error);
      toast.error('Failed to process cash out request');
    } finally {
      setLoading(false);
    }
  };

  // Animation for ORB earning
  const [showEarningAnimation, setShowEarningAnimation] = useState(false);
  const triggerEarningAnimation = (amount) => {
    setShowEarningAnimation(true);
    setTimeout(() => setShowEarningAnimation(false), 2000);
  };

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-800/20 border-2 border-yellow-200 dark:border-yellow-700 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">💰</span>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">ORB Wallet</h3>
            </div>
            <Chip color="warning" variant="flat" size="sm">
              Active
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Balance Display */}
          <div className="text-center mb-6 relative">
            <motion.div
              className="text-4xl font-bold text-yellow-600 mb-2"
              animate={showEarningAnimation ? { scale: [1, 1.1, 1] } : {}}
              transition={{ duration: 0.5 }}
            >
              {orbBalance.toLocaleString()} ORBs
            </motion.div>
            <div className="text-lg text-default-600 mb-1">
              ≈ ${usdcValue} USDC
            </div>
            <div className="text-sm text-default-500">
              Rate: 1 ORB = ${orbToUsdRate} USDC
            </div>

            {/* Earning Animation */}
            <AnimatePresence>
              {showEarningAnimation && (
                <motion.div
                  className="absolute top-0 left-1/2 transform -translate-x-1/2 text-green-500 font-bold"
                  initial={{ opacity: 0, y: 0, scale: 0.8 }}
                  animate={{ opacity: 1, y: -30, scale: 1 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 2 }}
                >
                  +50 ORBs! 🎉
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Recent Earnings */}
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-default-700 mb-3">Recent Earnings:</h4>
            <div className="space-y-2">
              {recentTransactions.slice(0, 3).map((transaction, idx) => (
                <div key={idx} className="flex justify-between items-center text-sm">
                  <div>
                    <div className="font-medium text-green-600">+{transaction.amount} ORBs</div>
                    <div className="text-xs text-default-500">{transaction.source}</div>
                  </div>
                  <div className="text-xs text-default-400">
                    {transaction.timeAgo || 'Just now'}
                  </div>
                </div>
              ))}
              
              {recentTransactions.length === 0 && (
                <div className="text-sm text-default-500 text-center py-2">
                  No recent transactions
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              color="primary" 
              variant="flat" 
              size="sm" 
              className="flex-1"
              onPress={() => onViewHistory && onViewHistory()}
            >
              📊 View History
            </Button>
            <Button 
              color="success" 
              size="sm" 
              className="flex-1"
              onPress={() => setShowCashOutModal(true)}
              isDisabled={orbBalance < 10} // Minimum cash out
            >
              💵 Cash Out
            </Button>
          </div>

          {/* Minimum cash out notice */}
          {orbBalance < 10 && (
            <div className="text-xs text-default-500 text-center mt-2">
              Minimum 10 ORBs required to cash out
            </div>
          )}
        </CardBody>
      </Card>

      {/* Cash Out Modal */}
      <Modal 
        isOpen={showCashOutModal} 
        onClose={() => setShowCashOutModal(false)}
        size="md"
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-xl font-semibold">💵 Cash Out ORBs</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-lg font-semibold mb-2">
                  Available Balance: {orbBalance} ORBs
                </div>
                <div className="text-default-600">
                  ≈ ${usdcValue} USDC
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Amount to Cash Out (ORBs)
                </label>
                <input
                  type="number"
                  min="10"
                  max={orbBalance}
                  value={cashOutAmount}
                  onChange={(e) => setCashOutAmount(Number(e.target.value))}
                  className="w-full p-3 border border-default-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Enter amount..."
                />
                <div className="text-sm text-default-500 mt-1">
                  You will receive: ${(cashOutAmount * orbToUsdRate).toFixed(2)} USDC
                </div>
              </div>

              <div className="bg-warning-50 border border-warning-200 rounded-lg p-3">
                <div className="text-sm text-warning-700">
                  <strong>Important:</strong> Cash out transactions are processed within 24 hours. 
                  A 5% processing fee applies to all cash outs.
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="flat"
                  onPress={() => setCashOutAmount(Math.floor(orbBalance * 0.25))}
                >
                  25%
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onPress={() => setCashOutAmount(Math.floor(orbBalance * 0.5))}
                >
                  50%
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onPress={() => setCashOutAmount(Math.floor(orbBalance * 0.75))}
                >
                  75%
                </Button>
                <Button
                  size="sm"
                  variant="flat"
                  onPress={() => setCashOutAmount(orbBalance)}
                >
                  Max
                </Button>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={() => setShowCashOutModal(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              color="success" 
              onPress={handleCashOut}
              isLoading={loading}
              disabled={cashOutAmount < 10 || cashOutAmount > orbBalance}
            >
              {loading ? 'Processing...' : `Cash Out ${cashOutAmount} ORBs`}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ORBWallet;
