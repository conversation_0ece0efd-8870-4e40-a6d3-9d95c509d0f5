/**
 * Improved Agreement Generator
 * 
 * This module provides an enhanced agreement generator that properly handles:
 * 1. Project type detection and terminology replacement
 * 2. Exhibit generation for different project types
 * 3. Company/contributor information handling
 * 4. Date and location formatting
 */

import { TEMPLATE_TYPES } from './templateManager';
import { ImprovedAgreementGenerator } from './improvedAgreementGenerator';

// Create a singleton instance
const improvedGenerator = new ImprovedAgreementGenerator();

/**
 * Generate a complete agreement based on project data
 * @param {Object} project - The project data
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - The customized agreement
 */
export const generateAgreement = async (project, options = {}) => {
  try {
    // Get the template type from options or use the default
    const templateType = options.templateType || TEMPLATE_TYPES.STANDARD;

    // Load the template using the template manager
    const templateText = await improvedGenerator.loadTemplate(templateType);

    // Generate the agreement using our improved generator
    return improvedGenerator.generateAgreement(templateText, project, options);
  } catch (error) {
    console.error('Error generating agreement:', error);
    throw error;
  }
};

/**
 * Regenerate an agreement for an existing project
 * @param {Object} project - The project data
 * @param {Object} options - Additional options
 * @returns {Promise<string>} - The regenerated agreement
 */
export const regenerateAgreement = async (project, options = {}) => {
  // Always use current date for regenerated agreements
  const today = new Date();
  return generateAgreement(project, {
    ...options,
    agreementDate: today
  });
};

export default {
  generateAgreement,
  regenerateAgreement,
  ImprovedAgreementGenerator
};
