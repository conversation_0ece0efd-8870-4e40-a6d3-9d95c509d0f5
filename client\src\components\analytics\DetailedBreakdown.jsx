import React, { useState } from 'react';
import { Card, CardBody, CardHeader, Button, Progress, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Detailed Breakdown Widget - 6x2 Bento Grid Component
 * 
 * Features:
 * - Comprehensive data breakdown by multiple categories
 * - Interactive progress bars and visualizations
 * - Export and drill-down capabilities
 * - Tabbed interface for different breakdown types
 */
const DetailedBreakdown = ({ data, period, className = "" }) => {
  const [selectedBreakdown, setSelectedBreakdown] = useState('project-type');

  // Mock detailed breakdown data
  const breakdownData = {
    'project-type': {
      title: 'By Project Type',
      icon: '💼',
      items: [
        { name: 'Development', value: 38400, percentage: 81, count: 65, color: 'bg-blue-500' },
        { name: 'Design', value: 6200, percentage: 13, count: 15, color: 'bg-purple-500' },
        { name: 'Testing', value: 2600, percentage: 6, count: 9, color: 'bg-green-500' }
      ]
    },
    'timeline': {
      title: 'By Timeline',
      icon: '⏰',
      items: [
        { name: 'Urgent (1-3 days)', value: 14200, percentage: 30, count: 28, color: 'bg-red-500' },
        { name: 'Short (1 week)', value: 19800, percentage: 42, count: 35, color: 'bg-orange-500' },
        { name: 'Medium (2-4 weeks)', value: 13200, percentage: 28, count: 26, color: 'bg-yellow-500' }
      ]
    },
    'difficulty': {
      title: 'By Difficulty',
      icon: '🏆',
      items: [
        { name: 'Easy (1-3★)', value: 9400, percentage: 20, count: 35, color: 'bg-green-500' },
        { name: 'Medium (4-6★)', value: 24100, percentage: 51, count: 38, color: 'bg-blue-500' },
        { name: 'Hard (7-10★)', value: 13700, percentage: 29, count: 16, color: 'bg-purple-500' }
      ]
    },
    'performance': {
      title: 'By Performance',
      icon: '📊',
      items: [
        { name: 'Excellent (95-100%)', value: 28300, percentage: 60, count: 53, color: 'bg-green-500' },
        { name: 'Good (85-94%)', value: 14200, percentage: 30, count: 28, color: 'bg-blue-500' },
        { name: 'Average (70-84%)', value: 4700, percentage: 10, count: 8, color: 'bg-yellow-500' }
      ]
    }
  };

  const currentBreakdown = breakdownData[selectedBreakdown];

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className={`detailed-breakdown ${className}`}>
      <Card className="bg-gradient-to-br from-slate-50 to-gray-100 dark:from-slate-900/20 dark:to-gray-800/20 border-2 border-slate-200 dark:border-slate-700">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">📊</span>
              <h3 className="text-lg font-semibold">Detailed Breakdown</h3>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="flat">
                Export Data
              </Button>
              <Button size="sm" variant="flat">
                Compare Periods
              </Button>
              <Button size="sm" variant="flat">
                Drill Down
              </Button>
              <Button size="sm" variant="flat">
                Custom Report
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Breakdown Type Selector */}
          <Tabs
            selectedKey={selectedBreakdown}
            onSelectionChange={setSelectedBreakdown}
            variant="bordered"
            className="mb-6"
          >
            <Tab
              key="project-type"
              title={
                <div className="flex items-center gap-2">
                  <span>💼</span>
                  <span>Project Type</span>
                </div>
              }
            />
            <Tab
              key="timeline"
              title={
                <div className="flex items-center gap-2">
                  <span>⏰</span>
                  <span>Timeline</span>
                </div>
              }
            />
            <Tab
              key="difficulty"
              title={
                <div className="flex items-center gap-2">
                  <span>🏆</span>
                  <span>Difficulty</span>
                </div>
              }
            />
            <Tab
              key="performance"
              title={
                <div className="flex items-center gap-2">
                  <span>📊</span>
                  <span>Performance</span>
                </div>
              }
            />
          </Tabs>

          {/* Breakdown Content */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <span className="text-xl">{currentBreakdown.icon}</span>
              <h4 className="text-lg font-semibold">{currentBreakdown.title}</h4>
            </div>

            {/* Breakdown Items */}
            <div className="space-y-4">
              {currentBreakdown.items.map((item, index) => (
                <motion.div
                  key={item.name}
                  className="p-4 bg-white/50 dark:bg-slate-800/50 rounded-lg"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                      <span className="font-medium">{item.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(item.value)} ({item.percentage}%)</div>
                      <div className="text-sm text-default-600">{item.count} missions</div>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="relative">
                    <div className="w-full bg-default-200 rounded-full h-3">
                      <motion.div
                        className={`h-3 rounded-full ${item.color}`}
                        initial={{ width: 0 }}
                        animate={{ width: `${item.percentage}%` }}
                        transition={{ duration: 1, delay: index * 0.2 }}
                      />
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-xs font-medium text-white mix-blend-difference">
                        {item.percentage}%
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Summary Stats */}
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h5 className="font-semibold mb-3">Summary Statistics</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {currentBreakdown.items.reduce((sum, item) => sum + item.count, 0)}
                  </div>
                  <div className="text-sm text-default-600">Total Items</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(currentBreakdown.items.reduce((sum, item) => sum + item.value, 0))}
                  </div>
                  <div className="text-sm text-default-600">Total Value</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round(currentBreakdown.items.reduce((sum, item) => sum + item.value, 0) / currentBreakdown.items.reduce((sum, item) => sum + item.count, 0))}
                  </div>
                  <div className="text-sm text-default-600">Avg Value</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {currentBreakdown.items.length}
                  </div>
                  <div className="text-sm text-default-600">Categories</div>
                </div>
              </div>
            </div>

            {/* Insights */}
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <h5 className="font-semibold mb-2 flex items-center gap-2">
                <span>💡</span>
                Key Insights
              </h5>
              <ul className="text-sm space-y-1">
                <li>• {currentBreakdown.items[0].name} represents the largest segment at {currentBreakdown.items[0].percentage}%</li>
                <li>• Average value per mission: {formatCurrency(Math.round(currentBreakdown.items.reduce((sum, item) => sum + item.value, 0) / currentBreakdown.items.reduce((sum, item) => sum + item.count, 0)))}</li>
                <li>• Most efficient category: {currentBreakdown.items.reduce((max, item) => (item.value / item.count) > (max.value / max.count) ? item : max).name}</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default DetailedBreakdown;
