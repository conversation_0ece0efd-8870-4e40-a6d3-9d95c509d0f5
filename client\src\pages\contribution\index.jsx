import axios from "axios";
import { useState, useEffect, useContext } from "react";
import { Link } from "react-router-dom";
import moment from "moment/moment";
import LoadingAnimation from "../../components/layout/LoadingAnimation";
import { UserContext } from "../../../contexts/netlify-identity.context";

const ContributionIndex = () => {
  const { currentUser } = useContext(UserContext);

  const [contributions, setcontributions] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchContributions = async () => {
      setError(null); // Reset error state
      try {
        if (!currentUser) {
          setError("User not authenticated. Please log in.");
          return;
        }

        if (!contributions) {
          // Netlify Identity token is automatically added by our axios interceptor
          axios
            .get("/api/contribution/index")
            .then(({ data }) => {
              setcontributions(data);
            })
            .catch((error) => {
              console.error("Error fetching contribution data:", error);
            });
        }
      } catch (error) {
        console.error("Error fetching contribution data:", error);
        setError("Failed to load contribution data.");
      }
    };
    fetchContributions();
  }, [currentUser, contributions]);
  if (error) {
    return (
      <div className="alert alert-danger text-center" role="alert">
        {error}
      </div>
    );
  }

  return (
    <>
      <div className="container mt-5">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1>Contributions</h1>
          <Link
            to="/contribution/new"
            className="btn btn-primary btn-lg fw-bold"
          >
            +
          </Link>
        </div>

        {/* Ensure contributions is not null or undefined before accessing contributions[0].name */}
        {contributions && contributions.length > 0 ? (
          <ul className="list-group">
            <li
              className="list-group-item"
              style={{
                backgroundColor: "#f8f9fa",
                color: "#495057",
                padding: "15px",
                borderRadius: "0.5rem 0.5rem 0 0",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div className="row fw-bold">
                <div className="col-md-4">Date Added</div>
                <div className="col-md-4">Contribution ID</div>
                <div className="col-md-4">Contributor</div>
              </div>
            </li>
            {contributions.map((contribution) => {
              return (
                <li key={contribution._id} className="list-group-item">
                  <div className="row">
                    <div className="col-md-4">
                      {moment(contribution.dateCreated).format("l")}
                    </div>
                    <div className="col-md-4">
                      <Link
                        to={`/contribution/${contribution._id}`}
                        className="text-decoration-none text-primary"
                      >
                        {contribution._id}
                      </Link>
                    </div>
                    <div className="col-md-4">
                      <Link
                        to={`/user/${contribution.contributor._id}`}
                        className="text-decoration-none text-primary"
                      >
                        {contribution.contributor.displayName}
                      </Link>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        ) : (
          <LoadingAnimation />
        )}
      </div>
    </>
  );
};

export default ContributionIndex;
