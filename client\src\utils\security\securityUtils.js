// Security Utilities
// Authentication & Security Agent: Frontend security utilities and helpers
// Created: January 16, 2025

import { supabase } from '../supabase/supabase.utils';

/**
 * Security utility functions for the frontend
 */

// Input sanitization
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

// XSS protection for displaying user content
export const sanitizeForDisplay = (content) => {
  if (typeof content !== 'string') return content;
  
  const div = document.createElement('div');
  div.textContent = content;
  return div.innerHTML;
};

// Validate email format
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password strength validation
export const validatePasswordStrength = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [
    password.length >= minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar
  ].filter(Boolean).length;
  
  return {
    isValid: score >= 4,
    score: score,
    requirements: {
      minLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar
    },
    strength: score <= 2 ? 'weak' : score <= 3 ? 'medium' : 'strong'
  };
};

// Check if user has admin role
export const hasAdminRole = (user, requiredRole = null) => {
  if (!user || !user.is_admin) return false;
  
  // Super admin has all permissions
  if (user.admin_role === 'super_admin') return true;
  
  // Check specific role if required
  if (requiredRole && user.admin_role !== requiredRole) return false;
  
  return true;
};

// Check if user can perform admin action
export const canPerformAdminAction = (user, action) => {
  if (!user || !user.is_admin) return false;
  
  const rolePermissions = {
    super_admin: ['*'], // All permissions
    platform_admin: [
      'user_management',
      'content_moderation',
      'system_monitoring',
      'admin_actions'
    ],
    support_admin: [
      'user_support',
      'ticket_management',
      'user_communication'
    ],
    financial_admin: [
      'payment_management',
      'financial_reports',
      'dispute_resolution'
    ],
    content_moderator: [
      'content_moderation',
      'flag_review',
      'content_removal'
    ]
  };
  
  const userPermissions = rolePermissions[user.admin_role] || [];
  return userPermissions.includes('*') || userPermissions.includes(action);
};

// Rate limiting for client-side actions
class RateLimiter {
  constructor() {
    this.requests = new Map();
  }
  
  isAllowed(key, maxRequests = 10, windowMs = 60000) {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.requests.has(key)) {
      this.requests.set(key, []);
    }
    
    const requests = this.requests.get(key);
    
    // Remove old requests
    const validRequests = requests.filter(time => time > windowStart);
    this.requests.set(key, validRequests);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    
    return true;
  }
}

export const rateLimiter = new RateLimiter();

// Secure API request wrapper
export const secureApiRequest = async (url, options = {}) => {
  try {
    // Get current session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      throw new Error('No active session');
    }
    
    // Add authorization header
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
      ...options.headers
    };
    
    // Make request
    const response = await fetch(url, {
      ...options,
      headers
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Secure API request failed:', error);
    throw error;
  }
};

// Log security event
export const logSecurityEvent = async (eventType, description, eventData = {}) => {
  try {
    await secureApiRequest('/.netlify/functions/security-events', {
      method: 'POST',
      body: JSON.stringify({
        event_type: eventType,
        description: description,
        event_data: eventData
      })
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
};

// Content flagging
export const flagContent = async (contentType, contentId, reason, details = '') => {
  try {
    // Rate limit flagging
    const rateLimitKey = `flag_${contentType}_${contentId}`;
    if (!rateLimiter.isAllowed(rateLimitKey, 3, 300000)) { // 3 flags per 5 minutes
      throw new Error('Rate limit exceeded for flagging this content');
    }
    
    const response = await secureApiRequest('/.netlify/functions/content-moderation/flag', {
      method: 'POST',
      body: JSON.stringify({
        contentType,
        contentId,
        flagReason: reason,
        flagDetails: details
      })
    });
    
    return response;
  } catch (error) {
    console.error('Failed to flag content:', error);
    throw error;
  }
};

// Session security monitoring
export const monitorSession = () => {
  let lastActivity = Date.now();
  const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  const WARNING_TIME = 5 * 60 * 1000; // 5 minutes before timeout
  
  const updateActivity = () => {
    lastActivity = Date.now();
  };
  
  const checkSession = () => {
    const now = Date.now();
    const timeSinceActivity = now - lastActivity;
    
    if (timeSinceActivity > SESSION_TIMEOUT) {
      // Session expired
      logSecurityEvent('session_timeout', 'User session expired due to inactivity');
      supabase.auth.signOut();
      return;
    }
    
    if (timeSinceActivity > SESSION_TIMEOUT - WARNING_TIME) {
      // Show warning
      const remainingTime = Math.ceil((SESSION_TIMEOUT - timeSinceActivity) / 1000 / 60);
      console.warn(`Session will expire in ${remainingTime} minutes`);
    }
  };
  
  // Monitor user activity
  const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
  events.forEach(event => {
    document.addEventListener(event, updateActivity, true);
  });
  
  // Check session every minute
  const interval = setInterval(checkSession, 60000);
  
  // Cleanup function
  return () => {
    events.forEach(event => {
      document.removeEventListener(event, updateActivity, true);
    });
    clearInterval(interval);
  };
};

// Initialize security monitoring
export const initializeSecurity = () => {
  // Start session monitoring
  const cleanupSession = monitorSession();
  
  // Log security initialization
  logSecurityEvent('security_initialized', 'Frontend security monitoring started');
  
  return cleanupSession;
};

// Export all utilities
export default {
  sanitizeInput,
  sanitizeForDisplay,
  isValidEmail,
  validatePasswordStrength,
  hasAdminRole,
  canPerformAdminAction,
  rateLimiter,
  secureApiRequest,
  logSecurityEvent,
  flagContent,
  monitorSession,
  initializeSecurity
};
