import React, { useState, useEffect, useRef } from 'react';
import { generateAgreementPDF, savePDFToDevice, markdownToHTML } from '../../utils/pdf/pdfGenerator';
import html2canvas from 'html2canvas';
import { toast } from 'react-hot-toast';

const PDFPreview = ({ agreementText, metadata, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [error, setError] = useState(null);
  const previewRef = useRef(null);

  useEffect(() => {
    const preparePreview = async () => {
      try {
        setLoading(true);
        setError(null);

        // Convert markdown to HTML for display
        try {
          const htmlContent = markdownToHTML(agreementText);

          // Store the HTML content for display
          previewRef.current = {
            html: htmlContent
          };

          // Generate PDF for download only
          try {
            const pdfBlob = await generateAgreementPDF(htmlContent, metadata);
            previewRef.current.blob = pdfBlob;
          } catch (pdfError) {
            console.error('Error generating PDF for download:', pdfError);
            // We'll still show the HTML preview even if PDF generation fails
          }
        } catch (markdownError) {
          console.error('Error converting markdown to HTML:', markdownError);
          setError('Failed to format agreement text. Please try again.');
        }
      } catch (err) {
        console.error('Error preparing preview:', err);
        setError('Failed to prepare agreement preview. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    preparePreview();

    // Cleanup
    return () => {
      if (previewRef.current?.url) {
        URL.revokeObjectURL(previewRef.current.url);
      }
    };
  }, [agreementText, metadata]);

  const handleDownload = async () => {
    try {
      // If we already have a blob, use it
      if (previewRef.current?.blob) {
        // Ensure filename is properly formatted
        const filename = (metadata?.filename || 'contributor-agreement.pdf')
          .replace(/^@+/, ''); // Remove any leading @ symbols

        // This is the only place where we should trigger the download
        savePDFToDevice(previewRef.current.blob, filename);
        toast.success('Downloading PDF...');
      }
      // Otherwise generate a PDF on demand
      else if (previewRef.current?.html) {
        setLoading(true);
        const htmlContent = previewRef.current.html;
        const pdfBlob = await generateAgreementPDF(htmlContent, metadata);

        // Ensure filename is properly formatted
        const filename = (metadata?.filename || 'contributor-agreement.pdf')
          .replace(/^@+/, ''); // Remove any leading @ symbols

        // This is the only place where we should trigger the download
        savePDFToDevice(pdfBlob, filename);
        setLoading(false);
        toast.success('Downloading PDF...');
      } else {
        toast.error('Unable to generate PDF for download');
      }
    } catch (error) {
      console.error('Error downloading PDF:', error);
      setLoading(false);
      toast.error('Failed to download PDF');
    }
  };

  return (
    <div className="pdf-preview-overlay">
      <div className="pdf-preview-container">
        <div className="pdf-preview-header">
          <h3>PDF Preview</h3>
          <div className="pdf-preview-actions">
            <button
              className="btn btn-primary me-2"
              onClick={handleDownload}
              disabled={loading}
            >
              <i className="bi bi-download me-1"></i> Download
            </button>
            <button
              className="btn btn-secondary"
              onClick={onClose}
            >
              <i className="bi bi-x-lg me-1"></i> Close
            </button>
          </div>
        </div>

        <div className="pdf-preview-content">
          {loading && (
            <div className="pdf-preview-loading">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p>Generating PDF preview...</p>
            </div>
          )}

          {error && (
            <div className="pdf-preview-error">
              <i className="bi bi-exclamation-triangle-fill text-danger fs-1 mb-3"></i>
              <p>{error}</p>
              <button
                className="btn btn-outline-primary mt-3"
                onClick={onClose}
              >
                Close
              </button>
            </div>
          )}

          {!loading && !error && previewRef.current?.html && (
            <div className="agreement-preview-content">
              <div
                className="agreement-html-preview"
                dangerouslySetInnerHTML={{ __html: previewRef.current.html }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PDFPreview;
