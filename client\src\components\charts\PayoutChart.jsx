import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Area,
  AreaChart
} from 'recharts';
import { <PERSON>, CardBody, CardHeader, <PERSON>, Button } from '@heroui/react';

/**
 * Payout Chart Component - Payout History and Projections
 * 
 * Features:
 * - Line chart for payout trends
 * - Area chart for cumulative payouts
 * - Payout status indicators
 * - Interactive tooltips
 * - Responsive design
 */
const PayoutChart = ({ 
  data = [], 
  height = 300, 
  showLegend = true,
  showProjections = true,
  className = "",
  title = "Payout History"
}) => {
  const [chartType, setChartType] = React.useState('line');

  // Sample payout data
  const defaultData = [
    { 
      month: 'Jan',
      payouts: 2200,
      pending: 350,
      cumulative: 2200,
      projected: 2400,
      status: 'completed'
    },
    { 
      month: 'Feb',
      payouts: 2800,
      pending: 580,
      cumulative: 5000,
      projected: 3000,
      status: 'completed'
    },
    { 
      month: 'Mar',
      payouts: 2400,
      pending: 600,
      cumulative: 7400,
      projected: 2600,
      status: 'completed'
    },
    { 
      month: 'Apr',
      payouts: 3500,
      pending: 620,
      cumulative: 10900,
      projected: 3200,
      status: 'completed'
    },
    { 
      month: 'May',
      payouts: 3200,
      pending: 850,
      cumulative: 14100,
      projected: 3400,
      status: 'processing'
    },
    { 
      month: 'Jun',
      payouts: 0,
      pending: 4200,
      cumulative: 14100,
      projected: 4500,
      status: 'pending'
    }
  ];

  const chartData = data.length > 0 ? data : defaultData;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const dataPoint = payload[0].payload;
      return (
        <div className="bg-white dark:bg-slate-800 p-4 border border-default-200 rounded-lg shadow-lg">
          <p className="font-semibold text-default-900 dark:text-default-100 mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: ${entry.value.toLocaleString()}
            </p>
          ))}
          <div className="mt-2 pt-2 border-t border-default-200">
            <p className="text-sm text-default-600">
              Status: <span className={`font-medium ${
                dataPoint.status === 'completed' ? 'text-green-600' :
                dataPoint.status === 'processing' ? 'text-yellow-600' :
                'text-red-600'
              }`}>
                {dataPoint.status.charAt(0).toUpperCase() + dataPoint.status.slice(1)}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Render line chart
  const renderLineChart = () => (
    <LineChart
      data={chartData}
      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
    >
      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
      <XAxis 
        dataKey="month" 
        stroke="#64748b"
        fontSize={12}
      />
      <YAxis 
        stroke="#64748b"
        fontSize={12}
        tickFormatter={(value) => `$${value.toLocaleString()}`}
      />
      <Tooltip content={<CustomTooltip />} />
      {showLegend && <Legend />}
      
      <Line
        type="monotone"
        dataKey="payouts"
        stroke="#10b981"
        strokeWidth={3}
        dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
        activeDot={{ r: 6, stroke: '#10b981', strokeWidth: 2 }}
        name="Completed Payouts"
      />
      <Line
        type="monotone"
        dataKey="pending"
        stroke="#f59e0b"
        strokeWidth={2}
        dot={{ fill: '#f59e0b', strokeWidth: 2, r: 3 }}
        activeDot={{ r: 5, stroke: '#f59e0b', strokeWidth: 2 }}
        name="Pending Payouts"
        strokeDasharray="5 5"
      />
      {showProjections && (
        <Line
          type="monotone"
          dataKey="projected"
          stroke="#8b5cf6"
          strokeWidth={2}
          dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 3 }}
          activeDot={{ r: 5, stroke: '#8b5cf6', strokeWidth: 2 }}
          name="Projected"
          strokeDasharray="3 3"
        />
      )}
    </LineChart>
  );

  // Render area chart
  const renderAreaChart = () => (
    <AreaChart
      data={chartData}
      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
    >
      <defs>
        <linearGradient id="cumulativeGradient" x1="0" y1="0" x2="0" y2="1">
          <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
          <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
        </linearGradient>
      </defs>
      <CartesianGrid strokeDasharray="3 3" stroke="#e2e8f0" />
      <XAxis 
        dataKey="month" 
        stroke="#64748b"
        fontSize={12}
      />
      <YAxis 
        stroke="#64748b"
        fontSize={12}
        tickFormatter={(value) => `$${value.toLocaleString()}`}
      />
      <Tooltip content={<CustomTooltip />} />
      {showLegend && <Legend />}
      
      <Area
        type="monotone"
        dataKey="cumulative"
        stroke="#3b82f6"
        strokeWidth={2}
        fill="url(#cumulativeGradient)"
        name="Cumulative Payouts"
      />
    </AreaChart>
  );

  return (
    <Card className={`h-full ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <span className="text-2xl">💳</span>
            <h3 className="text-lg font-semibold">{title}</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant={chartType === 'line' ? 'solid' : 'flat'}
              color="primary"
              onPress={() => setChartType('line')}
            >
              Trends
            </Button>
            <Button
              size="sm"
              variant={chartType === 'area' ? 'solid' : 'flat'}
              color="primary"
              onPress={() => setChartType('area')}
            >
              Cumulative
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        <div style={{ width: '100%', height: height }}>
          <ResponsiveContainer>
            {chartType === 'line' ? renderLineChart() : renderAreaChart()}
          </ResponsiveContainer>
        </div>
      </CardBody>
    </Card>
  );
};

export default PayoutChart;
