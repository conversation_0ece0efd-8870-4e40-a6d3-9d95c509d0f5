import React, { lazy } from 'react';
import SafeLazyComponentWrapper from '../common/LazyComponentWrapper';

/**
 * Lazy-loaded Contribution Analytics Dashboard Component
 * 
 * This component lazy-loads the large ContributionAnalyticsDashboard component to improve
 * initial bundle size and page load performance.
 * 
 * Task: O3 Performance Optimization - Component Lazy Loading
 */

// Lazy load the ContributionAnalyticsDashboard component
const ContributionAnalyticsDashboard = lazy(() => import('./ContributionAnalyticsDashboard'));

const LazyContributionAnalyticsDashboard = (props) => {
  return (
    <SafeLazyComponentWrapper
      className="w-full"
      minHeight="600px"
      showSpinner={true}
    >
      <ContributionAnalyticsDashboard {...props} />
    </SafeLazyComponentWrapper>
  );
};

export default LazyContributionAnalyticsDashboard;
