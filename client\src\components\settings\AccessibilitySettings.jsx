import React from 'react';
import SoundToggle from '../theme/SoundToggle';

const AccessibilitySettings = () => {
  return (
    <div className="accessibility-settings">
      <h3 className="settings-section-title">Accessibility Settings</h3>
      
      <div className="settings-section">
        <div className="settings-section-description">
          <p>Customize your experience with these accessibility options.</p>
        </div>
        
        <div className="settings-options">
          <SoundToggle />
          
          <div className="settings-info">
            <div className="info-icon">
              <i className="bi bi-info-circle"></i>
            </div>
            <p className="info-text">
              When sound effects are disabled, no sounds will play when toggling between light and dark mode.
              This setting is saved to your account and will apply across all your devices.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccessibilitySettings;
