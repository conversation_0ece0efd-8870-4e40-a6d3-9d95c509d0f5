// EnhancedRevenuePage - Comprehensive revenue management with tabbed interface
// Implements enhanced revenue page following wireframe specifications
import React, { useState, useContext, Suspense } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Tabs, Tab, Button } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { 
  DollarSign, 
  BarChart3, 
  Settings,
  Download,
  TrendingUp,
  Wallet,
  Target
} from 'lucide-react';

// Lazy load components for better performance
const EnhancedRevenueDashboard = React.lazy(() => import('../../components/revenue/EnhancedRevenueDashboard'));
const RevenueAnalytics = React.lazy(() => import('../../components/revenue/RevenueAnalytics'));
const RevenueSettings = React.lazy(() => import('../../components/revenue/RevenueSettings'));
const RevenueTracker = React.lazy(() => import('../../components/revenue/RevenueTracker'));
const RevenueDistribution = React.lazy(() => import('../../components/payments/RevenueDistribution'));

const EnhancedRevenuePage = () => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedPeriod, setSelectedPeriod] = useState('6m');

  // Loading component for Suspense
  const LoadingComponent = ({ message = "Loading..." }) => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-default-600">{message}</p>
      </div>
    </div>
  );

  const handleExportData = () => {
    // Implement data export functionality
    console.log('Exporting revenue data...');
  };

  const handleQuickAction = (action) => {
    switch (action) {
      case 'withdraw':
        console.log('Opening withdrawal modal...');
        break;
      case 'goals':
        setActiveTab('settings');
        break;
      case 'analytics':
        setActiveTab('analytics');
        break;
      default:
        console.log('Unknown action:', action);
    }
  };

  return (
    <div className="enhanced-revenue-page min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold flex items-center gap-3">
                <DollarSign className="text-green-500" size={40} />
                Treasury
              </h1>
              <p className="text-gray-600 text-lg mt-2">
                Comprehensive revenue management and financial insights
              </p>
            </div>
            
            <div className="flex items-center gap-3">
              <Button
                variant="light"
                startContent={<Download size={18} />}
                onPress={handleExportData}
              >
                Export Data
              </Button>
              <Button
                color="primary"
                startContent={<Wallet size={18} />}
                onPress={() => handleQuickAction('withdraw')}
              >
                Withdraw Funds
              </Button>
            </div>
          </div>

          {/* Quick Stats Bar */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Card className="bg-gradient-to-r from-green-500 to-emerald-600 text-white">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Total Earnings</p>
                      <p className="text-2xl font-bold">$12,450</p>
                    </div>
                    <DollarSign size={24} className="text-green-200" />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <Card className="bg-gradient-to-r from-blue-500 to-cyan-600 text-white">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">This Month</p>
                      <p className="text-2xl font-bold">$3,200</p>
                    </div>
                    <TrendingUp size={24} className="text-blue-200" />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Card className="bg-gradient-to-r from-purple-500 to-indigo-600 text-white">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">ORB Balance</p>
                      <p className="text-2xl font-bold">15,420</p>
                    </div>
                    <Wallet size={24} className="text-purple-200" />
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="bg-gradient-to-r from-orange-500 to-red-600 text-white">
                <CardBody className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-100 text-sm">Goal Progress</p>
                      <p className="text-2xl font-bold">80%</p>
                    </div>
                    <Target size={24} className="text-orange-200" />
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="mb-6">
            <CardBody className="p-0">
              <Tabs 
                selectedKey={activeTab} 
                onSelectionChange={setActiveTab}
                variant="underlined"
                size="lg"
                classNames={{
                  tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-primary",
                  tab: "max-w-fit px-6 py-4 h-16",
                  tabContent: "group-data-[selected=true]:text-primary text-lg"
                }}
              >
                <Tab
                  key="dashboard"
                  title={
                    <div className="flex items-center space-x-2">
                      <DollarSign size={20} />
                      <span>Dashboard</span>
                    </div>
                  }
                />
                <Tab
                  key="analytics"
                  title={
                    <div className="flex items-center space-x-2">
                      <BarChart3 size={20} />
                      <span>Analytics</span>
                    </div>
                  }
                />
                <Tab
                  key="tracker"
                  title={
                    <div className="flex items-center space-x-2">
                      <TrendingUp size={20} />
                      <span>Tracker</span>
                    </div>
                  }
                />
                <Tab
                  key="distribution"
                  title={
                    <div className="flex items-center space-x-2">
                      <Wallet size={20} />
                      <span>Distribution</span>
                    </div>
                  }
                />
                <Tab
                  key="settings"
                  title={
                    <div className="flex items-center space-x-2">
                      <Settings size={20} />
                      <span>Settings</span>
                    </div>
                  }
                />
              </Tabs>
            </CardBody>
          </Card>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Suspense fallback={<LoadingComponent message="Loading revenue data..." />}>
            {activeTab === 'dashboard' && (
              <EnhancedRevenueDashboard 
                onQuickAction={handleQuickAction}
                period={selectedPeriod}
              />
            )}
            
            {activeTab === 'analytics' && (
              <RevenueAnalytics 
                period={selectedPeriod}
                onPeriodChange={setSelectedPeriod}
              />
            )}
            
            {activeTab === 'tracker' && (
              <div className="space-y-6">
                <Card>
                  <CardBody className="p-6">
                    <div className="text-center py-8">
                      <TrendingUp size={48} className="mx-auto mb-4 text-gray-400" />
                      <h3 className="text-xl font-semibold mb-2">Revenue Tracker</h3>
                      <p className="text-gray-600 mb-4">
                        Track revenue entries and manage project-specific earnings
                      </p>
                      <p className="text-sm text-gray-500">
                        Select a specific project to view detailed revenue tracking
                      </p>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
            
            {activeTab === 'distribution' && (
              <div className="space-y-6">
                <Card>
                  <CardBody className="p-6">
                    <div className="text-center py-8">
                      <Wallet size={48} className="mx-auto mb-4 text-gray-400" />
                      <h3 className="text-xl font-semibold mb-2">Revenue Distribution</h3>
                      <p className="text-gray-600 mb-4">
                        Configure and manage revenue sharing across projects
                      </p>
                      <p className="text-sm text-gray-500">
                        Select a specific project to configure revenue distribution
                      </p>
                    </div>
                  </CardBody>
                </Card>
              </div>
            )}
            
            {activeTab === 'settings' && (
              <RevenueSettings />
            )}
          </Suspense>
        </motion.div>

        {/* Quick Actions Floating Panel */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8 }}
          className="fixed bottom-6 right-6 z-50"
        >
          <Card className="shadow-lg">
            <CardBody className="p-4">
              <div className="flex flex-col gap-2">
                <Button
                  size="sm"
                  color="success"
                  variant="flat"
                  onPress={() => handleQuickAction('withdraw')}
                  className="w-full"
                >
                  Quick Withdraw
                </Button>
                <Button
                  size="sm"
                  color="primary"
                  variant="flat"
                  onPress={() => handleQuickAction('goals')}
                  className="w-full"
                >
                  Set Goals
                </Button>
                <Button
                  size="sm"
                  color="secondary"
                  variant="flat"
                  onPress={() => handleQuickAction('analytics')}
                  className="w-full"
                >
                  View Analytics
                </Button>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default EnhancedRevenuePage;
