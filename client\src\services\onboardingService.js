// Onboarding Service - Frontend integration for onboarding flow
import { supabase } from '../utils/supabase/supabase.utils';

class OnboardingService {
  constructor() {
    this.baseUrl = '/.netlify/functions/onboarding';
    this.currentSession = null;
    this.listeners = new Set();
  }

  // Get auth headers for API calls
  async getAuthHeaders() {
    const { data: { session } } = await supabase.auth.getSession();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session?.access_token}`
    };
  }

  // Initialize onboarding session
  async initializeOnboarding(deviceInfo = {}) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/initialize`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          device_info: {
            userAgent: navigator.userAgent,
            screen: {
              width: window.screen.width,
              height: window.screen.height
            },
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            ...deviceInfo
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.currentSession = result.session_id;
      this.notifyListeners('session_initialized', result);
      
      return result;
    } catch (error) {
      console.error('Error initializing onboarding:', error);
      throw error;
    }
  }

  // Update onboarding progress
  async updateProgress(stepNumber, stepData = {}) {
    try {
      if (!this.currentSession) {
        throw new Error('No active onboarding session');
      }

      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/progress`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          session_id: this.currentSession,
          step_number: stepNumber,
          step_data: {
            timestamp: Date.now(),
            ...stepData
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('progress_updated', { stepNumber, stepData, result });
      
      return result;
    } catch (error) {
      console.error('Error updating onboarding progress:', error);
      throw error;
    }
  }

  // Complete onboarding
  async completeOnboarding(completionData = {}) {
    try {
      if (!this.currentSession) {
        throw new Error('No active onboarding session');
      }

      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/complete`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          session_id: this.currentSession,
          completion_data: {
            timestamp: Date.now(),
            ...completionData
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.currentSession = null;
      this.notifyListeners('onboarding_completed', result);
      
      return result;
    } catch (error) {
      console.error('Error completing onboarding:', error);
      throw error;
    }
  }

  // Get onboarding status
  async getOnboardingStatus() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/status`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      // Set current session if there's an incomplete one
      if (result.current_session && !result.onboarding_completed) {
        this.currentSession = result.current_session.id;
      }

      this.notifyListeners('status_loaded', result);
      
      return result;
    } catch (error) {
      console.error('Error getting onboarding status:', error);
      throw error;
    }
  }

  // Get onboarding analytics
  async getOnboardingAnalytics() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/analytics`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.analytics;
    } catch (error) {
      console.error('Error getting onboarding analytics:', error);
      throw error;
    }
  }

  // Save state to localStorage as backup
  saveStateToLocalStorage(state) {
    try {
      localStorage.setItem('onboarding_state_backup', JSON.stringify({
        ...state,
        timestamp: Date.now(),
        session_id: this.currentSession
      }));
    } catch (error) {
      console.warn('Could not save onboarding state to localStorage:', error);
    }
  }

  // Load state from localStorage
  loadStateFromLocalStorage() {
    try {
      const saved = localStorage.getItem('onboarding_state_backup');
      if (saved) {
        const state = JSON.parse(saved);
        // Only use if less than 24 hours old
        if (Date.now() - state.timestamp < 24 * 60 * 60 * 1000) {
          return state;
        }
      }
    } catch (error) {
      console.warn('Could not load onboarding state from localStorage:', error);
    }
    return null;
  }

  // Clear localStorage backup
  clearLocalStorageBackup() {
    try {
      localStorage.removeItem('onboarding_state_backup');
    } catch (error) {
      console.warn('Could not clear onboarding state from localStorage:', error);
    }
  }

  // Event listener management
  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  notifyListeners(event, data) {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in onboarding service listener:', error);
      }
    });
  }

  // Utility methods
  formatTimeToComplete(milliseconds) {
    if (!milliseconds) return 'Unknown';
    
    const minutes = Math.floor(milliseconds / 60000);
    const seconds = Math.floor((milliseconds % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }

  calculateCompletionRate(analytics) {
    if (!analytics || analytics.total_sessions === 0) return 0;
    return Math.round((analytics.completed_sessions / analytics.total_sessions) * 100);
  }

  // Recovery methods for network failures
  async recoverSession() {
    try {
      const status = await this.getOnboardingStatus();
      
      if (status.current_session && !status.onboarding_completed) {
        this.currentSession = status.current_session.id;
        return status.current_session;
      }
      
      return null;
    } catch (error) {
      console.error('Error recovering onboarding session:', error);
      return null;
    }
  }

  // Batch update for offline support (future enhancement)
  async syncOfflineChanges(changes) {
    // This would handle syncing changes made while offline
    // For now, just log the intent
    console.log('Offline changes to sync:', changes);
    // Implementation would depend on offline storage strategy
  }
}

// Create singleton instance
const onboardingService = new OnboardingService();

export default onboardingService;
