-- Simple fix for missing database columns - no fallbacks, just proper database structure
-- Run this to fix the 400 errors by adding the columns the application expects

-- ============================================================================
-- ADD MISSING COLUMNS TO TASKS TABLE
-- ============================================================================

-- Add due_date column to tasks table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'tasks' 
        AND column_name = 'due_date'
    ) THEN
        ALTER TABLE public.tasks ADD COLUMN due_date TIMESTAMPTZ;
        RAISE NOTICE 'Added due_date column to tasks table';
    ELSE
        RAISE NOTICE 'due_date column already exists in tasks table';
    END IF;
END $$;

-- ============================================================================
-- ADD MISSING COLUMNS TO PROJECT_CONTRIBUTORS TABLE
-- ============================================================================

-- Add user_id column to project_contributors table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'project_contributors' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE public.project_contributors ADD COLUMN user_id UUID REFERENCES auth.users(id);
        RAISE NOTICE 'Added user_id column to project_contributors table';
    ELSE
        RAISE NOTICE 'user_id column already exists in project_contributors table';
    END IF;
END $$;

-- ============================================================================
-- ENSURE CONTRIBUTION_TRACKING_CONFIG TABLE EXISTS
-- ============================================================================

-- Create contribution_tracking_config table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.contribution_tracking_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    tracking_enabled BOOLEAN DEFAULT true,
    point_system JSONB DEFAULT '{"base_points": 1}',
    reward_structure JSONB DEFAULT '{"type": "equal_split"}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    UNIQUE(project_id)
);

-- Add RLS policy for contribution_tracking_config
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'contribution_tracking_config' 
        AND policyname = 'Users can manage their project configs'
    ) THEN
        CREATE POLICY "Users can manage their project configs" ON public.contribution_tracking_config
            FOR ALL USING (
                project_id IN (
                    SELECT id FROM public.projects 
                    WHERE created_by = auth.uid()
                )
            );
        RAISE NOTICE 'Created RLS policy for contribution_tracking_config';
    ELSE
        RAISE NOTICE 'RLS policy already exists for contribution_tracking_config';
    END IF;
END $$;

-- Enable RLS on contribution_tracking_config
ALTER TABLE public.contribution_tracking_config ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- CREATE A REAL PROJECT FOR THE TEST USER (NOT A FALLBACK)
-- ============================================================================

-- Create a real project for the test user if they don't have one
INSERT INTO public.projects (
    name,
    description,
    status,
    created_by,
    project_type
) 
SELECT 
    'My First Project',
    'Getting started with project management',
    'active',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    'software'
WHERE NOT EXISTS (
    SELECT 1 FROM public.projects 
    WHERE created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
    AND status IN ('active', 'planning')
);

-- ============================================================================
-- ADD SAMPLE TASKS FOR THE REAL PROJECT
-- ============================================================================

-- Add some sample tasks to the user's project
INSERT INTO public.tasks (
    project_id,
    title,
    description,
    status,
    created_by,
    due_date
)
SELECT 
    p.id,
    'Setup Development Environment',
    'Configure development tools and workspace',
    'todo',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    NOW() + INTERVAL '7 days'
FROM public.projects p
WHERE p.created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
AND p.status = 'active'
AND NOT EXISTS (
    SELECT 1 FROM public.tasks t 
    WHERE t.project_id = p.id 
    AND t.title = 'Setup Development Environment'
)
LIMIT 1;

-- Add project contributor entry
INSERT INTO public.project_contributors (
    project_id,
    user_id,
    role,
    is_admin
)
SELECT 
    p.id,
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    'owner',
    true
FROM public.projects p
WHERE p.created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
AND p.status = 'active'
AND NOT EXISTS (
    SELECT 1 FROM public.project_contributors pc 
    WHERE pc.project_id = p.id 
    AND pc.user_id = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
)
LIMIT 1;

-- Add contribution tracking config for the project
INSERT INTO public.contribution_tracking_config (
    project_id,
    tracking_enabled,
    point_system,
    reward_structure
)
SELECT 
    p.id,
    true,
    '{"base_points": 1, "difficulty_multiplier": {"easy": 1, "medium": 2, "hard": 3}}',
    '{"type": "equal_split"}'
FROM public.projects p
WHERE p.created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
AND p.status = 'active'
AND NOT EXISTS (
    SELECT 1 FROM public.contribution_tracking_config ctc 
    WHERE ctc.project_id = p.id
)
LIMIT 1;

COMMIT;
