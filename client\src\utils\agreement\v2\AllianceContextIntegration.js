/**
 * Alliance Context Integration
 * 
 * Integrates alliance data (company information, jurisdiction, business entity details)
 * into agreement generation for proper legal context and compliance.
 */

import { supabase } from '../../supabase/supabase.utils.js';

export class AllianceContextIntegration {
  constructor() {
    this.jurisdictionDefaults = {
      'Delaware': {
        state: 'Delaware',
        governingLaw: 'Delaware',
        disputeResolution: 'Delaware Court of Chancery',
        corporationType: 'Delaware corporation'
      },
      'California': {
        state: 'California',
        governingLaw: 'California',
        disputeResolution: 'California Superior Court',
        corporationType: 'California corporation'
      },
      'Florida': {
        state: 'Florida',
        governingLaw: 'Florida',
        disputeResolution: 'Florida Circuit Court',
        corporationType: 'Florida corporation'
      },
      'New York': {
        state: 'New York',
        governingLaw: 'New York',
        disputeResolution: 'New York Supreme Court',
        corporationType: 'New York corporation'
      }
    };
  }

  /**
   * Fetch comprehensive alliance context for agreement generation
   */
  async fetchAllianceContext(allianceId) {
    console.log('🏢 Fetching alliance context for agreement generation...');
    
    try {
      // Fetch alliance with related company and member data
      const { data: alliance, error: allianceError } = await supabase
        .from('teams')
        .select(`
          *,
          company:companies!teams_company_id_fkey(*),
          creator:profiles!teams_created_by_fkey(*),
          members:team_members!team_members_team_id_fkey(
            *,
            profile:profiles!team_members_user_id_fkey(*)
          )
        `)
        .eq('id', allianceId)
        .single();
      
      if (allianceError) throw allianceError;
      
      // Process and structure the alliance context
      const context = await this.processAllianceContext(alliance);
      
      console.log('✅ Alliance context fetched and processed');
      
      return context;
      
    } catch (error) {
      console.error('❌ Failed to fetch alliance context:', error);
      throw new Error(`Failed to fetch alliance context: ${error.message}`);
    }
  }

  /**
   * Process alliance data into structured context
   */
  async processAllianceContext(alliance) {
    const context = {
      alliance: {
        id: alliance.id,
        name: alliance.name,
        description: alliance.description,
        type: alliance.alliance_type,
        isBusinessEntity: alliance.is_business_entity,
        industry: alliance.industry,
        status: alliance.status,
        createdAt: alliance.created_at
      },
      company: null,
      legalContext: null,
      signatoryInfo: null,
      members: []
    };

    // Process company information if it's a business entity
    if (alliance.is_business_entity && alliance.company) {
      context.company = this.processCompanyInfo(alliance.company);
      context.legalContext = this.processLegalContext(alliance.company, alliance);
      context.signatoryInfo = this.processSignatoryInfo(alliance.company);
    } else {
      // Handle individual/personal alliance
      context.company = this.processIndividualAllianceAsCompany(alliance);
      context.legalContext = this.processIndividualLegalContext(alliance);
      context.signatoryInfo = this.processIndividualSignatoryInfo(alliance);
    }

    // Process member information
    if (alliance.members && alliance.members.length > 0) {
      context.members = alliance.members.map(member => ({
        id: member.id,
        userId: member.user_id,
        role: member.role,
        status: member.status,
        joinedAt: member.joined_at,
        profile: member.profile ? {
          fullName: member.profile.full_name,
          email: member.profile.email,
          title: member.profile.title
        } : null
      }));
    }

    return context;
  }

  /**
   * Process company information for business entities
   */
  processCompanyInfo(company) {
    return {
      id: company.id,
      legalName: company.legal_name,
      businessName: company.business_name || company.legal_name,
      entityType: company.entity_type,
      incorporationState: company.incorporation_state,
      incorporationDate: company.incorporation_date,
      ein: company.ein,
      address: this.formatCompanyAddress(company),
      contactInfo: {
        email: company.contact_email,
        phone: company.contact_phone,
        website: company.website
      },
      billingInfo: {
        email: company.billing_email,
        address: company.billing_address || this.formatCompanyAddress(company)
      }
    };
  }

  /**
   * Process individual alliance as company (for solo entrepreneurs)
   */
  processIndividualAllianceAsCompany(alliance) {
    const creator = alliance.creator;
    
    return {
      id: null,
      legalName: alliance.business_name || `${alliance.name} LLC`,
      businessName: alliance.business_name || alliance.name,
      entityType: 'LLC', // Default for individual alliances
      incorporationState: alliance.jurisdiction || 'Delaware',
      incorporationDate: alliance.created_at,
      ein: null,
      address: creator?.business_address || creator?.address || '[Business Address]',
      contactInfo: {
        email: creator?.email || alliance.contact_email || '[<EMAIL>]',
        phone: creator?.phone || '[Phone Number]',
        website: alliance.website || null
      },
      billingInfo: {
        email: creator?.email || alliance.contact_email || '[<EMAIL>]',
        address: creator?.business_address || creator?.address || '[Billing Address]'
      }
    };
  }

  /**
   * Process legal context for business entities
   */
  processLegalContext(company, alliance) {
    const jurisdiction = company.incorporation_state || alliance.jurisdiction || 'Delaware';
    const defaults = this.jurisdictionDefaults[jurisdiction] || this.jurisdictionDefaults['Delaware'];
    
    return {
      jurisdiction: jurisdiction,
      governingLaw: company.governing_law || defaults.governingLaw,
      disputeResolution: company.dispute_resolution || defaults.disputeResolution,
      corporationType: `${jurisdiction} ${company.entity_type?.toLowerCase() || 'corporation'}`,
      complianceRequirements: this.getComplianceRequirements(jurisdiction, company.entity_type),
      taxClassification: company.tax_classification || this.getDefaultTaxClassification(company.entity_type)
    };
  }

  /**
   * Process legal context for individual alliances
   */
  processIndividualLegalContext(alliance) {
    const jurisdiction = alliance.jurisdiction || 'Delaware';
    const defaults = this.jurisdictionDefaults[jurisdiction] || this.jurisdictionDefaults['Delaware'];
    
    return {
      jurisdiction: jurisdiction,
      governingLaw: defaults.governingLaw,
      disputeResolution: defaults.disputeResolution,
      corporationType: `${jurisdiction} LLC`,
      complianceRequirements: this.getComplianceRequirements(jurisdiction, 'LLC'),
      taxClassification: 'Single Member LLC'
    };
  }

  /**
   * Process signatory information for business entities
   */
  processSignatoryInfo(company) {
    return {
      name: company.ceo_name || company.primary_contact_name || '[CEO Name]',
      title: company.ceo_title || 'Chief Executive Officer',
      email: company.ceo_email || company.contact_email || '[<EMAIL>]',
      authority: 'Full signing authority as Chief Executive Officer',
      dateOfAuthority: company.ceo_appointment_date || company.incorporation_date
    };
  }

  /**
   * Process signatory information for individual alliances
   */
  processIndividualSignatoryInfo(alliance) {
    const creator = alliance.creator;
    
    return {
      name: creator?.full_name || '[Owner Name]',
      title: creator?.title || 'Owner',
      email: creator?.email || '[<EMAIL>]',
      authority: 'Full signing authority as Owner',
      dateOfAuthority: alliance.created_at
    };
  }

  /**
   * Format company address
   */
  formatCompanyAddress(company) {
    if (company.address) return company.address;
    
    const addressParts = [
      company.street_address,
      company.city,
      company.state,
      company.zip_code
    ].filter(Boolean);
    
    return addressParts.length > 0 ? addressParts.join(', ') : '[Company Address]';
  }

  /**
   * Get compliance requirements for jurisdiction and entity type
   */
  getComplianceRequirements(jurisdiction, entityType) {
    const requirements = {
      'Delaware': {
        'Corporation': ['Annual franchise tax', 'Annual report filing', 'Board resolutions'],
        'LLC': ['Annual franchise tax', 'Registered agent requirement']
      },
      'California': {
        'Corporation': ['Annual franchise tax', 'Statement of Information', 'Board resolutions'],
        'LLC': ['Annual LLC tax', 'Statement of Information']
      },
      'Florida': {
        'Corporation': ['Annual report', 'Registered agent requirement'],
        'LLC': ['Annual report', 'Registered agent requirement']
      }
    };
    
    return requirements[jurisdiction]?.[entityType] || ['Standard corporate compliance requirements'];
  }

  /**
   * Get default tax classification
   */
  getDefaultTaxClassification(entityType) {
    const classifications = {
      'Corporation': 'C-Corporation',
      'LLC': 'Single Member LLC',
      'Partnership': 'Partnership',
      'Sole Proprietorship': 'Sole Proprietorship'
    };
    
    return classifications[entityType] || 'To be determined';
  }

  /**
   * Validate alliance context for agreement generation
   */
  validateAllianceContext(context) {
    const validationResults = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Required company information
    if (!context.company?.legalName || context.company.legalName.includes('[')) {
      validationResults.errors.push('Company legal name is required');
      validationResults.isValid = false;
    }

    if (!context.company?.address || context.company.address.includes('[')) {
      validationResults.warnings.push('Company address should be provided for legal compliance');
    }

    // Required signatory information
    if (!context.signatoryInfo?.name || context.signatoryInfo.name.includes('[')) {
      validationResults.errors.push('Signatory name is required');
      validationResults.isValid = false;
    }

    // Legal context validation
    if (!context.legalContext?.jurisdiction) {
      validationResults.errors.push('Legal jurisdiction is required');
      validationResults.isValid = false;
    }

    return validationResults;
  }

  /**
   * Get jurisdiction-specific agreement clauses
   */
  getJurisdictionSpecificClauses(jurisdiction) {
    const clauses = {
      'Delaware': {
        governingLaw: 'This Agreement shall be governed by and construed in accordance with the laws of the State of Delaware, without regard to its conflict of laws principles.',
        jurisdiction: 'Any legal action or proceeding arising under this Agreement shall be brought exclusively in the Delaware Court of Chancery.',
        corporateFormalities: 'The parties acknowledge that this Agreement has been duly authorized by all necessary corporate action.'
      },
      'California': {
        governingLaw: 'This Agreement shall be governed by and construed in accordance with the laws of the State of California, without regard to its conflict of laws principles.',
        jurisdiction: 'Any legal action or proceeding arising under this Agreement shall be brought exclusively in the California Superior Court.',
        corporateFormalities: 'The parties acknowledge that this Agreement has been duly authorized by all necessary corporate action.'
      },
      'Florida': {
        governingLaw: 'This Agreement shall be governed by and construed in accordance with the laws of the State of Florida, without regard to its conflict of laws principles.',
        jurisdiction: 'Any legal action or proceeding arising under this Agreement shall be brought exclusively in the Florida Circuit Court.',
        corporateFormalities: 'The parties acknowledge that this Agreement has been duly authorized by all necessary corporate action.'
      }
    };
    
    return clauses[jurisdiction] || clauses['Delaware'];
  }

  /**
   * Update alliance context
   */
  async updateAllianceContext(allianceId, updateData) {
    console.log('🔄 Updating alliance context...');
    
    try {
      // Update alliance record
      const { data: updatedAlliance, error: allianceError } = await supabase
        .from('teams')
        .update({
          ...updateData.alliance,
          updated_at: new Date().toISOString()
        })
        .eq('id', allianceId)
        .select()
        .single();
      
      if (allianceError) throw allianceError;
      
      // Update company record if it exists
      if (updateData.company && updatedAlliance.company_id) {
        const { error: companyError } = await supabase
          .from('companies')
          .update({
            ...updateData.company,
            updated_at: new Date().toISOString()
          })
          .eq('id', updatedAlliance.company_id);
        
        if (companyError) throw companyError;
      }
      
      console.log('✅ Alliance context updated');
      
      return updatedAlliance;
      
    } catch (error) {
      console.error('❌ Failed to update alliance context:', error);
      throw new Error(`Failed to update alliance context: ${error.message}`);
    }
  }

  /**
   * Get alliance context integration status
   */
  getIntegrationStatus() {
    return {
      version: '2.0.0',
      capabilities: {
        allianceContextFetching: true,
        businessEntitySupport: true,
        individualAllianceSupport: true,
        legalContextProcessing: true,
        jurisdictionSupport: ['Delaware', 'California', 'Florida', 'New York'],
        signatoryInfoProcessing: true,
        contextValidation: true,
        jurisdictionSpecificClauses: true
      },
      status: 'active'
    };
  }
}
