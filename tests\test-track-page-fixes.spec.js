import { test, expect } from '@playwright/test';

test.describe('Track Page Error Fixes', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page
    await page.goto('https://royalty.technology/login');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Track page loads without database errors', async ({ page }) => {
    console.log('🧪 Testing Track page database error fixes...');
    
    // Navigate to Track page
    await page.goto('https://royalty.technology/track');
    await page.waitForLoadState('networkidle');
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'track-page-test.png', fullPage: true });
    
    // Check that the page loaded successfully
    const pageTitle = await page.title();
    console.log('📄 Page title:', pageTitle);
    
    // Check for error messages in console
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Wait a bit for any async operations
    await page.waitForTimeout(3000);
    
    // Check if the main track interface is visible
    const trackInterface = await page.locator('[data-testid="track-interface"], .track-page, .enhanced-track-page').first();
    const isTrackVisible = await trackInterface.isVisible().catch(() => false);
    
    console.log('🎯 Track interface visible:', isTrackVisible);
    
    // Check for specific error indicators
    const errorMessages = await page.locator('.error, [data-testid="error"], .toast-error').count();
    console.log('❌ Error messages found:', errorMessages);
    
    // Check for loading states
    const loadingIndicators = await page.locator('.loading, [data-testid="loading"], .spinner').count();
    console.log('⏳ Loading indicators:', loadingIndicators);
    
    // Log any console errors
    if (consoleErrors.length > 0) {
      console.log('🚨 Console errors found:');
      consoleErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // The page should load without critical errors
    expect(errorMessages).toBeLessThan(3); // Allow for minor non-critical errors
    
    // Check that we don't have the specific 400 errors from before
    const has400Errors = consoleErrors.some(error => 
      error.includes('400') && error.includes('projects')
    );
    expect(has400Errors).toBeFalsy();
    
    // Check that WebSocket errors are reduced
    const hasWebSocketErrors = consoleErrors.some(error => 
      error.includes('WebSocket') && error.includes('failed')
    );
    
    if (hasWebSocketErrors) {
      console.log('⚠️ WebSocket errors still present, but this may be expected in some environments');
    }
  });

  test('Track page shows project interface', async ({ page }) => {
    console.log('🧪 Testing Track page project interface...');
    
    // Navigate to Track page
    await page.goto('https://royalty.technology/track');
    await page.waitForLoadState('networkidle');
    
    // Wait for the page to load and render
    await page.waitForTimeout(2000);
    
    // Look for project-related elements
    const projectElements = await page.locator('text=/project/i, text=/task/i, text=/track/i').count();
    console.log('📋 Project-related elements found:', projectElements);
    
    // Check for common track page elements
    const commonElements = [
      'button', 'input', 'div', 'span', 'h1', 'h2', 'h3'
    ];
    
    for (const element of commonElements) {
      const count = await page.locator(element).count();
      console.log(`🔍 ${element} elements:`, count);
    }
    
    // The page should have some interactive elements
    expect(projectElements).toBeGreaterThan(0);
  });

  test('No critical JavaScript errors on Track page', async ({ page }) => {
    console.log('🧪 Testing for critical JavaScript errors...');
    
    const criticalErrors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        // Filter for critical errors that would break functionality
        if (text.includes('TypeError') || 
            text.includes('ReferenceError') || 
            text.includes('Cannot read property') ||
            text.includes('Cannot read properties') ||
            text.includes('is not a function')) {
          criticalErrors.push(text);
        }
      }
    });
    
    // Navigate to Track page
    await page.goto('https://royalty.technology/track');
    await page.waitForLoadState('networkidle');
    
    // Wait for any async operations
    await page.waitForTimeout(3000);
    
    // Log critical errors if found
    if (criticalErrors.length > 0) {
      console.log('🚨 Critical JavaScript errors found:');
      criticalErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // Should have minimal critical errors
    expect(criticalErrors.length).toBeLessThan(2);
  });

  test('Database queries handle missing tables gracefully', async ({ page }) => {
    console.log('🧪 Testing graceful handling of database issues...');
    
    const databaseErrors = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.type() === 'warning') {
        const text = msg.text();
        if (text.includes('database') || 
            text.includes('table') || 
            text.includes('query') ||
            text.includes('PGRST') ||
            text.includes('supabase')) {
          databaseErrors.push(text);
        }
      }
    });
    
    // Navigate to Track page
    await page.goto('https://royalty.technology/track');
    await page.waitForLoadState('networkidle');
    
    // Wait for database operations
    await page.waitForTimeout(4000);
    
    // Log database-related messages
    if (databaseErrors.length > 0) {
      console.log('📊 Database-related messages:');
      databaseErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // Check that the page still functions despite database issues
    const pageContent = await page.content();
    expect(pageContent.length).toBeGreaterThan(1000); // Page should have substantial content
    
    // Should not have critical database errors that break the page
    const criticalDbErrors = databaseErrors.filter(error => 
      error.includes('Failed to load') && !error.includes('may be limited')
    );
    expect(criticalDbErrors.length).toBeLessThan(1);
  });
});
