/**
 * Agreement Storage & Management System
 * 
 * Handles storage, versioning, retrieval, and management of generated agreements
 * with full integration to existing database schema and user permissions.
 */

import { supabase } from '../../supabase/supabase.utils.js';

export class AgreementStorageManager {
  constructor() {
    this.maxVersionHistory = 10; // Keep last 10 versions
    this.supportedStatuses = ['draft', 'pending_review', 'active', 'expired', 'terminated', 'archived'];
  }

  /**
   * Store a new agreement
   */
  async storeAgreement(agreementData, metadata = {}) {
    console.log('💾 Storing new agreement...');
    
    try {
      // Prepare agreement record
      const agreementRecord = {
        agreement_name: agreementData.name || `${metadata.ventureName} - Contributor Agreement`,
        agreement_type: agreementData.type || 'contributor_agreement',
        alliance_id: metadata.allianceId,
        venture_id: metadata.ventureId,
        contributor_id: metadata.contributorId,
        template_id: null, // V2 system doesn't use template_id
        agreement_content: agreementData.content,
        generation_parameters: {
          generator_version: '2.0.0',
          template_type: metadata.templateType || 'standard',
          generated_at: new Date().toISOString(),
          accuracy_score: metadata.accuracyScore || 100,
          exhibits_generated: metadata.exhibitsGenerated || true,
          generation_context: metadata.generationContext,
          ...metadata.generationParameters
        },
        status: agreementData.status || 'draft',
        version: 1,
        created_by: metadata.createdBy,
        is_active: true,
        effective_date: agreementData.effectiveDate,
        expiration_date: agreementData.expirationDate,
        auto_renewal: agreementData.autoRenewal || false,
        created_at: new Date().toISOString()
      };
      
      // Store the agreement
      const { data: storedAgreement, error: storeError } = await supabase
        .from('generated_agreements')
        .insert([agreementRecord])
        .select()
        .single();
      
      if (storeError) throw storeError;
      
      // Create initial version record
      await this.createVersionRecord(storedAgreement.id, agreementData.content, 1, 'initial_creation');
      
      // Set up permissions
      await this.setupAgreementPermissions(storedAgreement.id, metadata);
      
      // Log the storage activity
      await this.logActivity(storedAgreement.id, metadata.createdBy, 'created', {
        version: 1,
        status: agreementRecord.status
      });
      
      console.log('✅ Agreement stored successfully');
      
      return storedAgreement;
      
    } catch (error) {
      console.error('❌ Failed to store agreement:', error);
      throw new Error(`Failed to store agreement: ${error.message}`);
    }
  }

  /**
   * Retrieve agreement by ID
   */
  async getAgreement(agreementId, options = {}) {
    console.log('📖 Retrieving agreement...');
    
    try {
      const query = supabase
        .from('generated_agreements')
        .select(`
          *,
          alliance:teams!generated_agreements_alliance_id_fkey(id, name),
          venture:projects!generated_agreements_venture_id_fkey(id, name, title),
          contributor:profiles!generated_agreements_contributor_id_fkey(id, full_name, email),
          creator:profiles!generated_agreements_created_by_fkey(id, full_name, email)
        `)
        .eq('id', agreementId);
      
      if (!options.includeInactive) {
        query.eq('is_active', true);
      }
      
      const { data: agreement, error } = await query.single();
      
      if (error) throw error;
      
      // Get version history if requested
      if (options.includeVersionHistory) {
        agreement.versions = await this.getVersionHistory(agreementId);
      }
      
      // Get activity log if requested
      if (options.includeActivityLog) {
        agreement.activityLog = await this.getActivityLog(agreementId);
      }
      
      console.log('✅ Agreement retrieved');
      
      return agreement;
      
    } catch (error) {
      console.error('❌ Failed to retrieve agreement:', error);
      throw new Error(`Failed to retrieve agreement: ${error.message}`);
    }
  }

  /**
   * Update agreement with versioning
   */
  async updateAgreement(agreementId, updateData, userId, updateReason = '') {
    console.log('🔄 Updating agreement with versioning...');
    
    try {
      // Get current agreement
      const currentAgreement = await this.getAgreement(agreementId);
      
      // Prepare update data
      const updateRecord = {
        ...updateData,
        version: currentAgreement.version + 1,
        updated_at: new Date().toISOString(),
        updated_by: userId
      };
      
      // Update the agreement
      const { data: updatedAgreement, error: updateError } = await supabase
        .from('generated_agreements')
        .update(updateRecord)
        .eq('id', agreementId)
        .select()
        .single();
      
      if (updateError) throw updateError;
      
      // Create version record if content changed
      if (updateData.agreement_content) {
        await this.createVersionRecord(
          agreementId,
          updateData.agreement_content,
          updatedAgreement.version,
          updateReason || 'content_update'
        );
      }
      
      // Log the update activity
      await this.logActivity(agreementId, userId, 'updated', {
        version: updatedAgreement.version,
        updateReason,
        changedFields: Object.keys(updateData)
      });
      
      console.log('✅ Agreement updated with versioning');
      
      return updatedAgreement;
      
    } catch (error) {
      console.error('❌ Failed to update agreement:', error);
      throw new Error(`Failed to update agreement: ${error.message}`);
    }
  }

  /**
   * Change agreement status
   */
  async changeStatus(agreementId, newStatus, userId, reason = '') {
    console.log(`📋 Changing agreement status to: ${newStatus}`);
    
    try {
      // Validate status
      if (!this.supportedStatuses.includes(newStatus)) {
        throw new Error(`Invalid status: ${newStatus}`);
      }
      
      // Update status
      const { data: updatedAgreement, error } = await supabase
        .from('generated_agreements')
        .update({
          status: newStatus,
          status_changed_at: new Date().toISOString(),
          status_changed_by: userId,
          updated_at: new Date().toISOString()
        })
        .eq('id', agreementId)
        .select()
        .single();
      
      if (error) throw error;
      
      // Log status change
      await this.logActivity(agreementId, userId, 'status_changed', {
        newStatus,
        reason,
        timestamp: new Date().toISOString()
      });
      
      console.log('✅ Agreement status changed');
      
      return updatedAgreement;
      
    } catch (error) {
      console.error('❌ Failed to change agreement status:', error);
      throw new Error(`Failed to change agreement status: ${error.message}`);
    }
  }

  /**
   * Create version record
   */
  async createVersionRecord(agreementId, content, version, changeReason) {
    try {
      const versionRecord = {
        agreement_id: agreementId,
        version_number: version,
        content: content,
        change_reason: changeReason,
        created_at: new Date().toISOString()
      };
      
      const { data: versionData, error } = await supabase
        .from('agreement_versions')
        .insert([versionRecord])
        .select()
        .single();
      
      if (error) throw error;
      
      // Clean up old versions if we exceed the limit
      await this.cleanupOldVersions(agreementId);
      
      return versionData;
      
    } catch (error) {
      console.error('❌ Failed to create version record:', error);
      throw error;
    }
  }

  /**
   * Get version history
   */
  async getVersionHistory(agreementId, limit = null) {
    try {
      let query = supabase
        .from('agreement_versions')
        .select('*')
        .eq('agreement_id', agreementId)
        .order('version_number', { ascending: false });
      
      if (limit) {
        query = query.limit(limit);
      }
      
      const { data: versions, error } = await query;
      
      if (error) throw error;
      
      return versions;
      
    } catch (error) {
      console.error('❌ Failed to get version history:', error);
      throw error;
    }
  }

  /**
   * Setup agreement permissions
   */
  async setupAgreementPermissions(agreementId, metadata) {
    try {
      const permissions = [];
      
      // Creator permissions
      if (metadata.createdBy) {
        permissions.push({
          agreement_id: agreementId,
          user_id: metadata.createdBy,
          permission_type: 'owner',
          can_read: true,
          can_edit: true,
          can_delete: true,
          can_share: true,
          granted_at: new Date().toISOString()
        });
      }
      
      // Contributor permissions
      if (metadata.contributorId && metadata.contributorId !== metadata.createdBy) {
        permissions.push({
          agreement_id: agreementId,
          user_id: metadata.contributorId,
          permission_type: 'contributor',
          can_read: true,
          can_edit: false,
          can_delete: false,
          can_share: false,
          granted_at: new Date().toISOString()
        });
      }
      
      // Alliance members permissions (if applicable)
      if (metadata.allianceId) {
        const { data: allianceMembers, error } = await supabase
          .from('team_members')
          .select('user_id, role')
          .eq('team_id', metadata.allianceId)
          .eq('status', 'active');
        
        if (!error && allianceMembers) {
          allianceMembers.forEach(member => {
            if (member.user_id !== metadata.createdBy && member.user_id !== metadata.contributorId) {
              const canEdit = ['founder', 'admin', 'lead'].includes(member.role);
              
              permissions.push({
                agreement_id: agreementId,
                user_id: member.user_id,
                permission_type: 'alliance_member',
                can_read: true,
                can_edit: canEdit,
                can_delete: false,
                can_share: canEdit,
                granted_at: new Date().toISOString()
              });
            }
          });
        }
      }
      
      // Insert permissions
      if (permissions.length > 0) {
        const { error: permError } = await supabase
          .from('agreement_permissions')
          .insert(permissions);
        
        if (permError) throw permError;
      }
      
    } catch (error) {
      console.error('❌ Failed to setup permissions:', error);
      // Don't throw - permissions are important but not critical for storage
    }
  }

  /**
   * Check user permissions for agreement
   */
  async checkPermissions(agreementId, userId) {
    try {
      const { data: permission, error } = await supabase
        .from('agreement_permissions')
        .select('*')
        .eq('agreement_id', agreementId)
        .eq('user_id', userId)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error;
      
      return permission || {
        can_read: false,
        can_edit: false,
        can_delete: false,
        can_share: false
      };
      
    } catch (error) {
      console.error('❌ Failed to check permissions:', error);
      return {
        can_read: false,
        can_edit: false,
        can_delete: false,
        can_share: false
      };
    }
  }

  /**
   * Log activity
   */
  async logActivity(agreementId, userId, action, details = {}) {
    try {
      const activityRecord = {
        agreement_id: agreementId,
        user_id: userId,
        action: action,
        details: details,
        timestamp: new Date().toISOString()
      };
      
      await supabase
        .from('agreement_audit_log')
        .insert([activityRecord]);
        
    } catch (error) {
      console.error('⚠️  Failed to log activity:', error);
      // Don't throw - logging is non-critical
    }
  }

  /**
   * Get activity log
   */
  async getActivityLog(agreementId, limit = 50) {
    try {
      const { data: activities, error } = await supabase
        .from('agreement_audit_log')
        .select(`
          *,
          user:profiles!agreement_audit_log_user_id_fkey(full_name, email)
        `)
        .eq('agreement_id', agreementId)
        .order('timestamp', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      
      return activities;
      
    } catch (error) {
      console.error('❌ Failed to get activity log:', error);
      return [];
    }
  }

  /**
   * Clean up old versions
   */
  async cleanupOldVersions(agreementId) {
    try {
      // Get version count
      const { count, error: countError } = await supabase
        .from('agreement_versions')
        .select('*', { count: 'exact', head: true })
        .eq('agreement_id', agreementId);
      
      if (countError) throw countError;
      
      // If we have more than the max, delete the oldest
      if (count > this.maxVersionHistory) {
        const { data: oldVersions, error: fetchError } = await supabase
          .from('agreement_versions')
          .select('id')
          .eq('agreement_id', agreementId)
          .order('version_number', { ascending: true })
          .limit(count - this.maxVersionHistory);
        
        if (fetchError) throw fetchError;
        
        if (oldVersions && oldVersions.length > 0) {
          const idsToDelete = oldVersions.map(v => v.id);
          
          const { error: deleteError } = await supabase
            .from('agreement_versions')
            .delete()
            .in('id', idsToDelete);
          
          if (deleteError) throw deleteError;
        }
      }
      
    } catch (error) {
      console.error('⚠️  Failed to cleanup old versions:', error);
      // Don't throw - cleanup is non-critical
    }
  }

  /**
   * Search agreements
   */
  async searchAgreements(searchParams, userId) {
    try {
      let query = supabase
        .from('generated_agreements')
        .select(`
          *,
          alliance:teams!generated_agreements_alliance_id_fkey(id, name),
          venture:projects!generated_agreements_venture_id_fkey(id, name, title),
          contributor:profiles!generated_agreements_contributor_id_fkey(id, full_name, email)
        `)
        .eq('is_active', true);
      
      // Apply filters
      if (searchParams.status) {
        query = query.eq('status', searchParams.status);
      }
      
      if (searchParams.agreementType) {
        query = query.eq('agreement_type', searchParams.agreementType);
      }
      
      if (searchParams.allianceId) {
        query = query.eq('alliance_id', searchParams.allianceId);
      }
      
      if (searchParams.ventureId) {
        query = query.eq('venture_id', searchParams.ventureId);
      }
      
      if (searchParams.contributorId) {
        query = query.eq('contributor_id', searchParams.contributorId);
      }
      
      if (searchParams.searchText) {
        query = query.ilike('agreement_name', `%${searchParams.searchText}%`);
      }
      
      // Apply user permissions filter
      // This would need to be implemented based on your permission system
      
      const { data: agreements, error } = await query
        .order('created_at', { ascending: false })
        .limit(searchParams.limit || 50);
      
      if (error) throw error;
      
      return agreements;
      
    } catch (error) {
      console.error('❌ Failed to search agreements:', error);
      throw new Error(`Failed to search agreements: ${error.message}`);
    }
  }

  /**
   * Archive agreement
   */
  async archiveAgreement(agreementId, userId, reason = '') {
    return await this.changeStatus(agreementId, 'archived', userId, reason);
  }

  /**
   * Delete agreement (soft delete)
   */
  async deleteAgreement(agreementId, userId, reason = '') {
    console.log('🗑️  Soft deleting agreement...');
    
    try {
      const { data: deletedAgreement, error } = await supabase
        .from('generated_agreements')
        .update({
          is_active: false,
          deleted_at: new Date().toISOString(),
          deleted_by: userId,
          deletion_reason: reason
        })
        .eq('id', agreementId)
        .select()
        .single();
      
      if (error) throw error;
      
      // Log deletion
      await this.logActivity(agreementId, userId, 'deleted', {
        reason,
        timestamp: new Date().toISOString()
      });
      
      console.log('✅ Agreement soft deleted');
      
      return deletedAgreement;
      
    } catch (error) {
      console.error('❌ Failed to delete agreement:', error);
      throw new Error(`Failed to delete agreement: ${error.message}`);
    }
  }

  /**
   * Get storage manager status
   */
  getManagerStatus() {
    return {
      version: '2.0.0',
      capabilities: {
        agreementStorage: true,
        versioning: true,
        permissionManagement: true,
        activityLogging: true,
        statusManagement: true,
        searchAndFilter: true,
        archiving: true,
        softDelete: true
      },
      supportedStatuses: this.supportedStatuses,
      maxVersionHistory: this.maxVersionHistory,
      status: 'active'
    };
  }
}
