import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardBody, Select, SelectItem } from '../ui/heroui';
import { templateManager, TEMPLATE_TYPES, TEMPLATE_DESCRIPTIONS } from '../../utils/agreement/templateManager';

/**
 * Component for selecting an agreement template
 * @param {Object} props - Component props
 * @param {string} props.selectedTemplate - Currently selected template type
 * @param {Function} props.onTemplateChange - Callback when template is changed
 * @param {boolean} props.showPreview - Whether to show template preview
 * @returns {JSX.Element} - Template selector component
 */
const TemplateSelector = ({
  selectedTemplate = TEMPLATE_TYPES.STANDARD,
  onTemplateChange,
  showPreview = false
}) => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [previewText, setPreviewText] = useState('');

  // Load available templates on component mount
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get available template types
        const availableTemplates = templateManager.getAvailableTemplateTypes();
        setTemplates(availableTemplates);

        // Load preview if needed
        if (showPreview && selectedTemplate) {
          const templateText = await templateManager.loadTemplate(selectedTemplate);
          // Show only first 500 characters as preview
          setPreviewText(templateText.substring(0, 500) + '...');
        }
      } catch (err) {
        console.error('Error loading templates:', err);
        setError('Failed to load templates. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadTemplates();
  }, [selectedTemplate, showPreview]);

  // Handle template change
  const handleTemplateChange = (newTemplate) => {
    if (onTemplateChange) {
      onTemplateChange(newTemplate);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader>
        <h5 className="mb-0">Agreement Template</h5>
      </CardHeader>
      <CardBody>
        {error && (
          <div className="mb-3 p-3 bg-red-100 border border-red-300 rounded-md">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <div className="mb-3">
          <Select
            label="Select Template Type"
            selectedKeys={selectedTemplate ? [selectedTemplate] : []}
            onSelectionChange={(keys) => handleTemplateChange(Array.from(keys)[0])}
            isDisabled={loading}
            placeholder="Select a template"
          >
            {templates.map(template => (
              <SelectItem key={template} value={template}>
                {template.charAt(0).toUpperCase() + template.slice(1)}
              </SelectItem>
            ))}
          </Select>
          <p className="text-sm text-muted-foreground mt-1">
            {templateManager.getTemplateDescription(selectedTemplate)}
          </p>
        </div>

        {showPreview && previewText && (
          <div className="mt-3">
            <h6>Template Preview</h6>
            <div className="border p-3 bg-muted rounded" style={{ maxHeight: '200px', overflow: 'auto' }}>
              <pre className="mb-0" style={{ whiteSpace: 'pre-wrap' }}>
                {previewText}
              </pre>
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default TemplateSelector;
