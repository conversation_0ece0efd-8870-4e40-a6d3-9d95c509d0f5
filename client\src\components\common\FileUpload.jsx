import React, { useState, useRef } from 'react';
import { toast } from 'react-hot-toast';
import { supabase } from '../../utils/supabase/supabase.utils';
import { Card, CardBody, Button } from '../ui/heroui';

const FileUpload = ({
  bucketName,
  folderPath,
  onUploadComplete,
  onUploadError,
  maxFiles = 5,
  maxFileSize = 10, // in MB
  allowedFileTypes = ['image/*', 'application/pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
  existingFiles = []
}) => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const fileInputRef = useRef(null);

  // Handle file selection
  const handleFileChange = async (e) => {
    const selectedFiles = Array.from(e.target.files);

    // Check if adding these files would exceed the maximum
    if (existingFiles.length + files.length + selectedFiles.length > maxFiles) {
      toast.error(`You can only upload a maximum of ${maxFiles} files`);
      return;
    }

    // Validate each file
    const validFiles = selectedFiles.filter(file => {
      // Check file size
      if (file.size > maxFileSize * 1024 * 1024) {
        toast.error(`File ${file.name} exceeds the maximum size of ${maxFileSize}MB`);
        return false;
      }

      // Check file type
      const fileType = file.type;
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

      const isValidType = allowedFileTypes.some(type => {
        if (type.startsWith('.')) {
          // Check by extension
          return fileExtension === type.toLowerCase();
        } else if (type.endsWith('/*')) {
          // Check by MIME type category
          const category = type.slice(0, -2);
          return fileType.startsWith(category);
        } else {
          // Check exact MIME type
          return fileType === type;
        }
      });

      if (!isValidType) {
        toast.error(`File ${file.name} has an unsupported file type`);
        return false;
      }

      return true;
    });

    if (validFiles.length > 0) {
      setFiles(prevFiles => [...prevFiles, ...validFiles]);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Automatically upload files when they're selected
      await uploadFiles(validFiles);
    }
  };

  // Remove a file from the list
  const removeFile = (index) => {
    setFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  // Remove an existing file
  const removeExistingFile = (index) => {
    if (onUploadComplete) {
      const updatedFiles = [...existingFiles];
      updatedFiles.splice(index, 1);
      onUploadComplete(updatedFiles);
    }
  };

  // Upload files to Supabase Storage
  const uploadFiles = async (filesToUpload = null) => {
    // Use provided files or fall back to the state
    const filesToProcess = filesToUpload || files;

    if (filesToProcess.length === 0) {
      toast.info('No files selected for upload');
      return;
    }

    setUploading(true);
    const uploadToastId = toast.loading(`Uploading ${filesToProcess.length} file(s)...`);

    try {
      const uploadedFiles = [];

      // Upload each file
      for (let i = 0; i < filesToProcess.length; i++) {
        const file = filesToProcess[i];
        setUploadProgress(prev => ({ ...prev, [i]: 0 }));

        // Create a unique file name
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = folderPath ? `${folderPath}/${fileName}` : fileName;

        console.log(`Uploading file ${file.name} to ${bucketName}/${filePath}`);

        // Upload to Supabase Storage
        const { data, error } = await supabase.storage
          .from(bucketName)
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true, // Changed to true to overwrite existing files with the same name
            onUploadProgress: (progress) => {
              const percent = Math.round((progress.loaded / progress.total) * 100);
              setUploadProgress(prev => ({ ...prev, [i]: percent }));
            }
          });

        if (error) {
          console.error(`Error uploading ${file.name}:`, error);
          throw new Error(`Error uploading ${file.name}: ${error.message}`);
        }

        console.log(`File ${file.name} uploaded successfully. Data:`, data);

        // Get public URL
        const { data: urlData } = supabase.storage
          .from(bucketName)
          .getPublicUrl(filePath);

        if (!urlData || !urlData.publicUrl) {
          console.error(`Failed to get public URL for ${file.name}`);
          throw new Error(`Failed to get public URL for ${file.name}`);
        }

        console.log(`Got public URL for ${file.name}:`, urlData.publicUrl);

        // Add file info to uploaded files
        uploadedFiles.push({
          name: file.name,
          size: file.size,
          type: file.type,
          url: urlData.publicUrl,
          path: filePath,
          uploaded_at: new Date().toISOString()
        });
      }

      // Combine with existing files
      const allFiles = [...existingFiles, ...uploadedFiles];

      console.log('All files after upload:', allFiles);

      // Call the onUploadComplete callback
      if (onUploadComplete) {
        console.log('Calling onUploadComplete with files:', allFiles);
        onUploadComplete(allFiles);
      } else {
        console.warn('No onUploadComplete callback provided');
      }

      // Clear the files list
      setFiles([]);

      toast.dismiss(uploadToastId);
      toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`);
    } catch (error) {
      console.error('Error uploading files:', error);

      toast.dismiss(uploadToastId);
      toast.error(`Upload failed: ${error.message}`);

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  return (
    <div className="file-upload-component">
      <div className="file-upload-header">
        <input
          type="file"
          id="file-input"
          ref={fileInputRef}
          onChange={handleFileChange}
          className="file-input"
          multiple
          accept={allowedFileTypes.join(',')}
          disabled={uploading}
        />
        <label htmlFor="file-input" className="file-upload-button">
          <i className="bi bi-upload"></i>
          {uploading ? 'Uploading...' : 'Select & Upload Files'}
        </label>

        {files.length > 0 && (
          <button
            type="button"
            className="upload-button"
            onClick={() => uploadFiles()}
            disabled={uploading}
          >
            {uploading ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span className="ms-2">Uploading...</span>
              </>
            ) : (
              <>
                <i className="bi bi-cloud-arrow-up"></i>
                Upload Pending Files
              </>
            )}
          </button>
        )}
      </div>

      {/* File limits info */}
      <div className="file-limits">
        <small>
          Max {maxFiles} files, up to {maxFileSize}MB each.
          Allowed types: images, PDFs, Office documents, text files.
          <br />
          <strong>Note:</strong> Files are automatically uploaded when selected.
        </small>
      </div>

      {/* Selected files list */}
      {files.length > 0 && (
        <div className="selected-files">
          <h6>Selected Files</h6>
          <ul className="file-list">
            {files.map((file, index) => (
              <li key={`new-${index}`} className="file-item">
                <div className="file-info">
                  <i className={getFileIcon(file.type)}></i>
                  <span className="file-name">{file.name}</span>
                  <span className="file-size">({formatFileSize(file.size)})</span>
                </div>

                {uploadProgress[index] !== undefined && uploadProgress[index] > 0 && uploadProgress[index] < 100 && (
                  <div className="progress">
                    <div
                      className="progress-bar"
                      role="progressbar"
                      style={{ width: `${uploadProgress[index]}%` }}
                      aria-valuenow={uploadProgress[index]}
                      aria-valuemin="0"
                      aria-valuemax="100"
                    >
                      {uploadProgress[index]}%
                    </div>
                  </div>
                )}

                <button
                  type="button"
                  className="remove-file-button"
                  onClick={() => removeFile(index)}
                  disabled={uploading}
                >
                  <i className="bi bi-x-circle"></i>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Existing files list */}
      {existingFiles.length > 0 && (
        <div className="existing-files">
          <h6>Attached Files</h6>
          <ul className="file-list">
            {existingFiles.map((file, index) => (
              <li key={`existing-${index}`} className="file-item">
                <div className="file-info">
                  <i className={getFileIcon(file.type)}></i>
                  <a href={file.url} target="_blank" rel="noopener noreferrer" className="file-name">
                    {file.name}
                  </a>
                  <span className="file-size">({formatFileSize(file.size)})</span>
                </div>

                <button
                  type="button"
                  className="remove-file-button"
                  onClick={() => removeExistingFile(index)}
                  disabled={uploading}
                >
                  <i className="bi bi-x-circle"></i>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

// Helper function to get appropriate icon for file type
const getFileIcon = (fileType) => {
  if (fileType.startsWith('image/')) {
    return 'bi bi-file-image';
  } else if (fileType === 'application/pdf') {
    return 'bi bi-file-pdf';
  } else if (fileType.includes('word') || fileType.includes('document')) {
    return 'bi bi-file-word';
  } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
    return 'bi bi-file-excel';
  } else if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
    return 'bi bi-file-ppt';
  } else if (fileType.includes('text/')) {
    return 'bi bi-file-text';
  } else {
    return 'bi bi-file-earmark';
  }
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(1) + ' KB';
  } else {
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  }
};

export default FileUpload;
