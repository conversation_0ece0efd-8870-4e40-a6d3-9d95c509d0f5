import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import axios from 'axios';
import { toast } from 'react-hot-toast';

const RoyalteaRoadmapTracker = () => {
  const { currentUser } = useContext(UserContext);
  const [phases, setPhases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Load data from database on component mount
  useEffect(() => {
    const fetchRoadmapData = async () => {
      try {
        setLoading(true);

        // First try to fetch from the API
        try {
          // Use absolute URLs to avoid path issues
          const siteUrl = window.location.origin;

          // First try the debug endpoint to check if functions are working
          console.log('Checking debug endpoint...');
          const debugResponse = await axios.get(`${siteUrl}/.netlify/functions/debug`);
          console.log('Debug response:', debugResponse.data);

          // Then try the test endpoint
          console.log('Checking test endpoint...');
          const testResponse = await axios.get(`${siteUrl}/.netlify/functions/test`);
          console.log('Test response:', testResponse.data);

          // If tests succeed, try to get the roadmap data
          console.log('Fetching roadmap data...');
          const { data } = await axios.get(`${siteUrl}/.netlify/functions/roadmap`);

          // If we have data from the database, use it
          if (data && Array.isArray(data) && data.length > 0) {
            console.log('Loaded roadmap data from API:', data);
            setPhases(data);
            return; // Exit early if successful
          } else {
            console.log('API returned empty data, falling back to localStorage');
          }
        } catch (apiError) {
          console.error('API error:', apiError);
          toast.error(`API error: ${apiError.message || 'Unknown error'}`);
        }

        // If API fails or returns empty data, try localStorage
        const savedData = localStorage.getItem('royalteaRoadmapData');
        if (savedData) {
          console.log('Loading data from localStorage');
          try {
            const parsedData = JSON.parse(savedData);
            setPhases(parsedData);
            return; // Exit early if successful
          } catch (parseError) {
            console.error('Error parsing localStorage data:', parseError);
          }
        }

        // If all else fails, use default data
        console.log('Using default data');
        setPhases(getDefaultData());

      } catch (error) {
        console.error('Unhandled error in fetchRoadmapData:', error);
        toast.error('Failed to load roadmap data');
        setPhases(getDefaultData());
      } finally {
        setLoading(false);
      }
    };

    fetchRoadmapData();
  }, []);

  // Save to localStorage whenever phases change
  useEffect(() => {
    // Skip saving on initial load
    if (loading) return;

    // Save to localStorage
    try {
      localStorage.setItem('royalteaRoadmapData', JSON.stringify(phases));
      console.log('Saved to localStorage');

      // Show saving indicator briefly
      setSaving(true);
      const saveTimeout = setTimeout(() => {
        setSaving(false);
        toast.success('Progress saved locally');
      }, 500);

      return () => clearTimeout(saveTimeout);
    } catch (localStorageError) {
      console.error('Error saving to localStorage:', localStorageError);
      toast.error('Failed to save progress');
      setSaving(false);
    }
  }, [phases, loading]);

  // Calculate overall progress stats
  const calculateStats = () => {
    let totalTasks = 0;
    let completedTasks = 0;

    phases.forEach(phase => {
      phase.sections.forEach(section => {
        totalTasks += section.tasks.length;
        completedTasks += section.tasks.filter(task => task.completed).length;
      });
    });

    return {
      totalTasks,
      completedTasks,
      progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    };
  };

  const stats = calculateStats();

  // Toggle task completion status
  const toggleTaskCompletion = (phaseId, sectionId, taskId) => {
    setPhases(prevPhases => {
      return prevPhases.map(phase => {
        if (phase.id === phaseId) {
          return {
            ...phase,
            sections: phase.sections.map(section => {
              if (section.id === sectionId) {
                return {
                  ...section,
                  tasks: section.tasks.map(task => {
                    if (task.id === taskId) {
                      return {
                        ...task,
                        completed: !task.completed
                      };
                    }
                    return task;
                  })
                };
              }
              return section;
            })
          };
        }
        return phase;
      });
    });
  };

  // Toggle phase expansion
  const togglePhaseExpansion = (phaseId) => {
    setPhases(prevPhases => {
      return prevPhases.map(phase => {
        if (phase.id === phaseId) {
          return {
            ...phase,
            expanded: !phase.expanded
          };
        }
        return phase;
      });
    });
  };

  // Calculate section progress
  const calculateSectionProgress = (section) => {
    const total = section.tasks.length;
    const completed = section.tasks.filter(task => task.completed).length;
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  // Calculate phase progress
  const calculatePhaseProgress = (phase) => {
    let totalTasks = 0;
    let completedTasks = 0;

    phase.sections.forEach(section => {
      totalTasks += section.tasks.length;
      completedTasks += section.tasks.filter(task => task.completed).length;
    });

    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  };

  // Clear all progress (reset all tasks to uncompleted)
  const resetProgress = () => {
    if (confirm('Are you sure you want to reset all progress? This cannot be undone.')) {
      setPhases(prevPhases => {
        return prevPhases.map(phase => {
          return {
            ...phase,
            sections: phase.sections.map(section => {
              return {
                ...section,
                tasks: section.tasks.map(task => {
                  return {
                    ...task,
                    completed: false
                  };
                })
              };
            })
          };
        });
      });
      toast.success('Progress reset successfully');
    }
  };

  // Export data function
  const exportData = () => {
    const dataStr = JSON.stringify(phases, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = 'royaltea-roadmap-data.json';

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    toast.success('Data exported successfully');
  };

  // Import data function
  const importData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';

    input.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const importedData = JSON.parse(event.target.result);
          if (confirm('Are you sure you want to import this data? Current progress will be replaced.')) {
            setPhases(importedData);
            toast.success('Data imported successfully');
          }
        } catch (error) {
          toast.error('Error importing data. Please check the file format.');
          console.error('Import error:', error);
        }
      };
      reader.readAsText(file);
    };

    input.click();
  };

  if (loading) {
    return (
      <div className="roadmap-loading">
        <div className="roadmap-spinner"></div>
      </div>
    );
  }

  return (
    <div className="roadmap-container">
      <div className="roadmap-wrapper">
        <div className="roadmap-header">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="roadmap-title">Royaltea MVP Development Tracker</h1>
              {saving && (
                <span className="roadmap-saving">Saving changes...</span>
              )}
              <div className="mt-2">
                <a href="/admin/roadmap-manager" className="text-sm text-blue-500 hover:underline">Manage Roadmap Data</a>
              </div>
            </div>
            <div className="roadmap-buttons">
              <button
                onClick={exportData}
                className="roadmap-button roadmap-button-blue"
              >
                Export Data
              </button>
              <button
                onClick={importData}
                className="roadmap-button roadmap-button-green"
              >
                Import Data
              </button>
              <button
                onClick={resetProgress}
                className="roadmap-button roadmap-button-red"
              >
                Reset All
              </button>
            </div>
          </div>

          <div className="roadmap-progress">
            <div className="roadmap-progress-header">
              <h2 className="roadmap-progress-title">Overall Progress</h2>
              <span className="roadmap-progress-percentage">{stats.progressPercentage}%</span>
            </div>
            <div className="roadmap-progress-bar-bg">
              <div
                className="roadmap-progress-bar"
                style={{ width: `${stats.progressPercentage}%` }}
              ></div>
            </div>
            <div className="roadmap-progress-stats">
              {stats.completedTasks} of {stats.totalTasks} tasks completed
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {phases.map((phase) => (
            <div key={phase.id} className="roadmap-phase">
              <div
                className="roadmap-phase-header"
                onClick={() => togglePhaseExpansion(phase.id)}
              >
                <div className="roadmap-phase-title">
                  <span className={`roadmap-phase-title-text ${calculatePhaseProgress(phase) === 100 ? 'completed' : ''}`}>
                    Phase {phase.id}: {phase.title}
                  </span>
                  <span className="roadmap-phase-timeframe">({phase.timeframe})</span>
                </div>
                <div className="roadmap-phase-progress">
                  <div className="roadmap-phase-progress-bar-container">
                    <div className="roadmap-phase-progress-bar-bg">
                      <div
                        className={`roadmap-phase-progress-bar ${calculatePhaseProgress(phase) === 100 ? 'completed' : 'in-progress'}`}
                        style={{ width: `${calculatePhaseProgress(phase)}%` }}
                      ></div>
                    </div>
                    <span className="roadmap-phase-progress-text">{calculatePhaseProgress(phase)}%</span>
                  </div>
                  <svg
                    className={`roadmap-phase-chevron ${phase.expanded ? 'expanded' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>

              {phase.expanded && (
                <div className="roadmap-phase-content">
                  {phase.sections.map((section) => (
                    <div key={section.id} className="roadmap-section">
                      <div className="roadmap-section-header">
                        <h3 className="roadmap-section-title">{section.id} {section.title}</h3>
                        <div className="roadmap-section-progress">
                          <div className="roadmap-section-progress-bar-bg">
                            <div
                              className={`roadmap-section-progress-bar ${calculateSectionProgress(section) === 100 ? 'completed' : 'in-progress'}`}
                              style={{ width: `${calculateSectionProgress(section)}%` }}
                            ></div>
                          </div>
                          <span className="roadmap-section-progress-text">{calculateSectionProgress(section)}%</span>
                        </div>
                      </div>

                      <ul className="roadmap-tasks">
                        {section.tasks.map((task) => (
                          <li key={task.id} className="roadmap-task">
                            <input
                              type="checkbox"
                              checked={task.completed}
                              onChange={() => toggleTaskCompletion(phase.id, section.id, task.id)}
                              className="roadmap-task-checkbox"
                            />
                            <span className={`roadmap-task-text ${task.completed ? 'completed' : ''}`}>
                              {task.text}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="roadmap-footer">
          <p>Developed for City of Gamers - Royaltea Project</p>
          <p>Progress is saved automatically to the database and in your browser's local storage</p>
        </div>
      </div>
    </div>
  );
};

// Default data function
const getDefaultData = () => [
  {
    id: 1,
    title: "Foundation & User Management",
    timeframe: "2-3 weeks",
    expanded: true,
    sections: [
      {
        id: "1.1",
        title: "Project Setup & Configuration",
        tasks: [
          { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
          { id: "1.1.2", text: "Set up development, staging, and production environments", completed: false },
          { id: "1.1.3", text: "Configure CI/CD pipeline for automated testing and deployment", completed: false },
          { id: "1.1.4", text: "Implement error logging and monitoring", completed: false }
        ]
      },
      {
        id: "1.2",
        title: "User Authentication & Profiles",
        tasks: [
          { id: "1.2.1", text: "Extend current authentication system with role-based permissions", completed: false },
          { id: "1.2.2", text: "Create user profile page with customization options", completed: false },
          { id: "1.2.3", text: "Implement profile image upload functionality", completed: false },
          { id: "1.2.4", text: "Add user skills and expertise fields", completed: false },
          { id: "1.2.5", text: "Create public profile view for networking", completed: false }
        ]
      }
    ]
  },
  {
    id: 2,
    title: "Project Management",
    timeframe: "2-3 weeks",
    expanded: false,
    sections: [
      {
        id: "2.1",
        title: "Project Creation Wizard",
        tasks: [
          { id: "2.1.1", text: "Design and implement multi-step project creation wizard", completed: true },
          { id: "2.1.2", text: "Step 1: Project Basics (name, description, type, privacy)", completed: true },
          { id: "2.1.3", text: "Step 2: Team & Contributors management", completed: true },
          { id: "2.1.4", text: "Step 3: Royalty Model Configuration", completed: false },
          { id: "2.1.5", text: "Step 4: Revenue Tranches Setup", completed: false },
          { id: "2.1.6", text: "Step 5: Contribution Tracking (placeholder for Phase 1)", completed: false },
          { id: "2.1.7", text: "Step 6: Milestones & Specifications (placeholder for Phase 1)", completed: false },
          { id: "2.1.8", text: "Step 7: Review & Agreement generation", completed: false }
        ]
      },
      {
        id: "2.2",
        title: "Basic Task Management",
        tasks: [
          { id: "2.2.1", text: "Create task creation and assignment functionality", completed: false },
          { id: "2.2.2", text: "Implement task statuses (to-do, in progress, review, complete)", completed: false },
          { id: "2.2.3", text: "Add task priority levels and deadlines", completed: false },
          { id: "2.2.4", text: "Build simple Kanban-style view for tasks", completed: false }
        ]
      }
    ]
  },
  {
    id: 3,
    title: "Contribution Tracking",
    timeframe: "3-4 weeks",
    expanded: false,
    sections: [
      {
        id: "3.1",
        title: "Manual Contribution System",
        tasks: [
          { id: "3.1.1", text: "Design contribution entry forms", completed: false },
          { id: "3.1.2", text: "Implement time tracking functionality", completed: false },
          { id: "3.1.3", text: "Create task difficulty rating system", completed: false },
          { id: "3.1.4", text: "Add contribution categories (code, art, audio, design, etc.)", completed: false },
          { id: "3.1.5", text: "Build contribution validation workflow", completed: false }
        ]
      },
      {
        id: "3.2",
        title: "Royalty Calculation Engine",
        tasks: [
          { id: "3.2.1", text: "Implement manual royalty calculation algorithms", completed: false },
          { id: "3.2.2", text: "Create royalty distribution visualization", completed: false },
          { id: "3.2.3", text: "Add royalty adjustment tools for project owners", completed: false },
          { id: "3.2.4", text: "Implement royalty history and tracking", completed: false },
          { id: "3.2.5", text: "Build export functionality for calculated distributions", completed: false }
        ]
      }
    ]
  },
  {
    id: 4,
    title: "Collaboration Tools",
    timeframe: "2-3 weeks",
    expanded: false,
    sections: [
      {
        id: "4.1",
        title: "Team Communication",
        tasks: [
          { id: "4.1.1", text: "Create project discussion boards", completed: false },
          { id: "4.1.2", text: "Implement comment functionality on tasks and contributions", completed: false },
          { id: "4.1.3", text: "Add @mentions and notifications", completed: false },
          { id: "4.1.4", text: "Build simple direct messaging system", completed: false }
        ]
      },
      {
        id: "4.2",
        title: "File Sharing & Asset Management",
        tasks: [
          { id: "4.2.1", text: "Implement basic file upload and organization", completed: false },
          { id: "4.2.2", text: "Create version control for shared assets", completed: false },
          { id: "4.2.3", text: "Add preview functionality for common file types", completed: false },
          { id: "4.2.4", text: "Implement access controls for sensitive files", completed: false }
        ]
      }
    ]
  },
  {
    id: 5,
    title: "Marketplace & Networking",
    timeframe: "3-4 weeks",
    expanded: false,
    sections: [
      {
        id: "5.1",
        title: "Project Discovery",
        tasks: [
          { id: "5.1.1", text: "Create public project listings", completed: false },
          { id: "5.1.2", text: "Implement search and filtering functionality", completed: false },
          { id: "5.1.3", text: "Add project categories and tags", completed: false },
          { id: "5.1.4", text: "Build featured projects section", completed: false }
        ]
      },
      {
        id: "5.2",
        title: "Talent Marketplace",
        tasks: [
          { id: "5.2.1", text: "Create developer profiles in marketplace", completed: false },
          { id: "5.2.2", text: "Implement skill-based search and filtering", completed: false },
          { id: "5.2.3", text: "Add availability indicators", completed: false },
          { id: "5.2.4", text: "Build request collaboration functionality", completed: false }
        ]
      }
    ]
  },
  {
    id: 6,
    title: "MVP Refinement & Testing",
    timeframe: "2-3 weeks",
    expanded: false,
    sections: [
      {
        id: "6.1",
        title: "User Experience Enhancements",
        tasks: [
          { id: "6.1.1", text: "Implement guided onboarding flows", completed: false },
          { id: "6.1.2", text: "Add contextual help and tooltips", completed: false },
          { id: "6.1.3", text: "Create dashboard analytics for users and projects", completed: false },
          { id: "6.1.4", text: "Improve responsive design for all device types", completed: false }
        ]
      },
      {
        id: "6.2",
        title: "Testing & Quality Assurance",
        tasks: [
          { id: "6.2.1", text: "Conduct comprehensive user testing", completed: false },
          { id: "6.2.2", text: "Perform security audit and penetration testing", completed: false },
          { id: "6.2.3", text: "Optimize performance and loading times", completed: false },
          { id: "6.2.4", text: "Fix identified bugs and issues", completed: false }
        ]
      }
    ]
  },
  {
    id: 7,
    title: "Launch Preparation",
    timeframe: "1-2 weeks",
    expanded: false,
    sections: [
      {
        id: "7.1",
        title: "Documentation",
        tasks: [
          { id: "7.1.1", text: "Create user guides and documentation", completed: false },
          { id: "7.1.2", text: "Develop API documentation for future integrations", completed: false },
          { id: "7.1.3", text: "Prepare release notes and feature highlights", completed: false }
        ]
      },
      {
        id: "7.2",
        title: "Launch Activities",
        tasks: [
          { id: "7.2.1", text: "Implement feedback collection mechanisms", completed: false },
          { id: "7.2.2", text: "Set up beta user program", completed: false },
          { id: "7.2.3", text: "Prepare marketing materials", completed: false },
          { id: "7.2.4", text: "Configure analytics for user adoption tracking", completed: false }
        ]
      }
    ]
  }
];

export default RoyalteaRoadmapTracker;
