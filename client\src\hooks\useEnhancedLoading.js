import { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'react-hot-toast';

/**
 * Enhanced Loading State Management Hook
 * 
 * Provides comprehensive loading state management with:
 * - Multiple concurrent loading operations
 * - Error handling and retry logic
 * - Progress tracking
 * - Timeout handling
 * - Loading analytics
 */

export const useEnhancedLoading = (options = {}) => {
  const {
    defaultTimeout = 30000, // 30 seconds
    enableAnalytics = true,
    enableRetry = true,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  const [loadingStates, setLoadingStates] = useState(new Map());
  const [errors, setErrors] = useState(new Map());
  const [progress, setProgress] = useState(new Map());
  const timeouts = useRef(new Map());
  const retryAttempts = useRef(new Map());
  const analytics = useRef({
    totalOperations: 0,
    successfulOperations: 0,
    failedOperations: 0,
    averageLoadTime: 0,
    loadTimes: []
  });

  // Start a loading operation
  const startLoading = useCallback((operationId, options = {}) => {
    const {
      timeout = defaultTimeout,
      showToast = false,
      message = 'Loading...'
    } = options;

    setLoadingStates(prev => new Map(prev).set(operationId, {
      isLoading: true,
      startTime: Date.now(),
      message,
      timeout
    }));

    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(operationId);
      return newErrors;
    });

    setProgress(prev => {
      const newProgress = new Map(prev);
      newProgress.delete(operationId);
      return newProgress;
    });

    // Set timeout
    if (timeout > 0) {
      const timeoutId = setTimeout(() => {
        setLoadingError(operationId, new Error('Operation timed out'), {
          isTimeout: true
        });
      }, timeout);
      
      timeouts.current.set(operationId, timeoutId);
    }

    if (showToast) {
      toast.loading(message, { id: operationId });
    }

    if (enableAnalytics) {
      analytics.current.totalOperations++;
    }
  }, [defaultTimeout, enableAnalytics]);

  // Stop a loading operation
  const stopLoading = useCallback((operationId, options = {}) => {
    const { showToast = false, successMessage = 'Completed!' } = options;
    
    const currentState = loadingStates.get(operationId);
    if (!currentState) return;

    const loadTime = Date.now() - currentState.startTime;

    setLoadingStates(prev => {
      const newStates = new Map(prev);
      newStates.delete(operationId);
      return newStates;
    });

    // Clear timeout
    const timeoutId = timeouts.current.get(operationId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeouts.current.delete(operationId);
    }

    // Clear retry attempts
    retryAttempts.current.delete(operationId);

    if (showToast) {
      toast.success(successMessage, { id: operationId });
    }

    if (enableAnalytics) {
      analytics.current.successfulOperations++;
      analytics.current.loadTimes.push(loadTime);
      
      // Keep only last 100 load times for average calculation
      if (analytics.current.loadTimes.length > 100) {
        analytics.current.loadTimes.shift();
      }
      
      analytics.current.averageLoadTime = 
        analytics.current.loadTimes.reduce((a, b) => a + b, 0) / 
        analytics.current.loadTimes.length;
    }
  }, [loadingStates, enableAnalytics]);

  // Set loading error
  const setLoadingError = useCallback((operationId, error, options = {}) => {
    const { 
      showToast = true, 
      isTimeout = false,
      enableAutoRetry = enableRetry 
    } = options;

    setLoadingStates(prev => {
      const newStates = new Map(prev);
      newStates.delete(operationId);
      return newStates;
    });

    setErrors(prev => new Map(prev).set(operationId, {
      error,
      timestamp: Date.now(),
      isTimeout,
      canRetry: enableAutoRetry && !isTimeout
    }));

    // Clear timeout
    const timeoutId = timeouts.current.get(operationId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeouts.current.delete(operationId);
    }

    if (showToast) {
      const message = isTimeout ? 'Operation timed out' : error.message || 'An error occurred';
      toast.error(message, { id: operationId });
    }

    if (enableAnalytics) {
      analytics.current.failedOperations++;
    }
  }, [enableRetry, enableAnalytics]);

  // Update progress
  const updateProgress = useCallback((operationId, progressValue, message) => {
    setProgress(prev => new Map(prev).set(operationId, {
      value: Math.max(0, Math.min(100, progressValue)),
      message: message || `${progressValue}% complete`
    }));
  }, []);

  // Retry operation
  const retryOperation = useCallback((operationId, operationFunc, options = {}) => {
    const currentAttempts = retryAttempts.current.get(operationId) || 0;
    
    if (currentAttempts >= maxRetries) {
      toast.error('Maximum retry attempts reached');
      return Promise.reject(new Error('Maximum retry attempts reached'));
    }

    retryAttempts.current.set(operationId, currentAttempts + 1);
    
    // Clear error
    setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(operationId);
      return newErrors;
    });

    // Add delay before retry
    const delay = retryDelay * Math.pow(2, currentAttempts); // Exponential backoff
    
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(operationFunc());
      }, delay);
    });
  }, [maxRetries, retryDelay]);

  // Execute operation with loading state management
  const executeWithLoading = useCallback(async (operationId, operationFunc, options = {}) => {
    try {
      startLoading(operationId, options);
      const result = await operationFunc((progress, message) => {
        updateProgress(operationId, progress, message);
      });
      stopLoading(operationId, options);
      return result;
    } catch (error) {
      setLoadingError(operationId, error, options);
      throw error;
    }
  }, [startLoading, stopLoading, setLoadingError, updateProgress]);

  // Get loading state for specific operation
  const getLoadingState = useCallback((operationId) => {
    return {
      isLoading: loadingStates.has(operationId),
      error: errors.get(operationId),
      progress: progress.get(operationId),
      canRetry: errors.get(operationId)?.canRetry || false
    };
  }, [loadingStates, errors, progress]);

  // Get overall loading state
  const isAnyLoading = loadingStates.size > 0;
  const hasAnyErrors = errors.size > 0;
  const loadingCount = loadingStates.size;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear all timeouts
      timeouts.current.forEach(timeoutId => clearTimeout(timeoutId));
      timeouts.current.clear();
    };
  }, []);

  return {
    // State
    isAnyLoading,
    hasAnyErrors,
    loadingCount,
    
    // Operations
    startLoading,
    stopLoading,
    setLoadingError,
    updateProgress,
    retryOperation,
    executeWithLoading,
    
    // Getters
    getLoadingState,
    
    // Analytics
    getAnalytics: () => ({ ...analytics.current }),
    
    // Utilities
    clearAllErrors: () => setErrors(new Map()),
    clearError: (operationId) => setErrors(prev => {
      const newErrors = new Map(prev);
      newErrors.delete(operationId);
      return newErrors;
    }),
    
    // Bulk operations
    stopAllLoading: () => {
      loadingStates.forEach((_, operationId) => {
        stopLoading(operationId);
      });
    }
  };
};

// Hook for simple loading state (single operation)
export const useSimpleLoading = (initialState = false) => {
  const [isLoading, setIsLoading] = useState(initialState);
  const [error, setError] = useState(null);
  
  const startLoading = useCallback(() => {
    setIsLoading(true);
    setError(null);
  }, []);
  
  const stopLoading = useCallback(() => {
    setIsLoading(false);
  }, []);
  
  const setLoadingError = useCallback((error) => {
    setIsLoading(false);
    setError(error);
  }, []);
  
  const executeWithLoading = useCallback(async (operationFunc) => {
    try {
      startLoading();
      const result = await operationFunc();
      stopLoading();
      return result;
    } catch (error) {
      setLoadingError(error);
      throw error;
    }
  }, [startLoading, stopLoading, setLoadingError]);
  
  return {
    isLoading,
    error,
    startLoading,
    stopLoading,
    setLoadingError,
    executeWithLoading,
    reset: () => {
      setIsLoading(false);
      setError(null);
    }
  };
};

export default useEnhancedLoading;
