import { useState } from "react";
import {
  signInWithGooglePopup,
  signInAuthUserWithEmailAndPassword,
} from "../../../utils/firebase/firebase.utils";
import { toast } from "react-hot-toast";
import { <PERSON> } from "react-router-dom";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/shadcn/button";

const Login = () => {
  const navigate = useNavigate();

  // GOOGLE SIGN IN
  const signInWithGoogle = async () => {
    try {
      console.log("Signing in with google...");
      const { user } = await signInWithGooglePopup();

      // Get Firebase authentication token
      const token = await user.getIdToken();

      // FIND A WAY TO ONLY DO THIS WHEN NECESSARY
      // Send user data and token to backend for MongoDB storage
      const { data: response } = await axios.post(
        "/register",
        {
          displayName: user.displayName,
          email: user.email,
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );
      console.log("Sent token.");

      if (response.error) {
        toast.error(response.error);
      } else {
        // THIS DOESN'T RUN WHEN THE POST REQUEST TO /register RETURNS AN ERROR IF THE USER ALREADY EXISTS.
        toast.success("Google sign-in successful! Welcome!");
        navigate("/user/index");
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      // ONLY DISPLAY THIS ERROR IF IT IS THE FIRST TIME REGISTERING THE USER AND IT FAILS:
      // toast.error("Google sign-in failed. Please try again.");
    }
  };

  const [data, setData] = useState({
    email: "",
    password: "",
  });

  const resetData = () => {
    setData({ email: "", password: "" });
  };

  const loginUser = async (e) => {
    e.preventDefault();
    const { email, password } = data;
    try {
      const { user } = await signInAuthUserWithEmailAndPassword(
        email,
        password
      );
      console.log(user);
      resetData();
      toast.success("Login successful!"); // Display success toast message
    } catch (error) {
      console.error("Error during login: ", error.message); // Log full error message
      if (error.message.includes("auth/invalid-credential")) {
        toast.error(
          "Invalid credentials. Please check your email and password."
        );
      } else if (error.message.includes("auth/wrong-password")) {
        toast.error("Incorrect email or password. Please try again.");
      } else if (error.message.includes("auth/user-not-found")) {
        toast.error("No user with that email found.");
      } else {
        toast.error("An unknown error occurred. Please try again.");
      }
    }
  };

  return (
    <div className="container-sm mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6" style={{ maxWidth: "500px" }}>
          <div className="card shadow-sm border-0 rounded-lg">
            <div className="card-body p-4 mx-auto">
              <h3 className="text-center mb-4">Log In</h3>
              <form onSubmit={loginUser} className="text-start">
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    Email
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    value={data.email}
                    onChange={(e) =>
                      setData({ ...data, email: e.target.value })
                    }
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="password" className="form-label">
                    Password
                  </label>
                  <input
                    type="password"
                    className="form-control"
                    id="password"
                    value={data.password}
                    onChange={(e) =>
                      setData({ ...data, password: e.target.value })
                    }
                  />
                  <Link className="mt-1 pointer text-dark" to="/password-reset">
                    Forgot Password?
                  </Link>
                </div>
                <div className="space-y-3">
                  <Button
                    type="submit"
                    variant="default"
                    size="lg"
                    className="w-full font-semibold"
                  >
                    Login
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    className="w-full font-semibold"
                    onClick={signInWithGoogle}
                  >
                    Sign in with Google
                  </Button>
                </div>
                <div className="mt-3 text-end">
                  No account yet? <Link to="/register">Sign up now!</Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
