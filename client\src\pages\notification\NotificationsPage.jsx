import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import NotificationItem from '../../components/notification/NotificationItem';
import { Button, Card, CardBody, CardHeader, Tabs, Tab, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { Bell, BellRing, CheckCircle, AlertCircle, Info, Users } from 'lucide-react';

const NotificationsPage = () => {
  const { currentUser } = useContext(UserContext);
  const [notifications, setNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 20;

  // Fetch notifications
  const fetchNotifications = async (tabType = activeTab, reset = false) => {
    if (!currentUser) return;

    setIsLoading(true);

    try {
      // Build query
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      // Filter by type if not 'all'
      if (tabType === 'friends') {
        query = query.or('type.eq.friend_request,type.eq.friend_request_accepted,type.eq.friend_request_rejected');
      } else if (tabType === 'projects') {
        query = query.or('type.eq.project_invitation,type.eq.project_invitation_accepted,type.eq.project_invitation_rejected');
      } else if (tabType === 'contributions') {
        query = query.or('type.eq.contribution_approved,type.eq.contribution_rejected,type.eq.contribution_changes_requested,type.eq.contribution_status_update');
      } else if (tabType === 'unread') {
        query = query.eq('is_read', false);
      }

      // Pagination
      const currentPage = reset ? 0 : page;
      query = query.range(currentPage * pageSize, (currentPage + 1) * pageSize - 1);

      const { data, error } = await query;

      if (error) throw error;

      if (reset) {
        setNotifications(data || []);
        setPage(0);
      } else {
        setNotifications(prev => [...prev, ...(data || [])]);
        setPage(currentPage + 1);
      }

      // Check if there are more notifications
      setHasMore((data || []).length === pageSize);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchNotifications('all', true);
  }, [currentUser]);

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    fetchNotifications(tab, true);
  };

  // Mark notification as read
  const markAsRead = async (notificationId) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) throw error;

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: true }
            : notification
        )
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', currentUser.id)
        .eq('is_read', false);

      if (error) throw error;

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: true }))
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  // Load more notifications
  const loadMore = () => {
    fetchNotifications(activeTab);
  };

  // Check if there are unread notifications
  const hasUnread = notifications.some(notification => !notification.is_read);

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-default-100 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-none shadow-lg">
            <CardBody className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                    <Bell size={24} className="text-primary" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                      Notifications
                    </h1>
                    <p className="text-default-600 mt-1">Stay updated with your latest activities</p>
                  </div>
                </div>
                {hasUnread && (
                  <Button
                    color="primary"
                    variant="bordered"
                    onClick={markAllAsRead}
                    startContent={<CheckCircle size={18} />}
                  >
                    Mark all as read
                  </Button>
                )}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Tabs and Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
            <CardHeader className="pb-0">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={handleTabChange}
                variant="underlined"
                color="primary"
                className="w-full"
              >
                <Tab
                  key="all"
                  title={
                    <div className="flex items-center gap-2">
                      <Bell size={16} />
                      <span>All</span>
                    </div>
                  }
                />
                <Tab
                  key="unread"
                  title={
                    <div className="flex items-center gap-2">
                      <BellRing size={16} />
                      <span>Unread</span>
                      {hasUnread && <Chip size="sm" color="primary" variant="solid">•</Chip>}
                    </div>
                  }
                />
                <Tab
                  key="friends"
                  title={
                    <div className="flex items-center gap-2">
                      <Users size={16} />
                      <span>Friends</span>
                    </div>
                  }
                />
                <Tab
                  key="projects"
                  title={
                    <div className="flex items-center gap-2">
                      <AlertCircle size={16} />
                      <span>Projects</span>
                    </div>
                  }
                />
                <Tab
                  key="contributions"
                  title={
                    <div className="flex items-center gap-2">
                      <Info size={16} />
                      <span>Contributions</span>
                    </div>
                  }
                />
              </Tabs>
            </CardHeader>
            <CardBody className="pt-6">
              {isLoading && page === 0 ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-default-600">Loading notifications...</span>
                </div>
              ) : notifications.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 rounded-full bg-default-100 flex items-center justify-center mx-auto mb-4">
                    <Bell size={32} className="text-default-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground mb-2">No notifications</h3>
                  <p className="text-default-600">You don't have any notifications at the moment.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {notifications.map(notification => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                    />
                  ))}

                  {hasMore && (
                    <div className="flex justify-center pt-6">
                      <Button
                        variant="bordered"
                        onClick={loadMore}
                        disabled={isLoading}
                        className="min-w-32"
                      >
                        {isLoading ? 'Loading...' : 'Load more'}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default NotificationsPage;
