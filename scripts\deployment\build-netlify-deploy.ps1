# Netlify Deploy Build Script
# This script creates a single folder that can be deployed to Netlify

Write-Host "=== Starting Netlify Deploy Build ===" -ForegroundColor Cyan

# Define directories
$rootDir = $PSScriptRoot
$clientDir = Join-Path -Path $rootDir -ChildPath "client"
$netlifyDir = Join-Path -Path $rootDir -ChildPath "netlify"
$deployDir = Join-Path -Path $rootDir -ChildPath "netlify-deploy"
$deployNetlifyDir = Join-Path -Path $deployDir -ChildPath "netlify"
$functionsDir = Join-Path -Path $deployNetlifyDir -ChildPath "functions"

# Clean up previous deploy directory if it exists
if (Test-Path $deployDir) {
    Write-Host "Removing previous deploy directory..." -ForegroundColor Yellow
    Remove-Item -Path $deployDir -Recurse -Force
}

# Create deploy directory structure
Write-Host "Creating deploy directory structure..." -ForegroundColor Yellow
New-Item -Path $deployDir -ItemType Directory | Out-Null
New-Item -Path $deployNetlifyDir -ItemType Directory | Out-Null
New-Item -Path $functionsDir -ItemType Directory | Out-Null
Write-Host "  Created directory structure:" -ForegroundColor Gray
Write-Host "    - $deployDir" -ForegroundColor Gray
Write-Host "    - $deployNetlifyDir" -ForegroundColor Gray
Write-Host "    - $functionsDir" -ForegroundColor Gray

# Build the client application
Write-Host "Building client application..." -ForegroundColor Yellow
$originalLocation = Get-Location
try {
    Set-Location -Path $clientDir

    # Check if node_modules exists
    if (-not (Test-Path "node_modules")) {
        Write-Host "  Installing dependencies..." -ForegroundColor Gray
        npm install
    }

    # Build the application (now using Vite)
    Write-Host "  Running build command (Vite)..." -ForegroundColor Gray
    npm run build

    # Copy the build output to the deploy directory
    Write-Host "  Copying build output to deploy directory..." -ForegroundColor Gray
    Copy-Item -Path "dist/*" -Destination $deployDir -Recurse
} catch {
    Write-Host "Error building client application: $_" -ForegroundColor Red
} finally {
    Set-Location -Path $originalLocation
}

# Copy Netlify functions
Write-Host "Copying Netlify functions..." -ForegroundColor Yellow

# Create the functions directory in the deployment folder
$deployFunctionsDir = Join-Path -Path $deployDir -ChildPath "netlify/functions"
if (-not (Test-Path $deployFunctionsDir)) {
    New-Item -Path $deployFunctionsDir -ItemType Directory -Force | Out-Null
    Write-Host "  Created functions directory at $deployFunctionsDir" -ForegroundColor Gray
}

# Check if the netlify/functions directory exists in the source
if (Test-Path $netlifyDir) {
    $netlifyFunctionsDir = Join-Path -Path $netlifyDir -ChildPath "functions"
    if (Test-Path $netlifyFunctionsDir) {
        # Copy all function files (both .js and .mjs)
        $sourceFiles = Get-ChildItem -Path $netlifyFunctionsDir -Include @("*.js", "*.mjs") -File
        foreach ($file in $sourceFiles) {
            $destPath = Join-Path -Path $deployFunctionsDir -ChildPath $file.Name
            Copy-Item -Path $file.FullName -Destination $destPath -Force
            Write-Host "  Copied function: $($file.Name)" -ForegroundColor Gray
        }

        # Copy package.json if it exists
        $packageJsonPath = Join-Path -Path $netlifyFunctionsDir -ChildPath "package.json"
        if (Test-Path $packageJsonPath) {
            $destPackageJsonPath = Join-Path -Path $deployFunctionsDir -ChildPath "package.json"
            Copy-Item -Path $packageJsonPath -Destination $destPackageJsonPath -Force
            Write-Host "  Copied package.json for functions" -ForegroundColor Gray
        }
    } else {
        Write-Host "  No Netlify functions found at $netlifyFunctionsDir" -ForegroundColor Yellow
    }
} else {
    Write-Host "  Netlify directory not found at $netlifyDir" -ForegroundColor Yellow
}

# Create a package.json file for the functions if it doesn't exist
$destPackageJsonPath = Join-Path -Path $deployFunctionsDir -ChildPath "package.json"
if (-not (Test-Path $destPackageJsonPath)) {
    Write-Host "  Creating package.json for functions..." -ForegroundColor Yellow
    $packageJsonContent = @"
{
  "name": "netlify-functions",
  "version": "1.0.0",
  "description": "Netlify Functions for Royaltea",
  "dependencies": {
    "@supabase/supabase-js": "^2.39.7",
    "nodemailer": "^6.9.9"
  }
}
"@
    Set-Content -Path $destPackageJsonPath -Value $packageJsonContent
    Write-Host "  Created package.json for functions" -ForegroundColor Gray
}

# Create guaranteed functions to ensure deployment works
Write-Host "Creating guaranteed functions..." -ForegroundColor Yellow

# Hello function (ES modules)
$helloFunctionPath = Join-Path -Path $deployFunctionsDir -ChildPath "hello.mjs"
$helloFunctionContent = @"
// Modern ES modules hello function
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  return new Response(
    JSON.stringify({
      message: "Hello from Netlify Functions (ES modules)!",
      timestamp: new Date().toISOString()
    }),
    { headers }
  );
};

// Configure the function path
export const config = {
  path: "/api/hello"
};
"@
Set-Content -Path $helloFunctionPath -Value $helloFunctionContent
Write-Host "  Created hello.mjs (ES modules)" -ForegroundColor Gray

# Roadmap function (ES modules)
$roadmapFunctionPath = Join-Path -Path $deployFunctionsDir -ChildPath "roadmap.mjs"
$roadmapFunctionContent = @"
// Roadmap API function using ES modules

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Hardcoded roadmap data for testing
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          }
        ]
      }
    ];

    // Calculate stats
    const stats = calculateStats(roadmapData);

    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'netlify-function'
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in roadmap function:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/roadmap"
};
"@
Set-Content -Path $roadmapFunctionPath -Value $roadmapFunctionContent
Write-Host "  Created roadmap.mjs (ES modules)" -ForegroundColor Gray

# Verify the functions were copied
$copiedFiles = Get-ChildItem -Path $deployFunctionsDir -Include @("*.js", "*.mjs") -File
if ($copiedFiles.Count -gt 0) {
    Write-Host "Successfully copied/created $($copiedFiles.Count) functions:" -ForegroundColor Green
    foreach ($file in $copiedFiles) {
        Write-Host "  - $($file.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "WARNING: No function files were found in the deployment directory!" -ForegroundColor Red
}

# Create guaranteed test functions
Write-Host "Creating guaranteed test functions..." -ForegroundColor Yellow

# Create package.json for functions if it doesn't exist
$destPackageJsonPath = Join-Path -Path $deployFunctionsDir -ChildPath "package.json"
if (-not (Test-Path $destPackageJsonPath)) {
    $packageJsonContent = @"
{
  "name": "netlify-functions",
  "version": "1.0.0",
  "description": "Netlify Functions for Royaltea",
  "dependencies": {
    "@supabase/supabase-js": "^2.39.7",
    "nodemailer": "^6.9.9"
  }
}
"@
    Set-Content -Path $destPackageJsonPath -Value $packageJsonContent
    Write-Host "  Created package.json for functions" -ForegroundColor Gray
}

# Legacy format function
$testFunctionPath = Join-Path -Path $deployFunctionsDir -ChildPath "test-function.js"
$testFunctionContent = @"
// Simple test function (legacy format)
exports.handler = async function(event, context) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'GET, OPTIONS'
    },
    body: JSON.stringify({
      success: true,
      message: "Legacy function format is working!",
      timestamp: new Date().toISOString()
    })
  };
};
"@
Set-Content -Path $testFunctionPath -Value $testFunctionContent
Write-Host "  Created test-function.js (legacy format)" -ForegroundColor Gray

# Modern format function
$modernFunctionPath = Join-Path -Path $deployFunctionsDir -ChildPath "hello.mjs"
$modernFunctionContent = @"
// Modern ES modules hello function
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  return new Response(
    JSON.stringify({
      message: "Hello from Netlify Functions (ES modules)!",
      timestamp: new Date().toISOString()
    }),
    { headers }
  );
};

// Configure the function path
export const config = {
  path: "/api/hello"
};
"@
Set-Content -Path $modernFunctionPath -Value $modernFunctionContent
Write-Host "  Created hello.mjs (modern format)" -ForegroundColor Gray

# Roadmap function
$roadmapFunctionPath = Join-Path -Path $deployFunctionsDir -ChildPath "roadmap.mjs"
$roadmapFunctionContent = @"
// Roadmap API function using ES modules

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

// Main function handler
export default async (req, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Hardcoded roadmap data for testing
    const roadmapData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          }
        ]
      }
    ];

    // Calculate stats
    const stats = calculateStats(roadmapData);

    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmapData,
        stats: stats,
        source: 'netlify-function'
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in roadmap function:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers
      }
    );
  }
};

// Configure the function path
export const config = {
  path: "/api/roadmap"
};
"@
Set-Content -Path $roadmapFunctionPath -Value $roadmapFunctionContent
Write-Host "  Created roadmap.mjs (ES modules)" -ForegroundColor Gray

# Copy netlify.toml
Write-Host "Copying netlify.toml..." -ForegroundColor Yellow
$netlifyTomlPath = Join-Path -Path $rootDir -ChildPath "netlify.toml"
if (Test-Path $netlifyTomlPath) {
    Copy-Item -Path $netlifyTomlPath -Destination $deployDir
    Write-Host "  Copied netlify.toml" -ForegroundColor Gray
}

# Copy test files
Write-Host "Copying test files..." -ForegroundColor Yellow

# Copy the API test page
$testApiPagePath = Join-Path -Path $deployDir -ChildPath "roadmap-api-test.html"
Copy-Item -Path "test-roadmap-api.html" -Destination $testApiPagePath -ErrorAction SilentlyContinue

# Copy the improved test page
$improvedTestApiPagePath = Join-Path -Path $deployDir -ChildPath "roadmap-api-test-improved.html"
Copy-Item -Path "roadmap-api-test-improved.html" -Destination $improvedTestApiPagePath -ErrorAction SilentlyContinue
if (Test-Path $improvedTestApiPagePath) {
    Write-Host "  Added roadmap-api-test-improved.html" -ForegroundColor Gray
}

# If the file doesn't exist, create it
if (-not (Test-Path $testApiPagePath)) {
    Write-Host "  Creating API test page..." -ForegroundColor Gray
    # Create a simplified test page to avoid PowerShell parsing issues
    $testApiPageContent = @'
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Roadmap API Test</title>
  <style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #333; }
    button { padding: 10px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px; margin-bottom: 20px; }
    button:hover { background-color: #45a049; }
    pre { background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow-x: auto; }
    .error { color: red; }
    .success { color: green; }
  </style>
</head>
<body>
  <h1>Roadmap API Test</h1>

  <div>
    <button id="testNetlifyFunction">Test Netlify Function</button>
    <button id="testPublicApi">Test Public API</button>
    <button id="testStaticApi">Test Static API</button>
    <button id="testLocalData">Test Local Data</button>
  </div>

  <div id="result">
    <h2>Result:</h2>
    <pre id="output">Click a button to test the API...</pre>
  </div>

  <script>
    // Test Netlify Function
    document.getElementById('testNetlifyFunction').addEventListener('click', async () => {
      const output = document.getElementById('output');
      output.innerHTML = 'Testing Netlify Function...';

      try {
        const response = await fetch('/.netlify/functions/roadmap-static');
        const data = await response.json();
        output.innerHTML = '<span class="success">Success!</span>\n\n' + JSON.stringify(data, null, 2);
      } catch (error) {
        output.innerHTML = '<span class="error">Error:</span>\n\n' + error.message;
      }
    });

    // Test Public API
    document.getElementById('testPublicApi').addEventListener('click', async () => {
      const output = document.getElementById('output');
      output.innerHTML = 'Testing Public API...';

      try {
        const response = await fetch('/api/roadmap');
        const data = await response.json();
        output.innerHTML = '<span class="success">Success!</span>\n\n' + JSON.stringify(data, null, 2);
      } catch (error) {
        output.innerHTML = '<span class="error">Error:</span>\n\n' + error.message;
      }
    });

    // Test Static API
    document.getElementById('testStaticApi').addEventListener('click', async () => {
      const output = document.getElementById('output');
      output.innerHTML = 'Testing Static API...';

      try {
        const response = await fetch('/api/roadmap-static');
        const data = await response.json();
        output.innerHTML = '<span class="success">Success!</span>\n\n' + JSON.stringify(data, null, 2);
      } catch (error) {
        output.innerHTML = '<span class="error">Error:</span>\n\n' + error.message;
      }
    });

    // Test Local Data
    document.getElementById('testLocalData').addEventListener('click', async () => {
      const output = document.getElementById('output');
      output.innerHTML = 'Loading local roadmap data...';

      try {
        // Sample roadmap data
        const roadmapData = [
          {
            id: 1,
            title: "Foundation & User Management",
            timeframe: "Completed",
            expanded: true,
            sections: [
              {
                id: "1.1",
                title: "Project Setup & Configuration",
                tasks: [
                  { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
                  { id: "1.1.2", text: "Set up development environment", completed: true },
                  { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
                ]
              }
            ]
          },
          {
            id: 2,
            title: "Project Creation & Management",
            timeframe: "Phase 1",
            expanded: false,
            sections: [
              {
                id: "2.1",
                title: "Project Wizard",
                tasks: [
                  { id: "2.1.1", text: "Design project creation flow", completed: true },
                  { id: "2.1.2", text: "Implement project basics form", completed: true },
                  { id: "2.1.3", text: "Add team & contributors section", completed: true }
                ]
              }
            ]
          }
        ];

        // Calculate stats
        const stats = calculateStats(roadmapData);

        // Create result object
        const result = {
          success: true,
          data: roadmapData,
          stats: stats
        };

        // Display the data
        output.innerHTML = '<span class="success">Success!</span>\n\n' + JSON.stringify(result, null, 2);
      } catch (error) {
        output.innerHTML = '<span class="error">Error:</span>\n\n' + error.message;
      }
    });

    // Function to calculate stats
    function calculateStats(phases) {
      let totalTasks = 0;
      let completedTasks = 0;
      let phaseStats = [];

      phases.forEach(phase => {
        let phaseTotalTasks = 0;
        let phaseCompletedTasks = 0;

        phase.sections.forEach(section => {
          phaseTotalTasks += section.tasks.length;
          phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
        });

        totalTasks += phaseTotalTasks;
        completedTasks += phaseCompletedTasks;

        phaseStats.push({
          id: phase.id,
          title: phase.title,
          timeframe: phase.timeframe,
          progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
        });
      });

      return {
        totalTasks,
        completedTasks,
        progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        phases: phaseStats
      };
    }
  </script>
</body>
</html>
'@
    Set-Content -Path $testApiPagePath -Value $testApiPageContent
}
Write-Host "  Added roadmap-api-test.html" -ForegroundColor Gray

# Copy the landing page examples
$landingPagePath = Join-Path -Path $deployDir -ChildPath "landing-page-example.html"
Copy-Item -Path "landing-page-roadmap-example.html" -Destination $landingPagePath -ErrorAction SilentlyContinue
if (Test-Path $landingPagePath) {
    Write-Host "  Added landing-page-example.html" -ForegroundColor Gray
}

# Copy the simple landing page example
$simpleLandingPagePath = Join-Path -Path $deployDir -ChildPath "simple-landing-example.html"
Copy-Item -Path "simple-landing-example.html" -Destination $simpleLandingPagePath -ErrorAction SilentlyContinue
if (Test-Path $simpleLandingPagePath) {
    Write-Host "  Added simple-landing-example.html" -ForegroundColor Gray
}

# Copy the function test pages
$functionTestPath = Join-Path -Path $deployDir -ChildPath "function-test.html"
Copy-Item -Path "function-test.html" -Destination $functionTestPath -ErrorAction SilentlyContinue
if (Test-Path $functionTestPath) {
    Write-Host "  Added function-test.html" -ForegroundColor Gray
}

# Copy the Netlify function test pages
$netlifyFunctionTestPath = Join-Path -Path $deployDir -ChildPath "netlify-function-test.html"
Copy-Item -Path "netlify-function-test.html" -Destination $netlifyFunctionTestPath -ErrorAction SilentlyContinue
if (Test-Path $netlifyFunctionTestPath) {
    Write-Host "  Added netlify-function-test.html" -ForegroundColor Gray
}

# Copy the function test pages
$simpleFunctionTestPath = Join-Path -Path $deployDir -ChildPath "simple-function-test.html"
Copy-Item -Path "simple-function-test.html" -Destination $simpleFunctionTestPath -ErrorAction SilentlyContinue
if (Test-Path $simpleFunctionTestPath) {
    Write-Host "  Added simple-function-test.html" -ForegroundColor Gray
}

# Copy the direct function test page
$directFunctionTestPath = Join-Path -Path $deployDir -ChildPath "direct-function-test.html"
Copy-Item -Path "direct-function-test.html" -Destination $directFunctionTestPath -ErrorAction SilentlyContinue
if (Test-Path $directFunctionTestPath) {
    Write-Host "  Added direct-function-test.html" -ForegroundColor Gray
}

# Copy the Netlify functions test page
$netlifyFunctionsTestPath = Join-Path -Path $deployDir -ChildPath "netlify-functions-test.html"
Copy-Item -Path "netlify-functions-test.html" -Destination $netlifyFunctionsTestPath -ErrorAction SilentlyContinue
if (Test-Path $netlifyFunctionsTestPath) {
    Write-Host "  Added netlify-functions-test.html" -ForegroundColor Gray
}

# Copy the API test page
$apiTestPath = Join-Path -Path $deployDir -ChildPath "api-test.html"
Copy-Item -Path "api-test.html" -Destination $apiTestPath -ErrorAction SilentlyContinue
if (Test-Path $apiTestPath) {
    Write-Host "  Added api-test.html" -ForegroundColor Gray
}

# Copy the static roadmap JSON file
$staticRoadmapPath = Join-Path -Path $deployDir -ChildPath "roadmap.json"
Copy-Item -Path "static-roadmap.json" -Destination $staticRoadmapPath -ErrorAction SilentlyContinue
if (Test-Path $staticRoadmapPath) {
    Write-Host "  Added roadmap.json" -ForegroundColor Gray
} else {
    # If the file doesn't exist, create it directly
    Write-Host "  Creating roadmap.json..." -ForegroundColor Gray
    Copy-Item -Path "static-roadmap.json" -Destination $staticRoadmapPath
}

# Create a package.json file for the functions
Write-Host "Creating package.json for functions..." -ForegroundColor Yellow
$packageJsonPath = Join-Path -Path $functionsDir -ChildPath "package.json"
$packageJsonContent = @"
{
  "name": "netlify-functions",
  "version": "1.0.0",
  "description": "Netlify Functions for Royaltea",
  "dependencies": {
    "@supabase/supabase-js": "^2.39.7"
  }
}
"@
Set-Content -Path $packageJsonPath -Value $packageJsonContent
Write-Host "  Created package.json for functions" -ForegroundColor Gray

# Copy the static-roadmap.json file to the functions directory for fallback
Write-Host "  Copying static-roadmap.json to functions directory..." -ForegroundColor Gray
$staticRoadmapFunctionsPath = Join-Path -Path $functionsDir -ChildPath "static-roadmap.json"
Copy-Item -Path "static-roadmap.json" -Destination $staticRoadmapFunctionsPath -ErrorAction SilentlyContinue

# Create a simple test function to verify deployment
Write-Host "Creating test function..." -ForegroundColor Yellow
$testFunctionPath = Join-Path -Path $functionsDir -ChildPath "deployment-test.js"
$testFunctionContent = @"
// Simple deployment test function
exports.handler = async function(event, context) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Allow-Methods': 'GET, OPTIONS'
    },
    body: JSON.stringify({
      success: true,
      message: "Netlify function deployment successful!",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      deployTime: new Date().toISOString()
    })
  };
};
"@
Set-Content -Path $testFunctionPath -Value $testFunctionContent
Write-Host "  Created deployment-test.js function" -ForegroundColor Gray

# Create agreement notifications function
Write-Host "Creating agreement notifications function..." -ForegroundColor Yellow
$agreementNotificationsPath = Join-Path -Path $functionsDir -ChildPath "agreement-notifications.js"
$agreementNotificationsContent = @"
// Agreement notification function
const { createClient } = require('@supabase/supabase-js');
const nodemailer = require('nodemailer');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Initialize email transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST || 'smtp.gmail.com',
  port: process.env.EMAIL_PORT || 587,
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS
  }
});

// Email templates
const emailTemplates = {
  newAgreement: {
    subject: 'New Agreement for Project: {{projectName}}',
    body: `
      <h2>New Agreement for {{projectName}}</h2>
      <p>Hello {{recipientName}},</p>
      <p>A new agreement has been created for the project <strong>{{projectName}}</strong>.</p>
      <p>Please review and sign the agreement at your earliest convenience.</p>
      <p><a href="{{agreementUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Agreement</a></p>
      <p>Thank you,<br>The Royaltea Team</p>
    `
  },
  agreementUpdated: {
    subject: 'Agreement Updated for Project: {{projectName}}',
    body: `
      <h2>Agreement Updated for {{projectName}}</h2>
      <p>Hello {{recipientName}},</p>
      <p>The agreement for project <strong>{{projectName}}</strong> has been updated to version {{version}}.</p>
      <p>Please review and sign the updated agreement at your earliest convenience.</p>
      <p><a href="{{agreementUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Agreement</a></p>
      <p>Thank you,<br>The Royaltea Team</p>
    `
  },
  agreementSigned: {
    subject: 'Agreement Signed for Project: {{projectName}}',
    body: `
      <h2>Agreement Signed for {{projectName}}</h2>
      <p>Hello {{recipientName}},</p>
      <p><strong>{{signerName}}</strong> has signed the agreement for project <strong>{{projectName}}</strong>.</p>
      <p><a href="{{agreementUrl}}" style="display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">View Agreement</a></p>
      <p>Thank you,<br>The Royaltea Team</p>
    `
  }
};

// Replace template variables
const replaceTemplateVars = (template, vars) => {
  let result = template;
  for (const [key, value] of Object.entries(vars)) {
    result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
  }
  return result;
};

// Send email notification
const sendEmailNotification = async (recipient, templateName, templateVars) => {
  const template = emailTemplates[templateName];

  if (!template) {
    throw new Error(`Template ${templateName} not found`);
  }

  const subject = replaceTemplateVars(template.subject, templateVars);
  const html = replaceTemplateVars(template.body, templateVars);

  const mailOptions = {
    from: process.env.EMAIL_FROM || '<EMAIL>',
    to: recipient,
    subject,
    html
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

// Notify about new agreement
const notifyNewAgreement = async (agreementId) => {
  try {
    // Get agreement details
    const { data: agreement, error: agreementError } = await supabase
      .from('contributor_agreements')
      .select(`
        *,
        project:projects(*),
        contributor:project_contributors(*)
      `)
      .eq('id', agreementId)
      .single();

    if (agreementError) throw agreementError;

    // Get contributor user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', agreement.contributor.user_id)
      .single();

    if (userError) throw userError;

    // Send notification email
    await sendEmailNotification(
      user.email,
      'newAgreement',
      {
        recipientName: user.display_name || user.email,
        projectName: agreement.project.name || agreement.project.title,
        agreementUrl: `https://royalty.technology/project/${agreement.project_id}/agreements`
      }
    );

    return { success: true };
  } catch (error) {
    console.error('Error notifying about new agreement:', error);
    return { success: false, error: error.message };
  }
};

// Notify about updated agreement
const notifyAgreementUpdated = async (agreementId) => {
  try {
    // Get agreement details
    const { data: agreement, error: agreementError } = await supabase
      .from('contributor_agreements')
      .select(`
        *,
        project:projects(*),
        contributor:project_contributors(*)
      `)
      .eq('id', agreementId)
      .single();

    if (agreementError) throw agreementError;

    // Get contributor user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', agreement.contributor.user_id)
      .single();

    if (userError) throw userError;

    // Send notification email
    await sendEmailNotification(
      user.email,
      'agreementUpdated',
      {
        recipientName: user.display_name || user.email,
        projectName: agreement.project.name || agreement.project.title,
        version: agreement.version || 1,
        agreementUrl: `https://royalty.technology/project/${agreement.project_id}/agreements`
      }
    );

    return { success: true };
  } catch (error) {
    console.error('Error notifying about updated agreement:', error);
    return { success: false, error: error.message };
  }
};

// Notify about signed agreement
const notifyAgreementSigned = async (agreementId) => {
  try {
    // Get agreement details
    const { data: agreement, error: agreementError } = await supabase
      .from('contributor_agreements')
      .select(`
        *,
        project:projects(*),
        contributor:project_contributors(*)
      `)
      .eq('id', agreementId)
      .single();

    if (agreementError) throw agreementError;

    // Get project owner
    const { data: projectOwner, error: ownerError } = await supabase
      .from('project_contributors')
      .select(`
        *,
        user:users(*)
      `)
      .eq('project_id', agreement.project_id)
      .eq('permission_level', 'Owner')
      .single();

    if (ownerError) throw ownerError;

    // Get signer details
    const { data: signer, error: signerError } = await supabase
      .from('users')
      .select('*')
      .eq('id', agreement.contributor.user_id)
      .single();

    if (signerError) throw signerError;

    // Send notification email to project owner
    await sendEmailNotification(
      projectOwner.user.email,
      'agreementSigned',
      {
        recipientName: projectOwner.user.display_name || projectOwner.user.email,
        signerName: signer.display_name || signer.email,
        projectName: agreement.project.name || agreement.project.title,
        agreementUrl: `https://royalty.technology/project/${agreement.project_id}/agreements`
      }
    );

    return { success: true };
  } catch (error) {
    console.error('Error notifying about signed agreement:', error);
    return { success: false, error: error.message };
  }
};

// Main handler
exports.handler = async (event, context) => {
  // Set CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS'
  };

  // Handle preflight request
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers
    };
  }

  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    const body = JSON.parse(event.body);
    const { action, agreementId } = body;

    if (!action || !agreementId) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Missing required parameters' })
      };
    }

    let result;

    switch (action) {
      case 'new':
        result = await notifyNewAgreement(agreementId);
        break;
      case 'update':
        result = await notifyAgreementUpdated(agreementId);
        break;
      case 'sign':
        result = await notifyAgreementSigned(agreementId);
        break;
      default:
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Invalid action' })
        };
    }

    return {
      statusCode: result.success ? 200 : 500,
      headers,
      body: JSON.stringify(result)
    };
  } catch (error) {
    console.error('Error processing request:', error);

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: error.message })
    };
  }
};
"@
Set-Content -Path $agreementNotificationsPath -Value $agreementNotificationsContent
Write-Host "  Created agreement-notifications.js function" -ForegroundColor Gray

Write-Host "=== Netlify Deploy Build Complete ===" -ForegroundColor Green
Write-Host "The deploy folder is ready at: $deployDir" -ForegroundColor Cyan
Write-Host "You can now deploy this folder to Netlify using the Netlify CLI or by dragging and dropping it to the Netlify dashboard." -ForegroundColor Cyan
Write-Host "After deployment, test the API by navigating to: https://your-site.netlify.app/roadmap-api-test.html" -ForegroundColor Yellow
