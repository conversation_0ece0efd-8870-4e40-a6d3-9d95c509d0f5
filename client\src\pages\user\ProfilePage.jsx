import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import LoadingAnimation from '../../components/layout/LoadingAnimation';
import TeamProfileSection from '../../components/team/TeamProfileSection';
import UserSkillsList from '../../components/skills/UserSkillsList';
import SkillForm from '../../components/skills/SkillForm';
import VerificationForm from '../../components/skills/VerificationForm';
import { Button, Card, CardBody, CardHeader, Input, Textarea, Avatar, Chip, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { User, Eye, Star, Upload, Save, Github, Twitter, Linkedin, Globe } from 'lucide-react';

const ProfilePage = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userData, setUserData] = useState({
    display_name: '',
    bio: '',
    avatar_url: '',
    certifications: [], // Read-only, earned through the platform
    awards: [], // Read-only, earned through the platform
    stats: {
      projects_completed: 0,
      contributions: 0,
      hours_tracked: 0
    },
    social_links: {
      github: '',
      twitter: '',
      linkedin: '',
      website: ''
    },
    preferences: {
      theme: 'light',
      notifications_enabled: true
    }
  });
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState('');

  // Skills management state
  const [showSkillForm, setShowSkillForm] = useState(false);
  const [showVerificationForm, setShowVerificationForm] = useState(false);
  const [selectedUserSkill, setSelectedUserSkill] = useState(null);

  // Load user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Get user data from Supabase
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', currentUser.id)
          .single();

        if (error) {
          throw error;
        }

        // Determine default avatar
        const defaultAvatar = data.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png';
        console.log('Default avatar path:', defaultAvatar);

        // Set user data with defaults for missing fields
        // Always prioritize the database display_name to prevent it from being reset
        setUserData({
          display_name: data.display_name || currentUser.user_metadata?.full_name || '',
          bio: data.bio || '',
          avatar_url: data.avatar_url || defaultAvatar,
          certifications: data.certifications || [],
          awards: data.awards || [],
          stats: data.stats || {
            projects_completed: 0,
            contributions: 0,
            hours_tracked: 0
          },
          social_links: data.social_links || {
            github: '',
            twitter: '',
            linkedin: '',
            website: ''
          },
          preferences: data.preferences || {
            theme: 'light',
            notifications_enabled: true
          }
        });

        // Log the display name to help with debugging
        console.log('Loaded display name:', data.display_name);

        // Set avatar preview
        const avatarUrl = data.avatar_url || (data.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png');
        setAvatarPreview(avatarUrl);
        console.log('Setting avatar preview to:', avatarUrl);
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser]);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle nested objects
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setUserData({
        ...userData,
        [parent]: {
          ...userData[parent],
          [child]: value
        }
      });
    } else {
      setUserData({
        ...userData,
        [name]: value
      });
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    const [parent, child] = name.split('.');

    setUserData({
      ...userData,
      [parent]: {
        ...userData[parent],
        [child]: checked
      }
    });
  };

  // Handle avatar file selection
  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('Image size should be less than 2MB');
      return;
    }

    setAvatarFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setAvatarPreview(reader.result);
    };
    reader.readAsDataURL(file);
  };

  // Upload avatar to Supabase storage
  const uploadAvatar = async () => {
    if (!avatarFile || !currentUser) return null;

    try {
      // Create a unique file name
      const fileExt = avatarFile.name.split('.').pop();
      const fileName = `avatar-${currentUser.id}.${fileExt}`;
      const filePath = fileName; // Simplified path

      console.log('Uploading avatar with path:', filePath);

      // First, try to delete any existing avatar with the same name
      try {
        await supabase.storage
          .from('user-avatars')
          .remove([filePath]);
        console.log('Removed existing avatar if any');
      } catch (deleteError) {
        // Ignore delete errors, just log them
        console.log('No existing avatar to delete or error:', deleteError);
      }

      // Upload to Supabase Storage
      const { data, error: uploadError } = await supabase.storage
        .from('user-avatars')
        .upload(filePath, avatarFile, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error('Upload error details:', uploadError);
        throw uploadError;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('user-avatars')
        .getPublicUrl(filePath);

      console.log('Upload successful, public URL:', urlData.publicUrl);

      // Add a timestamp to force browser to reload the image
      const cachedUrl = `${urlData.publicUrl}?t=${Date.now()}`;
      return cachedUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast.error(`Failed to upload profile picture: ${error.message || 'Unknown error'}`);
      return null;
    }
  };

  // Save profile data
  const handleSave = async () => {
    if (!currentUser) return;

    try {
      setSaving(true);

      // Upload avatar if a new one was selected
      let avatarUrl = userData.avatar_url;
      if (avatarFile) {
        const uploadedUrl = await uploadAvatar();
        if (uploadedUrl) {
          avatarUrl = uploadedUrl;
        }
      }

      // Update user data in Supabase
      const { error } = await supabase
        .from('users')
        .update({
          display_name: userData.display_name,
          bio: userData.bio,
          avatar_url: avatarUrl,
          social_links: userData.social_links,
          preferences: userData.preferences
        })
        .eq('id', currentUser.id);

      if (error) {
        throw error;
      }

      // Update the avatar URL in the state
      setUserData({
        ...userData,
        avatar_url: avatarUrl
      });

      toast.success('Profile updated successfully');

      // Reset the avatar file state
      setAvatarFile(null);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  // Navigate to public profile
  const viewPublicProfile = () => {
    window.location.href = `/profile/${currentUser.id}`;
  };

  // Skill management handlers
  const handleAddSkill = () => {
    setSelectedUserSkill(null);
    setShowSkillForm(true);
  };

  const handleEditSkill = (userSkill) => {
    setSelectedUserSkill(userSkill);
    setShowSkillForm(true);
  };

  const handleAddVerification = (userSkill) => {
    setSelectedUserSkill(userSkill);
    setShowVerificationForm(true);
  };

  const handleSkillSaved = (savedSkill) => {
    toast.success('Skill saved successfully');
    // Force refresh of skills list
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  const handleVerificationSaved = (savedVerification) => {
    toast.success('Verification added successfully');
    // Force refresh of skills list
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-default-100 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-primary/10 to-secondary/10 border-none shadow-lg">
            <CardBody className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                    Your Profile
                  </h1>
                  <p className="text-default-600 mt-2">Customize your profile and preferences</p>
                </div>
                <div className="flex gap-3">
                  <Button
                    variant="bordered"
                    startContent={<Eye size={18} />}
                    onClick={viewPublicProfile}
                    className="border-primary/30 hover:border-primary"
                  >
                    View Public Profile
                  </Button>
                  <Button
                    as="a"
                    href="/retro-profile"
                    variant="bordered"
                    startContent={<Star size={18} />}
                    className="border-secondary/30 hover:border-secondary"
                  >
                    Try Retro Profile
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <h2 className="text-xl font-semibold text-foreground">Basic Information</h2>
              </CardHeader>
              <CardBody className="space-y-6">
                <Input
                  label="Display Name"
                  placeholder="Enter your display name"
                  value={userData.display_name}
                  onChange={(e) => handleChange({ target: { name: 'display_name', value: e.target.value } })}
                  variant="bordered"
                  startContent={<User size={18} />}
                />

                <Textarea
                  label="Bio"
                  placeholder="Tell us about yourself"
                  value={userData.bio}
                  onChange={(e) => handleChange({ target: { name: 'bio', value: e.target.value } })}
                  variant="bordered"
                  minRows={4}
                />

                <div className="space-y-3">
                  <label className="text-sm font-medium text-foreground">Profile Picture</label>
                  <div className="flex items-center gap-4">
                    <Avatar
                      src={avatarPreview || userData.avatar_url || (userData.is_premium ? '/default-avatar-crown.png' : '/default-avatar-specs.png')}
                      size="lg"
                      className="w-20 h-20"
                    />
                    <div className="flex-1">
                      <input
                        type="file"
                        id="avatar"
                        accept="image/*"
                        onChange={handleAvatarChange}
                        className="hidden"
                      />
                      <Button
                        as="label"
                        htmlFor="avatar"
                        variant="bordered"
                        startContent={<Upload size={18} />}
                        className="cursor-pointer"
                      >
                        Upload New Picture
                      </Button>
                      <p className="text-xs text-default-500 mt-2">Max size: 2MB. Recommended: square image (1:1 ratio)</p>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Certifications */}
            <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <h2 className="text-xl font-semibold text-foreground">Certifications</h2>
              </CardHeader>
              <CardBody>
                <p className="text-sm text-default-600 mb-4">
                  Certifications are earned through completing courses and assessments on Royaltea.
                </p>

                {userData.certifications && userData.certifications.length > 0 ? (
                  <div className="space-y-3">
                    {userData.certifications.map((cert, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-success/10 border border-success/20">
                        <div className="w-10 h-10 rounded-full bg-success/20 flex items-center justify-center">
                          <Star size={18} className="text-success" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-foreground">{cert.name}</h3>
                          <p className="text-xs text-default-500">Earned on: {new Date(cert.earned_at).toLocaleDateString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 rounded-full bg-default-100 flex items-center justify-center mx-auto mb-3">
                      <Star size={24} className="text-default-400" />
                    </div>
                    <p className="text-default-600 mb-3">You haven't earned any certifications yet.</p>
                    <Button as="a" href="/learn" size="sm" color="primary" variant="bordered">
                      Start Learning
                    </Button>
                  </div>
                )}
              </CardBody>
            </Card>

            {/* Awards & Achievements */}
            <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <h2 className="text-xl font-semibold text-foreground">Awards & Achievements</h2>
              </CardHeader>
              <CardBody>
                <p className="text-sm text-default-600 mb-4">
                  Awards are earned by reaching milestones and accomplishments on Royaltea.
                </p>

                {userData.awards && userData.awards.length > 0 ? (
                  <div className="space-y-3">
                    {userData.awards.map((award, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-warning/10 border border-warning/20">
                        <div className="w-10 h-10 rounded-full bg-warning/20 flex items-center justify-center mt-1">
                          <Star size={18} className="text-warning" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-foreground">{award.name}</h3>
                          <p className="text-sm text-default-600 mb-1">{award.description}</p>
                          <p className="text-xs text-default-500">Earned on: {new Date(award.earned_at).toLocaleDateString()}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 rounded-full bg-default-100 flex items-center justify-center mx-auto mb-3">
                      <Star size={24} className="text-default-400" />
                    </div>
                    <p className="text-default-600 mb-2">You haven't earned any awards yet.</p>
                    <p className="text-xs text-default-500">Complete projects and track your contributions to earn awards.</p>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>
        </div>

        {/* Social Links Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-6"
        >
          <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <h2 className="text-xl font-semibold text-foreground">Social Links</h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="GitHub"
                  placeholder="Your GitHub profile URL"
                  value={userData.social_links.github}
                  onChange={(e) => handleChange({ target: { name: 'social_links.github', value: e.target.value } })}
                  variant="bordered"
                  startContent={<Github size={18} />}
                />

                <Input
                  label="Twitter"
                  placeholder="Your Twitter profile URL"
                  value={userData.social_links.twitter}
                  onChange={(e) => handleChange({ target: { name: 'social_links.twitter', value: e.target.value } })}
                  variant="bordered"
                  startContent={<Twitter size={18} />}
                />

                <Input
                  label="LinkedIn"
                  placeholder="Your LinkedIn profile URL"
                  value={userData.social_links.linkedin}
                  onChange={(e) => handleChange({ target: { name: 'social_links.linkedin', value: e.target.value } })}
                  variant="bordered"
                  startContent={<Linkedin size={18} />}
                />

                <Input
                  label="Personal Website"
                  placeholder="Your personal website URL"
                  value={userData.social_links.website}
                  onChange={(e) => handleChange({ target: { name: 'social_links.website', value: e.target.value } })}
                  variant="bordered"
                  startContent={<Globe size={18} />}
                />
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Skills Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mt-6"
        >
          <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <h2 className="text-xl font-semibold text-foreground">Skills</h2>
            </CardHeader>
            <CardBody>
              <p className="text-sm text-default-600 mb-4">
                Showcase your skills and expertise. Add verifications to increase your credibility.
              </p>
              <UserSkillsList
                userId={currentUser?.id}
                isOwnProfile={true}
                onAddSkill={handleAddSkill}
              />
            </CardBody>
          </Card>
        </motion.div>

        {/* Teams Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-6"
        >
          <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <h2 className="text-xl font-semibold text-foreground">Teams</h2>
            </CardHeader>
            <CardBody>
              <p className="text-sm text-default-600 mb-4">
                Teams allow you to collaborate with others on projects and manage ownership collectively.
              </p>
              <TeamProfileSection userId={currentUser?.id} />
            </CardBody>
          </Card>
        </motion.div>

        {/* Preferences & Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <h2 className="text-xl font-semibold text-foreground">Preferences</h2>
            </CardHeader>
            <CardBody className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">Theme</label>
                <select
                  value={userData.preferences.theme}
                  onChange={(e) => handleChange({ target: { name: 'preferences.theme', value: e.target.value } })}
                  className="w-full px-3 py-2 rounded-lg border border-default-300 bg-background text-foreground focus:border-primary focus:outline-none"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="system">System Default</option>
                </select>
              </div>

              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="notifications"
                  checked={userData.preferences.notifications_enabled}
                  onChange={(e) => handleCheckboxChange({ target: { name: 'preferences.notifications_enabled', checked: e.target.checked } })}
                  className="w-4 h-4 text-primary bg-background border-default-300 rounded focus:ring-primary"
                />
                <label htmlFor="notifications" className="text-sm font-medium text-foreground">
                  Enable Notifications
                </label>
              </div>
            </CardBody>
          </Card>

          <Card className="shadow-lg border-none bg-background/60 backdrop-blur-sm">
            <CardHeader className="pb-3">
              <h2 className="text-xl font-semibold text-foreground">Actions</h2>
            </CardHeader>
            <CardBody>
              <div className="flex flex-col gap-3">
                <Button
                  color="primary"
                  onClick={handleSave}
                  disabled={saving}
                  startContent={<Save size={18} />}
                  className="w-full"
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
                <Button
                  variant="bordered"
                  onClick={() => window.history.back()}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Skill Form Modal */}
      <SkillForm
        show={showSkillForm}
        onHide={() => setShowSkillForm(false)}
        userId={currentUser?.id}
        userSkill={selectedUserSkill}
        onSave={handleSkillSaved}
      />

      {/* Verification Form Modal */}
      {selectedUserSkill && (
        <VerificationForm
          show={showVerificationForm}
          onHide={() => setShowVerificationForm(false)}
          userSkill={selectedUserSkill}
          onSave={handleVerificationSaved}
        />
      )}
    </div>
  );
};

export default ProfilePage;
