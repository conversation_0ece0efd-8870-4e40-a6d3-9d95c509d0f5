import React from 'react';
import { motion } from 'framer-motion';

/**
 * OnboardingProgress Component
 * 
 * Progress indicator for onboarding flow
 * Shows current step and overall progress
 */
const OnboardingProgress = ({ 
  currentStep, 
  totalSteps = 4, 
  userGoal = null,
  className = "" 
}) => {
  // Calculate progress percentage
  const progressPercentage = (currentStep / totalSteps) * 100;

  // Get step labels based on user goal
  const getStepLabels = () => {
    const baseSteps = ['Welcome', 'Goal'];
    
    if (userGoal === 'project') {
      return [...baseSteps, 'Type', 'Team'];
    } else if (userGoal === 'work') {
      return [...baseSteps, 'Skills', 'Level'];
    } else if (userGoal === 'learn') {
      return [...baseSteps, 'Tutorial', 'Complete'];
    } else {
      return [...baseSteps, 'Setup', 'Complete'];
    }
  };

  const stepLabels = getStepLabels();

  return (
    <div className={`w-full ${className}`}>
      {/* Progress Bar */}
      <div className="relative mb-4">
        {/* Background Bar */}
        <div className="w-full h-1 bg-white bg-opacity-20 rounded-full overflow-hidden">
          {/* Progress Fill */}
          <motion.div
            className="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>

        {/* Step Indicators */}
        <div className="absolute top-0 left-0 w-full flex justify-between transform -translate-y-1/2">
          {stepLabels.map((label, index) => {
            const stepNumber = index + 1;
            const isCompleted = stepNumber < currentStep;
            const isCurrent = stepNumber === currentStep;
            const isUpcoming = stepNumber > currentStep;

            return (
              <motion.div
                key={stepNumber}
                className="flex flex-col items-center"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {/* Step Circle */}
                <motion.div
                  className={`
                    w-6 h-6 rounded-full border-2 flex items-center justify-center text-xs font-bold
                    ${isCompleted 
                      ? 'bg-green-500 border-green-500 text-white' 
                      : isCurrent 
                        ? 'bg-white border-white text-slate-900' 
                        : 'bg-transparent border-white border-opacity-30 text-white text-opacity-50'
                    }
                  `}
                  animate={{
                    scale: isCurrent ? 1.2 : 1,
                    backgroundColor: isCompleted ? '#10b981' : isCurrent ? '#ffffff' : 'transparent'
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {isCompleted ? (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      ✓
                    </motion.div>
                  ) : (
                    stepNumber
                  )}
                </motion.div>

                {/* Step Label */}
                <motion.div
                  className={`
                    mt-2 text-xs font-medium text-center min-w-[60px]
                    ${isCurrent 
                      ? 'text-white' 
                      : isCompleted 
                        ? 'text-green-300' 
                        : 'text-white text-opacity-50'
                    }
                  `}
                  animate={{
                    opacity: isCurrent ? 1 : isCompleted ? 0.8 : 0.5
                  }}
                >
                  {label}
                </motion.div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Progress Text */}
      <motion.div
        className="text-center text-white text-opacity-70 text-sm mt-8"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        Step {currentStep} of {totalSteps}
      </motion.div>
    </div>
  );
};

export default OnboardingProgress;
