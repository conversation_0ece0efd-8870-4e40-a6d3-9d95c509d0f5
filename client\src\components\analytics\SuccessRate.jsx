import React from 'react';
import { <PERSON>, CardBody, Card<PERSON>eader, Chip, Progress } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Success Rate Widget - 1x1 Bento Grid Component
 * 
 * Features:
 * - Mission completion success rate percentage
 * - Completed missions count
 * - Average completion time
 * - Quality score indicator
 */
const SuccessRate = ({ data, period, className = "" }) => {
  const {
    successRate = 0,
    completedMissions = 0,
    avgCompletionTime = 0,
    qualityScore = 0
  } = data || {};

  // Get success rate color
  const getSuccessColor = (rate) => {
    if (rate >= 95) return 'success';
    if (rate >= 90) return 'primary';
    if (rate >= 85) return 'warning';
    return 'danger';
  };

  // Get success rate icon
  const getSuccessIcon = (rate) => {
    if (rate >= 95) return '⚡';
    if (rate >= 90) return '🎯';
    if (rate >= 85) return '📊';
    return '⚠️';
  };

  const successColor = getSuccessColor(successRate);
  const successIcon = getSuccessIcon(successRate);

  return (
    <div className={`success-rate ${className}`}>
      <Card className="bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-900/20 dark:to-green-800/20 border-2 border-emerald-200 dark:border-emerald-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-xl">{successIcon}</span>
              <h3 className="text-sm font-semibold">Rate</h3>
            </div>
            <Chip color={successColor} variant="flat" size="sm">
              Success
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0 flex flex-col justify-center">
          {/* Main Success Rate Display */}
          <div className="text-center mb-4">
            <motion.div
              className="text-4xl font-bold text-emerald-600 mb-1"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              {successRate}%
            </motion.div>
            <div className="text-sm text-default-600">Success Rate</div>
          </div>

          {/* Progress Indicator */}
          <div className="mb-4">
            <Progress
              value={successRate}
              color={successColor}
              size="md"
              className="w-full"
              showValueLabel={false}
            />
          </div>

          {/* Mission Stats */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-default-600">Completed</span>
              <span className="text-sm font-semibold text-emerald-600">
                {completedMissions} Done
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-default-600">Avg Time</span>
              <span className="text-sm font-semibold">
                {avgCompletionTime}d
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-default-600">Quality</span>
              <div className="flex items-center gap-1">
                <span className="text-sm font-semibold">{qualityScore}</span>
                <span className="text-xs text-yellow-500">⭐</span>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default SuccessRate;
