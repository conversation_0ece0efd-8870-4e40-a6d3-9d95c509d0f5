/**
 * Data Validator - Comprehensive Input Validation
 * 
 * Provides strict validation of all input data required for agreement generation.
 * Uses JSON Schema validation with custom business rules.
 * Implements fail-fast approach - no processing with invalid data.
 */

import { ValidationError } from './errors/AgreementErrors.js';

export class DataValidator {
  constructor() {
    this.schema = this._buildValidationSchema();
  }

  /**
   * Validate complete agreement data
   * @param {Object} userData - Raw user input data
   * @returns {Promise<Object>} Validated and normalized data
   * @throws {ValidationError} If validation fails
   */
  async validateInputData(userData) {
    const errors = [];
    
    try {
      // Step 1: Basic structure validation
      this._validateBasicStructure(userData, errors);
      
      // Step 2: Company information validation
      this._validateCompanyInfo(userData.company, errors);
      
      // Step 3: Project information validation
      this._validateProjectInfo(userData.project, errors);
      
      // Step 4: Contributor information validation
      this._validateContributorInfo(userData.contributor, errors);
      
      // Step 5: Business rules validation
      this._validateBusinessRules(userData, errors);
      
      // If any errors found, throw validation error
      if (errors.length > 0) {
        throw new ValidationError('Input validation failed', errors);
      }
      
      // Step 6: Normalize and return validated data
      return this._normalizeData(userData);
      
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError('Validation process failed', [{ message: error.message }]);
    }
  }

  // Private validation methods

  _validateBasicStructure(userData, errors) {
    if (!userData || typeof userData !== 'object') {
      errors.push({ field: 'root', message: 'User data must be an object' });
      return;
    }

    const requiredSections = ['company', 'project', 'contributor'];
    requiredSections.forEach(section => {
      if (!userData[section] || typeof userData[section] !== 'object') {
        errors.push({ field: section, message: `${section} information is required` });
      }
    });
  }

  _validateCompanyInfo(company, errors) {
    if (!company) return;

    const requiredFields = {
      name: { minLength: 1, maxLength: 200, message: 'Company name is required (1-200 characters)' },
      address: { minLength: 10, maxLength: 500, message: 'Company address is required (10-500 characters)' },
      state: { minLength: 2, maxLength: 50, message: 'Company state is required (2-50 characters)' },
      city: { minLength: 1, maxLength: 100, message: 'Company city is required (1-100 characters)' },
      signerName: { minLength: 2, maxLength: 100, message: 'Signer name is required (2-100 characters)' },
      signerTitle: { minLength: 2, maxLength: 100, message: 'Signer title is required (2-100 characters)' },
      billingEmail: { type: 'email', message: 'Valid billing email is required' }
    };

    Object.entries(requiredFields).forEach(([field, rules]) => {
      const value = company[field];
      
      if (!value || (typeof value === 'string' && value.trim().length === 0)) {
        errors.push({ field: `company.${field}`, message: rules.message });
        return;
      }

      if (rules.type === 'email' && !this._isValidEmail(value)) {
        errors.push({ field: `company.${field}`, message: rules.message });
        return;
      }

      if (rules.minLength && value.length < rules.minLength) {
        errors.push({ field: `company.${field}`, message: rules.message });
        return;
      }

      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push({ field: `company.${field}`, message: rules.message });
        return;
      }
    });

    // Additional company validation
    if (company.name && company.name.toLowerCase().includes('city of gamers')) {
      errors.push({ 
        field: 'company.name', 
        message: 'Company name cannot contain "City of Gamers" - this should be the user\'s company name' 
      });
    }
  }

  _validateProjectInfo(project, errors) {
    if (!project) return;

    const requiredFields = {
      name: { minLength: 1, maxLength: 200, message: 'Project name is required (1-200 characters)' },
      description: { minLength: 10, maxLength: 1000, message: 'Project description is required (10-1000 characters)' },
      projectType: { 
        enum: ['game', 'software', 'music', 'film', 'art', 'book', 'app'], 
        message: 'Project type must be one of: game, software, music, film, art, book, app' 
      }
    };

    Object.entries(requiredFields).forEach(([field, rules]) => {
      const value = project[field];
      
      if (!value || (typeof value === 'string' && value.trim().length === 0)) {
        errors.push({ field: `project.${field}`, message: rules.message });
        return;
      }

      if (rules.enum && !rules.enum.includes(value)) {
        errors.push({ field: `project.${field}`, message: rules.message });
        return;
      }

      if (rules.minLength && value.length < rules.minLength) {
        errors.push({ field: `project.${field}`, message: rules.message });
        return;
      }

      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push({ field: `project.${field}`, message: rules.message });
        return;
      }
    });

    // Additional project validation
    if (project.name && project.name.toLowerCase().includes('village of the ages')) {
      errors.push({ 
        field: 'project.name', 
        message: 'Project name cannot contain "Village of The Ages" - this should be the user\'s project name' 
      });
    }
  }

  _validateContributorInfo(contributor, errors) {
    if (!contributor) return;

    const requiredFields = {
      name: { minLength: 2, maxLength: 100, message: 'Contributor name is required (2-100 characters)' },
      email: { type: 'email', message: 'Valid contributor email is required' }
    };

    const optionalFields = {
      address: { minLength: 10, maxLength: 500, message: 'If provided, contributor address must be 10-500 characters' }
    };

    // Validate required fields
    Object.entries(requiredFields).forEach(([field, rules]) => {
      const value = contributor[field];
      
      if (!value || (typeof value === 'string' && value.trim().length === 0)) {
        errors.push({ field: `contributor.${field}`, message: rules.message });
        return;
      }

      if (rules.type === 'email' && !this._isValidEmail(value)) {
        errors.push({ field: `contributor.${field}`, message: rules.message });
        return;
      }

      if (rules.minLength && value.length < rules.minLength) {
        errors.push({ field: `contributor.${field}`, message: rules.message });
        return;
      }

      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push({ field: `contributor.${field}`, message: rules.message });
        return;
      }
    });

    // Validate optional fields if provided
    Object.entries(optionalFields).forEach(([field, rules]) => {
      const value = contributor[field];
      
      if (value && typeof value === 'string' && value.trim().length > 0) {
        if (rules.minLength && value.length < rules.minLength) {
          errors.push({ field: `contributor.${field}`, message: rules.message });
          return;
        }

        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push({ field: `contributor.${field}`, message: rules.message });
          return;
        }
      }
    });
  }

  _validateBusinessRules(userData, errors) {
    // Business rule: Company and contributor cannot have the same email
    if (userData.company?.billingEmail && userData.contributor?.email) {
      if (userData.company.billingEmail.toLowerCase() === userData.contributor.email.toLowerCase()) {
        errors.push({
          field: 'business_rules',
          message: 'Company billing email and contributor email cannot be the same'
        });
      }
    }

    // Business rule: Validate state format (US states)
    if (userData.company?.state) {
      if (!this._isValidUSState(userData.company.state)) {
        errors.push({
          field: 'company.state',
          message: 'Company state must be a valid US state name or abbreviation'
        });
      }
    }

    // Business rule: Project name should not be generic
    if (userData.project?.name) {
      const genericNames = ['test', 'sample', 'example', 'demo', 'placeholder'];
      if (genericNames.some(generic => userData.project.name.toLowerCase().includes(generic))) {
        errors.push({
          field: 'project.name',
          message: 'Project name appears to be a placeholder - please use the actual project name'
        });
      }
    }
  }

  _normalizeData(userData) {
    return {
      company: {
        name: userData.company.name.trim(),
        address: userData.company.address.trim(),
        state: userData.company.state.trim(),
        city: userData.company.city.trim(),
        signerName: userData.company.signerName.trim(),
        signerTitle: userData.company.signerTitle.trim(),
        billingEmail: userData.company.billingEmail.trim().toLowerCase()
      },
      project: {
        name: userData.project.name.trim(),
        description: userData.project.description.trim(),
        projectType: userData.project.projectType.toLowerCase()
      },
      contributor: {
        name: userData.contributor.name.trim(),
        email: userData.contributor.email.trim().toLowerCase(),
        address: userData.contributor.address ? userData.contributor.address.trim() : null
      }
    };
  }

  // Helper methods

  _isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  _isValidUSState(state) {
    const usStates = [
      'Alabama', 'Alaska', 'Arizona', 'Arkansas', 'California', 'Colorado', 'Connecticut', 'Delaware',
      'Florida', 'Georgia', 'Hawaii', 'Idaho', 'Illinois', 'Indiana', 'Iowa', 'Kansas', 'Kentucky',
      'Louisiana', 'Maine', 'Maryland', 'Massachusetts', 'Michigan', 'Minnesota', 'Mississippi',
      'Missouri', 'Montana', 'Nebraska', 'Nevada', 'New Hampshire', 'New Jersey', 'New Mexico',
      'New York', 'North Carolina', 'North Dakota', 'Ohio', 'Oklahoma', 'Oregon', 'Pennsylvania',
      'Rhode Island', 'South Carolina', 'South Dakota', 'Tennessee', 'Texas', 'Utah', 'Vermont',
      'Virginia', 'Washington', 'West Virginia', 'Wisconsin', 'Wyoming',
      'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA',
      'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
      'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT',
      'VA', 'WA', 'WV', 'WI', 'WY'
    ];
    
    return usStates.includes(state);
  }

  _buildValidationSchema() {
    // JSON Schema for additional validation if needed
    return {
      type: 'object',
      required: ['company', 'project', 'contributor'],
      properties: {
        company: {
          type: 'object',
          required: ['name', 'address', 'state', 'city', 'signerName', 'signerTitle', 'billingEmail']
        },
        project: {
          type: 'object',
          required: ['name', 'description', 'projectType']
        },
        contributor: {
          type: 'object',
          required: ['name', 'email']
        }
      }
    };
  }
}
