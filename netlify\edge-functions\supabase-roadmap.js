// Supabase Roadmap Edge Function
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7';

// Initialize Supabase client
const initSupabase = (context) => {
  const supabaseUrl = context.env.SUPABASE_URL;
  const supabaseKey = context.env.SUPABASE_SERVICE_KEY || context.env.SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error(`Missing Supabase credentials: URL=${!!supabaseUrl}, Key=${!!supabaseKey}`);
  }

  console.log(`Initializing Supabase client with URL: ${supabaseUrl.substring(0, 15)}...`);
  return createClient(supabaseUrl, supabaseKey);
};

// Function to calculate stats
function calculateStats(phases) {
  let totalTasks = 0;
  let completedTasks = 0;
  let phaseStats = [];

  phases.forEach(phase => {
    let phaseTotalTasks = 0;
    let phaseCompletedTasks = 0;

    phase.sections.forEach(section => {
      phaseTotalTasks += section.tasks.length;
      phaseCompletedTasks += section.tasks.filter(task => task.completed).length;
    });

    totalTasks += phaseTotalTasks;
    completedTasks += phaseCompletedTasks;

    phaseStats.push({
      id: phase.id,
      title: phase.title,
      timeframe: phase.timeframe,
      progress: phaseTotalTasks > 0 ? Math.round((phaseCompletedTasks / phaseTotalTasks) * 100) : 0
    });
  });

  return {
    totalTasks,
    completedTasks,
    progressPercentage: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
    phases: phaseStats
  };
}

// Main function handler
export default async (request, context) => {
  // Set CORS headers for cross-origin access
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  // Handle OPTIONS request for CORS preflight
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers
    });
  }

  try {
    // Log environment variables (safely)
    console.log('Environment variables available:', Object.keys(context.env).join(', '));

    // Initialize Supabase client
    let supabase;
    try {
      supabase = initSupabase(context);
      console.log('Supabase client initialized successfully');
    } catch (initError) {
      console.error('Failed to initialize Supabase client:', initError);
      throw new Error(`Supabase initialization failed: ${initError.message}`);
    }

    // Query the roadmap data from Supabase
    console.log('Querying roadmap table...');
    let roadmapResult;
    try {
      roadmapResult = await supabase
        .from('roadmap')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(1);
    } catch (queryError) {
      console.error('Exception during Supabase query:', queryError);
      throw new Error(`Supabase query exception: ${queryError.message}`);
    }

    const { data: roadmapData, error: roadmapError } = roadmapResult || {};

    if (roadmapError) {
      console.error('Error fetching roadmap from Supabase:', roadmapError);
      throw new Error(`Roadmap query error: ${roadmapError.message}`);
    }

    console.log(`Roadmap data received: ${roadmapData ? roadmapData.length : 0} records`);

    // Check if we have data
    if (!roadmapData || roadmapData.length === 0) {
      console.log('No roadmap data found, trying RPC function...');

      // Try the RPC function as a fallback
      let rpcResult;
      try {
        rpcResult = await supabase.rpc('get_roadmap');
      } catch (rpcError) {
        console.error('Exception calling get_roadmap RPC:', rpcError);
        throw new Error(`RPC exception: ${rpcError.message}`);
      }

      const { data: rpcData, error: rpcError } = rpcResult || {};

      if (rpcError) {
        console.error('Error calling get_roadmap RPC:', rpcError);
        throw new Error(`RPC error: ${rpcError.message}`);
      }

      if (!rpcData) {
        console.error('No data returned from RPC function');
        throw new Error('No roadmap data found in Supabase');
      }

      console.log('RPC data received successfully');

      // Calculate stats
      const stats = calculateStats(rpcData);

      // Return the data
      return new Response(
        JSON.stringify({
          success: true,
          data: rpcData,
          stats: stats,
          source: 'supabase-rpc'
        }),
        { headers }
      );
    }

    // Process the roadmap data
    console.log('Processing roadmap data...');
    console.log('Roadmap data structure:', JSON.stringify(roadmapData[0], null, 2).substring(0, 100) + '...');

    const roadmap = roadmapData[0].data;

    if (!roadmap) {
      console.error('Invalid roadmap data structure:', roadmapData[0]);
      throw new Error('Invalid roadmap data structure: missing data property');
    }

    // Calculate stats
    const stats = calculateStats(roadmap);

    // Return the data
    return new Response(
      JSON.stringify({
        success: true,
        data: roadmap,
        stats: stats,
        source: 'supabase-direct'
      }),
      { headers }
    );
  } catch (error) {
    console.error('Error in supabase-roadmap edge function:', error);

    // Fall back to hardcoded data
    const fallbackData = [
      {
        id: 1,
        title: "Foundation & User Management",
        timeframe: "Completed",
        expanded: true,
        sections: [
          {
            id: "1.1",
            title: "Project Setup & Configuration",
            tasks: [
              { id: "1.1.1", text: "Finalize tech stack (React, Supabase)", completed: true },
              { id: "1.1.2", text: "Set up development environment", completed: true },
              { id: "1.1.3", text: "Configure Netlify deployment", completed: true }
            ]
          }
        ]
      },
      {
        id: 2,
        title: "Project Creation & Management",
        timeframe: "Phase 1",
        expanded: false,
        sections: [
          {
            id: "2.1",
            title: "Project Wizard",
            tasks: [
              { id: "2.1.1", text: "Design project creation flow", completed: true },
              { id: "2.1.2", text: "Implement project basics form", completed: true },
              { id: "2.1.3", text: "Add team & contributors section", completed: true }
            ]
          }
        ]
      },
      {
        id: 3,
        title: "Contribution Tracking System",
        timeframe: "Phase 2",
        expanded: false,
        sections: [
          {
            id: "3.1",
            title: "Manual Contribution Entry",
            tasks: [
              { id: "3.1.1", text: "Design contribution entry forms", completed: true },
              { id: "3.1.2", text: "Implement time tracking functionality", completed: true },
              { id: "3.1.3", text: "Add task selection from configured types", completed: true },
              { id: "3.1.4", text: "Implement difficulty rating selection", completed: true },
              { id: "3.1.5", text: "Create contribution description field", completed: true },
              { id: "3.1.6", text: "Add date range selection", completed: true },
              { id: "3.1.7", text: "Implement file/asset attachment", completed: false }
            ]
          }
        ]
      }
    ];

    // Calculate stats for fallback data
    const fallbackStats = calculateStats(fallbackData);

    // Create a safe error object for logging
    const errorDetails = {
      message: error.message,
      name: error.name,
      stack: error.stack ? error.stack.split('\n').slice(0, 3).join('\n') : 'No stack trace',
      cause: error.cause ? String(error.cause) : undefined
    };

    return new Response(
      JSON.stringify({
        success: true,
        data: fallbackData,
        stats: fallbackStats,
        source: 'fallback-data',
        error: error.message,
        errorDetails: errorDetails,
        debug: {
          timestamp: new Date().toISOString(),
          envVarsAvailable: Object.keys(context.env).length > 0,
          envVarNames: Object.keys(context.env)
        }
      }),
      {
        status: 200, // Return 200 with fallback data instead of 500
        headers
      }
    );
  }
};
