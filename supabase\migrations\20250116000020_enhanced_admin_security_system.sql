-- Enhanced Admin & Security System Migration
-- Authentication & Security Agent: Comprehensive admin tools and security features
-- Created: January 16, 2025

-- ============================================================================
-- ADMIN ROLE SYSTEM
-- ============================================================================

-- Ensure users table exists with admin capabilities
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    display_name TEXT,
    avatar_url TEXT,
    date_created TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Admin and role system
    is_admin BOOLEAN DEFAULT false,
    admin_role TEXT CHECK (admin_role IN ('super_admin', 'platform_admin', 'support_admin', 'financial_admin', 'content_moderator')),
    admin_permissions JSONB DEFAULT '{}'::jsonb,
    
    -- Security fields
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE,
    mfa_enabled BOOLEAN DEFAULT false,
    mfa_secret TEXT,
    
    -- Profile and status
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'banned', 'pending')),
    suspension_reason TEXT,
    suspended_until TIMESTAMP WITH TIME ZONE,
    suspended_by UUID REFERENCES auth.users(id),
    
    -- Additional profile fields
    headline TEXT,
    location TEXT,
    website TEXT,
    cover_image_url TEXT,
    status_message TEXT,
    availability_status TEXT,
    profile_views INTEGER DEFAULT 0,
    theme_settings JSONB DEFAULT '{}'::jsonb,
    custom_css TEXT,
    profile_song_url TEXT,
    privacy_settings JSONB DEFAULT '{}'::jsonb,
    skills JSONB DEFAULT '[]'::jsonb
);

-- ============================================================================
-- ADMIN ACTIONS AUDIT LOG
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.admin_actions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    admin_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL, -- 'user_suspend', 'content_remove', 'role_change', etc.
    target_type VARCHAR(50) NOT NULL, -- 'user', 'project', 'alliance', 'content', etc.
    target_id UUID,
    target_identifier TEXT, -- email, name, or other identifier for the target
    
    -- Action details
    reason TEXT,
    details JSONB DEFAULT '{}'::jsonb,
    previous_state JSONB DEFAULT '{}'::jsonb,
    new_state JSONB DEFAULT '{}'::jsonb,
    
    -- Metadata
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- CONTENT MODERATION SYSTEM
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.moderation_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_type VARCHAR(50) NOT NULL, -- 'project', 'profile', 'comment', 'message', etc.
    content_id UUID NOT NULL,
    content_preview TEXT, -- First 500 chars of content for quick review
    
    -- Flagging information
    flagged_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    flag_reason VARCHAR(100) NOT NULL,
    flag_details TEXT,
    flag_category VARCHAR(50), -- 'spam', 'harassment', 'inappropriate', 'copyright', etc.
    
    -- Review information
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'removed', 'edited', 'escalated')),
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    reviewed_by UUID REFERENCES auth.users(id),
    review_notes TEXT,
    review_decision_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    escalated_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    automated_flag BOOLEAN DEFAULT false,
    confidence_score DECIMAL(3,2), -- For automated flagging confidence
    related_reports INTEGER DEFAULT 1 -- Number of reports for this content
);

-- ============================================================================
-- SUPPORT TICKET SYSTEM
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.support_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ticket_number TEXT UNIQUE NOT NULL, -- Human-readable ticket number
    
    -- User information
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    user_email TEXT NOT NULL, -- Store email in case user is deleted
    user_name TEXT,
    
    -- Ticket details
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50) NOT NULL, -- 'technical', 'billing', 'account', 'feature_request', etc.
    subcategory VARCHAR(50),
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    
    -- Status and assignment
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'in_progress', 'waiting_user', 'resolved', 'closed')),
    assigned_to UUID REFERENCES auth.users(id),
    assigned_at TIMESTAMP WITH TIME ZONE,
    
    -- Resolution
    resolution_notes TEXT,
    resolution_category VARCHAR(50),
    customer_satisfaction_rating INTEGER CHECK (customer_satisfaction_rating BETWEEN 1 AND 5),
    customer_feedback TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    first_response_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    source VARCHAR(20) DEFAULT 'web' CHECK (source IN ('web', 'email', 'chat', 'phone')),
    tags TEXT[], -- Array of tags for categorization
    attachments JSONB DEFAULT '[]'::jsonb
);

-- ============================================================================
-- SYSTEM MONITORING & ALERTS
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.system_monitoring (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_type VARCHAR(50) NOT NULL, -- 'response_time', 'error_rate', 'uptime', 'user_activity'
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4) NOT NULL,
    metric_unit VARCHAR(20), -- 'ms', 'percent', 'count', etc.
    
    -- Thresholds and status
    threshold_value DECIMAL(15,4),
    threshold_type VARCHAR(10) CHECK (threshold_type IN ('min', 'max')),
    status VARCHAR(20) DEFAULT 'normal' CHECK (status IN ('normal', 'warning', 'critical')),
    
    -- Context and metadata
    component VARCHAR(50), -- 'database', 'api', 'frontend', 'payment', etc.
    environment VARCHAR(20) DEFAULT 'production',
    details JSONB DEFAULT '{}'::jsonb,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- SECURITY EVENTS LOG
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL, -- 'login_attempt', 'password_change', 'suspicious_activity', etc.
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    
    -- User and session information
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    session_id TEXT,
    ip_address INET,
    user_agent TEXT,
    
    -- Event details
    event_description TEXT NOT NULL,
    event_data JSONB DEFAULT '{}'::jsonb,
    
    -- Risk assessment
    risk_score INTEGER CHECK (risk_score BETWEEN 0 AND 100),
    automated_response TEXT, -- What automated action was taken
    
    -- Investigation
    investigated_by UUID REFERENCES auth.users(id),
    investigation_notes TEXT,
    false_positive BOOLEAN DEFAULT false,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Admin actions indexes
CREATE INDEX IF NOT EXISTS idx_admin_actions_admin_id ON public.admin_actions(admin_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_actions_target ON public.admin_actions(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_admin_actions_type ON public.admin_actions(action_type, created_at DESC);

-- Moderation queue indexes
CREATE INDEX IF NOT EXISTS idx_moderation_queue_status ON public.moderation_queue(status, priority, created_at);
CREATE INDEX IF NOT EXISTS idx_moderation_queue_content ON public.moderation_queue(content_type, content_id);
CREATE INDEX IF NOT EXISTS idx_moderation_queue_flagged_by ON public.moderation_queue(flagged_by, created_at DESC);

-- Support tickets indexes
CREATE INDEX IF NOT EXISTS idx_support_tickets_user ON public.support_tickets(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON public.support_tickets(status, priority, created_at);
CREATE INDEX IF NOT EXISTS idx_support_tickets_assigned ON public.support_tickets(assigned_to, status);
CREATE INDEX IF NOT EXISTS idx_support_tickets_number ON public.support_tickets(ticket_number);

-- System monitoring indexes
CREATE INDEX IF NOT EXISTS idx_system_monitoring_metric ON public.system_monitoring(metric_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_monitoring_status ON public.system_monitoring(status, created_at DESC);

-- Security events indexes
CREATE INDEX IF NOT EXISTS idx_security_events_user ON public.security_events(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_type ON public.security_events(event_type, severity, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON public.security_events(ip_address, created_at DESC);

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_admin_role ON public.users(admin_role) WHERE admin_role IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_status ON public.users(status);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON public.users(last_login_at DESC);

-- ============================================================================
-- ROW LEVEL SECURITY POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.admin_actions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.moderation_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.support_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_monitoring ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.security_events ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.is_admin = true
        )
    );

CREATE POLICY "Super admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.admin_role = 'super_admin'
        )
    );

-- Admin actions policies
CREATE POLICY "Admins can view admin actions" ON public.admin_actions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.is_admin = true
        )
    );

CREATE POLICY "Admins can create admin actions" ON public.admin_actions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.is_admin = true
        ) AND admin_id = auth.uid()
    );

-- Moderation queue policies
CREATE POLICY "Users can view content they flagged" ON public.moderation_queue
    FOR SELECT USING (flagged_by = auth.uid());

CREATE POLICY "Users can flag content" ON public.moderation_queue
    FOR INSERT WITH CHECK (flagged_by = auth.uid());

CREATE POLICY "Moderators can manage moderation queue" ON public.moderation_queue
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND (
                u.admin_role IN ('super_admin', 'platform_admin', 'content_moderator')
                OR u.is_admin = true
            )
        )
    );

-- Support tickets policies
CREATE POLICY "Users can view their own tickets" ON public.support_tickets
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create support tickets" ON public.support_tickets
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own tickets" ON public.support_tickets
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Support admins can manage all tickets" ON public.support_tickets
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND (
                u.admin_role IN ('super_admin', 'platform_admin', 'support_admin')
                OR u.is_admin = true
            )
        )
    );

-- System monitoring policies
CREATE POLICY "Admins can view system monitoring" ON public.system_monitoring
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.is_admin = true
        )
    );

CREATE POLICY "System can insert monitoring data" ON public.system_monitoring
    FOR INSERT WITH CHECK (true); -- Allow system inserts

-- Security events policies
CREATE POLICY "Users can view their own security events" ON public.security_events
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can view all security events" ON public.security_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users u
            WHERE u.id = auth.uid() AND u.is_admin = true
        )
    );

CREATE POLICY "System can insert security events" ON public.security_events
    FOR INSERT WITH CHECK (true); -- Allow system inserts

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to generate ticket numbers
CREATE OR REPLACE FUNCTION generate_ticket_number()
RETURNS TEXT AS $$
DECLARE
    ticket_num TEXT;
    counter INTEGER;
BEGIN
    -- Get current date in YYYYMMDD format
    ticket_num := 'TKT-' || to_char(now(), 'YYYYMMDD') || '-';

    -- Get the count of tickets created today
    SELECT COUNT(*) + 1 INTO counter
    FROM public.support_tickets
    WHERE DATE(created_at) = CURRENT_DATE;

    -- Pad with zeros to make it 4 digits
    ticket_num := ticket_num || lpad(counter::text, 4, '0');

    RETURN ticket_num;
END;
$$ LANGUAGE plpgsql;

-- Function to log admin actions
CREATE OR REPLACE FUNCTION log_admin_action(
    p_admin_id UUID,
    p_action_type TEXT,
    p_target_type TEXT,
    p_target_id UUID,
    p_target_identifier TEXT,
    p_reason TEXT,
    p_details JSONB DEFAULT '{}'::jsonb,
    p_previous_state JSONB DEFAULT '{}'::jsonb,
    p_new_state JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    action_id UUID;
BEGIN
    INSERT INTO public.admin_actions (
        admin_id,
        action_type,
        target_type,
        target_id,
        target_identifier,
        reason,
        details,
        previous_state,
        new_state
    ) VALUES (
        p_admin_id,
        p_action_type,
        p_target_type,
        p_target_id,
        p_target_identifier,
        p_reason,
        p_details,
        p_previous_state,
        p_new_state
    ) RETURNING id INTO action_id;

    RETURN action_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_event_type TEXT,
    p_severity TEXT,
    p_user_id UUID,
    p_event_description TEXT,
    p_event_data JSONB DEFAULT '{}'::jsonb,
    p_risk_score INTEGER DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO public.security_events (
        event_type,
        severity,
        user_id,
        event_description,
        event_data,
        risk_score
    ) VALUES (
        p_event_type,
        p_severity,
        p_user_id,
        p_event_description,
        p_event_data,
        p_risk_score
    ) RETURNING id INTO event_id;

    RETURN event_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    admin_status BOOLEAN;
BEGIN
    SELECT is_admin INTO admin_status
    FROM public.users
    WHERE id = user_id;

    RETURN COALESCE(admin_status, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check admin role
CREATE OR REPLACE FUNCTION has_admin_role(user_id UUID, required_role TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role TEXT;
    user_admin BOOLEAN;
BEGIN
    SELECT admin_role, is_admin INTO user_role, user_admin
    FROM public.users
    WHERE id = user_id;

    -- Super admin has all permissions
    IF user_role = 'super_admin' THEN
        RETURN true;
    END IF;

    -- Check specific role
    IF user_role = required_role THEN
        RETURN true;
    END IF;

    -- Fallback to general admin status for backward compatibility
    RETURN COALESCE(user_admin, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Trigger to auto-generate ticket numbers
CREATE OR REPLACE FUNCTION auto_generate_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.ticket_number IS NULL THEN
        NEW.ticket_number := generate_ticket_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_ticket_number
    BEFORE INSERT ON public.support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_ticket_number();

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_support_tickets_updated_at
    BEFORE UPDATE ON public.support_tickets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INITIAL DATA
-- ============================================================================

-- Create default admin user if none exists
DO $$
BEGIN
    -- Only create if no admin users exist
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE is_admin = true) THEN
        -- This will be handled by the application layer
        RAISE NOTICE 'No admin users found. First user to register will need to be manually promoted to admin.';
    END IF;
END $$;

-- Insert default system monitoring metrics
INSERT INTO public.system_monitoring (metric_type, metric_name, metric_value, metric_unit, threshold_value, threshold_type, component)
VALUES
    ('response_time', 'api_response_time', 0, 'ms', 2000, 'max', 'api'),
    ('error_rate', 'api_error_rate', 0, 'percent', 5, 'max', 'api'),
    ('uptime', 'system_uptime', 100, 'percent', 99, 'min', 'system')
ON CONFLICT DO NOTHING;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.users IS 'Extended user profiles with admin capabilities and security features';
COMMENT ON TABLE public.admin_actions IS 'Audit log of all administrative actions performed on the platform';
COMMENT ON TABLE public.moderation_queue IS 'Queue for content that has been flagged and needs moderation review';
COMMENT ON TABLE public.support_tickets IS 'Customer support ticket system for user assistance';
COMMENT ON TABLE public.system_monitoring IS 'System health and performance monitoring metrics';
COMMENT ON TABLE public.security_events IS 'Security-related events and potential threats log';

COMMENT ON COLUMN public.users.admin_role IS 'Specific admin role: super_admin, platform_admin, support_admin, financial_admin, content_moderator';
COMMENT ON COLUMN public.users.admin_permissions IS 'JSON object containing specific admin permissions';
COMMENT ON COLUMN public.users.mfa_enabled IS 'Whether multi-factor authentication is enabled for this user';
COMMENT ON COLUMN public.admin_actions.previous_state IS 'State of the target before the admin action';
COMMENT ON COLUMN public.admin_actions.new_state IS 'State of the target after the admin action';
COMMENT ON COLUMN public.moderation_queue.confidence_score IS 'Confidence score for automated flagging (0.00-1.00)';
COMMENT ON COLUMN public.support_tickets.ticket_number IS 'Human-readable ticket identifier (e.g., TKT-20250116-0001)';
COMMENT ON COLUMN public.security_events.risk_score IS 'Risk assessment score from 0-100';

-- Migration completed successfully
SELECT 'Enhanced Admin & Security System migration completed successfully' AS status;
