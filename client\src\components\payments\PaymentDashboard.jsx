import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Badge, Chip, Divider } from '@heroui/react';
import { toast } from 'react-toastify';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from '../layout/LoadingAnimation';
import TellerLinkComponent from './TellerLinkComponent';
import EscrowManager from './EscrowManager';
import TransactionHistory from './TransactionHistory';
import {
  DollarSign,
  Lock,
  TrendingUp,
  CreditCard,
  Clock,
  Building2,
  Plus,
  Download,
  ArrowUpRight,
  ArrowDownLeft,
  Shield,
  AlertCircle
} from 'lucide-react';

/**
 * PaymentDashboard component - Complete payment system with Teller integration
 * Enhanced to include payment methods, escrow management, and financial overview
 */
const PaymentDashboard = ({ companyId, className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [showTellerLink, setShowTellerLink] = useState(false);
  const [tellerData, setTellerData] = useState({
    paymentMethods: [],
    transfers: [],
    capabilities: null
  });
  
  // Data state
  const [commissions, setCommissions] = useState([]);
  const [recurringFees, setRecurringFees] = useState([]);
  const [financialTransactions, setFinancialTransactions] = useState([]);
  const [summary, setSummary] = useState({
    totalCommissionsPending: 0,
    totalCommissionsPaid: 0,
    activeRecurringFees: 0,
    monthlyRecurringTotal: 0,
    upcomingPayments: 0
  });

  useEffect(() => {
    if (companyId && currentUser) {
      fetchPaymentData();
    }
  }, [companyId, currentUser]);

  const fetchPaymentData = async () => {
    try {
      setLoading(true);
      
      // Get auth token
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }
      
      const headers = {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      };
      
      // Fetch commission payments
      const commissionsResponse = await fetch(`/.netlify/functions/commission-payments?company_id=${companyId}`, {
        headers
      });
      
      if (commissionsResponse.ok) {
        const commissionsData = await commissionsResponse.json();
        setCommissions(commissionsData.commissions || []);
      }
      
      // Fetch recurring fees
      const recurringResponse = await fetch(`/.netlify/functions/recurring-fees?company_id=${companyId}`, {
        headers
      });
      
      if (recurringResponse.ok) {
        const recurringData = await recurringResponse.json();
        setRecurringFees(recurringData.recurringFees || []);
      }
      
      // Fetch financial transactions
      const transactionsResponse = await fetch(`/.netlify/functions/financial-transactions?company_id=${companyId}`, {
        headers
      });
      
      if (transactionsResponse.ok) {
        const transactionsData = await transactionsResponse.json();
        setFinancialTransactions(transactionsData.transactions || []);
      }
      
      // Calculate summary
      calculateSummary();
      
    } catch (error) {
      console.error('Error fetching payment data:', error);
      toast.error('Failed to load payment data');
    } finally {
      setLoading(false);
    }
  };

  const calculateSummary = () => {
    // Calculate from fetched data
    const pendingCommissions = commissions
      .filter(c => c.financial_transaction?.status === 'pending')
      .reduce((sum, c) => sum + parseFloat(c.commission_amount || 0), 0);
      
    const paidCommissions = commissions
      .filter(c => c.financial_transaction?.status === 'paid')
      .reduce((sum, c) => sum + parseFloat(c.commission_amount || 0), 0);
      
    const activeRecurring = recurringFees.filter(f => f.is_active).length;
    
    const monthlyRecurring = recurringFees
      .filter(f => f.is_active && f.frequency === 'monthly')
      .reduce((sum, f) => sum + parseFloat(f.amount || 0), 0);
      
    const upcomingPayments = recurringFees
      .filter(f => f.is_active && new Date(f.next_payment_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000))
      .length;
    
    setSummary({
      totalCommissionsPending: pendingCommissions,
      totalCommissionsPaid: paidCommissions,
      activeRecurringFees: activeRecurring,
      monthlyRecurringTotal: monthlyRecurring,
      upcomingPayments
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusIcon = (status) => {
    const icons = {
      pending: '⏳',
      approved: '✅',
      processing: '🔄',
      paid: '💰',
      failed: '❌',
      cancelled: '🚫'
    };
    return icons[status] || '❓';
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  return (
    <div className="payment-dashboard">
      <div className="dashboard-header">
        <h2>💰 Payment Management</h2>
        <p>Commission payments and recurring fees for your studio</p>
      </div>

      {/* Summary Cards */}
      <div className="summary-grid">
        <div className="summary-card pending">
          <div className="card-icon">⏳</div>
          <div className="card-content">
            <h3>Pending Commissions</h3>
            <div className="amount">{formatCurrency(summary.totalCommissionsPending)}</div>
          </div>
        </div>
        
        <div className="summary-card paid">
          <div className="card-icon">💰</div>
          <div className="card-content">
            <h3>Paid Commissions</h3>
            <div className="amount">{formatCurrency(summary.totalCommissionsPaid)}</div>
          </div>
        </div>
        
        <div className="summary-card recurring">
          <div className="card-icon">🔄</div>
          <div className="card-content">
            <h3>Active Recurring Fees</h3>
            <div className="amount">{summary.activeRecurringFees}</div>
            <div className="subtitle">Monthly: {formatCurrency(summary.monthlyRecurringTotal)}</div>
          </div>
        </div>
        
        <div className="summary-card upcoming">
          <div className="card-icon">📅</div>
          <div className="card-content">
            <h3>Upcoming Payments</h3>
            <div className="amount">{summary.upcomingPayments}</div>
            <div className="subtitle">Next 7 days</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button 
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          📊 Overview
        </button>
        <button 
          className={`tab-button ${activeTab === 'commissions' ? 'active' : ''}`}
          onClick={() => setActiveTab('commissions')}
        >
          💼 Commissions
        </button>
        <button 
          className={`tab-button ${activeTab === 'recurring' ? 'active' : ''}`}
          onClick={() => setActiveTab('recurring')}
        >
          🔄 Recurring Fees
        </button>
        <button
          className={`tab-button ${activeTab === 'transactions' ? 'active' : ''}`}
          onClick={() => setActiveTab('transactions')}
        >
          📋 All Transactions
        </button>
        <button
          className={`tab-button ${activeTab === 'teller' ? 'active' : ''}`}
          onClick={() => setActiveTab('teller')}
        >
          🏦 Payment Methods
        </button>
        <button
          className={`tab-button ${activeTab === 'escrow' ? 'active' : ''}`}
          onClick={() => setActiveTab('escrow')}
        >
          🔒 Escrow
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && (
          <div className="overview-tab">
            <div className="recent-activity">
              <h3>🕒 Recent Activity</h3>
              <div className="activity-list">
                {financialTransactions.slice(0, 5).map(transaction => (
                  <div key={transaction.id} className="activity-item">
                    <div className="activity-icon">
                      {getStatusIcon(transaction.status)}
                    </div>
                    <div className="activity-content">
                      <div className="activity-title">{transaction.description}</div>
                      <div className="activity-meta">
                        {formatDate(transaction.created_at)} • {formatCurrency(transaction.gross_amount)}
                      </div>
                    </div>
                    <div className={`activity-status ${transaction.status}`}>
                      {transaction.status}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'commissions' && (
          <div className="commissions-tab">
            <div className="tab-header">
              <h3>💼 Commission Payments</h3>
              <button className="create-button">+ New Commission</button>
            </div>
            
            <div className="commissions-list">
              {commissions.map(commission => (
                <div key={commission.id} className="commission-card">
                  <div className="commission-header">
                    <div className="commission-title">{commission.product_or_service}</div>
                    <div className={`commission-status ${commission.financial_transaction?.status}`}>
                      {getStatusIcon(commission.financial_transaction?.status)} {commission.financial_transaction?.status}
                    </div>
                  </div>
                  
                  <div className="commission-details">
                    <div className="detail-row">
                      <span>Sales Rep:</span>
                      <span>{commission.sales_rep?.user_metadata?.full_name || commission.sales_rep?.email}</span>
                    </div>
                    <div className="detail-row">
                      <span>Sale Amount:</span>
                      <span>{formatCurrency(commission.sales_amount)}</span>
                    </div>
                    <div className="detail-row">
                      <span>Commission Rate:</span>
                      <span>{commission.commission_rate}%</span>
                    </div>
                    <div className="detail-row">
                      <span>Commission Amount:</span>
                      <span className="commission-amount">{formatCurrency(commission.commission_amount)}</span>
                    </div>
                    <div className="detail-row">
                      <span>Sale Date:</span>
                      <span>{formatDate(commission.sale_date)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'recurring' && (
          <div className="recurring-tab">
            <div className="tab-header">
              <h3>🔄 Recurring Fees</h3>
              <button className="create-button">+ New Recurring Fee</button>
            </div>
            
            <div className="recurring-list">
              {recurringFees.map(fee => (
                <div key={fee.id} className={`recurring-card ${fee.is_active ? 'active' : 'inactive'}`}>
                  <div className="recurring-header">
                    <div className="recurring-title">{fee.description}</div>
                    <div className={`recurring-status ${fee.is_active ? 'active' : 'inactive'}`}>
                      {fee.is_active ? '🟢 Active' : '🔴 Inactive'}
                    </div>
                  </div>
                  
                  <div className="recurring-details">
                    <div className="detail-row">
                      <span>Payee:</span>
                      <span>{fee.payee?.user_metadata?.full_name || fee.payee?.email}</span>
                    </div>
                    <div className="detail-row">
                      <span>Amount:</span>
                      <span className="fee-amount">{formatCurrency(fee.amount)}</span>
                    </div>
                    <div className="detail-row">
                      <span>Frequency:</span>
                      <span className="frequency">{fee.frequency}</span>
                    </div>
                    <div className="detail-row">
                      <span>Next Payment:</span>
                      <span>{formatDate(fee.next_payment_date)}</span>
                    </div>
                    <div className="detail-row">
                      <span>Type:</span>
                      <span className="fee-type">{fee.fee_type.replace('_', ' ')}</span>
                    </div>
                  </div>
                  
                  <div className="recurring-actions">
                    {fee.is_active ? (
                      <button className="pause-button">⏸️ Pause</button>
                    ) : (
                      <button className="resume-button">▶️ Resume</button>
                    )}
                    <button className="edit-button">✏️ Edit</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="transactions-tab">
            <TransactionHistory
              transactions={financialTransactions}
              showAll={true}
              onRefresh={fetchPaymentData}
            />
          </div>
        )}

        {activeTab === 'teller' && (
          <div className="teller-tab">
            <div className="tab-header">
              <h3>🏦 Payment Methods & Bank Accounts</h3>
              <Button
                color="primary"
                startContent={<Plus size={16} />}
                onPress={() => setShowTellerLink(true)}
              >
                Add Payment Method
              </Button>
            </div>

            <div className="teller-content">
              {tellerData.paymentMethods.length === 0 ? (
                <Card>
                  <CardBody className="p-8 text-center">
                    <CreditCard size={48} className="mx-auto mb-4 text-gray-300" />
                    <h3 className="text-lg font-semibold mb-2">No Payment Methods Connected</h3>
                    <p className="text-gray-600 mb-4">
                      Connect your bank account to receive payments and manage transfers
                    </p>
                    <Button
                      color="primary"
                      startContent={<Plus size={16} />}
                      onPress={() => setShowTellerLink(true)}
                    >
                      Connect Bank Account
                    </Button>
                  </CardBody>
                </Card>
              ) : (
                <div className="payment-methods-grid">
                  {tellerData.paymentMethods.map((method, index) => (
                    <Card key={index} className="payment-method-card">
                      <CardBody className="p-4">
                        <div className="flex items-center gap-3">
                          <Building2 className="text-blue-500" size={24} />
                          <div>
                            <h4 className="font-medium">{method.bank_name}</h4>
                            <p className="text-sm text-gray-600">••••{method.account_mask}</p>
                          </div>
                          <Chip size="sm" color="success" variant="flat" className="ml-auto">
                            Connected
                          </Chip>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'escrow' && (
          <div className="escrow-tab">
            <EscrowManager onRefresh={fetchPaymentData} />
          </div>
        )}
      </div>

      {/* Teller Link Modal */}
      <AnimatePresence>
        {showTellerLink && (
          <TellerLinkComponent
            isOpen={showTellerLink}
            onClose={() => setShowTellerLink(false)}
            onSuccess={(data) => {
              console.log('Payment method added:', data);
              setShowTellerLink(false);

              // Update teller data
              setTellerData(prev => ({
                ...prev,
                paymentMethods: [
                  ...prev.paymentMethods,
                  {
                    bank_name: data.bank?.name || 'Connected Bank',
                    account_mask: '0000',
                    access_token: data.access_token,
                    item_id: data.item_id
                  }
                ]
              }));

              fetchPaymentData();
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default PaymentDashboard;
