/**
 * Agreement Integration Service
 * 
 * Main service that integrates Agreement Generator V2 with the Royaltea platform.
 * Handles data mapping, generation triggers, and storage integration.
 */

import { AgreementGeneratorV2 } from './AgreementGeneratorV2.js';
import { ExhibitGenerator } from './ExhibitGenerator.js';
import { PlatformDataMapper } from './PlatformDataMapper.js';
import { supabase } from '../../supabase/supabase.utils.js';

export class AgreementIntegrationService {
  constructor() {
    this.generator = new AgreementGeneratorV2();
    this.exhibitGenerator = new ExhibitGenerator();
    this.dataMapper = new PlatformDataMapper();
  }

  /**
   * Generate agreement from platform data (main integration point)
   */
  async generateAgreementFromPlatformData(ventureId, contributorId, options = {}) {
    try {
      console.log('🔄 Starting agreement generation from platform data...');
      
      // Step 1: Fetch all required data from platform
      const platformData = await this.fetchPlatformData(ventureId, contributorId);
      
      // Step 2: Map platform data to Agreement Generator V2 format
      const agreementData = this.dataMapper.mapVentureToAgreementData(
        platformData.venture,
        platformData.alliance,
        platformData.contributor,
        platformData.milestones
      );
      
      // Step 3: Generate dynamic exhibits
      const exhibits = await this.generateDynamicExhibits(agreementData, platformData);
      
      // Step 4: Generate the agreement
      const result = await this.generateAgreementWithExhibits(agreementData, exhibits, options);
      
      // Step 5: Store the generated agreement (if requested)
      if (options.saveToDatabase !== false) {
        await this.storeGeneratedAgreement(result, ventureId, contributorId, platformData);
      }
      
      console.log('✅ Agreement generation completed successfully');
      return result;
      
    } catch (error) {
      console.error('❌ Agreement generation failed:', error);
      throw new Error(`Agreement generation failed: ${error.message}`);
    }
  }

  /**
   * Fetch all required platform data
   */
  async fetchPlatformData(ventureId, contributorId) {
    console.log('📋 Fetching platform data...');
    
    try {
      // Fetch venture/project data
      const { data: venture, error: ventureError } = await supabase
        .from('projects')
        .select(`
          *,
          alliance:teams!projects_team_id_fkey(
            *,
            company:companies!teams_company_id_fkey(*),
            created_by_user:profiles!teams_created_by_fkey(*)
          )
        `)
        .eq('id', ventureId)
        .single();
      
      if (ventureError) throw ventureError;
      
      // Fetch contributor data
      const { data: contributor, error: contributorError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', contributorId)
        .single();
      
      if (contributorError) throw contributorError;
      
      // Fetch milestones
      const { data: milestones, error: milestonesError } = await supabase
        .from('venture_milestones')
        .select('*')
        .eq('venture_id', ventureId)
        .order('due_date', { ascending: true });
      
      if (milestonesError) throw milestonesError;
      
      // Fetch project specifications if they exist
      const { data: specifications, error: specsError } = await supabase
        .from('project_specifications')
        .select('*')
        .eq('project_id', ventureId)
        .single();
      
      // Specifications are optional, so don't throw on error
      
      console.log('✅ Platform data fetched successfully');
      
      return {
        venture: {
          ...venture,
          specifications: specifications || {}
        },
        alliance: venture.alliance,
        contributor,
        milestones: milestones || []
      };
      
    } catch (error) {
      console.error('❌ Failed to fetch platform data:', error);
      throw new Error(`Failed to fetch platform data: ${error.message}`);
    }
  }

  /**
   * Generate dynamic exhibits from platform data
   */
  async generateDynamicExhibits(agreementData, platformData) {
    console.log('🔧 Generating dynamic exhibits...');
    
    try {
      // Generate Exhibit I (Specifications)
      const exhibitI = this.exhibitGenerator.generateExhibitI(agreementData.project);
      
      // Generate Exhibit II (Milestones/Roadmap)
      const exhibitII = this.exhibitGenerator.generateExhibitII(
        agreementData.project, 
        agreementData.milestones
      );
      
      console.log('✅ Dynamic exhibits generated');
      
      return {
        exhibitI,
        exhibitII
      };
      
    } catch (error) {
      console.error('❌ Failed to generate exhibits:', error);
      throw new Error(`Failed to generate exhibits: ${error.message}`);
    }
  }

  /**
   * Generate agreement with dynamic exhibits
   */
  async generateAgreementWithExhibits(agreementData, exhibits, options = {}) {
    console.log('⚙️  Generating agreement with exhibits...');
    
    try {
      // Load the template
      const template = await this.generator.loadTemplate('standard');
      
      // Process the template with agreement data
      let agreement = await this.generator.processTemplate(template, agreementData);
      
      // Replace exhibit placeholders with generated content
      agreement = agreement.replace(/\{\{EXHIBIT_I_CONTENT\}\}/g, exhibits.exhibitI);
      agreement = agreement.replace(/\{\{EXHIBIT_II_CONTENT\}\}/g, exhibits.exhibitII);
      
      // Final validation
      const validationResult = await this.generator.validateOutput(agreement);
      
      console.log('✅ Agreement generated with exhibits');
      
      return {
        success: true,
        agreement: agreement,
        metadata: {
          generatedAt: new Date().toISOString(),
          accuracyScore: validationResult.accuracyScore,
          templateType: 'standard',
          exhibitsGenerated: true,
          ...options.metadata
        },
        exhibits: {
          exhibitI: exhibits.exhibitI,
          exhibitII: exhibits.exhibitII
        },
        validationResults: validationResult
      };
      
    } catch (error) {
      console.error('❌ Failed to generate agreement:', error);
      throw new Error(`Failed to generate agreement: ${error.message}`);
    }
  }

  /**
   * Store generated agreement in database
   */
  async storeGeneratedAgreement(result, ventureId, contributorId, platformData) {
    console.log('💾 Storing generated agreement...');
    
    try {
      const agreementData = {
        agreement_name: `${platformData.venture.name} - Contributor Agreement`,
        agreement_type: 'contributor_agreement',
        alliance_id: platformData.alliance?.id,
        venture_id: ventureId,
        contributor_id: contributorId,
        template_id: null, // We're using the V2 system
        agreement_content: result.agreement,
        generation_parameters: {
          generator_version: '2.0.0',
          template_type: 'standard',
          generated_at: result.metadata.generatedAt,
          accuracy_score: result.metadata.accuracyScore,
          exhibits_generated: true,
          platform_data_version: '1.0.0'
        },
        status: 'draft',
        version: 1,
        created_by: contributorId,
        is_active: true
      };
      
      const { data: storedAgreement, error: storeError } = await supabase
        .from('generated_agreements')
        .insert([agreementData])
        .select()
        .single();
      
      if (storeError) throw storeError;
      
      // Log the agreement generation activity
      await this.logAgreementActivity(ventureId, contributorId, storedAgreement.id, 'generated');
      
      console.log('✅ Agreement stored successfully');
      
      return storedAgreement;
      
    } catch (error) {
      console.error('❌ Failed to store agreement:', error);
      throw new Error(`Failed to store agreement: ${error.message}`);
    }
  }

  /**
   * Log agreement activity for audit trail
   */
  async logAgreementActivity(ventureId, userId, agreementId, action) {
    try {
      const activityData = {
        agreement_id: agreementId,
        user_id: userId,
        action: action,
        details: {
          venture_id: ventureId,
          timestamp: new Date().toISOString(),
          generator_version: '2.0.0'
        }
      };
      
      await supabase
        .from('agreement_audit_log')
        .insert([activityData]);
        
    } catch (error) {
      console.error('⚠️  Failed to log agreement activity:', error);
      // Don't throw - this is non-critical
    }
  }

  /**
   * Generate agreement for venture creation flow
   */
  async generateForVentureCreation(ventureData, allianceData, creatorData, options = {}) {
    console.log('🚀 Generating agreement for venture creation...');
    
    try {
      // Map the creation data to agreement format
      const agreementData = this.dataMapper.mapVentureToAgreementData(
        ventureData,
        allianceData,
        creatorData,
        ventureData.milestones || []
      );
      
      // Generate exhibits
      const exhibits = await this.generateDynamicExhibits(agreementData, {
        venture: ventureData,
        alliance: allianceData,
        contributor: creatorData,
        milestones: ventureData.milestones || []
      });
      
      // Generate the agreement
      const result = await this.generateAgreementWithExhibits(agreementData, exhibits, {
        ...options,
        metadata: {
          ...options.metadata,
          generationContext: 'venture_creation'
        }
      });
      
      console.log('✅ Venture creation agreement generated');
      return result;
      
    } catch (error) {
      console.error('❌ Failed to generate venture creation agreement:', error);
      throw new Error(`Failed to generate venture creation agreement: ${error.message}`);
    }
  }

  /**
   * Generate agreement for contributor invitation flow
   */
  async generateForContributorInvitation(ventureId, inviteeData, inviterData, options = {}) {
    console.log('👥 Generating agreement for contributor invitation...');
    
    try {
      // Use the main generation method
      const result = await this.generateAgreementFromPlatformData(ventureId, inviteeData.id, {
        ...options,
        metadata: {
          ...options.metadata,
          generationContext: 'contributor_invitation',
          inviter_id: inviterData.id
        }
      });
      
      console.log('✅ Contributor invitation agreement generated');
      return result;
      
    } catch (error) {
      console.error('❌ Failed to generate contributor invitation agreement:', error);
      throw new Error(`Failed to generate contributor invitation agreement: ${error.message}`);
    }
  }

  /**
   * Regenerate agreement with updated data
   */
  async regenerateAgreement(agreementId, options = {}) {
    console.log('🔄 Regenerating agreement with updated data...');
    
    try {
      // Fetch existing agreement
      const { data: existingAgreement, error: fetchError } = await supabase
        .from('generated_agreements')
        .select('*')
        .eq('id', agreementId)
        .single();
      
      if (fetchError) throw fetchError;
      
      // Generate new version
      const result = await this.generateAgreementFromPlatformData(
        existingAgreement.venture_id,
        existingAgreement.contributor_id,
        {
          ...options,
          saveToDatabase: false // We'll handle versioning manually
        }
      );
      
      // Update the existing agreement with new version
      const { data: updatedAgreement, error: updateError } = await supabase
        .from('generated_agreements')
        .update({
          agreement_content: result.agreement,
          generation_parameters: {
            ...existingAgreement.generation_parameters,
            regenerated_at: new Date().toISOString(),
            previous_version: existingAgreement.version,
            accuracy_score: result.metadata.accuracyScore
          },
          version: existingAgreement.version + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', agreementId)
        .select()
        .single();
      
      if (updateError) throw updateError;
      
      // Log the regeneration activity
      await this.logAgreementActivity(
        existingAgreement.venture_id,
        existingAgreement.contributor_id,
        agreementId,
        'regenerated'
      );
      
      console.log('✅ Agreement regenerated successfully');
      
      return {
        ...result,
        storedAgreement: updatedAgreement
      };
      
    } catch (error) {
      console.error('❌ Failed to regenerate agreement:', error);
      throw new Error(`Failed to regenerate agreement: ${error.message}`);
    }
  }

  /**
   * Get system status and capabilities
   */
  getSystemInfo() {
    return {
      version: '2.0.0',
      capabilities: {
        platformIntegration: true,
        dynamicExhibits: true,
        dataMapping: true,
        databaseStorage: true,
        auditLogging: true,
        versionControl: true
      },
      supportedTriggers: [
        'venture_creation',
        'contributor_invitation',
        'manual_generation',
        'data_update_regeneration'
      ],
      accuracyTarget: '100%',
      status: 'production_ready'
    };
  }
}
