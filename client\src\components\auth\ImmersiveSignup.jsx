import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Input, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';

/**
 * ImmersiveSignup Component
 * 
 * Full-screen immersive signup experience following wireframe specifications
 * Includes username, email, password, and birth date fields
 * Connects to onboarding flow for new users
 */
const ImmersiveSignup = ({ 
  onSwitchToLogin, 
  onCancel,
  redirectTo = '/onboarding' 
}) => {
  const { signup, loginWithGoogle, loginWithGithub, isLoading } = useContext(UserContext);
  const navigate = useNavigate();
  const location = useLocation();
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    birthDay: '',
    birthMonth: '',
    birthYear: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  // Generate years for birth date (18-100 years old)
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 83 }, (_, i) => currentYear - 18 - i);
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Clear error when user starts typing
    
    // Calculate password strength
    if (field === 'password') {
      setPasswordStrength(calculatePasswordStrength(value));
    }
  };

  // Calculate password strength
  const calculatePasswordStrength = (password) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[a-z]/.test(password)) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    return strength;
  };

  // Validate form data
  const validateForm = () => {
    if (!formData.username || formData.username.length < 3) {
      setError('Username must be at least 3 characters long');
      return false;
    }
    
    if (!/^[\w\s]{3,15}$/.test(formData.username)) {
      setError('Username must be 3-15 characters and contain only letters, numbers, underscores, and spaces');
      return false;
    }
    
    if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Please enter a valid email address');
      return false;
    }
    
    if (!formData.password || formData.password.length < 6) {
      setError('Password must be at least 6 characters long');
      return false;
    }
    
    if (!formData.birthDay || !formData.birthMonth || !formData.birthYear) {
      setError('Please enter your complete birth date');
      return false;
    }
    
    return true;
  };

  // Handle email/password signup
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    if (!validateForm()) return;
    
    setLoading(true);

    try {
      // Create birth date
      const birthDate = new Date(
        parseInt(formData.birthYear),
        months.indexOf(formData.birthMonth),
        parseInt(formData.birthDay)
      );

      await signup(formData.email, formData.password, {
        full_name: formData.username,
        birth_date: birthDate.toISOString(),
        username: formData.username
      });
      
      // Redirect to onboarding for new users
      navigate(redirectTo, { replace: true });
      
      toast.success('Account created successfully! Welcome to Royaltea!');
    } catch (err) {
      console.error('Signup error:', err);
      setError(err.message || 'Failed to create account. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle social authentication
  const handleSocialAuth = async (provider) => {
    setError('');
    setLoading(true);
    
    try {
      if (provider === 'google') {
        await loginWithGoogle();
      } else if (provider === 'github') {
        await loginWithGithub();
      }
      
      // Redirect to onboarding for new users
      navigate(redirectTo, { replace: true });
      toast.success(`Welcome! Account created with ${provider}.`);
    } catch (err) {
      console.error('Social auth error:', err);
      setError(err.message || `Failed to sign up with ${provider}`);
    } finally {
      setLoading(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Get password strength color
  const getPasswordStrengthColor = () => {
    if (passwordStrength < 50) return 'danger';
    if (passwordStrength < 75) return 'warning';
    return 'success';
  };

  return (
    <motion.div
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-primary-50 to-secondary-50"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Exit button */}
      {onCancel && (
        <motion.div
          className="absolute top-6 right-6 z-10"
          variants={itemVariants}
        >
          <Button
            variant="light"
            size="lg"
            onPress={onCancel}
            isIconOnly
            className="text-foreground hover:bg-default-100"
          >
            <i className="bi bi-x-lg text-2xl"></i>
          </Button>
        </motion.div>
      )}

      <div className="max-w-md mx-auto w-full">
        {/* Welcome Title */}
        <motion.div variants={itemVariants} className="text-center mb-12">
          <h1 className="text-5xl font-bold text-foreground mb-4">
            Create Your Account
          </h1>
          <p className="text-lg text-default-600">
            Join the revolution in fair compensation
          </p>
        </motion.div>

        {/* Signup Form */}
        <motion.div variants={itemVariants}>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Username Input */}
            <div>
              <Input
                type="text"
                label="Username"
                placeholder="your-username"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={loading || isLoading}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
                startContent={
                  <i className="bi bi-person text-default-400"></i>
                }
                description="3-15 characters, letters, numbers, underscores, and spaces only"
              />
            </div>

            {/* Email Input */}
            <div>
              <Input
                type="email"
                label="Email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={loading || isLoading}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
                startContent={
                  <i className="bi bi-envelope text-default-400"></i>
                }
              />
            </div>

            {/* Password Input */}
            <div>
              <Input
                type={showPassword ? "text" : "password"}
                label="Password"
                placeholder="Create a strong password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                size="lg"
                variant="bordered"
                isRequired
                isDisabled={loading || isLoading}
                classNames={{
                  input: "text-lg",
                  inputWrapper: "h-14"
                }}
                startContent={
                  <i className="bi bi-lock text-default-400"></i>
                }
                endContent={
                  <Button
                    variant="light"
                    size="sm"
                    isIconOnly
                    onPress={() => setShowPassword(!showPassword)}
                    className="text-default-400 hover:text-default-600"
                  >
                    <i className={`bi ${showPassword ? 'bi-eye-slash' : 'bi-eye'}`}></i>
                  </Button>
                }
              />
              
              {/* Password Strength Indicator */}
              {formData.password && (
                <div className="mt-2">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-default-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 bg-${getPasswordStrengthColor()}`}
                        style={{ width: `${passwordStrength}%` }}
                      ></div>
                    </div>
                    <span className={`text-xs text-${getPasswordStrengthColor()}`}>
                      {passwordStrength < 50 ? 'Weak' : passwordStrength < 75 ? 'Good' : 'Strong'}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Birth Date */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Birth Date
              </label>
              <div className="grid grid-cols-3 gap-2">
                <Select
                  placeholder="Day"
                  value={formData.birthDay}
                  onChange={(value) => handleInputChange('birthDay', value)}
                  size="lg"
                  variant="bordered"
                  isRequired
                  isDisabled={loading || isLoading}
                >
                  {days.map((day) => (
                    <SelectItem key={day} value={day.toString()}>
                      {day}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  placeholder="Month"
                  value={formData.birthMonth}
                  onChange={(value) => handleInputChange('birthMonth', value)}
                  size="lg"
                  variant="bordered"
                  isRequired
                  isDisabled={loading || isLoading}
                >
                  {months.map((month) => (
                    <SelectItem key={month} value={month}>
                      {month}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  placeholder="Year"
                  value={formData.birthYear}
                  onChange={(value) => handleInputChange('birthYear', value)}
                  size="lg"
                  variant="bordered"
                  isRequired
                  isDisabled={loading || isLoading}
                >
                  {years.map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-danger-50 border border-danger-200 rounded-lg p-3"
              >
                <p className="text-danger text-sm">{error}</p>
              </motion.div>
            )}

            {/* Sign Up Button */}
            <Button
              type="submit"
              size="lg"
              className="w-full bg-primary text-white font-semibold py-4 text-lg"
              isLoading={loading || isLoading}
              isDisabled={!formData.username || !formData.email || !formData.password}
            >
              {loading || isLoading ? 'Creating Account...' : 'Sign Up'}
            </Button>
          </form>
        </motion.div>

        {/* Social Login */}
        <motion.div variants={itemVariants} className="mt-8">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-default-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-background text-default-500">Or continue with</span>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-2 gap-3">
            <Button
              variant="bordered"
              size="lg"
              onPress={() => handleSocialAuth('google')}
              isDisabled={loading || isLoading}
              className="h-12"
            >
              <i className="bi bi-google text-lg mr-2"></i>
              Google
            </Button>
            <Button
              variant="bordered"
              size="lg"
              onPress={() => handleSocialAuth('github')}
              isDisabled={loading || isLoading}
              className="h-12"
            >
              <i className="bi bi-github text-lg mr-2"></i>
              GitHub
            </Button>
          </div>
        </motion.div>

        {/* Footer Links */}
        <motion.div variants={itemVariants} className="mt-8 text-center">
          {/* Switch to Login */}
          {onSwitchToLogin && (
            <div className="flex items-center justify-center space-x-2">
              <span className="text-default-600">Already have an account?</span>
              <Button
                variant="light"
                onPress={onSwitchToLogin}
                className="text-primary hover:text-primary-600 font-semibold"
              >
                Login ↓
              </Button>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ImmersiveSignup;
