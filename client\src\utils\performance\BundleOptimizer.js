// Bundle Optimizer Utility
// Integration & Services Agent: JavaScript bundle optimization and code splitting

import React from 'react';

// Dynamic import utilities for code splitting
export const loadComponent = async (componentPath) => {
  try {
    const module = await import(componentPath);
    return module.default || module;
  } catch (error) {
    console.error(`Failed to load component: ${componentPath}`, error);
    throw error;
  }
};

// Lazy load components with error boundaries
export const createLazyComponent = (importFunc, fallback = null) => {
  const LazyComponent = React.lazy(importFunc);
  
  return React.forwardRef((props, ref) => (
    <React.Suspense fallback={fallback || <div>Loading...</div>}>
      <LazyComponent ref={ref} {...props} />
    </React.Suspense>
  ));
};

// Route-based code splitting
export const createLazyRoute = (importFunc) => {
  return React.lazy(importFunc);
};

// Preload components for better UX
export const preloadComponent = (importFunc) => {
  const componentImport = importFunc();
  
  // Store the promise to avoid duplicate imports
  if (\!preloadComponent._cache) {
    preloadComponent._cache = new Map();
  }
  
  const cacheKey = importFunc.toString();
  if (\!preloadComponent._cache.has(cacheKey)) {
    preloadComponent._cache.set(cacheKey, componentImport);
  }
  
  return componentImport;
};

// Bundle size analyzer
export class BundleAnalyzer {
  constructor() {
    this.loadedModules = new Set();
    this.moduleStats = new Map();
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  // Track module loading
  trackModuleLoad(moduleName, size = 0) {
    if (\!this.isEnabled) return;

    this.loadedModules.add(moduleName);
    this.moduleStats.set(moduleName, {
      name: moduleName,
      size,
      loadTime: Date.now(),
      loadOrder: this.loadedModules.size
    });

    if (this.isEnabled) {
      console.log(`📦 Module loaded: ${moduleName} (${size} bytes)`);
    }
  }

  // Get bundle statistics
  getBundleStats() {
    const stats = {
      totalModules: this.loadedModules.size,
      totalSize: 0,
      moduleDetails: []
    };

    for (const [name, info] of this.moduleStats.entries()) {
      stats.totalSize += info.size;
      stats.moduleDetails.push(info);
    }

    // Sort by size (largest first)
    stats.moduleDetails.sort((a, b) => b.size - a.size);

    return stats;
  }

  // Identify large modules
  getLargeModules(threshold = 100000) { // 100KB threshold
    return Array.from(this.moduleStats.values())
      .filter(module => module.size > threshold)
      .sort((a, b) => b.size - a.size);
  }

  // Generate optimization recommendations
  getOptimizationRecommendations() {
    const recommendations = [];
    const largeModules = this.getLargeModules();
    const stats = this.getBundleStats();

    // Check for large modules
    if (largeModules.length > 0) {
      recommendations.push({
        type: 'large-modules',
        severity: 'warning',
        message: `Found ${largeModules.length} large modules (>100KB)`,
        modules: largeModules.map(m => m.name),
        suggestion: 'Consider code splitting or lazy loading for these modules'
      });
    }

    // Check total bundle size
    if (stats.totalSize > 1000000) { // 1MB threshold
      recommendations.push({
        type: 'large-bundle',
        severity: 'error',
        message: `Total bundle size is ${(stats.totalSize / 1024 / 1024).toFixed(2)}MB`,
        suggestion: 'Implement aggressive code splitting and lazy loading'
      });
    }

    return recommendations;
  }
}

// Memory management utilities
export class MemoryManager {
  constructor() {
    this.cleanupTasks = new Set();
    this.memoryThreshold = 50 * 1024 * 1024; // 50MB
    this.checkInterval = 30000; // 30 seconds
    
    this.startMemoryMonitoring();
  }

  // Start monitoring memory usage
  startMemoryMonitoring() {
    if (\!('memory' in performance)) return;

    setInterval(() => {
      const memory = performance.memory;
      const usedMemory = memory.usedJSHeapSize;
      
      if (usedMemory > this.memoryThreshold) {
        this.triggerCleanup();
      }
    }, this.checkInterval);
  }

  // Register cleanup task
  registerCleanup(cleanupFunction) {
    this.cleanupTasks.add(cleanupFunction);
    
    // Return unregister function
    return () => {
      this.cleanupTasks.delete(cleanupFunction);
    };
  }

  // Trigger memory cleanup
  triggerCleanup() {
    console.log('🧹 Triggering memory cleanup...');
    
    for (const cleanup of this.cleanupTasks) {
      try {
        cleanup();
      } catch (error) {
        console.error('Cleanup task failed:', error);
      }
    }

    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  // Get memory usage info
  getMemoryInfo() {
    if (\!('memory' in performance)) {
      return { supported: false };
    }

    const memory = performance.memory;
    return {
      supported: true,
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    };
  }
}

// Create singleton instances
export const bundleAnalyzer = new BundleAnalyzer();
export const memoryManager = new MemoryManager();

export default {
  loadComponent,
  createLazyComponent,
  createLazyRoute,
  preloadComponent,
  bundleAnalyzer,
  memoryManager
};
