// RevenueAnalytics - Advanced revenue analytics and insights
// Implements revenue analytics following dashboard specifications
import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Badge, Progress, Tabs, Tab, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  TrendingUp, 
  TrendingDown,
  BarChart3, 
  PieChart,
  Calendar,
  Target,
  Award,
  Users,
  DollarSign,
  Activity,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

const RevenueAnalytics = ({ className = "", period = '6m' }) => {
  const { currentUser } = useContext(UserContext);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      totalRevenue: 47200,
      monthlyAverage: 7867,
      growth: 15.3,
      bestMonth: 'December',
      bestMonthAmount: 12400,
      projectedAnnual: 94400
    },
    performance: {
      topVentures: [
        { name: 'TaskMaster Pro', revenue: 18600, growth: 23.5, share: 39.4 },
        { name: 'Creative Studio', revenue: 14200, growth: 12.8, share: 30.1 },
        { name: 'Testing Tool', revenue: 8900, growth: -5.2, share: 18.9 },
        { name: 'Analytics Platform', revenue: 5500, growth: 45.7, share: 11.6 }
      ],
      revenueStreams: {
        fixed: { amount: 18400, percentage: 39, growth: 8.2 },
        revenue: { amount: 23600, percentage: 50, growth: 22.1 },
        bonuses: { amount: 5200, percentage: 11, growth: 15.8 }
      },
      workTypes: {
        development: { amount: 28300, percentage: 60, hours: 340 },
        design: { amount: 12400, percentage: 26, hours: 155 },
        testing: { amount: 4200, percentage: 9, hours: 84 },
        consulting: { amount: 2300, percentage: 5, hours: 23 }
      }
    },
    trends: {
      monthlyData: [6200, 7800, 8400, 9200, 8600, 7200],
      labels: ['Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan'],
      predictions: [7800, 8200, 8600],
      predictionLabels: ['Feb', 'Mar', 'Apr']
    },
    insights: {
      recommendations: [
        {
          type: 'opportunity',
          title: 'Revenue Share Focus',
          description: 'Revenue share projects show 22% higher growth than fixed-rate work',
          action: 'Consider prioritizing revenue share opportunities',
          impact: 'high'
        },
        {
          type: 'warning',
          title: 'Testing Tool Decline',
          description: 'Testing Tool project revenue decreased by 5.2% this month',
          action: 'Review project status and engagement level',
          impact: 'medium'
        },
        {
          type: 'success',
          title: 'Analytics Platform Growth',
          description: 'New Analytics Platform project showing 45.7% growth',
          action: 'Consider increasing time allocation to this project',
          impact: 'high'
        }
      ],
      metrics: {
        efficiency: 85,
        diversification: 72,
        growth: 88,
        stability: 76
      }
    }
  });

  useEffect(() => {
    loadAnalyticsData();
  }, [period, currentUser]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // In production, this would fetch real analytics data
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Mock data varies by period
      const periodMultiplier = period === '3m' ? 0.5 : period === '1y' ? 2 : 1;
      
      setAnalyticsData(prev => ({
        ...prev,
        overview: {
          ...prev.overview,
          totalRevenue: Math.round(prev.overview.totalRevenue * periodMultiplier),
          monthlyAverage: Math.round(prev.overview.monthlyAverage * periodMultiplier)
        }
      }));
      
    } catch (error) {
      console.error('Error loading analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const getGrowthColor = (value) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getGrowthIcon = (value) => {
    return value >= 0 ? <ArrowUpRight size={16} /> : <ArrowDownRight size={16} />;
  };

  const getInsightIcon = (type) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="text-green-500" size={20} />;
      case 'warning': return <TrendingDown className="text-yellow-500" size={20} />;
      case 'success': return <Award className="text-blue-500" size={20} />;
      default: return <Activity className="text-gray-500" size={20} />;
    }
  };

  const getInsightColor = (type) => {
    switch (type) {
      case 'opportunity': return 'border-green-200 bg-green-50';
      case 'warning': return 'border-yellow-200 bg-yellow-50';
      case 'success': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getMetricColor = (value) => {
    if (value >= 80) return 'success';
    if (value >= 60) return 'warning';
    return 'danger';
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardBody className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-gray-600">Loading revenue analytics...</p>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`revenue-analytics space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="text-blue-500" size={28} />
            Revenue Analytics
          </h2>
          <p className="text-gray-600">Comprehensive revenue performance and insights</p>
        </div>
        <Select
          selectedKeys={[period]}
          onSelectionChange={(keys) => setPeriod && setPeriod(Array.from(keys)[0])}
          className="w-32"
          size="sm"
        >
          <SelectItem key="3m">3 Months</SelectItem>
          <SelectItem key="6m">6 Months</SelectItem>
          <SelectItem key="1y">1 Year</SelectItem>
        </Select>
      </div>

      {/* Navigation Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs 
            selectedKey={activeTab} 
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-6 py-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            <Tab
              key="overview"
              title={
                <div className="flex items-center space-x-2">
                  <BarChart3 size={18} />
                  <span>Overview</span>
                </div>
              }
            />
            <Tab
              key="performance"
              title={
                <div className="flex items-center space-x-2">
                  <Target size={18} />
                  <span>Performance</span>
                </div>
              }
            />
            <Tab
              key="trends"
              title={
                <div className="flex items-center space-x-2">
                  <TrendingUp size={18} />
                  <span>Trends</span>
                </div>
              }
            />
            <Tab
              key="insights"
              title={
                <div className="flex items-center space-x-2">
                  <Activity size={18} />
                  <span>Insights</span>
                </div>
              }
            />
          </Tabs>
        </CardBody>
      </Card>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Key Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-600">Total Revenue</h3>
                    <DollarSign className="text-green-500" size={20} />
                  </div>
                  <div className="text-2xl font-bold text-green-600 mb-2">
                    {formatCurrency(analyticsData.overview.totalRevenue)}
                  </div>
                  <div className={`flex items-center gap-1 text-sm ${getGrowthColor(analyticsData.overview.growth)}`}>
                    {getGrowthIcon(analyticsData.overview.growth)}
                    <span>{formatPercentage(analyticsData.overview.growth)} from last period</span>
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-600">Monthly Average</h3>
                    <Calendar className="text-blue-500" size={20} />
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-2">
                    {formatCurrency(analyticsData.overview.monthlyAverage)}
                  </div>
                  <div className="text-sm text-gray-600">
                    Best: {analyticsData.overview.bestMonth} ({formatCurrency(analyticsData.overview.bestMonthAmount)})
                  </div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardBody className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-600">Projected Annual</h3>
                    <Target className="text-purple-500" size={20} />
                  </div>
                  <div className="text-2xl font-bold text-purple-600 mb-2">
                    {formatCurrency(analyticsData.overview.projectedAnnual)}
                  </div>
                  <div className="text-sm text-gray-600">
                    Based on current trends
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </div>
      )}

      {activeTab === 'performance' && (
        <div className="space-y-6">
          {/* Top Projects */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Top Performing Projects</h3>
            </CardHeader>
            <CardBody className="p-6">
              <div className="space-y-4">
                {analyticsData.performance.topVentures.map((project, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 font-bold">#{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-semibold">{project.name}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>Revenue: {formatCurrency(project.revenue)}</span>
                          <span>Share: {project.share}%</span>
                        </div>
                      </div>
                    </div>
                    <div className={`flex items-center gap-1 ${getGrowthColor(project.growth)}`}>
                      {getGrowthIcon(project.growth)}
                      <span className="font-medium">{formatPercentage(project.growth)}</span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardBody>
          </Card>

          {/* Revenue Streams */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Revenue Streams</h3>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  {Object.entries(analyticsData.performance.revenueStreams).map(([type, data]) => (
                    <div key={type} className="flex items-center justify-between">
                      <div>
                        <span className="capitalize font-medium">{type}</span>
                        <div className="text-sm text-gray-600">{data.percentage}% of total</div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{formatCurrency(data.amount)}</div>
                        <div className={`text-sm ${getGrowthColor(data.growth)}`}>
                          {formatPercentage(data.growth)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Work Type Analysis</h3>
              </CardHeader>
              <CardBody className="p-6">
                <div className="space-y-4">
                  {Object.entries(analyticsData.performance.workTypes).map(([type, data]) => (
                    <div key={type} className="flex items-center justify-between">
                      <div>
                        <span className="capitalize font-medium">{type}</span>
                        <div className="text-sm text-gray-600">{data.hours}h • {data.percentage}%</div>
                      </div>
                      <div className="font-bold">{formatCurrency(data.amount)}</div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      )}

      {activeTab === 'trends' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Revenue Trends</h3>
            </CardHeader>
            <CardBody className="p-6">
              <div className="text-center py-12 text-gray-500">
                <BarChart3 size={48} className="mx-auto mb-4 opacity-50" />
                <p>Interactive trend charts coming soon!</p>
                <p className="text-sm">Revenue trends and predictions over time</p>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {activeTab === 'insights' && (
        <div className="space-y-6">
          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Performance Metrics</h3>
            </CardHeader>
            <CardBody className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(analyticsData.insights.metrics).map(([metric, value]) => (
                  <div key={metric} className="text-center">
                    <div className="text-2xl font-bold mb-2">{value}%</div>
                    <Progress 
                      value={value} 
                      color={getMetricColor(value)} 
                      size="sm" 
                      className="mb-2"
                    />
                    <div className="text-sm text-gray-600 capitalize">{metric}</div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>

          {/* AI Insights */}
          <div className="space-y-4">
            {analyticsData.insights.recommendations.map((insight, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className={`border ${getInsightColor(insight.type)}`}>
                  <CardBody className="p-6">
                    <div className="flex items-start gap-4">
                      {getInsightIcon(insight.type)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">{insight.title}</h4>
                          <Badge 
                            color={insight.impact === 'high' ? 'danger' : insight.impact === 'medium' ? 'warning' : 'default'}
                            size="sm"
                          >
                            {insight.impact} impact
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{insight.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-blue-600">{insight.action}</span>
                          <Button size="sm" variant="light" color="primary">
                            Learn More
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RevenueAnalytics;
