import React from 'react';
import { motion } from 'framer-motion';
import { useLocation, useNavigate } from 'react-router-dom';
import { Breadcrumbs, BreadcrumbItem, Button } from '@heroui/react';

/**
 * NavigationBreadcrumbs Component
 * 
 * Provides spatial navigation context and breadcrumbs for the experimental navigation system.
 * Shows users their current location and allows easy navigation back to previous levels.
 */

const NavigationBreadcrumbs = ({ 
  currentCanvas = null,
  currentView = 'grid',
  onNavigateToView,
  className = "" 
}) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Canvas definitions for breadcrumb context
  const canvasDefinitions = {
    wizard: { title: 'Project Creation', icon: '🚀', parent: 'start' },
    projects: { title: 'Projects', icon: '📁', parent: 'start' },
    studios: { title: 'Studios', icon: '🏢', parent: 'social' },
    missions: { title: 'Missions', icon: '🎯', parent: 'track' },
    contributions: { title: 'Contributions', icon: '📊', parent: 'track' },
    validation: { title: 'Validation', icon: '✅', parent: 'track' },
    analytics: { title: 'Analytics', icon: '📈', parent: 'track' },
    revenue: { title: 'Revenue', icon: '💰', parent: 'earn' },
    escrow: { title: 'Escrow', icon: '🏦', parent: 'earn' },
    royalty: { title: 'Royalty', icon: '👑', parent: 'earn' },
    learn: { title: 'Learn', icon: '📚', parent: 'learn' },
    profile: { title: 'Profile', icon: '👤', parent: 'profile' },
    social: { title: 'Social', icon: '👥', parent: 'social' },
    teams: { title: 'Teams', icon: '👥', parent: 'social' },
    settings: { title: 'Settings', icon: '⚙️', parent: 'settings' },
    help: { title: 'Help', icon: '❓', parent: 'help' },
    bugs: { title: 'Bug Reports', icon: '🐛', parent: 'help' }
  };

  // Journey definitions
  const journeys = {
    start: { title: 'Start', icon: '🚀', color: 'primary' },
    track: { title: 'Track', icon: '📊', color: 'secondary' },
    earn: { title: 'Earn', icon: '💰', color: 'success' },
    learn: { title: 'Learn', icon: '📚', color: 'warning' },
    profile: { title: 'Profile', icon: '👤', color: 'default' },
    social: { title: 'Social', icon: '👥', color: 'secondary' },
    settings: { title: 'Settings', icon: '⚙️', color: 'default' },
    help: { title: 'Help', icon: '❓', color: 'primary' }
  };

  // Build breadcrumb path
  const buildBreadcrumbPath = () => {
    const path = [];
    
    // Add dashboard root
    path.push({
      key: 'dashboard',
      title: 'Dashboard',
      icon: '🏠',
      onClick: () => onNavigateToView?.('grid')
    });

    if (currentCanvas && canvasDefinitions[currentCanvas]) {
      const canvas = canvasDefinitions[currentCanvas];
      const parentJourney = journeys[canvas.parent];

      // Add parent journey if it exists
      if (parentJourney) {
        path.push({
          key: canvas.parent,
          title: parentJourney.title,
          icon: parentJourney.icon,
          onClick: () => onNavigateToView?.('world')
        });
      }

      // Add current canvas
      path.push({
        key: currentCanvas,
        title: canvas.title,
        icon: canvas.icon,
        current: true
      });
    }

    return path;
  };

  const breadcrumbPath = buildBreadcrumbPath();

  // View indicators
  const viewIndicators = {
    grid: { title: 'Grid View', icon: '⊞', description: 'Overview of all sections' },
    world: { title: 'World View', icon: '🗺️', description: 'Navigate between cards' },
    content: { title: 'Content View', icon: '📄', description: 'Focused content area' }
  };

  const currentViewInfo = viewIndicators[currentView] || viewIndicators.content;

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`flex items-center justify-between p-3 bg-background/80 backdrop-blur-sm border-b border-divider/50 ${className}`}
    >
      {/* Breadcrumbs */}
      <div className="flex items-center gap-2">
        <Breadcrumbs 
          size="sm"
          separator="/"
          className="text-muted-foreground"
        >
          {breadcrumbPath.map((item, index) => (
            <BreadcrumbItem 
              key={item.key}
              className={item.current ? 'text-foreground font-medium' : 'text-muted-foreground hover:text-foreground cursor-pointer'}
              onClick={item.onClick}
            >
              <span className="flex items-center gap-1">
                <span className="text-sm">{item.icon}</span>
                {item.title}
              </span>
            </BreadcrumbItem>
          ))}
        </Breadcrumbs>
      </div>

      {/* View Indicator */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{currentViewInfo.icon}</span>
          <span>{currentViewInfo.title}</span>
          <span className="text-xs opacity-75">({currentViewInfo.description})</span>
        </div>

        {/* Navigation Hints */}
        {currentView !== 'grid' && (
          <Button
            size="sm"
            variant="flat"
            onClick={() => onNavigateToView?.('grid')}
            className="text-xs"
          >
            ← Back to Grid
          </Button>
        )}
      </div>
    </motion.div>
  );
};

export default NavigationBreadcrumbs;
