import axios from "axios";
import { useState, useEffect, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import LoadingAnimation from "../../components/layout/LoadingAnimation";
import DeleteButton from "../../components/form/DeleteButton";
import { UserContext } from "../../../contexts/user.context";
import { getAuth } from "firebase/auth";
import { Link } from "react-router-dom";

const EditUser = () => {
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const auth = getAuth();

  // Edit User
  const { id } = useParams();
  const [data, setData] = useState({
    displayName: "",
    email: "",
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const token = await currentUser.getIdToken();
        const { data } = await axios.get(`/user/${id}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        setData({
          displayName: data.user.displayName,
          email: data.user.email,
        });
      } catch (error) {
        if (error.response?.data?.error) {
          toast.error(error.response.data.error);
        } else {
          toast.error("An unexpected error occurred. Please try again.");
        }
        console.error("Error fetching user:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [id, currentUser]);

  if (data.email === "") {
    return <LoadingAnimation />; // Prevent rendering if user is null
  }

  const updateUser = async (e) => {
    e.preventDefault();

    try {
      const token = auth.currentUser
        ? await auth.currentUser.getIdToken()
        : null;

      const response = await axios.patch(
        `/user/${id}`,
        { displayName: data.displayName, email: data.email },
        { headers: token ? { Authorization: `Bearer ${token}` } : {} }
      );
      if (response.data.error) {
        toast.error(response.data.error);
      } else {
        toast.success("User updated successfully!");
        navigate(`/user/${id}`);
      }
    } catch (error) {
      console.error("Error updating user:", error);
      if (error.message.includes("401")) {
        toast.error("Unauthorized to make changes to this user.");
      } else {
        toast.error("Failed to update user.");
      }
    }
  };

  const cancelEdit = () => {
    navigate(`/user/${id}`);
  };

  return (
    <div className="mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6">
          <div className="card shadow-sm border-0 rounded-lg">
            <div className="card-body p-4">
              <div className="mb-4">
                <h3 className="text-center">Edit User</h3>
                <p>{id}</p>
              </div>
              <form onSubmit={updateUser} className="text-start">
                <div className="mb-3">
                  <label htmlFor="displayName" className="form-label">
                    Username
                  </label>
                  <input
                    type="text"
                    id="displayName"
                    className="form-control"
                    value={data.displayName}
                    onChange={(e) =>
                      setData({ ...data, displayName: e.target.value })
                    }
                    required
                  />
                </div>
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="form-control"
                    value={data.email}
                    onChange={(e) =>
                      setData({ ...data, email: e.target.value })
                    }
                    required
                  />
                </div>
                <Link className="mt-1 pointer text-dark" to="/password-reset">
                  Reset Password
                </Link>
                <div className="d-flex justify-content-between mt-4">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={cancelEdit}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-primary">
                    Update
                  </button>
                </div>
              </form>
              <DeleteButton id={id} item="user" user={data} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditUser;
