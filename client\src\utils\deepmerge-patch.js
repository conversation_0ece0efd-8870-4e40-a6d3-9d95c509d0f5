/**
 * Deepmerge Package Patch
 * 
 * This module provides a compatibility layer for the deepmerge package
 * to fix the ES module import issue with HeroUI.
 * 
 * The issue: HeroUI tries to import deepmerge with `import deepmerge from 'deepmerge'`
 * but the deepmerge package exports the function as the main export, not as default.
 */

// Import the deepmerge package - Vite will handle the CommonJS to ESM conversion
import * as deepmergePackage from 'deepmerge';

// The deepmerge package exports the deepmerge function as the main export
// In CommonJS: module.exports = deepmerge;
// In ESM conversion: { default: deepmerge }
const deepmerge = deepmergePackage.default || deepmergePackage;

// Export both named and default for maximum compatibility
export default deepmerge;
export { deepmerge };
