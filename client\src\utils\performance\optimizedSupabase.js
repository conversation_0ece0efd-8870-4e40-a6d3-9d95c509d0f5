import { supabase } from '../supabase/supabase.utils';
import { apiCache } from './apiCache';

/**
 * Optimized Supabase Client with Caching
 * 
 * This wrapper provides intelligent caching for Supabase queries to improve
 * application performance and reduce database load.
 * 
 * Task: O3 Performance Optimization - Database Query Caching
 */

export const optimizedSupabase = {
  ...supabase,
  
  // Cached select queries
  cachedSelect: async (table, query = '*', filters = {}, options = {}) => {
    const { 
      cacheKey = null, 
      ttl = 5 * 60 * 1000, // 5 minutes default
      forceRefresh = false 
    } = options;
    
    const key = cacheKey || `${table}_${query}_${JSON.stringify(filters)}`;
    
    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      const cached = apiCache.get(key);
      if (cached) {
        console.log(`[Supabase Cache Hit] ${key}`);
        return { data: cached, error: null, fromCache: true };
      }
    }
    
    console.log(`[Supabase Cache Miss] Querying ${table}`);
    
    let queryBuilder = supabase.from(table).select(query);
    
    // Apply filters
    Object.entries(filters).forEach(([column, value]) => {
      if (Array.isArray(value)) {
        queryBuilder = queryBuilder.in(column, value);
      } else if (typeof value === 'object' && value !== null) {
        // Handle complex filters like { gte: 100 }, { like: '%search%' }
        Object.entries(value).forEach(([operator, operatorValue]) => {
          queryBuilder = queryBuilder[operator](column, operatorValue);
        });
      } else {
        queryBuilder = queryBuilder.eq(column, value);
      }
    });
    
    const { data, error } = await queryBuilder;
    
    if (!error && data) {
      // Use custom TTL if provided
      const originalTtl = apiCache.ttl;
      apiCache.ttl = ttl;
      apiCache.set(key, data);
      apiCache.ttl = originalTtl;
    }
    
    return { data, error, fromCache: false };
  },
  
  // Cached single record query
  cachedSelectSingle: async (table, query = '*', filters = {}, options = {}) => {
    const result = await optimizedSupabase.cachedSelect(table, query, filters, options);
    
    if (result.data && result.data.length > 0) {
      return { ...result, data: result.data[0] };
    }
    
    return { ...result, data: null };
  },
  
  // Cached count query
  cachedCount: async (table, filters = {}, options = {}) => {
    const { cacheKey = null, ttl = 2 * 60 * 1000 } = options; // 2 minutes for counts
    
    const key = cacheKey || `${table}_count_${JSON.stringify(filters)}`;
    
    const cached = apiCache.get(key);
    if (cached) {
      return { count: cached, error: null, fromCache: true };
    }
    
    let queryBuilder = supabase.from(table).select('id', { count: 'exact', head: true });
    
    Object.entries(filters).forEach(([column, value]) => {
      if (Array.isArray(value)) {
        queryBuilder = queryBuilder.in(column, value);
      } else {
        queryBuilder = queryBuilder.eq(column, value);
      }
    });
    
    const { count, error } = await queryBuilder;
    
    if (!error && count !== null) {
      const originalTtl = apiCache.ttl;
      apiCache.ttl = ttl;
      apiCache.set(key, count);
      apiCache.ttl = originalTtl;
    }
    
    return { count, error, fromCache: false };
  },
  
  // Invalidate cache for table
  invalidateCache: (table) => {
    let invalidatedCount = 0;
    
    for (const key of apiCache.cache.keys()) {
      if (key.startsWith(table)) {
        apiCache.cache.delete(key);
        invalidatedCount++;
      }
    }
    
    console.log(`[Cache Invalidation] Invalidated ${invalidatedCount} entries for table: ${table}`);
    return invalidatedCount;
  },
  
  // Invalidate specific cache key
  invalidateCacheKey: (key) => {
    const existed = apiCache.cache.has(key);
    apiCache.cache.delete(key);
    
    if (existed) {
      console.log(`[Cache Invalidation] Invalidated key: ${key}`);
    }
    
    return existed;
  },
  
  // Warm cache with common queries
  warmCache: async (warmupQueries) => {
    console.log(`[Cache Warming] Starting warmup for ${warmupQueries.length} queries`);
    
    const results = await Promise.allSettled(
      warmupQueries.map(async ({ table, query, filters, options }) => {
        return optimizedSupabase.cachedSelect(table, query, filters, options);
      })
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    console.log(`[Cache Warming] Completed: ${successful}/${warmupQueries.length} successful`);
    
    return results;
  },
  
  // Get cache statistics
  getCacheStats: () => {
    return apiCache.getStats();
  },
  
  // Clear all cache
  clearCache: () => {
    apiCache.clear();
    console.log('[Cache] All cache cleared');
  },
  
  // Optimized insert with cache invalidation
  insertWithCacheInvalidation: async (table, data, invalidatePatterns = []) => {
    const result = await supabase.from(table).insert(data);
    
    if (!result.error) {
      // Invalidate table cache
      optimizedSupabase.invalidateCache(table);
      
      // Invalidate additional patterns
      invalidatePatterns.forEach(pattern => {
        for (const key of apiCache.cache.keys()) {
          if (key.includes(pattern)) {
            apiCache.cache.delete(key);
          }
        }
      });
    }
    
    return result;
  },
  
  // Optimized update with cache invalidation
  updateWithCacheInvalidation: async (table, data, filters, invalidatePatterns = []) => {
    let queryBuilder = supabase.from(table).update(data);
    
    Object.entries(filters).forEach(([column, value]) => {
      queryBuilder = queryBuilder.eq(column, value);
    });
    
    const result = await queryBuilder;
    
    if (!result.error) {
      // Invalidate table cache
      optimizedSupabase.invalidateCache(table);
      
      // Invalidate additional patterns
      invalidatePatterns.forEach(pattern => {
        for (const key of apiCache.cache.keys()) {
          if (key.includes(pattern)) {
            apiCache.cache.delete(key);
          }
        }
      });
    }
    
    return result;
  },
  
  // Optimized delete with cache invalidation
  deleteWithCacheInvalidation: async (table, filters, invalidatePatterns = []) => {
    let queryBuilder = supabase.from(table).delete();
    
    Object.entries(filters).forEach(([column, value]) => {
      queryBuilder = queryBuilder.eq(column, value);
    });
    
    const result = await queryBuilder;
    
    if (!result.error) {
      // Invalidate table cache
      optimizedSupabase.invalidateCache(table);
      
      // Invalidate additional patterns
      invalidatePatterns.forEach(pattern => {
        for (const key of apiCache.cache.keys()) {
          if (key.includes(pattern)) {
            apiCache.cache.delete(key);
          }
        }
      });
    }
    
    return result;
  }
};

// Common cache warmup queries for the application
export const commonWarmupQueries = [
  { table: 'users', query: 'id, display_name, avatar_url', filters: {}, options: { ttl: 10 * 60 * 1000 } },
  { table: 'projects', query: 'id, title, description, status', filters: { status: 'active' }, options: { ttl: 5 * 60 * 1000 } },
  { table: 'skills', query: 'id, name, category', filters: {}, options: { ttl: 30 * 60 * 1000 } }
];

// Initialize cache warming on app start
export const initializeCacheWarming = async () => {
  try {
    await optimizedSupabase.warmCache(commonWarmupQueries);
  } catch (error) {
    console.error('[Cache Warming] Failed:', error);
  }
};

export default optimizedSupabase;
