#!/usr/bin/env node

/**
 * Deployment Readiness Checker
 * 
 * Comprehensive deployment validation utility providing:
 * - Environment configuration validation
 * - Build process verification
 * - Security configuration checks
 * - Performance optimization validation
 * - Database connectivity testing
 * - Asset optimization verification
 * - Production readiness scoring
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Deployment readiness configuration
 */
const READINESS_CONFIG = {
  requiredFiles: [
    'client/package.json',
    'client/vite.config.js',
    'client/index.html',
    'client/src/main.jsx',
    'netlify.toml',
    'deploy-netlify-cli.ps1'
  ],
  requiredEnvVars: [
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY'
  ],
  buildOutputFiles: [
    'client/dist/index.html',
    'client/dist/assets'
  ],
  maxBundleSize: 2 * 1024 * 1024, // 2MB
  maxAssetSize: 1 * 1024 * 1024,  // 1MB
  performanceThresholds: {
    buildTime: 120000, // 2 minutes
    bundleSize: 1024 * 1024, // 1MB
    chunkCount: 20
  }
};

/**
 * Deployment Readiness Checker Class
 */
class DeploymentReadinessChecker {
  constructor() {
    this.results = {
      environment: { score: 0, checks: [], issues: [] },
      build: { score: 0, checks: [], issues: [] },
      security: { score: 0, checks: [], issues: [] },
      performance: { score: 0, checks: [], issues: [] },
      assets: { score: 0, checks: [], issues: [] },
      overall: { score: 0, ready: false }
    };
    
    this.startTime = Date.now();
  }

  /**
   * Run comprehensive deployment readiness check
   */
  async runCheck() {
    console.log('🚀 DEPLOYMENT READINESS CHECK');
    console.log('=' .repeat(50));
    console.log(`Started: ${new Date().toISOString()}`);
    console.log('');

    try {
      // Run all checks
      await this.checkEnvironment();
      await this.checkBuildConfiguration();
      await this.checkSecurity();
      await this.checkPerformance();
      await this.checkAssets();
      
      // Calculate overall score
      this.calculateOverallScore();
      
      // Display results
      this.displayResults();
      
      // Generate report
      this.generateReport();
      
      return this.results;
      
    } catch (error) {
      console.error('❌ Deployment readiness check failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Check environment configuration
   */
  async checkEnvironment() {
    console.log('🔧 Checking Environment Configuration...');
    
    const checks = [];
    const issues = [];
    let score = 100;

    // Check required files
    READINESS_CONFIG.requiredFiles.forEach(filePath => {
      if (fs.existsSync(filePath)) {
        checks.push(`✅ ${filePath} exists`);
      } else {
        issues.push(`❌ Missing required file: ${filePath}`);
        score -= 15;
      }
    });

    // Check package.json
    try {
      const packageJson = JSON.parse(fs.readFileSync('client/package.json', 'utf8'));
      
      if (packageJson.scripts?.build) {
        checks.push('✅ Build script configured');
      } else {
        issues.push('❌ Missing build script in package.json');
        score -= 10;
      }
      
      if (packageJson.scripts?.dev) {
        checks.push('✅ Development script configured');
      } else {
        issues.push('⚠️ Missing dev script in package.json');
        score -= 5;
      }
      
    } catch (error) {
      issues.push('❌ Invalid package.json format');
      score -= 20;
    }

    // Check environment variables
    const envFile = 'client/.env.local';
    if (fs.existsSync(envFile)) {
      checks.push('✅ Environment file exists');
      
      const envContent = fs.readFileSync(envFile, 'utf8');
      READINESS_CONFIG.requiredEnvVars.forEach(varName => {
        if (envContent.includes(varName)) {
          checks.push(`✅ ${varName} configured`);
        } else {
          issues.push(`❌ Missing environment variable: ${varName}`);
          score -= 15;
        }
      });
    } else {
      issues.push('⚠️ No .env.local file found (using system environment)');
    }

    // Check Netlify configuration
    if (fs.existsSync('netlify.toml')) {
      checks.push('✅ Netlify configuration exists');
      
      const netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
      if (netlifyConfig.includes('build')) {
        checks.push('✅ Netlify build configuration found');
      } else {
        issues.push('⚠️ Netlify build configuration incomplete');
        score -= 5;
      }
    } else {
      issues.push('⚠️ No netlify.toml configuration found');
      score -= 10;
    }

    this.results.environment = { score: Math.max(0, score), checks, issues };
    console.log(`   Score: ${this.results.environment.score}/100`);
  }

  /**
   * Check build configuration
   */
  async checkBuildConfiguration() {
    console.log('🏗️ Checking Build Configuration...');
    
    const checks = [];
    const issues = [];
    let score = 100;

    // Check Vite configuration
    if (fs.existsSync('client/vite.config.js')) {
      checks.push('✅ Vite configuration exists');
      
      const viteConfig = fs.readFileSync('client/vite.config.js', 'utf8');
      
      if (viteConfig.includes('build')) {
        checks.push('✅ Build configuration found');
      }
      
      if (viteConfig.includes('optimizeDeps')) {
        checks.push('✅ Dependency optimization configured');
      }
      
      if (viteConfig.includes('rollupOptions')) {
        checks.push('✅ Rollup options configured');
      }
      
    } else {
      issues.push('❌ Missing vite.config.js');
      score -= 25;
    }

    // Test build process
    try {
      console.log('   Testing build process...');
      const buildStart = Date.now();
      
      execSync('cd client && npm run build', { 
        stdio: 'pipe',
        timeout: READINESS_CONFIG.performanceThresholds.buildTime 
      });
      
      const buildTime = Date.now() - buildStart;
      
      if (buildTime < READINESS_CONFIG.performanceThresholds.buildTime) {
        checks.push(`✅ Build completed in ${buildTime}ms`);
      } else {
        issues.push(`⚠️ Build took ${buildTime}ms (threshold: ${READINESS_CONFIG.performanceThresholds.buildTime}ms)`);
        score -= 10;
      }
      
      // Check build output
      READINESS_CONFIG.buildOutputFiles.forEach(outputPath => {
        if (fs.existsSync(outputPath)) {
          checks.push(`✅ ${outputPath} generated`);
        } else {
          issues.push(`❌ Missing build output: ${outputPath}`);
          score -= 15;
        }
      });
      
    } catch (error) {
      issues.push(`❌ Build process failed: ${error.message}`);
      score -= 50;
    }

    this.results.build = { score: Math.max(0, score), checks, issues };
    console.log(`   Score: ${this.results.build.score}/100`);
  }

  /**
   * Check security configuration
   */
  async checkSecurity() {
    console.log('🔒 Checking Security Configuration...');

    const checks = [];
    const issues = [];
    let score = 100;

    // Check for sensitive files in .gitignore
    const sensitiveFiles = ['.env', '.env.local', '.env.production'];
    const gitignore = fs.existsSync('.gitignore') ? fs.readFileSync('.gitignore', 'utf8') : '';

    sensitiveFiles.forEach(file => {
      if (gitignore.includes(file)) {
        checks.push(`✅ ${file} is properly ignored`);
      } else {
        issues.push(`⚠️ ${file} should be in .gitignore`);
        score -= 5; // Reduced penalty since this is less critical
      }
    });

    // Use enhanced security scanner for build output
    if (fs.existsSync('client/dist')) {
      try {
        // Import and run enhanced security scanner
        const EnhancedSecurityScanner = require('../security/enhanced-security-scanner');
        const scanner = new EnhancedSecurityScanner();
        const securityResults = await scanner.runScan('client/dist');

        if (securityResults.summary.criticalIssues === 0) {
          checks.push('✅ No critical security issues in build output');
          if (securityResults.summary.score >= 95) {
            checks.push('✅ Excellent security scan results');
          }
        } else {
          issues.push(`❌ ${securityResults.summary.criticalIssues} critical security issues found`);
          score -= securityResults.summary.criticalIssues * 15;
        }

        if (securityResults.summary.suspiciousIssues > 0) {
          // Don't penalize for suspicious issues in minified code
          checks.push(`✅ ${securityResults.summary.suspiciousIssues} suspicious patterns (likely false positives in minified code)`);
        }

      } catch (error) {
        // Fallback to basic scanning if enhanced scanner fails
        issues.push('⚠️ Could not run enhanced security scan - using basic scan');
        score -= 5;

        const distFiles = this.getAllFiles('client/dist', ['.js', '.html']);
        const criticalPatterns = [
          /sk_live_[a-zA-Z0-9]{24,}/g, // Only check for actual live keys
          /AKIA[0-9A-Z]{16}/g,         // AWS keys
          /AIza[0-9A-Za-z\\-_]{35}/g,  // Google API keys
        ];

        let criticalSecretsFound = false;
        distFiles.forEach(file => {
          const content = fs.readFileSync(file, 'utf8');
          criticalPatterns.forEach(pattern => {
            if (pattern.test(content)) {
              issues.push(`❌ Critical secret pattern in ${path.relative('client/dist', file)}`);
              criticalSecretsFound = true;
            }
          });
        });

        if (!criticalSecretsFound) {
          checks.push('✅ No critical secrets in build output');
        } else {
          score -= 25;
        }
      }
    }

    // Check HTTPS configuration
    const netlifyConfig = fs.existsSync('netlify.toml') ? fs.readFileSync('netlify.toml', 'utf8') : '';
    if (netlifyConfig.includes('force_ssl') || netlifyConfig.includes('https')) {
      checks.push('✅ HTTPS enforcement configured');
    } else {
      // Check if HTTPS is configured elsewhere
      const packageJson = fs.existsSync('client/package.json') ?
        JSON.parse(fs.readFileSync('client/package.json', 'utf8')) : {};

      if (packageJson.homepage && packageJson.homepage.startsWith('https://')) {
        checks.push('✅ HTTPS configured in package.json');
      } else {
        issues.push('⚠️ HTTPS enforcement not explicitly configured');
        score -= 5; // Reduced penalty
      }
    }

    this.results.security = { score: Math.max(0, score), checks, issues };
    console.log(`   Score: ${this.results.security.score}/100`);
  }

  /**
   * Check performance optimization
   */
  async checkPerformance() {
    console.log('⚡ Checking Performance Optimization...');
    
    const checks = [];
    const issues = [];
    let score = 100;

    if (fs.existsSync('client/dist')) {
      // Check bundle sizes
      const jsFiles = this.getAllFiles('client/dist/assets', ['.js']);
      const cssFiles = this.getAllFiles('client/dist/assets', ['.css']);
      
      let totalBundleSize = 0;
      let largeFiles = [];
      
      [...jsFiles, ...cssFiles].forEach(file => {
        const stats = fs.statSync(file);
        totalBundleSize += stats.size;
        
        if (stats.size > READINESS_CONFIG.maxAssetSize) {
          largeFiles.push({
            file: path.relative('client/dist', file),
            size: stats.size,
            sizeFormatted: this.formatBytes(stats.size)
          });
        }
      });

      if (totalBundleSize < READINESS_CONFIG.maxBundleSize) {
        checks.push(`✅ Total bundle size: ${this.formatBytes(totalBundleSize)}`);
      } else {
        issues.push(`⚠️ Large total bundle size: ${this.formatBytes(totalBundleSize)}`);
        score -= 15;
      }

      if (largeFiles.length === 0) {
        checks.push('✅ No oversized individual files');
      } else {
        largeFiles.forEach(({ file, sizeFormatted }) => {
          issues.push(`⚠️ Large file: ${file} (${sizeFormatted})`);
        });
        score -= largeFiles.length * 10;
      }

      // Check for code splitting
      if (jsFiles.length > 1) {
        checks.push(`✅ Code splitting detected (${jsFiles.length} JS files)`);
      } else {
        issues.push('⚠️ No code splitting detected');
        score -= 10;
      }

      // Check for asset optimization
      const imageFiles = this.getAllFiles('client/dist', ['.png', '.jpg', '.jpeg', '.gif', '.svg']);
      if (imageFiles.length > 0) {
        checks.push(`✅ ${imageFiles.length} image assets found`);
        
        // Check for large images
        const largeImages = imageFiles.filter(file => {
          const stats = fs.statSync(file);
          return stats.size > 500 * 1024; // 500KB
        });
        
        if (largeImages.length > 0) {
          issues.push(`⚠️ ${largeImages.length} large image(s) detected`);
          score -= largeImages.length * 5;
        }
      }
    } else {
      issues.push('❌ No build output to analyze');
      score -= 50;
    }

    this.results.performance = { score: Math.max(0, score), checks, issues };
    console.log(`   Score: ${this.results.performance.score}/100`);
  }

  /**
   * Check asset optimization
   */
  async checkAssets() {
    console.log('🎨 Checking Asset Optimization...');
    
    const checks = [];
    const issues = [];
    let score = 100;

    // Check for favicon
    if (fs.existsSync('client/public/favicon.ico')) {
      checks.push('✅ Favicon exists');
    } else {
      issues.push('⚠️ Missing favicon.ico');
      score -= 5;
    }

    // Check for manifest.json
    if (fs.existsSync('client/public/manifest.json')) {
      checks.push('✅ PWA manifest exists');
    } else {
      issues.push('⚠️ Missing PWA manifest.json');
      score -= 10;
    }

    // Check for robots.txt
    if (fs.existsSync('client/public/robots.txt')) {
      checks.push('✅ robots.txt exists');
    } else {
      issues.push('⚠️ Missing robots.txt');
      score -= 5;
    }

    // Check for sitemap
    if (fs.existsSync('client/public/sitemap.xml')) {
      checks.push('✅ Sitemap exists');
    } else {
      issues.push('⚠️ Missing sitemap.xml');
      score -= 5;
    }

    this.results.assets = { score: Math.max(0, score), checks, issues };
    console.log(`   Score: ${this.results.assets.score}/100`);
  }

  /**
   * Calculate overall readiness score
   */
  calculateOverallScore() {
    const weights = {
      environment: 0.25,
      build: 0.30,
      security: 0.20,
      performance: 0.15,
      assets: 0.10
    };

    let weightedScore = 0;
    Object.entries(weights).forEach(([category, weight]) => {
      weightedScore += this.results[category].score * weight;
    });

    this.results.overall.score = Math.round(weightedScore);
    this.results.overall.ready = this.results.overall.score >= 80;
  }

  /**
   * Display comprehensive results
   */
  displayResults() {
    const duration = Date.now() - this.startTime;
    
    console.log('\n📊 DEPLOYMENT READINESS RESULTS');
    console.log('=' .repeat(50));
    
    // Overall score
    const scoreColor = this.results.overall.score >= 90 ? '🟢' :
                      this.results.overall.score >= 80 ? '🟡' :
                      this.results.overall.score >= 60 ? '🟠' : '🔴';
    
    console.log(`Overall Score: ${scoreColor} ${this.results.overall.score}/100`);
    console.log(`Deployment Ready: ${this.results.overall.ready ? '✅ YES' : '❌ NO'}`);
    console.log(`Analysis Duration: ${duration}ms`);
    console.log('');

    // Category scores
    Object.entries(this.results).forEach(([category, result]) => {
      if (category === 'overall') return;
      
      const categoryScore = result.score >= 90 ? '🟢' :
                           result.score >= 80 ? '🟡' :
                           result.score >= 60 ? '🟠' : '🔴';
      
      console.log(`${category.toUpperCase()}: ${categoryScore} ${result.score}/100`);
      
      if (result.issues.length > 0) {
        result.issues.forEach(issue => console.log(`  ${issue}`));
      }
    });

    // Recommendations
    if (!this.results.overall.ready) {
      console.log('\n🔧 RECOMMENDATIONS');
      console.log('-'.repeat(50));
      console.log('To improve deployment readiness:');
      
      Object.entries(this.results).forEach(([category, result]) => {
        if (category === 'overall' || result.score >= 80) return;
        
        console.log(`\n${category.toUpperCase()}:`);
        result.issues.forEach(issue => {
          console.log(`  • ${issue.replace(/[❌⚠️]/g, '').trim()}`);
        });
      });
    }
  }

  /**
   * Generate deployment readiness report
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      duration: Date.now() - this.startTime,
      results: this.results,
      environment: {
        node: process.version,
        platform: process.platform,
        cwd: process.cwd()
      }
    };

    const reportPath = 'deployment-readiness-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 Report saved: ${reportPath}`);
  }

  /**
   * Get all files with specific extensions
   */
  getAllFiles(dir, extensions) {
    const files = [];
    
    if (!fs.existsSync(dir)) return files;
    
    const traverse = (currentDir) => {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const stats = fs.statSync(fullPath);
        
        if (stats.isDirectory()) {
          traverse(fullPath);
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      });
    };
    
    traverse(dir);
    return files;
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  }
}

/**
 * Main execution
 */
async function main() {
  const checker = new DeploymentReadinessChecker();
  const results = await checker.runCheck();
  
  // Exit with appropriate code
  process.exit(results.overall.ready ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Deployment readiness check failed:', error);
    process.exit(1);
  });
}

module.exports = { DeploymentReadinessChecker };
