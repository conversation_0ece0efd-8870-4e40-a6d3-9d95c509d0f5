import React, { useState, useEffect, useContext, useRef } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import DatePicker from 'react-datepicker';
import { toast } from 'react-hot-toast';
import { protectAllFormFields } from '../../utils/browserExtensionHandler.js';

const CURRENCY_OPTIONS = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' },
];

// Revenue sources will be fetched from the database

const RevenueEntryForm = ({ projectId, onSuccess, onCancel, initialData = null }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(false);
  const [project, setProject] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [revenueSources, setRevenueSources] = useState([]);
  const [attachmentFiles, setAttachmentFiles] = useState([]);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const [formData, setFormData] = useState({
    amount: '',
    currency: 'USD',
    source_id: '',
    description: '',
    reference_number: '',
    date_received: new Date(),
    status: 'pending',
    in_escrow: true, // Always start with escrow enabled
    escrow_reason: 'Tranche requirements not met',
    escrow_release_condition: 'Revenue will be released when tranche conditions are met or royalty payments are due',
    escrow_release_date: null,
    escrow_notes: '',
    metadata: {}
  });

  const [showEscrowOptions, setShowEscrowOptions] = useState(false);
  const formRef = useRef(null);

  // Fetch project data and revenue configuration
  useEffect(() => {
    const fetchProjectAndSources = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch project data
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', projectId)
          .single();

        if (projectError) throw projectError;

        setProject(projectData);

        // Fetch revenue sources
        const { data: sourcesData, error: sourcesError } = await supabase
          .from('revenue_sources')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (sourcesError) throw sourcesError;

        setRevenueSources(sourcesData || []);

        // If editing, set initial form data and fetch attachments
        if (initialData) {
          setIsEditing(true);

          // Format the date
          const formattedData = {
            ...initialData,
            date_received: initialData.date_received ? new Date(initialData.date_received) : new Date()
          };

          setFormData(formattedData);

          // Fetch attachments if any
          if (initialData.has_attachments) {
            const { data: attachmentsData, error: attachmentsError } = await supabase
              .from('revenue_attachments')
              .select('*')
              .eq('revenue_id', initialData.id);

            if (attachmentsError) {
              console.error('Error fetching attachments:', attachmentsError);
            } else if (attachmentsData && attachmentsData.length > 0) {
              // Store attachment information
              console.log('Existing attachments:', attachmentsData);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchProjectAndSources();
  }, [projectId, initialData]);

  // Protect form fields from browser extension interference
  useEffect(() => {
    if (formRef.current) {
      protectAllFormFields(formRef.current);
    }
  }, [formData]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle date change
  const handleDateChange = (date) => {
    setFormData(prev => ({
      ...prev,
      date_received: date
    }));
  };

  // Handle amount input
  const handleAmountChange = (e) => {
    const value = e.target.value;
    // Allow only numbers and decimal point
    if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
      setFormData(prev => ({
        ...prev,
        amount: value
      }));
    }
  };

  // Handle file selection
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      setAttachmentFiles(prev => [...prev, ...files]);
    }
  };

  // Upload attachment files to Supabase Storage
  const uploadAttachments = async (revenueId, files) => {
    if (!files || files.length === 0) return [];

    try {
      setIsUploading(true);
      setUploadProgress(0);

      const attachments = [];
      const totalFiles = files.length;
      let filesProcessed = 0;

      for (const file of files) {
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `revenue-attachments/${projectId}/${revenueId}/${fileName}`;

        // Upload file to Supabase Storage
        const { data, error } = await supabase.storage
          .from('revenue-attachments')
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: false,
            onUploadProgress: (progress) => {
              const fileProgress = Math.round((progress.loaded / progress.total) * 100);
              const overallProgress = Math.round(((filesProcessed * 100) + fileProgress) / totalFiles);
              setUploadProgress(overallProgress);
            }
          });

        if (error) {
          console.error('Error uploading file:', error);
          toast.error(`Failed to upload ${file.name}`);
          continue;
        }

        // Get public URL
        const { data: urlData } = supabase.storage
          .from('revenue-attachments')
          .getPublicUrl(filePath);

        // Create attachment record
        const { data: attachmentData, error: attachmentError } = await supabase
          .from('revenue_attachments')
          .insert({
            revenue_id: revenueId,
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            file_path: filePath,
            uploaded_by: currentUser.id
          })
          .select()
          .single();

        if (attachmentError) {
          console.error('Error creating attachment record:', attachmentError);
        } else {
          attachments.push(attachmentData);
        }

        filesProcessed++;
      }

      return attachments;
    } catch (error) {
      console.error('Error uploading attachments:', error);
      toast.error('Failed to upload attachments');
      return [];
    } finally {
      setIsUploading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!currentUser) {
      toast.error('You must be logged in to submit revenue data');
      return;
    }

    if (!projectId) {
      toast.error('Project ID is required');
      return;
    }

    try {
      setLoading(true);

      // Prepare revenue data
      const revenueData = {
        ...formData,
        project_id: projectId,
        created_by: currentUser.id,
        amount: parseFloat(formData.amount),
        in_escrow: true // Always put revenue in escrow by default
      };

      // Set default escrow reason if not provided
      if (!revenueData.escrow_reason) {
        revenueData.escrow_reason = "Tranche requirements not met";
      }

      // Set default escrow release condition if not provided
      if (!revenueData.escrow_release_condition) {
        revenueData.escrow_release_condition = "Revenue will be released when tranche conditions are met or royalty payments are due";
      }

      // Ensure escrow fields are properly formatted
      revenueData.escrow_release_date = formData.escrow_release_date
        ? formData.escrow_release_date.toISOString().split('T')[0]
        : null;

      let result;

      if (isEditing) {
        // Update existing revenue entry
        const { data, error } = await supabase
          .from('revenue_entries')
          .update(revenueData)
          .eq('id', initialData.id)
          .select()
          .single();

        if (error) throw error;
        result = data;

        // Upload any new attachments
        if (attachmentFiles.length > 0) {
          await uploadAttachments(result.id, attachmentFiles);
        }

        toast.success('Revenue entry updated successfully');
      } else {
        // Create new revenue entry
        const { data, error } = await supabase
          .from('revenue_entries')
          .insert([revenueData])
          .select()
          .single();

        if (error) throw error;
        result = data;

        // Upload attachments if provided
        if (attachmentFiles.length > 0) {
          await uploadAttachments(result.id, attachmentFiles);
        }

        // Always create escrow record
        const { error: escrowError } = await supabase
          .from('revenue_escrow')
          .insert({
            revenue_id: result.id,
            project_id: projectId,
            amount: revenueData.amount,
            currency: revenueData.currency,
            escrow_date: new Date().toISOString().split('T')[0],
            release_date: revenueData.escrow_release_date,
            status: 'active',
            reason: revenueData.escrow_reason,
            release_condition: revenueData.escrow_release_condition,
            created_by: currentUser.id,
            notes: revenueData.escrow_notes
          });

        if (escrowError) {
          console.error('Error creating escrow record:', escrowError);
          toast.error('Revenue entry added but escrow record creation failed');
        } else {
          toast.success('Revenue entry added and placed in escrow');
        }
      }

      // Reset form
      setFormData({
        amount: '',
        currency: 'USD',
        source_id: '',
        description: '',
        reference_number: '',
        date_received: new Date(),
        status: 'pending',
        in_escrow: true, // Keep escrow enabled
        escrow_reason: 'Tranche requirements not met',
        escrow_release_condition: 'Revenue will be released when tranche conditions are met or royalty payments are due',
        escrow_release_date: null,
        escrow_notes: '',
        metadata: {}
      });
      setShowEscrowOptions(false);
      setAttachmentFiles([]);

      // Call success callback
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('Error submitting revenue:', error);
      toast.error('Failed to submit revenue entry');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !project) {
    return <div className="loading-spinner">Loading...</div>;
  }

  return (
    <div className="revenue-entry-form">
      <form ref={formRef} onSubmit={handleSubmit}>
        <div className="form-grid">
          {/* Amount */}
          <div className="form-group">
            <label htmlFor="amount">Amount*</label>
            <div className="amount-input-group">
              <select
                name="currency"
                value={formData.currency}
                onChange={handleInputChange}
                className="currency-select"
              >
                {CURRENCY_OPTIONS.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.symbol} {currency.code}
                  </option>
                ))}
              </select>
              <input
                type="text"
                id="amount"
                name="amount"
                value={formData.amount}
                onChange={handleAmountChange}
                className="amount-input"
                placeholder="0.00"
                required
                autoComplete="off"
                data-form-type="financial"
                data-lpignore="true"
                data-1p-ignore="true"
              />
            </div>
          </div>

          {/* Date Received */}
          <div className="form-group">
            <label htmlFor="date_received">Date Received*</label>
            <DatePicker
              id="date_received"
              selected={formData.date_received}
              onChange={handleDateChange}
              className="form-control"
              dateFormat="MMMM d, yyyy"
              maxDate={new Date()}
              required
            />
          </div>

          {/* Source */}
          <div className="form-group">
            <label htmlFor="source_id">Revenue Source*</label>
            <select
              id="source_id"
              name="source_id"
              value={formData.source_id}
              onChange={handleInputChange}
              className="form-control"
              required
            >
              <option value="">Select Revenue Source</option>
              {revenueSources.map((source) => (
                <option key={source.id} value={source.id}>
                  {source.name}
                </option>
              ))}
            </select>
          </div>

          {/* Reference Number */}
          <div className="form-group">
            <label htmlFor="reference_number">Reference Number</label>
            <input
              type="text"
              id="reference_number"
              name="reference_number"
              value={formData.reference_number}
              onChange={handleInputChange}
              className="form-control"
              placeholder="Invoice or transaction reference"
              autoComplete="off"
              data-form-type="reference"
              data-lpignore="true"
              data-1p-ignore="true"
            />
          </div>
        </div>

        {/* Description */}
        <div className="form-group">
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            className="form-control"
            rows="3"
            placeholder="Provide details about this revenue entry"
            autoComplete="off"
            data-form-type="description"
            data-lpignore="true"
            data-1p-ignore="true"
          />
        </div>

        {/* Receipt Upload */}
        <div className="form-group">
          <label htmlFor="attachments">Attachments</label>
          <div className="receipt-upload-container">
            <input
              type="file"
              id="attachments"
              onChange={handleFileChange}
              className="receipt-input"
              accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.csv"
              multiple
            />
            <label htmlFor="attachments" className="receipt-upload-button">
              <i className="bi bi-upload"></i>
              {attachmentFiles.length > 0 ? `${attachmentFiles.length} files selected` : 'Choose Files'}
            </label>
          </div>
          {attachmentFiles.length > 0 && (
            <div className="selected-files">
              <ul className="attachment-list">
                {attachmentFiles.map((file, index) => (
                  <li key={index} className="attachment-item">
                    <i className="bi bi-file-earmark"></i> {file.name}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {isUploading && (
            <div className="upload-progress">
              <div className="progress-bar" style={{ width: `${uploadProgress}%` }}></div>
              <span className="progress-text">{uploadProgress}%</span>
            </div>
          )}
        </div>

        {/* Escrow Options */}
        <div className="form-group">
          <div className="escrow-toggle">
            <div className="escrow-info-banner">
              <i className="bi bi-info-circle"></i>
              <span>Revenue will automatically be held in escrow until tranche conditions are met or royalty payments are due.</span>
            </div>
            <button
              type="button"
              className="btn btn-outline-secondary btn-sm"
              onClick={() => {
                setShowEscrowOptions(!showEscrowOptions);
                if (!showEscrowOptions) {
                  setFormData(prev => ({ ...prev, in_escrow: true }));
                }
              }}
            >
              {showEscrowOptions ? 'Hide Escrow Details' : 'Show Escrow Details'}
            </button>
          </div>

          {showEscrowOptions && (
            <div className="escrow-options">
              <div className="form-group">
                <label htmlFor="escrow_reason">Reason for Escrow*</label>
                <select
                  id="escrow_reason"
                  name="escrow_reason"
                  value={formData.escrow_reason}
                  onChange={handleInputChange}
                  className="form-control"
                  required={formData.in_escrow}
                >
                  <option value="">Select Reason</option>
                  <option value="Tranche requirements not met">Tranche requirements not met</option>
                  <option value="No eligible contributors">No eligible contributors</option>
                  <option value="Pending contributor validation">Pending contributor validation</option>
                  <option value="Legal review required">Legal review required</option>
                  <option value="Dispute resolution">Dispute resolution</option>
                  <option value="Milestone completion pending">Milestone completion pending</option>
                  <option value="Contract requirements">Contract requirements</option>
                  <option value="Other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="escrow_release_condition">Release Condition</label>
                <textarea
                  id="escrow_release_condition"
                  name="escrow_release_condition"
                  value={formData.escrow_release_condition}
                  onChange={handleInputChange}
                  className="form-control"
                  rows="2"
                  placeholder="Conditions that must be met for release"
                  autoComplete="off"
                  data-form-type="conditions"
                  data-lpignore="true"
                  data-1p-ignore="true"
                />
              </div>

              <div className="form-group">
                <label htmlFor="escrow_release_date">Expected Release Date</label>
                <DatePicker
                  id="escrow_release_date"
                  selected={formData.escrow_release_date}
                  onChange={date => setFormData(prev => ({ ...prev, escrow_release_date: date }))}
                  className="form-control"
                  dateFormat="MMMM d, yyyy"
                  minDate={new Date()}
                  placeholderText="Select expected release date"
                />
              </div>

              <div className="form-group">
                <label htmlFor="escrow_notes">Escrow Notes</label>
                <textarea
                  id="escrow_notes"
                  name="escrow_notes"
                  value={formData.escrow_notes}
                  onChange={handleInputChange}
                  className="form-control"
                  rows="2"
                  placeholder="Additional notes about this escrow"
                  autoComplete="off"
                  data-form-type="notes"
                  data-lpignore="true"
                  data-1p-ignore="true"
                />
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          {onCancel && (
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
              disabled={loading || isUploading}
            >
              Cancel
            </button>
          )}

          <button
            type="submit"
            className="btn btn-primary"
            disabled={loading || isUploading}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span className="ms-2">Saving...</span>
              </>
            ) : (
              isEditing ? 'Update Revenue Entry' : 'Add Revenue Entry'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default RevenueEntryForm;
