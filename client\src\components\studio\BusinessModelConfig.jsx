import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react';

const BusinessModelConfig = ({ studioId, onClose, onUpdate }) => {
  return (
    <Modal isOpen={true} onClose={onClose} size="2xl">
      <ModalContent>
        <ModalHeader>
          <h3>Business Model Configuration</h3>
        </ModalHeader>
        <ModalBody>
          <div className="text-center py-8">
            <p className="text-gray-600">Business model configuration coming soon...</p>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default BusinessModelConfig;
