// Security Logger
// Authentication & Security Agent: Enhanced security event logging and monitoring
// Created: January 16, 2025

/**
 * Enhanced Security Logger
 * 
 * Provides comprehensive security event logging with real-time monitoring,
 * threat detection, and automated response capabilities
 */

// Security event types
export const SECURITY_EVENT_TYPES = {
  // Authentication events
  LOGIN_SUCCESS: 'login_success',
  LOGIN_FAILURE: 'login_failure',
  LOGOUT: 'logout',
  SESSION_EXPIRED: 'session_expired',
  PASSWORD_CHANGE: 'password_change',
  
  // Authorization events
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
  PERMISSION_DENIED: 'permission_denied',
  PRIVILEGE_ESCALATION: 'privilege_escalation',
  
  // Security violations
  SUSPICIOUS_ACTIVITY: 'suspicious_activity',
  MALICIOUS_INPUT: 'malicious_input',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  CSRF_ATTEMPT: 'csrf_attempt',
  XSS_ATTEMPT: 'xss_attempt',
  SQL_INJECTION_ATTEMPT: 'sql_injection_attempt',
  
  // System events
  SECURITY_SCAN: 'security_scan',
  VULNERABILITY_DETECTED: 'vulnerability_detected',
  SECURITY_UPDATE: 'security_update',
  CONFIGURATION_CHANGE: 'configuration_change',
  
  // User events
  ACCOUNT_LOCKED: 'account_locked',
  ACCOUNT_UNLOCKED: 'account_unlocked',
  PROFILE_UPDATED: 'profile_updated',
  SENSITIVE_DATA_ACCESS: 'sensitive_data_access',
  
  // Admin events
  ADMIN_ACTION: 'admin_action',
  USER_MANAGEMENT: 'user_management',
  SYSTEM_CONFIGURATION: 'system_configuration',
  AUDIT_LOG_ACCESS: 'audit_log_access'
};

// Security event severity levels
export const SECURITY_SEVERITY = {
  CRITICAL: 'critical',
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low',
  INFO: 'info'
};

// Risk score thresholds
const RISK_THRESHOLDS = {
  CRITICAL: 80,
  HIGH: 60,
  MEDIUM: 40,
  LOW: 20
};

/**
 * Security Logger Class
 */
class SecurityLogger {
  constructor() {
    this.eventQueue = [];
    this.isProcessing = false;
    this.batchSize = 10;
    this.flushInterval = 5000; // 5 seconds
    this.maxQueueSize = 1000;
    this.eventCounts = new Map();
    this.suspiciousPatterns = new Map();
    
    // Start background processing
    this.startBackgroundProcessing();
  }

  /**
   * Log security event
   */
  logSecurityEvent(eventType, severity, userId, description, metadata = {}, riskScore = 0) {
    const event = {
      id: this.generateEventId(),
      eventType,
      severity,
      userId,
      description,
      metadata: this.sanitizeMetadata(metadata),
      riskScore,
      timestamp: new Date().toISOString(),
      userAgent: this.getUserAgent(),
      ipAddress: this.getClientIP(),
      sessionId: this.getSessionId(),
      url: window.location.href,
      referrer: document.referrer
    };

    // Add to queue
    this.addToQueue(event);

    // Check for immediate threats
    this.checkImmediateThreats(event);

    // Update pattern detection
    this.updatePatternDetection(event);

    return event.id;
  }

  /**
   * Log authentication event
   */
  logAuthEvent(eventType, userId, success, metadata = {}) {
    const severity = success ? SECURITY_SEVERITY.INFO : SECURITY_SEVERITY.MEDIUM;
    const riskScore = success ? 0 : 30;
    
    return this.logSecurityEvent(
      eventType,
      severity,
      userId,
      `Authentication ${success ? 'successful' : 'failed'}`,
      { success, ...metadata },
      riskScore
    );
  }

  /**
   * Log authorization event
   */
  logAuthzEvent(eventType, userId, resource, action, allowed, metadata = {}) {
    const severity = allowed ? SECURITY_SEVERITY.INFO : SECURITY_SEVERITY.MEDIUM;
    const riskScore = allowed ? 0 : 40;
    
    return this.logSecurityEvent(
      eventType,
      severity,
      userId,
      `Authorization ${allowed ? 'granted' : 'denied'} for ${action} on ${resource}`,
      { resource, action, allowed, ...metadata },
      riskScore
    );
  }

  /**
   * Log security violation
   */
  logSecurityViolation(violationType, userId, details, metadata = {}) {
    const severity = SECURITY_SEVERITY.HIGH;
    const riskScore = 70;
    
    return this.logSecurityEvent(
      SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
      severity,
      userId,
      `Security violation detected: ${violationType} - ${details}`,
      { violationType, details, ...metadata },
      riskScore
    );
  }

  /**
   * Log admin action
   */
  logAdminAction(adminUserId, action, targetUserId, details, metadata = {}) {
    const severity = SECURITY_SEVERITY.MEDIUM;
    const riskScore = 20;
    
    return this.logSecurityEvent(
      SECURITY_EVENT_TYPES.ADMIN_ACTION,
      severity,
      adminUserId,
      `Admin action: ${action} on user ${targetUserId} - ${details}`,
      { action, targetUserId, details, ...metadata },
      riskScore
    );
  }

  /**
   * Log user activity
   */
  logUserActivity(userId, activity, details, metadata = {}) {
    const severity = SECURITY_SEVERITY.INFO;
    const riskScore = 0;
    
    return this.logSecurityEvent(
      'user_activity',
      severity,
      userId,
      `User activity: ${activity} - ${details}`,
      { activity, details, ...metadata },
      riskScore
    );
  }

  /**
   * Add event to processing queue
   */
  addToQueue(event) {
    // Prevent queue overflow
    if (this.eventQueue.length >= this.maxQueueSize) {
      console.warn('Security event queue overflow, dropping oldest events');
      this.eventQueue = this.eventQueue.slice(-this.maxQueueSize / 2);
    }

    this.eventQueue.push(event);

    // Immediate processing for critical events
    if (event.severity === SECURITY_SEVERITY.CRITICAL || event.riskScore >= RISK_THRESHOLDS.CRITICAL) {
      this.processEventImmediately(event);
    }
  }

  /**
   * Process event immediately for critical threats
   */
  async processEventImmediately(event) {
    try {
      // Send to server immediately
      await this.sendToServer([event]);
      
      // Trigger real-time alerts
      this.triggerRealTimeAlert(event);
      
      // Log to console for immediate visibility
      console.error('CRITICAL SECURITY EVENT:', event);
      
    } catch (error) {
      console.error('Failed to process critical security event:', error);
    }
  }

  /**
   * Check for immediate security threats
   */
  checkImmediateThreats(event) {
    // Check for brute force attacks
    if (event.eventType === SECURITY_EVENT_TYPES.LOGIN_FAILURE) {
      this.checkBruteForceAttack(event);
    }

    // Check for privilege escalation attempts
    if (event.eventType === SECURITY_EVENT_TYPES.UNAUTHORIZED_ACCESS) {
      this.checkPrivilegeEscalation(event);
    }

    // Check for suspicious patterns
    if (event.riskScore >= RISK_THRESHOLDS.HIGH) {
      this.checkSuspiciousPatterns(event);
    }
  }

  /**
   * Check for brute force attacks
   */
  checkBruteForceAttack(event) {
    const key = `login_failures_${event.userId || event.ipAddress}`;
    const timeWindow = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;

    if (!this.eventCounts.has(key)) {
      this.eventCounts.set(key, { count: 0, firstAttempt: Date.now() });
    }

    const attempts = this.eventCounts.get(key);
    attempts.count++;

    if (attempts.count >= maxAttempts && (Date.now() - attempts.firstAttempt) <= timeWindow) {
      this.logSecurityEvent(
        SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
        SECURITY_SEVERITY.HIGH,
        event.userId,
        'Potential brute force attack detected',
        {
          attempts: attempts.count,
          timeWindow: timeWindow,
          ipAddress: event.ipAddress
        },
        80
      );
    }

    // Clean up old attempts
    if (Date.now() - attempts.firstAttempt > timeWindow) {
      this.eventCounts.delete(key);
    }
  }

  /**
   * Check for privilege escalation attempts
   */
  checkPrivilegeEscalation(event) {
    const key = `privilege_escalation_${event.userId}`;
    const timeWindow = 5 * 60 * 1000; // 5 minutes
    const maxAttempts = 3;

    if (!this.eventCounts.has(key)) {
      this.eventCounts.set(key, { count: 0, firstAttempt: Date.now() });
    }

    const attempts = this.eventCounts.get(key);
    attempts.count++;

    if (attempts.count >= maxAttempts && (Date.now() - attempts.firstAttempt) <= timeWindow) {
      this.logSecurityEvent(
        SECURITY_EVENT_TYPES.PRIVILEGE_ESCALATION,
        SECURITY_SEVERITY.CRITICAL,
        event.userId,
        'Privilege escalation attempt detected',
        {
          attempts: attempts.count,
          timeWindow: timeWindow
        },
        90
      );
    }
  }

  /**
   * Update pattern detection
   */
  updatePatternDetection(event) {
    const patternKey = `${event.eventType}_${event.userId || 'anonymous'}`;
    
    if (!this.suspiciousPatterns.has(patternKey)) {
      this.suspiciousPatterns.set(patternKey, {
        count: 0,
        firstOccurrence: Date.now(),
        lastOccurrence: Date.now()
      });
    }

    const pattern = this.suspiciousPatterns.get(patternKey);
    pattern.count++;
    pattern.lastOccurrence = Date.now();

    // Check for suspicious frequency
    const timeWindow = 10 * 60 * 1000; // 10 minutes
    const suspiciousThreshold = 20;

    if (pattern.count >= suspiciousThreshold && 
        (pattern.lastOccurrence - pattern.firstOccurrence) <= timeWindow) {
      this.logSecurityEvent(
        SECURITY_EVENT_TYPES.SUSPICIOUS_ACTIVITY,
        SECURITY_SEVERITY.HIGH,
        event.userId,
        `Suspicious activity pattern detected: ${event.eventType}`,
        {
          eventType: event.eventType,
          frequency: pattern.count,
          timeWindow: timeWindow
        },
        75
      );
    }
  }

  /**
   * Start background processing
   */
  startBackgroundProcessing() {
    setInterval(() => {
      this.processEventQueue();
    }, this.flushInterval);
  }

  /**
   * Process event queue
   */
  async processEventQueue() {
    if (this.isProcessing || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Process events in batches
      while (this.eventQueue.length > 0) {
        const batch = this.eventQueue.splice(0, this.batchSize);
        await this.sendToServer(batch);
      }
    } catch (error) {
      console.error('Error processing security event queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Send events to server
   */
  async sendToServer(events) {
    try {
      const response = await fetch('/.netlify/functions/security-events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`
        },
        body: JSON.stringify({ events })
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to send security events to server:', error);
      // Re-queue events for retry
      this.eventQueue.unshift(...events);
      throw error;
    }
  }

  /**
   * Trigger real-time alert
   */
  triggerRealTimeAlert(event) {
    // Trigger browser notification for critical events
    if (event.severity === SECURITY_SEVERITY.CRITICAL && 'Notification' in window) {
      if (Notification.permission === 'granted') {
        new Notification('Security Alert', {
          body: event.description,
          icon: '/security-alert-icon.png',
          tag: 'security-alert'
        });
      }
    }

    // Trigger custom event for real-time UI updates
    window.dispatchEvent(new CustomEvent('securityAlert', {
      detail: event
    }));
  }

  /**
   * Utility methods
   */
  generateEventId() {
    return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  sanitizeMetadata(metadata) {
    // Remove sensitive information from metadata
    const sanitized = { ...metadata };
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'credential'];
    
    for (const key of Object.keys(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  getUserAgent() {
    return navigator.userAgent;
  }

  getClientIP() {
    // This would be set by the server in a real implementation
    return 'client_ip_placeholder';
  }

  getSessionId() {
    // Get session ID from storage or context
    return sessionStorage.getItem('sessionId') || 'no_session';
  }

  getAuthToken() {
    // Get auth token from storage
    return localStorage.getItem('authToken') || '';
  }
}

// Create singleton instance
const securityLogger = new SecurityLogger();

// Export convenience functions
export const logSecurityEvent = (eventType, severity, userId, description, metadata, riskScore) => {
  return securityLogger.logSecurityEvent(eventType, severity, userId, description, metadata, riskScore);
};

export const logAuthEvent = (eventType, userId, success, metadata) => {
  return securityLogger.logAuthEvent(eventType, userId, success, metadata);
};

export const logAuthzEvent = (eventType, userId, resource, action, allowed, metadata) => {
  return securityLogger.logAuthzEvent(eventType, userId, resource, action, allowed, metadata);
};

export const logSecurityViolation = (violationType, userId, details, metadata) => {
  return securityLogger.logSecurityViolation(violationType, userId, details, metadata);
};

export const logAdminAction = (adminUserId, action, targetUserId, details, metadata) => {
  return securityLogger.logAdminAction(adminUserId, action, targetUserId, details, metadata);
};

export const logUserActivity = (userId, activity, details, metadata) => {
  return securityLogger.logUserActivity(userId, activity, details, metadata);
};

export default securityLogger;
