/**
 * Platform Data Mapper for Agreement Generator V2
 * 
 * Converts existing Royaltea platform data structures (ventures, alliances, 
 * milestones, contributors) into the format required by Agreement Generator V2.
 */

export class PlatformDataMapper {
  constructor() {
    this.projectTypeMapping = {
      'software': 'software',
      'game': 'game', 
      'music': 'music',
      'film': 'film',
      'art': 'art',
      'creative': 'art', // fallback
      'consulting': 'software' // fallback
    };
  }

  /**
   * Convert platform venture/project data to Agreement Generator V2 format
   */
  mapVentureToAgreementData(venture, alliance, contributor, milestones = []) {
    return {
      company: this.mapAllianceToCompanyData(alliance),
      project: this.mapVentureToProjectData(venture, milestones),
      contributor: this.mapContributorData(contributor),
      agreement: this.mapAgreementMetadata(venture, alliance),
      milestones: this.mapMilestonesToExhibitData(milestones, venture),
      specifications: this.mapVentureToSpecifications(venture)
    };
  }

  /**
   * Map alliance data to company information for agreements
   */
  mapAllianceToCompanyData(alliance) {
    // Handle both business entity alliances and individual alliances
    if (alliance.is_business_entity && alliance.company_id) {
      return this.mapBusinessEntityToCompany(alliance);
    } else {
      return this.mapIndividualAllianceToCompany(alliance);
    }
  }

  /**
   * Map business entity alliance to company data
   */
  mapBusinessEntityToCompany(alliance) {
    const company = alliance.company || {};
    
    return {
      name: company.legal_name || alliance.name,
      legalName: company.legal_name || alliance.name,
      address: this.formatAddress(company),
      state: company.state || company.jurisdiction || 'Delaware',
      city: company.city || this.extractCityFromAddress(company.address),
      signerName: company.ceo_name || company.primary_contact_name || '[CEO Name]',
      signerTitle: company.ceo_title || 'Chief Executive Officer',
      billingEmail: company.billing_email || company.contact_email || alliance.contact_email || '[<EMAIL>]'
    };
  }

  /**
   * Map individual alliance to company data (for solo entrepreneurs)
   */
  mapIndividualAllianceToCompany(alliance) {
    const creator = alliance.created_by_user || alliance.creator || {};
    
    return {
      name: alliance.business_name || `${alliance.name} LLC`,
      legalName: alliance.business_name || `${alliance.name} LLC`,
      address: creator.business_address || creator.address || '[Business Address]',
      state: alliance.jurisdiction || creator.state || 'Delaware',
      city: creator.city || this.extractCityFromAddress(creator.address) || '[City]',
      signerName: creator.full_name || creator.name || '[Owner Name]',
      signerTitle: creator.title || 'Owner',
      billingEmail: creator.email || alliance.contact_email || '[<EMAIL>]'
    };
  }

  /**
   * Map venture/project data to project information
   */
  mapVentureToProjectData(venture, milestones = []) {
    const projectType = this.projectTypeMapping[venture.venture_type || venture.project_type] || 'software';
    
    return {
      name: venture.name || venture.title,
      description: this.formatProjectDescription(venture.description, projectType),
      projectType: projectType,
      specifications: this.extractProjectSpecifications(venture, milestones)
    };
  }

  /**
   * Map contributor data to agreement format
   */
  mapContributorData(contributor) {
    return {
      name: contributor.full_name || contributor.name || '[Contributor Name]',
      email: contributor.email || '[<EMAIL>]',
      address: contributor.address || '[Contributor Address]'
    };
  }

  /**
   * Map agreement metadata
   */
  mapAgreementMetadata(venture, alliance) {
    return {
      effectiveDate: this.formatEffectiveDate(venture.start_date),
      jurisdiction: alliance.jurisdiction || 'Delaware',
      governingLaw: alliance.governing_law || alliance.jurisdiction || 'Delaware'
    };
  }

  /**
   * Map milestones to exhibit data format
   */
  mapMilestonesToExhibitData(milestones, venture) {
    if (!milestones || milestones.length === 0) {
      return this.generateDefaultMilestones(venture);
    }

    // Group milestones by phases or create phases
    const phases = this.groupMilestonesIntoPhases(milestones);
    
    return {
      phases: phases.map(phase => ({
        name: phase.name,
        duration: phase.duration,
        tasks: phase.milestones.map(m => m.title || m.name)
      })),
      milestones: milestones.map(milestone => ({
        name: milestone.title || milestone.name,
        deadline: this.formatDate(milestone.due_date),
        deliverables: milestone.deliverables || milestone.acceptance_criteria || []
      }))
    };
  }

  /**
   * Extract project specifications from venture data
   */
  extractProjectSpecifications(venture, milestones = []) {
    const specs = venture.specifications || {};
    const projectType = this.projectTypeMapping[venture.venture_type || venture.project_type] || 'software';

    return {
      coreFeatures: this.extractCoreFeatures(venture, specs),
      technical: this.extractTechnicalRequirements(venture, specs, projectType),
      platforms: specs.platforms || this.getDefaultPlatforms(projectType),
      artStyle: specs.art_style || specs.artStyle,
      audioRequirements: specs.audio_requirements || specs.audioRequirements,
      deliverables: this.extractDeliverables(venture, milestones)
    };
  }

  /**
   * Extract core features from venture data
   */
  extractCoreFeatures(venture, specs) {
    if (specs.core_features || specs.coreFeatures) {
      return specs.core_features || specs.coreFeatures;
    }

    // Generate from venture description and objectives
    const features = [];
    
    if (venture.objectives && Array.isArray(venture.objectives)) {
      venture.objectives.forEach(objective => {
        features.push({
          name: objective.title || objective.name,
          description: objective.description,
          details: objective.requirements || []
        });
      });
    }

    // If no structured objectives, create from description
    if (features.length === 0 && venture.description) {
      features.push({
        name: 'Core Functionality',
        description: venture.description,
        details: []
      });
    }

    return features;
  }

  /**
   * Extract technical requirements
   */
  extractTechnicalRequirements(venture, specs, projectType) {
    const technical = specs.technical || {};
    
    // Add project-specific defaults
    const defaults = this.getDefaultTechnicalRequirements(projectType);
    
    return {
      ...defaults,
      ...technical
    };
  }

  /**
   * Extract deliverables from venture and milestones
   */
  extractDeliverables(venture, milestones) {
    const deliverables = [];
    
    // From venture deliverables
    if (venture.deliverables && Array.isArray(venture.deliverables)) {
      deliverables.push(...venture.deliverables);
    }
    
    // From milestone deliverables
    milestones.forEach(milestone => {
      if (milestone.deliverables && Array.isArray(milestone.deliverables)) {
        deliverables.push(...milestone.deliverables);
      }
    });
    
    // Default deliverables if none specified
    if (deliverables.length === 0) {
      const projectType = this.projectTypeMapping[venture.venture_type || venture.project_type] || 'software';
      deliverables.push(...this.getDefaultDeliverables(projectType));
    }
    
    return [...new Set(deliverables)]; // Remove duplicates
  }

  /**
   * Group milestones into logical phases
   */
  groupMilestonesIntoPhases(milestones) {
    // If milestones already have phases, use them
    const phaseGroups = {};
    
    milestones.forEach(milestone => {
      const phase = milestone.phase || this.inferPhaseFromMilestone(milestone);
      if (!phaseGroups[phase]) {
        phaseGroups[phase] = {
          name: phase,
          milestones: [],
          duration: null
        };
      }
      phaseGroups[phase].milestones.push(milestone);
    });
    
    return Object.values(phaseGroups);
  }

  /**
   * Infer phase from milestone data
   */
  inferPhaseFromMilestone(milestone) {
    const title = (milestone.title || milestone.name || '').toLowerCase();
    
    if (title.includes('planning') || title.includes('setup') || title.includes('design')) {
      return 'Phase 1: Planning and Design';
    } else if (title.includes('development') || title.includes('implementation') || title.includes('build')) {
      return 'Phase 2: Development';
    } else if (title.includes('testing') || title.includes('qa') || title.includes('polish')) {
      return 'Phase 3: Testing and Polish';
    } else if (title.includes('launch') || title.includes('deploy') || title.includes('release')) {
      return 'Phase 4: Launch';
    } else {
      return 'Phase 2: Development'; // Default
    }
  }

  /**
   * Generate default milestones for projects without defined milestones
   */
  generateDefaultMilestones(venture) {
    const projectType = this.projectTypeMapping[venture.venture_type || venture.project_type] || 'software';
    
    const defaultPhases = {
      software: [
        {
          name: 'Phase 1: Planning and Architecture',
          tasks: ['Requirements analysis', 'System architecture design', 'Technology stack selection']
        },
        {
          name: 'Phase 2: Core Development',
          tasks: ['Core functionality implementation', 'Database design and setup', 'API development']
        },
        {
          name: 'Phase 3: Testing and Deployment',
          tasks: ['Unit and integration testing', 'User acceptance testing', 'Production deployment']
        }
      ],
      game: [
        {
          name: 'Phase 1: Pre-Production',
          tasks: ['Game design document', 'Art style guide', 'Technical prototype']
        },
        {
          name: 'Phase 2: Production',
          tasks: ['Core gameplay implementation', 'Asset creation', 'Level design']
        },
        {
          name: 'Phase 3: Polish and Launch',
          tasks: ['Bug fixing and optimization', 'Audio implementation', 'Platform submission']
        }
      ]
    };
    
    return {
      phases: defaultPhases[projectType] || defaultPhases.software
    };
  }

  // Helper methods
  formatAddress(company) {
    if (company.address) return company.address;
    
    const parts = [
      company.street_address,
      company.city,
      company.state,
      company.zip_code
    ].filter(Boolean);
    
    return parts.length > 0 ? parts.join(', ') : '[Company Address]';
  }

  extractCityFromAddress(address) {
    if (!address) return null;
    
    // Simple city extraction - assumes "City, State" format
    const parts = address.split(',');
    if (parts.length >= 2) {
      return parts[parts.length - 2].trim();
    }
    
    return null;
  }

  formatProjectDescription(description, projectType) {
    if (!description) {
      return `A ${projectType} project with comprehensive features and functionality`;
    }
    
    // Ensure description starts with article
    const firstWord = description.trim().split(' ')[0].toLowerCase();
    if (!['a', 'an', 'the'].includes(firstWord)) {
      const article = ['a', 'e', 'i', 'o', 'u'].includes(description[0].toLowerCase()) ? 'an' : 'a';
      return `${article} ${description}`;
    }
    
    return description;
  }

  formatEffectiveDate(startDate) {
    if (startDate) {
      return new Date(startDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
    return '[ ], 20[__]'; // Placeholder format matching lawyer template
  }

  formatDate(date) {
    if (!date) return null;
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getDefaultTechnicalRequirements(projectType) {
    const defaults = {
      software: {
        'Programming Languages': 'To be determined based on project requirements',
        'Framework': 'Modern web framework (React, Vue, Angular)',
        'Database': 'PostgreSQL or MongoDB',
        'Hosting': 'Cloud hosting (AWS, Google Cloud, or Azure)'
      },
      game: {
        'Engine': 'Unity or Unreal Engine',
        'Platform': 'PC, with potential mobile/console expansion',
        'Art Style': 'To be defined in collaboration with art team',
        'Audio': 'Professional audio implementation and sound design'
      },
      music: {
        'Recording Quality': 'Professional studio quality (24-bit/96kHz minimum)',
        'Format': 'WAV, FLAC, and MP3 deliverables',
        'Mixing': 'Professional mixing and mastering',
        'Distribution': 'Digital distribution ready'
      }
    };
    
    return defaults[projectType] || defaults.software;
  }

  getDefaultPlatforms(projectType) {
    const platforms = {
      software: ['Web', 'Mobile'],
      game: ['PC', 'Mobile'],
      music: ['Digital Streaming', 'Physical Media'],
      film: ['Digital', 'Streaming Platforms'],
      art: ['Digital', 'Print']
    };
    
    return platforms[projectType] || platforms.software;
  }

  getDefaultDeliverables(projectType) {
    const deliverables = {
      software: [
        'Working software application',
        'Source code and documentation',
        'User manuals and guides',
        'Testing documentation'
      ],
      game: [
        'Playable game build',
        'Source code and project files',
        'Art assets and documentation',
        'Audio files and implementation'
      ],
      music: [
        'Final audio tracks in required formats',
        'Source files and project sessions',
        'Album artwork and promotional materials'
      ]
    };
    
    return deliverables[projectType] || deliverables.software;
  }
}
