// CRITICAL COMPLIANCE: Financial Transaction Processing API
// Day 2 - Developer 3: Tax-compliant payment processing with approval workflows

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Get user from authorization header
async function getUser(authHeader) {
  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  const { data: { user }, error } = await supabase.auth.getUser(token);

  if (error || !user) {
    throw new Error('Invalid authentication token');
  }

  return user;
}

// Validation functions
function validateTransactionData(data) {
  const errors = [];

  // Required fields
  if (!data.company_id) errors.push('Company ID is required');
  if (!data.transaction_type) errors.push('Transaction type is required');
  if (!data.gross_amount || data.gross_amount <= 0) errors.push('Valid gross amount is required');
  if (!data.payee_user_id) errors.push('Payee user ID is required');
  if (!data.description?.trim()) errors.push('Description is required');

  // Validate transaction type
  const validTypes = ['commission', 'recurring_fee', 'royalty', 'expense', 'refund', 'bonus', 'salary'];
  if (data.transaction_type && !validTypes.includes(data.transaction_type)) {
    errors.push('Invalid transaction type');
  }

  // Validate amounts
  if (data.gross_amount && (isNaN(data.gross_amount) || data.gross_amount < 0)) {
    errors.push('Gross amount must be a positive number');
  }

  if (data.tax_amount && (isNaN(data.tax_amount) || data.tax_amount < 0)) {
    errors.push('Tax amount must be a positive number');
  }

  if (data.backup_withholding_rate && (data.backup_withholding_rate < 0 || data.backup_withholding_rate > 100)) {
    errors.push('Backup withholding rate must be between 0 and 100');
  }

  return errors;
}

// Check if user has permission to manage finances for company
async function checkFinancialPermission(userId, companyId) {
  // Check if user is admin of any team linked to this company
  const { data, error } = await supabase
    .from('teams')
    .select(`
      id,
      team_members!inner(user_id, is_admin)
    `)
    .eq('company_id', companyId)
    .eq('team_members.user_id', userId)
    .eq('team_members.is_admin', true);

  if (error) {
    console.error('Error checking financial permission:', error);
    return false;
  }

  return data && data.length > 0;
}

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    const { httpMethod, path, headers, body } = event;
    const authHeader = headers.authorization || headers.Authorization;

    // Get authenticated user
    const user = await getUser(authHeader);

    // Route handling
    const pathParts = path.split('/').filter(p => p);
    const transactionId = pathParts[pathParts.length - 1];

    switch (httpMethod) {
      case 'GET':
        if (transactionId && transactionId !== 'financial-transactions') {
          // Get specific transaction
          return await getTransaction(transactionId, user.id);
        } else {
          // List transactions with filters
          const queryParams = new URLSearchParams(event.queryStringParameters || {});
          return await listTransactions(user.id, queryParams);
        }

      case 'POST':
        // Create transaction
        const createData = JSON.parse(body);
        return await createTransaction(createData, user.id);

      case 'PUT':
        // Update transaction (approve, reject, etc.)
        const updateData = JSON.parse(body);
        return await updateTransaction(transactionId, updateData, user.id);

      default:
        return {
          statusCode: 405,
          headers: corsHeaders,
          body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

  } catch (error) {
    console.error('Financial Transaction API Error:', error);
    return {
      statusCode: error.message.includes('authentication') ? 401 : 500,
      headers: corsHeaders,
      body: JSON.stringify({
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString()
      })
    };
  }
};

// List transactions for user
async function listTransactions(userId, queryParams) {
  const companyId = queryParams.get('company_id');
  const status = queryParams.get('status');
  const transactionType = queryParams.get('transaction_type');
  const taxYear = queryParams.get('tax_year') || new Date().getFullYear();
  const limit = Math.min(parseInt(queryParams.get('limit')) || 50, 100);
  const offset = parseInt(queryParams.get('offset')) || 0;

  let query = supabase
    .from('financial_transactions')
    .select('*')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  // Apply filters
  if (companyId) query = query.eq('company_id', companyId);
  if (status) query = query.eq('status', status);
  if (transactionType) query = query.eq('transaction_type', transactionType);
  if (taxYear) query = query.eq('tax_year', taxYear);

  const { data, error, count } = await query;

  if (error) {
    throw new Error(`Failed to fetch transactions: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      transactions: data,
      count: count,
      pagination: {
        limit,
        offset,
        hasMore: count > offset + limit
      },
      timestamp: new Date().toISOString()
    })
  };
}

// Get specific transaction
async function getTransaction(transactionId, userId) {
  const { data, error } = await supabase
    .from('financial_transactions')
    .select('*')
    .eq('id', transactionId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Transaction not found' })
      };
    }
    throw new Error(`Failed to fetch transaction: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      transaction: data,
      timestamp: new Date().toISOString()
    })
  };
}

// Create new transaction
async function createTransaction(data, userId) {
  // Validate input data
  const errors = validateTransactionData(data);
  if (errors.length > 0) {
    return {
      statusCode: 400,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Validation failed',
        details: errors
      })
    };
  }

  // Check financial permission
  const hasPermission = await checkFinancialPermission(userId, data.company_id);
  if (!hasPermission) {
    return {
      statusCode: 403,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Insufficient permissions to create financial transactions for this company'
      })
    };
  }

  // Prepare transaction data
  const transactionData = {
    company_id: data.company_id,
    project_id: data.project_id || null,
    team_id: data.team_id || null,
    transaction_type: data.transaction_type,
    transaction_category: data.transaction_category || 'business_payment',
    gross_amount: parseFloat(data.gross_amount),
    currency: data.currency || 'USD',
    backup_withholding_rate: parseFloat(data.backup_withholding_rate) || 0,
    payee_user_id: data.payee_user_id,
    payee_company_id: data.payee_company_id || null,
    payer_company_id: data.company_id,
    description: data.description.trim(),
    reference_number: data.reference_number?.trim(),
    payment_method: data.payment_method,
    created_by: userId
  };

  const { data: transaction, error } = await supabase
    .from('financial_transactions')
    .insert(transactionData)
    .select('*')
    .single();

  if (error) {
    throw new Error(`Failed to create transaction: ${error.message}`);
  }

  return {
    statusCode: 201,
    headers: corsHeaders,
    body: JSON.stringify({
      transaction,
      message: 'Transaction created successfully',
      timestamp: new Date().toISOString()
    })
  };
}

// Update transaction (approve, reject, etc.)
async function updateTransaction(transactionId, data, userId) {
  // Check if transaction exists and user has permission
  const { data: existingTransaction, error: fetchError } = await supabase
    .from('financial_transactions')
    .select('*, company_id')
    .eq('id', transactionId)
    .single();

  if (fetchError) {
    if (fetchError.code === 'PGRST116') {
      return {
        statusCode: 404,
        headers: corsHeaders,
        body: JSON.stringify({ error: 'Transaction not found' })
      };
    }
    throw new Error(`Failed to fetch transaction: ${fetchError.message}`);
  }

  // Check financial permission
  const hasPermission = await checkFinancialPermission(userId, existingTransaction.company_id);
  if (!hasPermission) {
    return {
      statusCode: 403,
      headers: corsHeaders,
      body: JSON.stringify({
        error: 'Insufficient permissions to update this transaction'
      })
    };
  }

  // Prepare update data
  const updateData = {};

  // Handle approval/rejection
  if (data.action === 'approve') {
    updateData.status = 'approved';
    updateData.approved_by = userId;
    updateData.approved_at = new Date().toISOString();
    updateData.approval_notes = data.approval_notes;
  } else if (data.action === 'reject') {
    updateData.status = 'cancelled';
    updateData.approval_notes = data.approval_notes || 'Transaction rejected';
  } else if (data.action === 'process') {
    updateData.status = 'processing';
    updateData.processed_at = new Date().toISOString();
    updateData.payment_method = data.payment_method;
    updateData.external_transaction_id = data.external_transaction_id;
  } else if (data.action === 'complete') {
    updateData.status = 'paid';
    updateData.processed_at = new Date().toISOString();
    updateData.external_transaction_id = data.external_transaction_id;
  } else {
    // Regular field updates
    if (data.description) updateData.description = data.description;
    if (data.reference_number) updateData.reference_number = data.reference_number;
    if (data.approval_notes) updateData.approval_notes = data.approval_notes;
  }

  const { data: transaction, error } = await supabase
    .from('financial_transactions')
    .update(updateData)
    .eq('id', transactionId)
    .select('*')
    .single();

  if (error) {
    throw new Error(`Failed to update transaction: ${error.message}`);
  }

  return {
    statusCode: 200,
    headers: corsHeaders,
    body: JSON.stringify({
      transaction,
      message: 'Transaction updated successfully',
      timestamp: new Date().toISOString()
    })
  };
}
