// Vulnerability Scanner
// Authentication & Security Agent: Automated security vulnerability detection
// Created: January 16, 2025

/**
 * Comprehensive vulnerability scanner for the Royaltea platform
 * Detects common security vulnerabilities and provides remediation guidance
 */

class VulnerabilityScanner {
  constructor() {
    this.vulnerabilities = [];
    this.scanResults = {
      critical: [],
      high: [],
      medium: [],
      low: [],
      info: []
    };
  }

  /**
   * Run comprehensive security scan
   */
  async runFullScan() {
    console.log('🔍 Starting comprehensive security scan...');
    
    this.vulnerabilities = [];
    this.scanResults = {
      critical: [],
      high: [],
      medium: [],
      low: [],
      info: []
    };

    // Run all security checks
    await this.scanXSSVulnerabilities();
    await this.scanAuthenticationSecurity();
    await this.scanSessionSecurity();
    await this.scanInputValidation();
    await this.scanAPIEndpoints();
    await this.scanContentSecurityPolicy();
    await this.scanHTTPSConfiguration();
    await this.scanCookieSecurity();
    await this.scanRateLimiting();
    await this.scanErrorHandling();

    return this.generateReport();
  }

  /**
   * Scan for XSS vulnerabilities
   */
  async scanXSSVulnerabilities() {
    const xssPayloads = [
      '<script>alert("xss")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("xss")',
      '<svg onload="alert(1)">',
      '<iframe src="javascript:alert(1)"></iframe>'
    ];

    for (const payload of xssPayloads) {
      try {
        // Test input sanitization
        const sanitized = this.testInputSanitization(payload);
        if (sanitized.includes('<script>') || sanitized.includes('javascript:')) {
          this.addVulnerability('critical', 'XSS_VULNERABILITY', {
            description: 'Input sanitization is not properly filtering XSS payloads',
            payload: payload,
            recommendation: 'Implement proper input sanitization and output encoding'
          });
        }
      } catch (error) {
        this.addVulnerability('medium', 'XSS_SCAN_ERROR', {
          description: 'Error during XSS vulnerability scan',
          error: error.message
        });
      }
    }
  }

  /**
   * Scan authentication security
   */
  async scanAuthenticationSecurity() {
    try {
      // Check for weak password policies
      const weakPasswords = ['password', '123456', 'admin', 'test'];
      for (const password of weakPasswords) {
        const strength = this.testPasswordStrength(password);
        if (strength.isValid) {
          this.addVulnerability('high', 'WEAK_PASSWORD_POLICY', {
            description: 'Password policy allows weak passwords',
            password: password,
            recommendation: 'Strengthen password requirements'
          });
        }
      }

      // Check JWT token security
      await this.scanJWTSecurity();

      // Check session management
      await this.scanSessionManagement();

    } catch (error) {
      this.addVulnerability('medium', 'AUTH_SCAN_ERROR', {
        description: 'Error during authentication security scan',
        error: error.message
      });
    }
  }

  /**
   * Scan JWT security
   */
  async scanJWTSecurity() {
    try {
      // Check for JWT in localStorage (security risk)
      if (typeof window !== 'undefined' && window.localStorage) {
        const localStorageKeys = Object.keys(localStorage);
        for (const key of localStorageKeys) {
          const value = localStorage.getItem(key);
          if (value && (value.includes('eyJ') || key.toLowerCase().includes('token'))) {
            this.addVulnerability('high', 'JWT_IN_LOCALSTORAGE', {
              description: 'JWT tokens stored in localStorage are vulnerable to XSS attacks',
              key: key,
              recommendation: 'Use httpOnly cookies or secure session storage'
            });
          }
        }
      }
    } catch (error) {
      this.addVulnerability('low', 'JWT_SCAN_ERROR', {
        description: 'Error during JWT security scan',
        error: error.message
      });
    }
  }

  /**
   * Scan session security
   */
  async scanSessionSecurity() {
    try {
      // Check session timeout configuration
      const sessionTimeout = 30 * 60 * 1000; // 30 minutes
      if (sessionTimeout > 60 * 60 * 1000) { // More than 1 hour
        this.addVulnerability('medium', 'LONG_SESSION_TIMEOUT', {
          description: 'Session timeout is too long, increasing security risk',
          timeout: sessionTimeout,
          recommendation: 'Reduce session timeout to 30 minutes or less'
        });
      }

      // Check for session fixation protection
      this.checkSessionFixationProtection();

    } catch (error) {
      this.addVulnerability('low', 'SESSION_SCAN_ERROR', {
        description: 'Error during session security scan',
        error: error.message
      });
    }
  }

  /**
   * Scan input validation
   */
  async scanInputValidation() {
    const sqlInjectionPayloads = [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "'; INSERT INTO users VALUES ('hacker', 'password'); --"
    ];

    for (const payload of sqlInjectionPayloads) {
      try {
        // Test SQL injection protection
        const sanitized = this.testInputSanitization(payload);
        if (sanitized.includes('DROP TABLE') || sanitized.includes('INSERT INTO')) {
          this.addVulnerability('critical', 'SQL_INJECTION_VULNERABILITY', {
            description: 'Input validation does not prevent SQL injection',
            payload: payload,
            recommendation: 'Use parameterized queries and proper input validation'
          });
        }
      } catch (error) {
        this.addVulnerability('medium', 'INPUT_VALIDATION_SCAN_ERROR', {
          description: 'Error during input validation scan',
          error: error.message
        });
      }
    }
  }

  /**
   * Scan API endpoints for security issues
   */
  async scanAPIEndpoints() {
    const testEndpoints = [
      '/.netlify/functions/admin-management',
      '/.netlify/functions/content-moderation',
      '/.netlify/functions/auth-middleware'
    ];

    for (const endpoint of testEndpoints) {
      try {
        // Test for unauthorized access
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        if (response.status === 200) {
          this.addVulnerability('high', 'UNAUTHORIZED_API_ACCESS', {
            description: 'API endpoint accessible without authentication',
            endpoint: endpoint,
            recommendation: 'Implement proper authentication middleware'
          });
        }
      } catch (error) {
        // Expected for protected endpoints
        this.addVulnerability('info', 'API_ENDPOINT_PROTECTED', {
          description: 'API endpoint properly protected',
          endpoint: endpoint
        });
      }
    }
  }

  /**
   * Scan Content Security Policy
   */
  async scanContentSecurityPolicy() {
    try {
      // Check if CSP headers are present
      const response = await fetch(window.location.href);
      const cspHeader = response.headers.get('Content-Security-Policy');
      
      if (!cspHeader) {
        this.addVulnerability('high', 'MISSING_CSP_HEADER', {
          description: 'Content Security Policy header is missing',
          recommendation: 'Implement CSP headers to prevent XSS attacks'
        });
      } else {
        // Check for unsafe CSP directives
        if (cspHeader.includes("'unsafe-eval'")) {
          this.addVulnerability('medium', 'UNSAFE_CSP_EVAL', {
            description: 'CSP allows unsafe-eval which can enable XSS',
            recommendation: 'Remove unsafe-eval from CSP directives'
          });
        }
      }
    } catch (error) {
      this.addVulnerability('low', 'CSP_SCAN_ERROR', {
        description: 'Error during CSP scan',
        error: error.message
      });
    }
  }

  /**
   * Scan HTTPS configuration
   */
  async scanHTTPSConfiguration() {
    try {
      if (window.location.protocol !== 'https:' && window.location.hostname !== 'localhost') {
        this.addVulnerability('critical', 'HTTP_INSTEAD_OF_HTTPS', {
          description: 'Application is not using HTTPS',
          recommendation: 'Implement HTTPS with proper SSL/TLS configuration'
        });
      }

      // Check for HSTS header
      const response = await fetch(window.location.href);
      const hstsHeader = response.headers.get('Strict-Transport-Security');
      
      if (!hstsHeader) {
        this.addVulnerability('medium', 'MISSING_HSTS_HEADER', {
          description: 'HTTP Strict Transport Security header is missing',
          recommendation: 'Implement HSTS header to prevent protocol downgrade attacks'
        });
      }
    } catch (error) {
      this.addVulnerability('low', 'HTTPS_SCAN_ERROR', {
        description: 'Error during HTTPS configuration scan',
        error: error.message
      });
    }
  }

  /**
   * Scan cookie security
   */
  async scanCookieSecurity() {
    try {
      if (typeof document !== 'undefined') {
        const cookies = document.cookie.split(';');
        for (const cookie of cookies) {
          const [name, value] = cookie.split('=');
          if (name && value) {
            // Check for secure flag
            if (!cookie.includes('Secure') && window.location.protocol === 'https:') {
              this.addVulnerability('medium', 'INSECURE_COOKIE', {
                description: 'Cookie missing Secure flag',
                cookie: name.trim(),
                recommendation: 'Add Secure flag to all cookies in HTTPS'
              });
            }

            // Check for HttpOnly flag
            if (!cookie.includes('HttpOnly')) {
              this.addVulnerability('medium', 'COOKIE_MISSING_HTTPONLY', {
                description: 'Cookie missing HttpOnly flag',
                cookie: name.trim(),
                recommendation: 'Add HttpOnly flag to prevent XSS access'
              });
            }
          }
        }
      }
    } catch (error) {
      this.addVulnerability('low', 'COOKIE_SCAN_ERROR', {
        description: 'Error during cookie security scan',
        error: error.message
      });
    }
  }

  /**
   * Scan rate limiting implementation
   */
  async scanRateLimiting() {
    try {
      // Test rate limiting by making multiple requests
      const testEndpoint = '/.netlify/functions/test-rate-limit';
      let requestCount = 0;
      let rateLimited = false;

      for (let i = 0; i < 10; i++) {
        try {
          const response = await fetch(testEndpoint);
          requestCount++;
          
          if (response.status === 429) {
            rateLimited = true;
            break;
          }
        } catch (error) {
          // Expected for non-existent endpoint
          break;
        }
      }

      if (!rateLimited && requestCount > 5) {
        this.addVulnerability('medium', 'MISSING_RATE_LIMITING', {
          description: 'Rate limiting not implemented or too permissive',
          requestCount: requestCount,
          recommendation: 'Implement proper rate limiting to prevent abuse'
        });
      }
    } catch (error) {
      this.addVulnerability('low', 'RATE_LIMIT_SCAN_ERROR', {
        description: 'Error during rate limiting scan',
        error: error.message
      });
    }
  }

  /**
   * Scan error handling for information disclosure
   */
  async scanErrorHandling() {
    try {
      // Test error responses for information disclosure
      const response = await fetch('/.netlify/functions/non-existent-endpoint');
      const errorText = await response.text();

      if (errorText.includes('stack trace') || errorText.includes('internal error')) {
        this.addVulnerability('medium', 'INFORMATION_DISCLOSURE', {
          description: 'Error responses may disclose sensitive information',
          recommendation: 'Implement generic error messages for production'
        });
      }
    } catch (error) {
      this.addVulnerability('low', 'ERROR_HANDLING_SCAN_ERROR', {
        description: 'Error during error handling scan',
        error: error.message
      });
    }
  }

  /**
   * Helper methods
   */
  testInputSanitization(input) {
    // Import sanitization function if available
    try {
      const { sanitizeInput } = require('./securityUtils');
      return sanitizeInput(input);
    } catch (error) {
      return input; // Return unsanitized if function not available
    }
  }

  testPasswordStrength(password) {
    try {
      const { validatePasswordStrength } = require('./securityUtils');
      return validatePasswordStrength(password);
    } catch (error) {
      return { isValid: true }; // Assume valid if function not available
    }
  }

  checkSessionFixationProtection() {
    // Check if session ID changes after authentication
    // This would require integration with actual session management
    this.addVulnerability('info', 'SESSION_FIXATION_CHECK', {
      description: 'Session fixation protection should be verified manually',
      recommendation: 'Ensure session ID changes after authentication'
    });
  }

  addVulnerability(severity, type, details) {
    const vulnerability = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      severity,
      type,
      timestamp: new Date().toISOString(),
      ...details
    };

    this.vulnerabilities.push(vulnerability);
    this.scanResults[severity].push(vulnerability);
  }

  generateReport() {
    const totalVulnerabilities = this.vulnerabilities.length;
    const criticalCount = this.scanResults.critical.length;
    const highCount = this.scanResults.high.length;
    const mediumCount = this.scanResults.medium.length;
    const lowCount = this.scanResults.low.length;
    const infoCount = this.scanResults.info.length;

    const report = {
      summary: {
        totalVulnerabilities,
        criticalCount,
        highCount,
        mediumCount,
        lowCount,
        infoCount,
        riskScore: this.calculateRiskScore()
      },
      vulnerabilities: this.scanResults,
      recommendations: this.generateRecommendations(),
      scanTimestamp: new Date().toISOString()
    };

    console.log('🔍 Security scan completed:', report.summary);
    return report;
  }

  calculateRiskScore() {
    const weights = { critical: 10, high: 7, medium: 4, low: 2, info: 0 };
    const score = Object.entries(this.scanResults).reduce((total, [severity, vulns]) => {
      return total + (vulns.length * weights[severity]);
    }, 0);

    return Math.min(100, score); // Cap at 100
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.scanResults.critical.length > 0) {
      recommendations.push('🚨 CRITICAL: Address critical vulnerabilities immediately');
    }
    if (this.scanResults.high.length > 0) {
      recommendations.push('⚠️ HIGH: Fix high-severity issues within 24 hours');
    }
    if (this.scanResults.medium.length > 0) {
      recommendations.push('📋 MEDIUM: Address medium-severity issues within a week');
    }

    recommendations.push('🔒 Implement regular security scans');
    recommendations.push('📚 Provide security training for development team');
    recommendations.push('🛡️ Consider implementing a Web Application Firewall (WAF)');

    return recommendations;
  }
}

export default VulnerabilityScanner;
