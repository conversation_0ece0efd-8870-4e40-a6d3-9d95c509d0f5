import { test, expect } from '@playwright/test';

// Test configuration
const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  
  // Check if we need to authenticate
  const needsAuth = await page.locator('input[type="email"]').isVisible();
  
  if (needsAuth) {
    console.log('📝 Filling in credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Verify authentication worked
    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error('Authentication failed');
    }
    
    console.log('✅ Authentication successful');
    return true;
  }
  
  console.log('✅ Already authenticated');
  return true;
}

// Helper function to take screenshot for evidence
async function takeEvidence(page, testName, step) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `evidence-${testName}-${step}-${timestamp}.png`;
  await page.screenshot({ path: `test-results/${filename}`, fullPage: true });
  console.log(`📸 Evidence captured: ${filename}`);
  return filename;
}

test.describe('Comprehensive User Flow Audit', () => {
  let authWorking = false;

  test.beforeEach(async ({ page }) => {
    try {
      authWorking = await authenticate(page);
    } catch (error) {
      console.log('❌ Authentication failed:', error.message);
      authWorking = false;
    }
  });

  test('1. Authentication Flow - Complete Verification', async ({ page }) => {
    // Test logout and re-login
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Look for logout functionality
    const logoutSelectors = [
      'text="Logout"',
      'text="Sign Out"',
      '[data-testid="logout"]',
      'button:has-text("Logout")',
      'a:has-text("Logout")'
    ];
    
    let logoutFound = false;
    for (const selector of logoutSelectors) {
      if (await page.locator(selector).count() > 0) {
        logoutFound = true;
        console.log(`✅ Logout option found: ${selector}`);
        break;
      }
    }
    
    if (!logoutFound) {
      console.log('❌ FAIL: No logout functionality visible');
      await takeEvidence(page, 'auth-flow', 'no-logout');
      expect(logoutFound).toBe(true);
    }
    
    expect(authWorking).toBe(true);
    console.log('✅ PASS: Authentication flow verified');
  });

  test('2. Dashboard - Core Elements Verification', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Check for essential dashboard elements
    const requiredElements = [
      { name: 'Main Navigation', selectors: ['nav', '[role="navigation"]', '.navigation'] },
      { name: 'User Profile/Avatar', selectors: ['[data-testid="user-avatar"]', '.avatar', 'img[alt*="profile"]', 'button:has-text("Profile")'] },
      { name: 'Main Content Area', selectors: ['main', '[role="main"]', '.main-content'] },
      { name: 'Start Button/Link', selectors: ['text="Start"', 'a[href*="start"]', 'button:has-text("Start")'] },
      { name: 'Track Button/Link', selectors: ['text="Track"', 'a[href*="track"]', 'button:has-text("Track")'] },
      { name: 'Earn Button/Link', selectors: ['text="Earn"', 'a[href*="earn"]', 'button:has-text("Earn")'] }
    ];
    
    let failedElements = [];
    
    for (const element of requiredElements) {
      let found = false;
      for (const selector of element.selectors) {
        if (await page.locator(selector).count() > 0) {
          found = true;
          console.log(`✅ ${element.name} found: ${selector}`);
          break;
        }
      }
      
      if (!found) {
        failedElements.push(element.name);
        console.log(`❌ FAIL: ${element.name} not found`);
        await takeEvidence(page, 'dashboard', element.name.toLowerCase().replace(/\s+/g, '-'));
      }
    }
    
    if (failedElements.length > 0) {
      console.log(`❌ FAIL: Missing dashboard elements: ${failedElements.join(', ')}`);
      expect(failedElements.length).toBe(0);
    } else {
      console.log('✅ PASS: All essential dashboard elements present');
    }
  });

  test('3. Navigation Flow - Start Page', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/start`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await takeEvidence(page, 'navigation', 'start-page');
    
    // Check for project creation functionality
    const projectCreationElements = [
      { name: 'Create Project Button', selectors: ['text="Create Project"', 'text="New Project"', 'text="Start Project"', 'button:has-text("Create")'] },
      { name: 'Project Form', selectors: ['form', '[data-testid="project-form"]', 'input[placeholder*="project"]'] },
      { name: 'Project Templates', selectors: ['text="Template"', '.template', '[data-testid="template"]'] },
      { name: 'Getting Started Guide', selectors: ['text="Getting Started"', 'text="How to"', '.guide', '.tutorial'] }
    ];
    
    let foundElements = [];
    let missingElements = [];
    
    for (const element of projectCreationElements) {
      let found = false;
      for (const selector of element.selectors) {
        if (await page.locator(selector).count() > 0) {
          found = true;
          foundElements.push(element.name);
          console.log(`✅ ${element.name} found`);
          break;
        }
      }
      
      if (!found) {
        missingElements.push(element.name);
        console.log(`❌ FAIL: ${element.name} not found`);
      }
    }
    
    if (foundElements.length === 0) {
      console.log('❌ FAIL: No project creation functionality visible on Start page');
      expect(foundElements.length).toBeGreaterThan(0);
    } else {
      console.log(`✅ PASS: Start page has project creation elements: ${foundElements.join(', ')}`);
    }
  });

  test('4. Navigation Flow - Track Page', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/track`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await takeEvidence(page, 'navigation', 'track-page');
    
    // Check for tracking functionality
    const trackingElements = [
      { name: 'Projects List', selectors: ['text="Projects"', '.project', '[data-testid="project"]', 'text="No projects"'] },
      { name: 'Time Tracking', selectors: ['text="Time"', 'text="Hours"', 'input[type="time"]', 'button:has-text("Start")'] },
      { name: 'Task Management', selectors: ['text="Task"', 'text="Mission"', '.task', '[data-testid="task"]'] },
      { name: 'Progress Indicators', selectors: ['text="Progress"', '.progress', 'text="%"', '[role="progressbar"]'] },
      { name: 'Analytics/Charts', selectors: ['svg', 'canvas', 'text="Analytics"', '.chart'] }
    ];
    
    let foundElements = [];
    let missingElements = [];
    
    for (const element of trackingElements) {
      let found = false;
      for (const selector of element.selectors) {
        if (await page.locator(selector).count() > 0) {
          found = true;
          foundElements.push(element.name);
          console.log(`✅ ${element.name} found`);
          break;
        }
      }
      
      if (!found) {
        missingElements.push(element.name);
        console.log(`❌ FAIL: ${element.name} not found`);
      }
    }
    
    if (foundElements.length === 0) {
      console.log('❌ FAIL: No tracking functionality visible on Track page');
      expect(foundElements.length).toBeGreaterThan(0);
    } else {
      console.log(`✅ PASS: Track page has tracking elements: ${foundElements.join(', ')}`);
    }
  });

  test('5. Navigation Flow - Earn Page', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/earn`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await takeEvidence(page, 'navigation', 'earn-page');
    
    // Check for earning/revenue functionality
    const earningElements = [
      { name: 'Revenue Dashboard', selectors: ['text="Revenue"', 'text="Earnings"', 'text="Income"', '.revenue'] },
      { name: 'Payment Information', selectors: ['text="Payment"', 'text="Payout"', 'text="$"', 'text="USD"'] },
      { name: 'Gig Work/Opportunities', selectors: ['text="Gig"', 'text="Opportunity"', 'text="Job"', 'text="Work"'] },
      { name: 'Commission Tracking', selectors: ['text="Commission"', 'text="Royalty"', 'text="Share"', 'text="%"'] },
      { name: 'Financial Analytics', selectors: ['svg', 'canvas', 'text="Analytics"', '.chart'] }
    ];
    
    let foundElements = [];
    let missingElements = [];
    
    for (const element of earningElements) {
      let found = false;
      for (const selector of element.selectors) {
        if (await page.locator(selector).count() > 0) {
          found = true;
          foundElements.push(element.name);
          console.log(`✅ ${element.name} found`);
          break;
        }
      }
      
      if (!found) {
        missingElements.push(element.name);
        console.log(`❌ FAIL: ${element.name} not found`);
      }
    }
    
    if (foundElements.length === 0) {
      console.log('❌ FAIL: No earning functionality visible on Earn page');
      expect(foundElements.length).toBeGreaterThan(0);
    } else {
      console.log(`✅ PASS: Earn page has earning elements: ${foundElements.join(', ')}`);
    }
  });

  test('6. Studio/Team Management Flow', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Test Studios page
    await page.goto(`${PRODUCTION_URL}/studios`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await takeEvidence(page, 'studios', 'studios-page');
    
    const studioElements = [
      { name: 'Create Studio Button', selectors: ['text="Create Studio"', 'text="New Studio"', 'button:has-text("Create")'] },
      { name: 'Studios List', selectors: ['text="Studio"', '.studio', '[data-testid="studio"]', 'text="No studios"'] },
      { name: 'Studio Management', selectors: ['text="Manage"', 'text="Settings"', 'text="Members"'] }
    ];
    
    let studioFunctionality = false;
    
    for (const element of studioElements) {
      for (const selector of element.selectors) {
        if (await page.locator(selector).count() > 0) {
          studioFunctionality = true;
          console.log(`✅ ${element.name} found`);
          break;
        }
      }
    }
    
    if (!studioFunctionality) {
      console.log('❌ FAIL: No studio functionality visible');
      expect(studioFunctionality).toBe(true);
    } else {
      console.log('✅ PASS: Studio functionality present');
    }
  });

  test('7. Project Creation Flow - End-to-End', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/start`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Look for project creation entry point
    const createButtons = [
      'text="Create Project"',
      'text="New Project"', 
      'text="Start Project"',
      'button:has-text("Create")',
      'a[href*="project"]'
    ];
    
    let createButtonFound = false;
    for (const selector of createButtons) {
      if (await page.locator(selector).count() > 0) {
        createButtonFound = true;
        console.log(`✅ Create project button found: ${selector}`);
        
        try {
          await page.click(selector);
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(2000);
          
          await takeEvidence(page, 'project-creation', 'after-click');
          
          // Check if we're on a project creation form
          const formElements = [
            'input[placeholder*="name"]',
            'input[placeholder*="title"]',
            'textarea',
            'form',
            'text="Project Name"',
            'text="Description"'
          ];
          
          let formFound = false;
          for (const formSelector of formElements) {
            if (await page.locator(formSelector).count() > 0) {
              formFound = true;
              console.log(`✅ Project creation form found: ${formSelector}`);
              break;
            }
          }
          
          if (!formFound) {
            console.log('❌ FAIL: No project creation form after clicking create button');
            expect(formFound).toBe(true);
          }
          
        } catch (error) {
          console.log(`❌ FAIL: Error clicking create button: ${error.message}`);
          await takeEvidence(page, 'project-creation', 'click-error');
          expect(false).toBe(true);
        }
        break;
      }
    }
    
    if (!createButtonFound) {
      console.log('❌ FAIL: No project creation button found');
      await takeEvidence(page, 'project-creation', 'no-button');
      expect(createButtonFound).toBe(true);
    }
  });

  test('8. User Profile and Settings Access', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Look for profile/settings access
    const profileElements = [
      'text="Profile"',
      'text="Settings"',
      'text="Account"',
      '[data-testid="user-menu"]',
      '.avatar',
      'img[alt*="profile"]'
    ];
    
    let profileAccess = false;
    for (const selector of profileElements) {
      if (await page.locator(selector).count() > 0) {
        profileAccess = true;
        console.log(`✅ Profile access found: ${selector}`);
        
        try {
          await page.click(selector);
          await page.waitForTimeout(1000);
          
          // Check if profile menu or page opened
          const profileMenuElements = [
            'text="Settings"',
            'text="Profile"',
            'text="Logout"',
            'text="Account"'
          ];
          
          let menuOpened = false;
          for (const menuSelector of profileMenuElements) {
            if (await page.locator(menuSelector).count() > 0) {
              menuOpened = true;
              console.log(`✅ Profile menu opened: ${menuSelector}`);
              break;
            }
          }
          
          if (!menuOpened) {
            console.log('⚠️ Profile element clicked but no menu/page opened');
          }
          
        } catch (error) {
          console.log(`⚠️ Could not click profile element: ${error.message}`);
        }
        break;
      }
    }
    
    if (!profileAccess) {
      console.log('❌ FAIL: No profile/settings access found');
      await takeEvidence(page, 'profile', 'no-access');
      expect(profileAccess).toBe(true);
    } else {
      console.log('✅ PASS: Profile access available');
    }
  });

  test('9. Mobile Responsiveness Verification', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await takeEvidence(page, 'mobile', 'main-view');
    
    // Check if navigation is accessible on mobile
    const mobileNavElements = [
      'button[aria-label*="menu"]',
      'button[aria-label*="Menu"]',
      '.hamburger',
      '[data-testid="mobile-menu"]',
      'text="☰"',
      'svg[data-testid="menu"]'
    ];
    
    let mobileNavFound = false;
    for (const selector of mobileNavElements) {
      if (await page.locator(selector).count() > 0) {
        mobileNavFound = true;
        console.log(`✅ Mobile navigation found: ${selector}`);
        break;
      }
    }
    
    // Check if main navigation buttons are visible
    const mainNavButtons = ['text="Start"', 'text="Track"', 'text="Earn"'];
    let visibleNavButtons = 0;
    
    for (const button of mainNavButtons) {
      const isVisible = await page.locator(button).isVisible();
      if (isVisible) {
        visibleNavButtons++;
        console.log(`✅ Mobile nav button visible: ${button}`);
      } else {
        console.log(`❌ Mobile nav button not visible: ${button}`);
      }
    }
    
    if (!mobileNavFound && visibleNavButtons === 0) {
      console.log('❌ FAIL: No mobile navigation found');
      await takeEvidence(page, 'mobile', 'no-navigation');
      expect(mobileNavFound || visibleNavButtons > 0).toBe(true);
    } else {
      console.log('✅ PASS: Mobile navigation available');
    }
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('10. Data Persistence and State Management', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Test if user state persists across page refreshes
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Check if user is still authenticated after refresh
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Verify we're still authenticated (not seeing login form)
    const loginForm = await page.locator('input[type="email"]').isVisible();
    
    if (loginForm) {
      console.log('❌ FAIL: User session not persisted after page refresh');
      await takeEvidence(page, 'persistence', 'session-lost');
      expect(loginForm).toBe(false);
    } else {
      console.log('✅ PASS: User session persisted after page refresh');
    }
  });
});
