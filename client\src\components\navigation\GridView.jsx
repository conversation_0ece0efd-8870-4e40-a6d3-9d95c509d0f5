import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/react';
import { useNavigation } from '../../contexts/NavigationContext';

/**
 * Grid View Component
 *
 * Displays navigation canvases in a beautiful bento grid layout.
 * Optimized for quick selection and overview of all available areas.
 */
const GridView = ({ canvases, currentCanvas, onNavigate, onDragStart, onCardHover, onCardHoverEnd }) => {
  const { hiddenCanvases, favoriteCanvases } = useNavigation();
  const [clickStartPos, setClickStartPos] = React.useState(null);
  const [clickThreshold] = React.useState(5); // pixels
  const [hoveredCard, setHoveredCard] = React.useState(null);
  const [showContextMenu, setShowContextMenu] = React.useState(null);
  const [hoverTimeout, setHoverTimeout] = React.useState(null);

  // Filter out hidden canvases
  const visibleCanvases = React.useMemo(() => {
    return Object.values(canvases).filter(canvas => !hiddenCanvases.includes(canvas.id));
  }, [canvases, hiddenCanvases]);

  // Handle mouse down on canvas card
  const handleCardMouseDown = (e, canvasId) => {
    // Prevent event bubbling to background
    e.stopPropagation();
    setClickStartPos({ x: e.clientX, y: e.clientY, canvasId });
  };

  // Handle mouse up on canvas card
  const handleCardMouseUp = (e, canvasId) => {
    if (!clickStartPos || clickStartPos.canvasId !== canvasId) return;

    const distance = Math.sqrt(
      Math.pow(e.clientX - clickStartPos.x, 2) +
      Math.pow(e.clientY - clickStartPos.y, 2)
    );

    // If movement is below threshold, treat as click
    if (distance < clickThreshold) {
      onNavigate(canvasId);
    }

    setClickStartPos(null);
  };

  // Handle mouse enter on canvas card
  const handleCardMouseEnter = (canvasId) => {
    setHoveredCard(canvasId);

    // Notify parent component for debug UI
    if (onCardHover) {
      onCardHover(canvasId);
    }

    // Clear any existing timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
    }

    // Set timeout for context menu
    const timeout = setTimeout(() => {
      setShowContextMenu(canvasId);
    }, 1000); // Show context menu after 1 second

    setHoverTimeout(timeout);
  };

  // Handle mouse leave on canvas card
  const handleCardMouseLeave = () => {
    setHoveredCard(null);

    // Notify parent component for debug UI
    if (onCardHoverEnd) {
      onCardHoverEnd();
    }

    // Clear timeout
    if (hoverTimeout) {
      clearTimeout(hoverTimeout);
      setHoverTimeout(null);
    }

    // Hide context menu after a short delay
    setTimeout(() => {
      setShowContextMenu(null);
    }, 200);
  };

  // Define grid layout configuration based on Start-Track-Earn journey
  const gridLayout = {
    // Row 1: Secondary tools - Learn, Profile, Settings, Admin (if available)
    row1: ['learn', 'profile', 'settings', ...(canvases.admin ? ['admin'] : [])],
    // Row 2: Primary journey - Start, Home (large), Track
    row2: ['start', 'home', 'track'],
    // Row 3: Earn (spans remaining width) - Revenue focus
    row3: ['earn']
  };

  // Get grid item size classes
  const getGridClasses = (canvasId) => {
    if (canvasId === 'home') {
      return 'col-span-2 row-span-2'; // Large center piece
    }
    if (canvasId === 'earn') {
      return 'col-span-4'; // Full width bottom
    }
    return 'col-span-1'; // Standard size
  };

  // Get canvas priority for animation delay - emphasize Start-Track-Earn journey
  const getAnimationDelay = (canvasId) => {
    const priorities = {
      // Primary journey - animate first
      home: 0,    // Central hub
      start: 1,   // Begin journey
      track: 2,   // Track work
      earn: 3,    // Earn rewards

      // Secondary tools - animate after
      profile: 4,
      learn: 5,
      settings: 6,
      admin: 7
    };
    return priorities[canvasId] || 8; // Default for any other canvases
  };

  // Render individual canvas card
  const renderCanvasCard = (canvas, index) => {
    const gridClasses = getGridClasses(canvas.id);
    const isLarge = canvas.id === 'home';
    const isWide = canvas.id === 'earn';
    const isHovered = hoveredCard === canvas.id;
    const hasContextMenu = showContextMenu === canvas.id;
    const isActive = currentCanvas === canvas.id;
    const isFavorite = favoriteCanvases.includes(canvas.id);

    return (
      <motion.div
        key={canvas.id}
        className={`${gridClasses} relative`}
        initial={{ opacity: 0, scale: 0.8, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{
          delay: getAnimationDelay(canvas.id) * 0.1,
          duration: 0.4,
          type: "spring",
          stiffness: 100
        }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Card
          data-canvas-card
          className={`
            h-full cursor-pointer transition-all duration-300 group relative
            ${isHovered
              ? 'ring-2 ring-white/50 shadow-xl shadow-white/20'
              : 'hover:ring-1 hover:ring-white/30 hover:shadow-lg'
            }
          `}
          onMouseDown={(e) => handleCardMouseDown(e, canvas.id)}
          onMouseUp={(e) => handleCardMouseUp(e, canvas.id)}
          onMouseEnter={() => handleCardMouseEnter(canvas.id)}
          onMouseLeave={handleCardMouseLeave}
        >
          <CardBody className={`
            p-0 relative overflow-hidden bg-gradient-to-br ${canvas.color}
            flex flex-col items-center justify-center text-white
            ${isLarge ? 'min-h-[200px]' : isWide ? 'min-h-[120px]' : 'min-h-[140px]'}
          `}>
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-20">
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(255,255,255,0.2),transparent_60%)]" />
              <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_30%,rgba(255,255,255,0.05)_30%,rgba(255,255,255,0.05)_70%,transparent_70%)] bg-[length:30px_30px]" />
            </div>

            {/* Hover Effect */}
            <div className="absolute inset-0 bg-white/0 group-hover:bg-white/10 transition-all duration-300" />

            {/* Content */}
            <div className="relative z-10 text-center p-4">
              <motion.div
                className={`${isLarge ? 'text-6xl mb-4' : isWide ? 'text-4xl mb-3' : 'text-3xl mb-2'}`}
                animate={isActive ? {
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                } : {}}
                transition={{ duration: 2, repeat: Infinity }}
              >
                {canvas.icon}
              </motion.div>

              <h3 className={`font-bold ${isLarge ? 'text-2xl mb-2' : isWide ? 'text-xl mb-1' : 'text-lg mb-1'}`}>
                {canvas.title}
              </h3>

              <p className={`opacity-90 ${isLarge ? 'text-base px-4' : 'text-sm px-2'}`}>
                {canvas.description}
              </p>

              {/* Additional info for large cards */}
              {isLarge && (
                <motion.div
                  className="mt-4 text-sm opacity-75"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.75 }}
                  transition={{ delay: 0.5 }}
                >
                  Your central command center
                </motion.div>
              )}
            </div>

            {/* Active Indicator */}
            {isActive && (
              <>
                <motion.div
                  className="absolute inset-0 border-2 border-white/50 rounded-lg"
                  animate={{ opacity: [0.3, 0.7, 0.3] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
                <div className="absolute top-3 right-3">
                  <motion.div
                    className="w-3 h-3 bg-white rounded-full"
                    animate={{ scale: [1, 1.5, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </div>
              </>
            )}

            {/* Connection Count Badge */}
            <div className="absolute bottom-3 left-3">
              <div className="bg-white/20 backdrop-blur-sm rounded-full px-2 py-1 text-xs">
                {canvas.connections.length} links
              </div>
            </div>

            {/* Favorite Star */}
            {isFavorite && (
              <div className="absolute top-3 left-3">
                <motion.div
                  className="text-yellow-400 text-lg"
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  ⭐
                </motion.div>
              </div>
            )}

            {/* Quick Action Hint */}
            <motion.div
              className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity"
              initial={{ scale: 0 }}
              whileHover={{ scale: 1 }}
            >
              <div className="bg-white/20 backdrop-blur-sm rounded-full p-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </motion.div>

            {/* Context Menu */}
            {hasContextMenu && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-black/80 backdrop-blur-md rounded-lg border border-white/20 p-2 min-w-[120px] z-50"
              >
                <div className="space-y-1">
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white/10 rounded transition-colors"
                    onClick={() => onNavigate(canvas.id)}
                  >
                    🚀 Navigate
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white/10 rounded transition-colors"
                    onClick={() => {
                      // Implement canvas edit functionality
                      const newTitle = prompt('Enter new canvas title:', canvas.title);
                      if (newTitle && newTitle.trim()) {
                        // Update canvas title
                        onCanvasUpdate?.(canvas.id, { title: newTitle.trim() });
                        setActiveTooltip(null); // Close tooltip
                      }
                    }}
                  >
                    ✏️ Edit
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-white hover:bg-white/10 rounded transition-colors"
                    onClick={() => {
                      // Show canvas information modal
                      const canvasInfo = {
                        title: canvas.title,
                        description: canvas.description || 'No description available',
                        created: canvas.created_at ? new Date(canvas.created_at).toLocaleDateString() : 'Unknown',
                        lastModified: canvas.updated_at ? new Date(canvas.updated_at).toLocaleDateString() : 'Unknown',
                        components: canvas.components?.length || 0,
                        category: canvas.category || 'General'
                      };

                      alert(`Canvas Information:\n\nTitle: ${canvasInfo.title}\nDescription: ${canvasInfo.description}\nCreated: ${canvasInfo.created}\nLast Modified: ${canvasInfo.lastModified}\nComponents: ${canvasInfo.components}\nCategory: ${canvasInfo.category}`);
                      setActiveTooltip(null); // Close tooltip
                    }}
                  >
                    ℹ️ Info
                  </button>
                </div>

                {/* Arrow pointing up */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-black/80"></div>
              </motion.div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    );
  };

  return (
    <motion.div
      className="relative w-full h-full flex items-center justify-center p-8"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.3 }}
    >


      {/* Background Glow */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl pointer-events-none" />

      {/* Grid Container */}
      <div className="relative max-w-6xl w-full" style={{ zIndex: 10 }}>
        {/* Grid Layout */}
        <div className="grid grid-cols-4 gap-4 auto-rows-fr">
          {visibleCanvases.map((canvas, index) =>
            renderCanvasCard(canvas, index)
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default GridView;
