import React, { useState, useEffect } from 'react';
import { getUserSkills, getVerificationLevelName, getVerificationLevelIcon, calculateSkillScore } from '../../utils/skills/skills.utils';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Chip as Badge,
  Progress,
  Accordion,
  AccordionItem
} from '../ui/heroui';

const UserSkillsList = ({ userId, isOwnProfile = false, onAddSkill }) => {
  const [loading, setLoading] = useState(true);
  const [userSkills, setUserSkills] = useState([]);
  const [expandedSkill, setExpandedSkill] = useState(null);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const loadUserSkills = async () => {
      try {
        setLoading(true);
        const skills = await getUserSkills(userId);
        setUserSkills(skills);
      } catch (error) {
        console.error('Error loading user skills:', error);
      } finally {
        setLoading(false);
      }
    };

    loadUserSkills();
  }, [userId]);

  const toggleSkillDetails = (skillId) => {
    setExpandedSkill(expandedSkill === skillId ? null : skillId);
  };

  const getSkillBadgeVariant = (verificationLevel) => {
    const variants = {
      0: 'secondary',
      1: 'info',
      2: 'default',
      3: 'success',
      4: 'warning',
      5: 'danger'
    };

    return variants[verificationLevel] || 'secondary';
  };

  const filterSkills = () => {
    let filteredSkills = [...userSkills];

    // Apply category filter
    if (filter !== 'all') {
      filteredSkills = filteredSkills.filter(userSkill =>
        userSkill.skill.category === filter
      );
    }

    // Apply search term
    if (searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase();
      filteredSkills = filteredSkills.filter(userSkill =>
        userSkill.skill.name.toLowerCase().includes(term) ||
        userSkill.skill.area.toLowerCase().includes(term) ||
        userSkill.skill.category.toLowerCase().includes(term) ||
        (userSkill.skill.micro_skill && userSkill.skill.micro_skill.toLowerCase().includes(term))
      );
    }

    return filteredSkills;
  };

  const getUniqueCategories = () => {
    const categories = new Set();
    userSkills.forEach(userSkill => {
      categories.add(userSkill.skill.category);
    });
    return Array.from(categories);
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" role="status">
          <span className="sr-only">Loading skills...</span>
        </div>
        <p className="mt-3 text-muted-foreground">Loading skills...</p>
      </div>
    );
  }

  const filteredSkills = filterSkills();
  const categories = getUniqueCategories();

  return (
    <div className="user-skills-list space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center">
          <i className="bi bi-stars mr-2"></i> Skills
        </h2>
        {isOwnProfile && (
          <Button variant="outline" size="sm" onClick={onAddSkill}>
            <i className="bi bi-plus-circle mr-2"></i> Add Skill
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Input
          type="text"
          placeholder="Search skills..."
          value={searchTerm}
          onValueChange={setSearchTerm}
          startContent={<i className="bi bi-search text-muted-foreground"></i>}
        />
        <Select
          selectedKeys={filter ? [filter] : []}
          onSelectionChange={(keys) => setFilter(Array.from(keys)[0])}
          placeholder="All Categories"
        >
          <SelectItem key="all" value="all">All Categories</SelectItem>
          {categories.map(category => (
            <SelectItem key={category} value={category}>{category}</SelectItem>
          ))}
        </Select>
      </div>

      {filteredSkills.length === 0 ? (
        <div className="text-center py-12 space-y-4">
          <i className="bi bi-mortarboard-fill text-6xl text-muted-foreground"></i>
          <p className="text-muted-foreground">No skills found{filter !== 'all' ? ` in ${filter}` : ''}.</p>
          {isOwnProfile && (
            <Button variant="outline" size="sm" onClick={onAddSkill}>
              Add Your First Skill
            </Button>
          )}
        </div>
      ) : (
        <Accordion className="space-y-3">
          {filteredSkills.map((userSkill) => {
            const skill = userSkill.skill;
            const skillScore = calculateSkillScore(userSkill);
            const verificationLevel = userSkill.verification_level;

            return (
              <AccordionItem
                key={userSkill.id}
                aria-label={skill.name}
                title={
                  <div className="flex justify-between items-center w-full pr-4">
                    <div className="text-left">
                      <h3 className="font-medium text-base">
                        {skill.name}
                        {skill.micro_skill && (
                          <span className="text-muted-foreground"> - {skill.micro_skill}</span>
                        )}
                      </h3>
                      <div className="text-sm text-muted-foreground">
                        <span>{skill.category}</span>
                        <span className="mx-2">•</span>
                        <span>{skill.area}</span>
                      </div>
                    </div>
                    <Badge color={getSkillBadgeVariant(verificationLevel)}>
                      <i className={`${getVerificationLevelIcon(verificationLevel)} mr-1`}></i>
                      {getVerificationLevelName(verificationLevel)}
                    </Badge>
                  </div>
                }
              >
                  <div className="space-y-4 pt-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Skill Proficiency</span>
                        <span className="text-sm font-medium">{skillScore}%</span>
                      </div>
                      <Progress
                        value={skillScore}
                        className="h-2"
                      />
                    </div>

                    {userSkill.verifications && userSkill.verifications.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="font-medium">Verifications</h4>
                        <div className="space-y-2">
                          {userSkill.verifications.map(verification => (
                            <div key={verification.id} className="p-3 border rounded-md space-y-1">
                              <div className="flex items-center text-sm font-medium">
                                <i className="bi bi-check-circle-fill text-green-500 mr-2"></i>
                                {verification.verification_type.replace('_', ' ')}
                              </div>
                              {verification.verification_source && (
                                <div className="text-sm text-muted-foreground">
                                  Source: {verification.verification_source}
                                </div>
                              )}
                              <div className="text-xs text-muted-foreground">
                                Verified on {new Date(verification.verified_at).toLocaleDateString()}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {isOwnProfile && (
                      <div className="flex gap-2 pt-2">
                        <Button variant="outline" size="sm">
                          <i className="bi bi-pencil-fill mr-2"></i> Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <i className="bi bi-plus-circle-fill mr-2"></i> Add Verification
                        </Button>
                        <Button variant="outline" size="sm">
                          <i className="bi bi-trash-fill mr-2"></i> Remove
                        </Button>
                      </div>
                    )}
                  </div>
              </AccordionItem>
            );
          })}
        </Accordion>
      )}
    </div>
  );
};

export default UserSkillsList;
