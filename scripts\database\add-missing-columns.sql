-- Add missing columns that the Track page application expects
-- Based on the 400 errors, we need to add these columns

-- ============================================================================
-- ADD MISSING COLUMNS TO PROJECTS TABLE
-- ============================================================================

-- Add created_by column to projects (the app filters by this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'created_by'
    ) THEN
        ALTER TABLE public.projects ADD COLUMN created_by UUID;
        -- Set existing projects to have a default user (you can update this later)
        UPDATE public.projects SET created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b' WHERE created_by IS NULL;
    END IF;
END $$;

-- Add status column to projects (the app filters by this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'projects' 
        AND column_name = 'status'
    ) THEN
        ALTER TABLE public.projects ADD COLUMN status TEXT DEFAULT 'active';
        -- Set existing projects to active status
        UPDATE public.projects SET status = 'active' WHERE status IS NULL;
    END IF;
END $$;

-- ============================================================================
-- ADD MISSING COLUMNS TO ACTIVE_TIMERS TABLE
-- ============================================================================

-- Add is_active column to active_timers (the app filters by this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'active_timers' 
        AND column_name = 'is_active'
    ) THEN
        ALTER TABLE public.active_timers ADD COLUMN is_active BOOLEAN DEFAULT true;
        -- Set existing timers to active
        UPDATE public.active_timers SET is_active = true WHERE is_active IS NULL;
    END IF;
END $$;

-- ============================================================================
-- CREATE MISSING CONTRIBUTION_TRACKING_CONFIG TABLE
-- ============================================================================

-- The app is trying to query this table but it doesn't exist
CREATE TABLE IF NOT EXISTS public.contribution_tracking_config (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    tracking_enabled BOOLEAN DEFAULT true,
    point_system JSONB DEFAULT '{"base_points": 1, "difficulty_multiplier": {"easy": 1, "medium": 2, "hard": 3}}',
    reward_structure JSONB DEFAULT '{"type": "equal_split"}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS and create policies
ALTER TABLE public.contribution_tracking_config ENABLE ROW LEVEL SECURITY;

-- Simple permissive policy matching your existing setup
CREATE POLICY "Users can view contribution config" 
ON public.contribution_tracking_config FOR SELECT 
USING (true);

CREATE POLICY "Users can manage contribution config" 
ON public.contribution_tracking_config FOR ALL 
USING (true)
WITH CHECK (true);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.contribution_tracking_config TO authenticated;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_contribution_tracking_config_project_id 
ON public.contribution_tracking_config(project_id);

-- ============================================================================
-- ADD MISSING COLUMNS TO PROJECT_CONTRIBUTORS TABLE
-- ============================================================================

-- Add role column if it doesn't exist (the app queries this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'project_contributors' 
        AND column_name = 'role'
    ) THEN
        ALTER TABLE public.project_contributors ADD COLUMN role TEXT DEFAULT 'contributor';
        -- Set existing contributors to default role
        UPDATE public.project_contributors SET role = 'contributor' WHERE role IS NULL;
    END IF;
END $$;

-- Add is_admin column if it doesn't exist (the app queries this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'project_contributors' 
        AND column_name = 'is_admin'
    ) THEN
        ALTER TABLE public.project_contributors ADD COLUMN is_admin BOOLEAN DEFAULT false;
        -- Set existing contributors to non-admin
        UPDATE public.project_contributors SET is_admin = false WHERE is_admin IS NULL;
    END IF;
END $$;

-- ============================================================================
-- ADD MISSING COLUMNS TO TASKS TABLE
-- ============================================================================

-- Add created_by column to tasks if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'tasks' 
        AND column_name = 'created_by'
    ) THEN
        ALTER TABLE public.tasks ADD COLUMN created_by UUID;
        -- Set existing tasks to have a default creator
        UPDATE public.tasks SET created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b' WHERE created_by IS NULL;
    END IF;
END $$;

COMMIT;
