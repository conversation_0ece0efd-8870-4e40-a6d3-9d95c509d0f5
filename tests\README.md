# Testing Files

This directory contains all testing-related files for the Royaltea platform.

## 📋 Test Categories

### **Root Level Tests**
- `test-*.js` - Various testing scripts
- `verify-*.js` - Verification and validation scripts

### **Legacy Tests**
- `legacy/tests-examples/` - Example test files and demos

### **E2E Tests**
- `e2e/` - End-to-end testing with Playwright
  - Authentication tests
  - Navigation tests
  - Component loading tests
  - Alliance management tests

## 🧪 Test Types

### **Unit Tests**
- Component testing
- Utility function testing
- Service layer testing

### **Integration Tests**
- API endpoint testing
- Database integration testing
- Authentication flow testing

### **End-to-End Tests**
- User journey testing
- Cross-browser testing
- Performance testing

## 🚀 Running Tests

### **E2E Tests with <PERSON><PERSON>**
```bash
# Run all E2E tests
npx playwright test

# Run specific test file
npx playwright test e2e/navigation-cards-test.spec.js

# Run tests in headed mode
npx playwright test --headed
```

### **Custom Test Scripts**
```bash
# Run verification scripts
node tests/verify-agreements.js
node tests/verify-roadmap-update.js

# Run test data scripts
node tests/test-agreement-generator.js
node tests/test-payment-apis.js
```

## 📊 Test Configuration

### **Playwright Configuration**
- Configuration file: `../playwright.config.js`
- Test directory: `e2e/`
- Browsers: Chromium, Firefox, WebKit

### **Test Data**
- Test users and authentication
- Mock data for development
- Fixture files for consistent testing

## 🔧 Test Environment Setup

### **Prerequisites**
```bash
# Install Playwright
npm install @playwright/test

# Install browsers
npx playwright install
```

### **Environment Variables**
```bash
# Testing environment
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=[test-anon-key]
```

## 📚 Test Documentation

### **Writing Tests**
- Follow Playwright best practices
- Use page object model for complex flows
- Include proper assertions and error handling
- Document test scenarios and expected outcomes

### **Test Maintenance**
- Update tests when features change
- Remove obsolete tests
- Keep test data current
- Monitor test performance

## ⚠️ Important Notes

- Tests should be independent and idempotent
- Use test-specific data, not production data
- Clean up test data after test runs
- Ensure tests work in CI/CD environment

## 📚 Related Documentation

- [Testing Strategy](../docs/testing-strategy-specification.md)
- [Playwright Documentation](https://playwright.dev/)
- [Development Workflow](../docs/development-workflow.md)
