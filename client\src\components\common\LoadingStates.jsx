import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CardBody } from '@heroui/react';
import { RefreshCw, AlertCircle, Wifi, WifiOff } from 'lucide-react';

/**
 * Enhanced Loading States Component
 * 
 * Provides comprehensive loading states for all async operations:
 * - Button loading states
 * - Form loading states  
 * - Page loading states
 * - Error states with retry
 * - Network status indicators
 */

// Enhanced Loading Button
export const LoadingButton = ({ 
  isLoading = false, 
  children, 
  loadingText = "Loading...",
  disabled = false,
  variant = "solid",
  color = "primary",
  size = "md",
  startContent = null,
  endContent = null,
  onClick,
  className = "",
  ...props 
}) => {
  return (
    <Button
      variant={variant}
      color={color}
      size={size}
      disabled={disabled || isLoading}
      onClick={onClick}
      className={`${className} ${isLoading ? 'cursor-not-allowed' : ''}`}
      startContent={isLoading ? <Spinner size="sm" color="current" /> : startContent}
      endContent={!isLoading ? endContent : null}
      {...props}
    >
      {isLoading ? loadingText : children}
    </Button>
  );
};

// Form Loading Overlay
export const FormLoadingOverlay = ({ 
  isLoading = false, 
  message = "Processing...", 
  children 
}) => {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-white/80 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-10 rounded-lg"
        >
          <div className="flex flex-col items-center gap-3">
            <Spinner size="lg" color="primary" />
            <p className="text-sm text-default-600">{message}</p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

// Enhanced Error State
export const ErrorState = ({ 
  error, 
  onRetry, 
  title = "Something went wrong",
  description,
  showRetry = true,
  retryText = "Try Again",
  className = ""
}) => {
  const errorMessage = error?.message || description || "An unexpected error occurred";
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`flex flex-col items-center justify-center p-8 text-center ${className}`}
    >
      <div className="text-danger mb-4">
        <AlertCircle size={48} />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
      <p className="text-default-600 mb-6 max-w-md">{errorMessage}</p>
      {showRetry && onRetry && (
        <LoadingButton
          onClick={onRetry}
          variant="bordered"
          color="danger"
          startContent={<RefreshCw size={16} />}
        >
          {retryText}
        </LoadingButton>
      )}
    </motion.div>
  );
};

// Network Status Indicator
export const NetworkStatus = () => {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine);
  
  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  if (isOnline) return null;
  
  return (
    <motion.div
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className="fixed top-0 left-0 right-0 bg-danger text-white p-2 text-center z-50"
    >
      <div className="flex items-center justify-center gap-2">
        <WifiOff size={16} />
        <span className="text-sm">You're offline. Some features may not work.</span>
      </div>
    </motion.div>
  );
};

// Loading Progress Bar
export const LoadingProgress = ({ 
  progress = 0, 
  isIndeterminate = false,
  className = "",
  color = "primary" 
}) => {
  return (
    <div className={`w-full bg-default-200 rounded-full h-2 overflow-hidden ${className}`}>
      <motion.div
        className={`h-full bg-${color} rounded-full`}
        initial={{ width: 0 }}
        animate={{ 
          width: isIndeterminate ? "100%" : `${progress}%`,
          x: isIndeterminate ? ["-100%", "100%"] : 0
        }}
        transition={
          isIndeterminate 
            ? { repeat: Infinity, duration: 1.5, ease: "easeInOut" }
            : { duration: 0.3 }
        }
      />
    </div>
  );
};

// Page Loading State
export const PageLoadingState = ({
  message = "Loading page...",
  showProgress = false,
  progress = 0
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <Spinner size="lg" color="primary" className="mb-4" />
        <p className="text-lg text-foreground mb-2">{message}</p>
        {showProgress && (
          <div className="w-64 mx-auto">
            <LoadingProgress progress={progress} />
            <p className="text-sm text-default-600 mt-2">{progress}% complete</p>
          </div>
        )}
      </motion.div>
    </div>
  );
};

// Data Loading State
export const DataLoadingState = ({
  message = "Loading data...",
  size = "md",
  className = ""
}) => {
  const sizeClasses = {
    sm: "p-4",
    md: "p-8",
    lg: "p-12"
  };

  return (
    <div className={`flex flex-col items-center justify-center ${sizeClasses[size]} ${className}`}>
      <Spinner size={size} color="primary" className="mb-3" />
      <p className="text-default-600">{message}</p>
    </div>
  );
};

// Inline Loading State
export const InlineLoadingState = ({
  message = "Loading...",
  size = "sm"
}) => {
  return (
    <div className="flex items-center gap-2">
      <Spinner size={size} color="primary" />
      <span className="text-sm text-default-600">{message}</span>
    </div>
  );
};

// Loading State Manager Hook
export const useLoadingState = (initialState = false) => {
  const [isLoading, setIsLoading] = React.useState(initialState);
  const [error, setError] = React.useState(null);

  const startLoading = React.useCallback(() => {
    setIsLoading(true);
    setError(null);
  }, []);

  const stopLoading = React.useCallback(() => {
    setIsLoading(false);
  }, []);

  const setLoadingError = React.useCallback((error) => {
    setIsLoading(false);
    setError(error);
  }, []);

  const reset = React.useCallback(() => {
    setIsLoading(false);
    setError(null);
  }, []);

  return {
    isLoading,
    error,
    startLoading,
    stopLoading,
    setLoadingError,
    reset
  };
};

export default {
  LoadingButton,
  FormLoadingOverlay,
  ErrorState,
  NetworkStatus,
  LoadingProgress,
  PageLoadingState,
  DataLoadingState,
  InlineLoadingState,
  useLoadingState
};
