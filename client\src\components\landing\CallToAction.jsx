import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@heroui/react';

/**
 * Call to Action Component - Ready to Keep the Tea?
 * 
 * Features:
 * - Final conversion section with compelling messaging
 * - Large, prominent call-to-action button
 * - Trust indicators and reassurance
 * - Animated background elements
 */
const CallToAction = ({ onGetStarted }) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 1,
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, ease: "easeOut" }
    }
  };

  const floatingVariants = {
    animate: {
      y: [0, -15, 0],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Large Background Shapes */}
        <motion.div
          className="absolute top-10 left-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute bottom-10 right-10 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.3, 0.5, 0.3]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        
        {/* Floating Particles */}
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-white/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -200, 0],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 4 + Math.random() * 3,
              repeat: Infinity,
              delay: Math.random() * 3,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        className="relative z-10 text-center px-6 max-w-5xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
      >
        {/* Main Headline */}
        <motion.div variants={itemVariants} className="mb-8">
          <motion.h2 
            className="text-6xl md:text-8xl font-bold text-white mb-6"
            variants={floatingVariants}
            animate="animate"
          >
            🚀 Ready to Keep the Tea?
          </motion.h2>
        </motion.div>

        {/* Subheadline */}
        <motion.div variants={itemVariants} className="mb-12">
          <p className="text-2xl md:text-3xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Join thousands of creators building fair, transparent creative businesses
          </p>
        </motion.div>

        {/* Primary CTA Button */}
        <motion.div variants={itemVariants} className="mb-12">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold px-16 py-8 text-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 rounded-2xl"
              onPress={onGetStarted}
            >
              Get Started Free
            </Button>
          </motion.div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div variants={itemVariants} className="mb-16">
          <div className="flex items-center justify-center gap-2 text-lg text-gray-300 mb-4">
            <span className="text-2xl">✨</span>
            <span>No credit card required • 5-minute setup</span>
          </div>
        </motion.div>

        {/* Social Proof Stats */}
        <motion.div variants={itemVariants} className="mb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <motion.div 
              className="text-center p-6 bg-white/5 rounded-xl backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">2,500+</div>
              <div className="text-gray-300">Active Creators</div>
            </motion.div>
            
            <motion.div 
              className="text-center p-6 bg-white/5 rounded-xl backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">$2.3M+</div>
              <div className="text-gray-300">Revenue Distributed</div>
            </motion.div>
            
            <motion.div 
              className="text-center p-6 bg-white/5 rounded-xl backdrop-blur-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">94%</div>
              <div className="text-gray-300">Satisfaction Rate</div>
            </motion.div>
          </div>
        </motion.div>

        {/* Final Reassurance */}
        <motion.div variants={itemVariants}>
          <div className="max-w-3xl mx-auto">
            <p className="text-xl text-gray-400 mb-8">
              Start building your fair, transparent creative business today. 
              Join the revolution that's changing how creative professionals collaborate and earn.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-gray-400">
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>Free forever plan</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>No setup fees</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-green-400">✓</span>
                <span>Cancel anytime</span>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/20 pointer-events-none" />
    </div>
  );
};

export default CallToAction;
