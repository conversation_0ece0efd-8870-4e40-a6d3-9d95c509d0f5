import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Tabs, Tab, Button, Select, SelectItem, Badge } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';

// Import enhanced analytics components
import RealTimeAnalyticsDashboard from './RealTimeAnalyticsDashboard';
import AutomatedReportScheduler from './AutomatedReportScheduler';
import PredictiveInsights from './PredictiveInsights';
import AnalyticsDashboard from './AnalyticsDashboard';
import AnalyticsDataService from '../../services/AnalyticsDataService';

/**
 * Enhanced Analytics Dashboard
 * 
 * Comprehensive analytics platform featuring:
 * - Real-time data monitoring
 * - Automated report scheduling
 * - Predictive analytics and forecasting
 * - Advanced data export capabilities
 * - Mobile-optimized experience
 */
const EnhancedAnalyticsDashboard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [isExporting, setIsExporting] = useState(false);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Tab configuration
  const tabs = [
    {
      key: 'overview',
      title: 'Overview',
      icon: '📊',
      description: 'Comprehensive analytics overview',
      component: AnalyticsDashboard
    },
    {
      key: 'realtime',
      title: 'Real-Time',
      icon: '📡',
      description: 'Live data monitoring',
      component: RealTimeAnalyticsDashboard
    },
    {
      key: 'predictive',
      title: 'Predictive',
      icon: '🔮',
      description: 'AI-powered forecasting',
      component: PredictiveInsights
    },
    {
      key: 'reports',
      title: 'Reports',
      icon: '📋',
      description: 'Automated reporting',
      component: AutomatedReportScheduler
    }
  ];

  // Period options
  const periodOptions = [
    { key: '7d', label: '7 Days' },
    { key: '30d', label: '30 Days' },
    { key: '90d', label: '90 Days' },
    { key: '6m', label: '6 Months' },
    { key: '1y', label: '1 Year' }
  ];

  // Export format options
  const exportFormats = [
    { key: 'json', label: 'JSON', description: 'Raw data format' },
    { key: 'csv', label: 'CSV', description: 'Spreadsheet format' },
    { key: 'pdf', label: 'PDF', description: 'Report format' },
    { key: 'xlsx', label: 'Excel', description: 'Excel workbook' }
  ];

  // Load analytics data
  const loadAnalyticsData = async () => {
    if (!currentUser) return;
    
    try {
      setLoading(true);
      
      const data = await AnalyticsDataService.getAnalyticsOverview(
        currentUser.id,
        selectedPeriod
      );
      
      setAnalyticsData(data);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Handle data export
  const handleExport = async (format) => {
    if (!currentUser) return;
    
    try {
      setIsExporting(true);
      
      const exportConfig = {
        dataType: activeTab,
        format,
        period: selectedPeriod,
        filters: {}
      };
      
      const result = await AnalyticsDataService.exportAnalyticsData(
        currentUser.id,
        exportConfig
      );
      
      if (format === 'json') {
        // Download JSON data directly
        const blob = new Blob([JSON.stringify(result, null, 2)], {
          type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${activeTab}-${selectedPeriod}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } else {
        // Handle other formats with download URL
        if (result.downloadUrl) {
          window.open(result.downloadUrl, '_blank');
        }
      }
      
      toast.success(`Analytics data exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Error exporting analytics data:', error);
      toast.error('Failed to export analytics data');
    } finally {
      setIsExporting(false);
    }
  };

  // Initialize component
  useEffect(() => {
    loadAnalyticsData();
  }, [currentUser, selectedPeriod]);

  // Get current tab configuration
  const currentTab = tabs.find(tab => tab.key === activeTab);
  const CurrentComponent = currentTab?.component;

  return (
    <div className={`enhanced-analytics-dashboard ${className}`}>
      {/* Header */}
      <Card className="mb-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">📈</span>
              <div>
                <h1 className="text-2xl font-bold">Enhanced Analytics</h1>
                <p className="text-default-600">
                  Comprehensive analytics platform with real-time insights
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Select
                selectedKeys={[selectedPeriod]}
                onSelectionChange={(keys) => setSelectedPeriod(Array.from(keys)[0])}
                className="w-32"
                size="sm"
                label="Period"
              >
                {periodOptions.map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
              
              <Select
                placeholder="Export"
                className="w-32"
                size="sm"
                onSelectionChange={(keys) => {
                  const format = Array.from(keys)[0];
                  if (format) handleExport(format);
                }}
                isDisabled={isExporting}
              >
                {exportFormats.map(format => (
                  <SelectItem key={format.key} description={format.description}>
                    {format.label}
                  </SelectItem>
                ))}
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Analytics Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            {tabs.map(tab => (
              <Tab
                key={tab.key}
                title={
                  <div className="flex items-center space-x-2">
                    <span>{tab.icon}</span>
                    <span>{tab.title}</span>
                    {tab.key === 'realtime' && (
                      <Badge color="success" variant="flat" size="sm">
                        Live
                      </Badge>
                    )}
                    {tab.key === 'predictive' && (
                      <Badge color="secondary" variant="flat" size="sm">
                        AI
                      </Badge>
                    )}
                  </div>
                }
              >
                <div className="p-6">
                  {/* Tab Description */}
                  <div className="mb-6 text-center">
                    <p className="text-default-600">{tab.description}</p>
                  </div>

                  {/* Tab Content */}
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeTab}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      {CurrentComponent && (
                        <CurrentComponent
                          currentUser={currentUser}
                          selectedPeriod={selectedPeriod}
                          analyticsData={analyticsData}
                          loading={loading}
                          onDataRefresh={loadAnalyticsData}
                        />
                      )}
                    </motion.div>
                  </AnimatePresence>
                </div>
              </Tab>
            ))}
          </Tabs>
        </CardBody>
      </Card>

      {/* Quick Actions */}
      <Card className="mt-6">
        <CardHeader>
          <h3 className="text-lg font-semibold">Quick Actions</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button
              variant="flat"
              color="primary"
              onClick={() => setActiveTab('realtime')}
              startContent={<span>📡</span>}
            >
              View Live Data
            </Button>
            
            <Button
              variant="flat"
              color="secondary"
              onClick={() => setActiveTab('predictive')}
              startContent={<span>🔮</span>}
            >
              AI Insights
            </Button>
            
            <Button
              variant="flat"
              color="success"
              onClick={() => handleExport('pdf')}
              isLoading={isExporting}
              startContent={<span>📄</span>}
            >
              Export Report
            </Button>
            
            <Button
              variant="flat"
              color="warning"
              onClick={() => setActiveTab('reports')}
              startContent={<span>⏰</span>}
            >
              Schedule Report
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Performance Metrics Summary */}
      {analyticsData && (
        <Card className="mt-6">
          <CardHeader>
            <h3 className="text-lg font-semibold">Performance Summary</h3>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-success">
                  ${analyticsData.revenue?.total?.toLocaleString() || '0'}
                </div>
                <div className="text-sm text-default-600">Total Revenue</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-primary">
                  {analyticsData.projects?.completed || 0}
                </div>
                <div className="text-sm text-default-600">Completed Projects</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-secondary">
                  {analyticsData.performance?.score || 0}
                </div>
                <div className="text-sm text-default-600">Performance Score</div>
              </div>
              
              <div>
                <div className="text-2xl font-bold text-warning">
                  {analyticsData.revenue?.growth?.toFixed(1) || '0'}%
                </div>
                <div className="text-sm text-default-600">Growth Rate</div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default EnhancedAnalyticsDashboard;
