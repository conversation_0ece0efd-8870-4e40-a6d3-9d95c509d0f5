import React, { useState, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Select, SelectItem, Switch, Checkbox, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Progress } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';

/**
 * ExportManager Component - Comprehensive Data Export Functionality
 * 
 * Features:
 * - Multiple export formats (PDF, Excel, CSV, JSON, PNG, SVG)
 * - Customizable data selection and filtering
 * - Scheduled exports and automated reporting
 * - Template-based report generation
 * - Batch export processing
 * - Export history and download management
 */
const ExportManager = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [isExporting, setIsExporting] = useState(false);
  const [exportHistory, setExportHistory] = useState([]);
  
  // Export configuration state
  const [exportConfig, setExportConfig] = useState({
    format: 'pdf',
    dataTypes: ['financial', 'projects'],
    dateRange: '6m',
    includeCharts: true,
    includeRawData: false,
    template: 'standard',
    schedule: 'manual'
  });

  // Export format options
  const exportFormats = {
    pdf: {
      icon: '📄',
      name: 'PDF Report',
      description: 'Professional formatted report',
      features: ['Charts', 'Tables', 'Branding']
    },
    excel: {
      icon: '📊',
      name: 'Excel Workbook',
      description: 'Spreadsheet with multiple sheets',
      features: ['Raw Data', 'Formulas', 'Charts']
    },
    csv: {
      icon: '📋',
      name: 'CSV Data',
      description: 'Comma-separated values',
      features: ['Raw Data', 'Lightweight', 'Universal']
    },
    json: {
      icon: '🔧',
      name: 'JSON Data',
      description: 'Structured data format',
      features: ['API Ready', 'Structured', 'Programmatic']
    },
    png: {
      icon: '🖼️',
      name: 'PNG Images',
      description: 'Chart images',
      features: ['High Quality', 'Presentations', 'Sharing']
    },
    svg: {
      icon: '🎨',
      name: 'SVG Graphics',
      description: 'Scalable vector graphics',
      features: ['Scalable', 'Editable', 'Web Ready']
    }
  };

  // Data type options
  const dataTypes = {
    financial: {
      icon: '💰',
      name: 'Financial Data',
      description: 'Revenue, expenses, profits'
    },
    projects: {
      icon: '🎯',
      name: 'Project Analytics',
      description: 'Performance, timelines, success rates'
    },
    skills: {
      icon: '🎓',
      name: 'Skill Development',
      description: 'Progress, certifications, levels'
    },
    achievements: {
      icon: '🏆',
      name: 'Achievements',
      description: 'Badges, milestones, rewards'
    },
    studios: {
      icon: '🤝',
      name: 'Studio Data',
      description: 'Team performance, collaboration'
    }
  };

  // Report templates
  const reportTemplates = {
    standard: {
      name: 'Standard Report',
      description: 'Comprehensive overview with all metrics'
    },
    executive: {
      name: 'Executive Summary',
      description: 'High-level insights for leadership'
    },
    detailed: {
      name: 'Detailed Analysis',
      description: 'In-depth analysis with raw data'
    },
    financial: {
      name: 'Financial Focus',
      description: 'Revenue and financial performance only'
    },
    performance: {
      name: 'Performance Review',
      description: 'Project and skill performance metrics'
    }
  };

  // Mock export history
  const mockExportHistory = [
    {
      id: 'exp_001',
      name: 'Q4 Financial Report',
      format: 'pdf',
      size: '2.4 MB',
      created: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      status: 'completed',
      downloadUrl: '#'
    },
    {
      id: 'exp_002',
      name: 'Project Analytics Data',
      format: 'excel',
      size: '1.8 MB',
      created: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      status: 'completed',
      downloadUrl: '#'
    },
    {
      id: 'exp_003',
      name: 'Skills Progress Charts',
      format: 'png',
      size: '856 KB',
      created: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: 'completed',
      downloadUrl: '#'
    }
  ];

  // Handle export process
  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate export progress
      const progressSteps = [
        { step: 'Collecting data...', progress: 20 },
        { step: 'Processing analytics...', progress: 40 },
        { step: 'Generating charts...', progress: 60 },
        { step: 'Formatting report...', progress: 80 },
        { step: 'Finalizing export...', progress: 100 }
      ];

      for (const step of progressSteps) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setExportProgress(step.progress);
        toast.loading(step.step, { id: 'export-progress' });
      }

      // Add to export history
      const newExport = {
        id: `exp_${Date.now()}`,
        name: `${reportTemplates[exportConfig.template].name} - ${new Date().toLocaleDateString()}`,
        format: exportConfig.format,
        size: `${(Math.random() * 3 + 0.5).toFixed(1)} MB`,
        created: new Date(),
        status: 'completed',
        downloadUrl: '#'
      };

      setExportHistory(prev => [newExport, ...prev]);
      
      toast.success('Export completed successfully!', { id: 'export-progress' });
      setShowExportModal(false);
    } catch (error) {
      toast.error('Export failed. Please try again.', { id: 'export-progress' });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // Update export configuration
  const updateConfig = (field, value) => {
    setExportConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Toggle data type selection
  const toggleDataType = (type) => {
    setExportConfig(prev => ({
      ...prev,
      dataTypes: prev.dataTypes.includes(type)
        ? prev.dataTypes.filter(t => t !== type)
        : [...prev.dataTypes, type]
    }));
  };

  // Format file size
  const formatFileSize = (size) => {
    return size;
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'completed': 'success',
      'processing': 'primary',
      'failed': 'danger',
      'pending': 'warning'
    };
    return colors[status] || 'default';
  };

  return (
    <div className={`export-manager ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 mb-6">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">📥</span>
              <div>
                <h2 className="text-2xl font-bold">Export Manager</h2>
                <p className="text-default-600">Export and download your analytics data</p>
              </div>
            </div>
            <Button
              color="primary"
              variant="shadow"
              onPress={() => setShowExportModal(true)}
            >
              + New Export
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Export Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        {Object.entries(exportFormats).slice(0, 3).map(([key, format]) => (
          <motion.div
            key={key}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            whileHover={{ scale: 1.02 }}
          >
            <Card 
              isPressable
              onPress={() => {
                updateConfig('format', key);
                setShowExportModal(true);
              }}
              className="cursor-pointer hover:shadow-lg transition-all"
            >
              <CardBody className="p-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-2xl">{format.icon}</span>
                  <div>
                    <h3 className="font-semibold">{format.name}</h3>
                    <p className="text-sm text-default-600">{format.description}</p>
                  </div>
                </div>
                <div className="flex flex-wrap gap-1">
                  {format.features.map((feature, index) => (
                    <Chip key={index} size="sm" variant="flat" color="primary">
                      {feature}
                    </Chip>
                  ))}
                </div>
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Export History */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">📋 Export History</h3>
        </CardHeader>
        <CardBody className="pt-0">
          {mockExportHistory.length > 0 ? (
            <div className="space-y-3">
              {mockExportHistory.map((exportItem, index) => (
                <motion.div
                  key={exportItem.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 border border-default-200 rounded-lg hover:bg-default-50 dark:hover:bg-default-900/20 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-xl">{exportFormats[exportItem.format]?.icon}</span>
                    <div>
                      <div className="font-medium">{exportItem.name}</div>
                      <div className="text-sm text-default-600">
                        {formatDate(exportItem.created)} • {formatFileSize(exportItem.size)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Chip color={getStatusColor(exportItem.status)} size="sm" variant="flat">
                      {exportItem.status}
                    </Chip>
                    <Button
                      size="sm"
                      variant="bordered"
                      onPress={() => toast.success('Download started!')}
                    >
                      Download
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📥</div>
              <h3 className="text-lg font-semibold mb-2">No exports yet</h3>
              <p className="text-default-600 mb-4">Create your first export to get started</p>
              <Button
                color="primary"
                onPress={() => setShowExportModal(true)}
              >
                Create Export
              </Button>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Export Configuration Modal */}
      <Modal 
        isOpen={showExportModal} 
        onClose={() => setShowExportModal(false)}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <span className="text-xl">📥 Configure Export</span>
          </ModalHeader>
          
          <ModalBody>
            <div className="space-y-6">
              {/* Export Format */}
              <div>
                <h4 className="font-semibold mb-3">Export Format</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {Object.entries(exportFormats).map(([key, format]) => (
                    <Card
                      key={key}
                      isPressable
                      onPress={() => updateConfig('format', key)}
                      className={`cursor-pointer transition-all ${
                        exportConfig.format === key 
                          ? 'border-2 border-primary bg-primary-50 dark:bg-primary-900/20' 
                          : 'border border-default-200 hover:border-primary-300'
                      }`}
                    >
                      <CardBody className="p-3 text-center">
                        <div className="text-xl mb-1">{format.icon}</div>
                        <div className="text-sm font-medium">{format.name}</div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Data Selection */}
              <div>
                <h4 className="font-semibold mb-3">Data to Include</h4>
                <div className="space-y-2">
                  {Object.entries(dataTypes).map(([key, type]) => (
                    <div key={key} className="flex items-center gap-3">
                      <Checkbox
                        isSelected={exportConfig.dataTypes.includes(key)}
                        onValueChange={() => toggleDataType(key)}
                      />
                      <div className="flex items-center gap-2">
                        <span>{type.icon}</span>
                        <div>
                          <div className="font-medium">{type.name}</div>
                          <div className="text-sm text-default-600">{type.description}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Date Range"
                  selectedKeys={[exportConfig.dateRange]}
                  onSelectionChange={(keys) => updateConfig('dateRange', Array.from(keys)[0])}
                >
                  <SelectItem key="1m">Last Month</SelectItem>
                  <SelectItem key="3m">Last 3 Months</SelectItem>
                  <SelectItem key="6m">Last 6 Months</SelectItem>
                  <SelectItem key="1y">Last Year</SelectItem>
                  <SelectItem key="all">All Time</SelectItem>
                </Select>

                <Select
                  label="Report Template"
                  selectedKeys={[exportConfig.template]}
                  onSelectionChange={(keys) => updateConfig('template', Array.from(keys)[0])}
                >
                  {Object.entries(reportTemplates).map(([key, template]) => (
                    <SelectItem key={key} description={template.description}>
                      {template.name}
                    </SelectItem>
                  ))}
                </Select>
              </div>

              {/* Additional Options */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Include Charts</div>
                    <div className="text-sm text-default-600">Add visual charts to the export</div>
                  </div>
                  <Switch
                    isSelected={exportConfig.includeCharts}
                    onValueChange={(value) => updateConfig('includeCharts', value)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Include Raw Data</div>
                    <div className="text-sm text-default-600">Add detailed raw data tables</div>
                  </div>
                  <Switch
                    isSelected={exportConfig.includeRawData}
                    onValueChange={(value) => updateConfig('includeRawData', value)}
                  />
                </div>
              </div>

              {/* Export Progress */}
              {isExporting && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Export Progress</span>
                    <span>{exportProgress}%</span>
                  </div>
                  <Progress value={exportProgress} color="primary" size="sm" />
                </div>
              )}
            </div>
          </ModalBody>
          
          <ModalFooter>
            <Button 
              variant="light" 
              onPress={() => setShowExportModal(false)}
              isDisabled={isExporting}
            >
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={handleExport}
              isLoading={isExporting}
              isDisabled={isExporting || exportConfig.dataTypes.length === 0}
            >
              {isExporting ? 'Exporting...' : 'Start Export'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ExportManager;
