// SocialHub - Enhanced social features integration component
// Integrates comprehensive social networking with existing basic features
import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Badge, Tabs, Tab } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import socialService from '../../services/socialService';
import AllyNetworkDashboard from './AllyNetworkDashboard';
import {
  MessageCircle,
  Bell,
  Users,
  TrendingUp,
  Heart,
  Share2,
  Send,
  UserPlus,
  Network,
  Award,
  Briefcase
} from 'lucide-react';

const SocialHub = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [activities, setActivities] = useState([]);
  const [newUpdate, setNewUpdate] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (currentUser) {
      initializeSocialFeatures();
    }
  }, [currentUser]);

  const initializeSocialFeatures = async () => {
    try {
      setIsLoading(true);
      
      // Initialize social service
      await socialService.initialize(currentUser);
      
      // Load notifications
      await loadNotifications();
      
      // Load activity feed
      await loadActivityFeed();
      
    } catch (error) {
      console.error('Error initializing social features:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadNotifications = async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;

      setNotifications(data || []);
      const unread = data ? data.filter(n => !n.is_read).length : 0;
      setUnreadCount(unread);
    } catch (error) {
      console.error('Error loading notifications:', error);
    }
  };

  const loadActivityFeed = async () => {
    try {
      const feedData = await socialService.getActivityFeed();
      setActivities(feedData);
    } catch (error) {
      console.error('Error loading activity feed:', error);
    }
  };

  const sendFriendRequest = async (targetUserId) => {
    try {
      await socialService.sendFriendRequest(targetUserId, 'Would like to connect!');
      console.log('Friend request sent');
    } catch (error) {
      console.error('Error sending friend request:', error);
    }
  };

  const postUpdate = async () => {
    if (!newUpdate.trim()) return;

    try {
      await socialService.createActivity('social_update', newUpdate);
      setNewUpdate('');
      await loadActivityFeed();
    } catch (error) {
      console.error('Error posting update:', error);
    }
  };

  if (!currentUser) {
    return null;
  }

  return (
    <div className={`social-hub space-y-6 ${className}`}>
      {/* Social Features Header */}
      <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <CardBody className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">Social Hub</h2>
              <p className="text-blue-100">Connect, collaborate, and share your achievements</p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{unreadCount}</div>
                <div className="text-sm text-blue-100">Notifications</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{activities.length}</div>
                <div className="text-sm text-blue-100">Activities</div>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Enhanced Navigation Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={setActiveTab}
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary",
              tab: "max-w-fit px-6 py-4 h-12",
              tabContent: "group-data-[selected=true]:text-primary"
            }}
          >
            <Tab
              key="overview"
              title={
                <div className="flex items-center space-x-2">
                  <TrendingUp size={18} />
                  <span>Overview</span>
                </div>
              }
            />
            <Tab
              key="network"
              title={
                <div className="flex items-center space-x-2">
                  <Network size={18} />
                  <span>Ally Network</span>
                </div>
              }
            />
            <Tab
              key="messages"
              title={
                <div className="flex items-center space-x-2">
                  <MessageCircle size={18} />
                  <span>Messages</span>
                  {unreadCount > 0 && (
                    <Badge content={unreadCount} color="danger" size="sm" />
                  )}
                </div>
              }
            />
            <Tab
              key="achievements"
              title={
                <div className="flex items-center space-x-2">
                  <Award size={18} />
                  <span>Achievements</span>
                </div>
              }
            />
          </Tabs>
        </CardBody>
      </Card>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onPress={() => setActiveTab('messages')}>
              <CardBody className="p-4 text-center">
                <MessageCircle className="mx-auto mb-2 text-blue-500" size={32} />
                <h3 className="font-semibold">Messages</h3>
                <p className="text-sm text-gray-600">Chat with connections</p>
                {unreadCount > 0 && (
                  <Badge content={unreadCount} color="danger" size="sm" className="mt-2" />
                )}
              </CardBody>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onPress={() => setActiveTab('network')}>
              <CardBody className="p-4 text-center">
                <Users className="mx-auto mb-2 text-green-500" size={32} />
                <h3 className="font-semibold">Ally Network</h3>
                <p className="text-sm text-gray-600">Manage your connections</p>
              </CardBody>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onPress={() => setActiveTab('achievements')}>
              <CardBody className="p-4 text-center">
                <Award className="mx-auto mb-2 text-purple-500" size={32} />
                <h3 className="font-semibold">Achievements</h3>
                <p className="text-sm text-gray-600">View your progress</p>
              </CardBody>
            </Card>
          </div>

          {/* Share Update */}
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Share an Update</h3>
              <div className="space-y-4">
                <textarea
                  value={newUpdate}
                  onChange={(e) => setNewUpdate(e.target.value)}
                  placeholder="What's happening in your work?"
                  className="w-full p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                />
                <div className="flex justify-end">
                  <Button
                    color="primary"
                    onPress={postUpdate}
                    isDisabled={!newUpdate.trim()}
                    startContent={<Send size={16} />}
                  >
                    Share Update
                  </Button>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Recent Notifications */}
          <Card>
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Recent Notifications</h3>
                <Badge content={unreadCount} color="danger" />
              </div>

              {isLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Bell size={32} className="mx-auto mb-2 opacity-50" />
                  <p>No notifications yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {notifications.slice(0, 5).map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border ${
                        notification.is_read ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium">{notification.title}</h4>
                          <p className="text-sm text-gray-600">{notification.message}</p>
                        </div>
                        <span className="text-xs text-gray-500">
                          {new Date(notification.created_at).toLocaleDateString()}
                        </span>
                      </div>

                      {notification.type === 'friend_request' && !notification.is_read && (
                        <div className="mt-2 flex gap-2">
                          <Button size="sm" color="success">Accept</Button>
                          <Button size="sm" variant="light">Decline</Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>

          {/* Activity Feed */}
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Activity Feed</h3>

              {activities.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <TrendingUp size={32} className="mx-auto mb-2 opacity-50" />
                  <p>No activity yet</p>
                  <p className="text-sm">Start connecting and sharing to see activity here</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {activities.slice(0, 5).map((activity) => (
                    <div key={activity.id} className="border-l-4 border-blue-500 pl-4">
                      <h4 className="font-medium">{activity.title}</h4>
                      {activity.description && (
                        <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                      )}
                      <div className="flex items-center gap-4 mt-2">
                        <Button size="sm" variant="light" startContent={<Heart size={14} />}>
                          {activity.likes_count || 0}
                        </Button>
                        <Button size="sm" variant="light" startContent={<MessageCircle size={14} />}>
                          {activity.comments_count || 0}
                        </Button>
                        <Button size="sm" variant="light" startContent={<Share2 size={14} />}>
                          Share
                        </Button>
                        <span className="text-xs text-gray-500 ml-auto">
                          {new Date(activity.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      )}

      {/* Ally Network Tab */}
      {activeTab === 'network' && (
        <div className="space-y-6">
          <AllyNetworkDashboard />
        </div>
      )}

      {/* Messages Tab */}
      {activeTab === 'messages' && (
        <div className="space-y-6">
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Messages</h3>
              <div className="text-center py-8 text-gray-500">
                <MessageCircle size={32} className="mx-auto mb-2 opacity-50" />
                <p>Enhanced messaging system coming soon!</p>
                <p className="text-sm">Direct messaging with allies and group conversations</p>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Achievements Tab */}
      {activeTab === 'achievements' && (
        <div className="space-y-6">
          <Card>
            <CardBody className="p-6">
              <h3 className="text-lg font-semibold mb-4">Achievements</h3>
              <div className="text-center py-8 text-gray-500">
                <Award size={32} className="mx-auto mb-2 opacity-50" />
                <p>Achievement system coming soon!</p>
                <p className="text-sm">Track your progress and unlock new achievements</p>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {/* Floating Action Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          isIconOnly
          color="primary"
          size="lg"
          className="shadow-lg"
          onPress={() => console.log('Open chat')}
        >
          <MessageCircle size={24} />
        </Button>
      </div>
    </div>
  );
};

export default SocialHub;
