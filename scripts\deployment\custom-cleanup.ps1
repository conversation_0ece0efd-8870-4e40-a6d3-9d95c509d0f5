# Custom Repository Cleanup Script
# This script removes unnecessary files and folders while preserving Supabase CLI files

Write-Host "=== Custom Repository Cleanup ===" -ForegroundColor Cyan

# Define the root folder
$rootFolder = $PSScriptRoot

# List of directories to keep
$directoriesToKeep = @(
    "client",
    "netlify",
    "supabase",
    ".netlify"  # Keep .netlify for Netlify CLI configuration
)

# List of files to keep in the root directory
$filesToKeep = @(
    "README.md",
    "netlify.toml",
    "package.json",
    "package-lock.json",
    ".gitignore",
    "supabase-assistant-config.js",  # Keep Supabase assistant config
    "build-netlify-deploy.ps1",      # Keep main deployment script
    "deploy-netlify-cli.ps1",        # Keep Netlify CLI deployment script
    "custom-cleanup.ps1"             # Keep this script
)

# Delete all directories except those in the keep list
Write-Host "Deleting unnecessary directories..." -ForegroundColor Yellow
Get-ChildItem -Path $rootFolder -Directory | Where-Object { $directoriesToKeep -notcontains $_.Name } | ForEach-Object {
    Write-Host "  Deleting directory: $($_.FullName)" -ForegroundColor Gray
    Remove-Item -Path $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
}

# Delete all files in the root directory except those in the keep list
Write-Host "Deleting unnecessary files in root directory..." -ForegroundColor Yellow
Get-ChildItem -Path $rootFolder -File | Where-Object { $filesToKeep -notcontains $_.Name } | ForEach-Object {
    Write-Host "  Deleting file: $($_.FullName)" -ForegroundColor Gray
    Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
}

# Clean up client directory
Write-Host "Cleaning up client directory..." -ForegroundColor Yellow
$clientFolder = Join-Path -Path $rootFolder -ChildPath "client"
if (Test-Path $clientFolder) {
    # Delete node_modules if it exists
    $nodeModulesFolder = Join-Path -Path $clientFolder -ChildPath "node_modules"
    if (Test-Path $nodeModulesFolder) {
        Write-Host "  Deleting node_modules folder..." -ForegroundColor Gray
        Remove-Item -Path $nodeModulesFolder -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    # Delete dist folder if it exists
    $distFolder = Join-Path -Path $clientFolder -ChildPath "dist"
    if (Test-Path $distFolder) {
        Write-Host "  Deleting dist folder..." -ForegroundColor Gray
        Remove-Item -Path $distFolder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Clean up netlify directory
Write-Host "Cleaning up netlify directory..." -ForegroundColor Yellow
$netlifyFolder = Join-Path -Path $rootFolder -ChildPath "netlify"
if (Test-Path $netlifyFolder) {
    # Delete node_modules if it exists
    $nodeModulesFolder = Join-Path -Path $netlifyFolder -ChildPath "node_modules"
    if (Test-Path $nodeModulesFolder) {
        Write-Host "  Deleting node_modules folder..." -ForegroundColor Gray
        Remove-Item -Path $nodeModulesFolder -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Delete temporary build folders
Write-Host "Deleting temporary build folders..." -ForegroundColor Yellow
$tempBuildFolders = @(
    "netlify-deploy",
    "netlify-upload",
    "deploy",
    "functions-deploy"
)

foreach ($folder in $tempBuildFolders) {
    $folderPath = Join-Path -Path $rootFolder -ChildPath $folder
    if (Test-Path $folderPath) {
        Write-Host "  Deleting folder: $folder" -ForegroundColor Gray
        Remove-Item -Path $folderPath -Recurse -Force -ErrorAction SilentlyContinue
    }
}

# Delete temporary files
Write-Host "Deleting temporary files..." -ForegroundColor Yellow
$tempFilePatterns = @(
    "*.log",
    "*.tmp",
    ".DS_Store",
    "Thumbs.db"
)

foreach ($pattern in $tempFilePatterns) {
    Get-ChildItem -Path $rootFolder -Filter $pattern -Recurse -File -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "  Deleting file: $($_.FullName)" -ForegroundColor Gray
        Remove-Item -Path $_.FullName -Force -ErrorAction SilentlyContinue
    }
}

# Delete root node_modules if it exists
$rootNodeModules = Join-Path -Path $rootFolder -ChildPath "node_modules"
if (Test-Path $rootNodeModules) {
    Write-Host "Deleting root node_modules folder..." -ForegroundColor Yellow
    Remove-Item -Path $rootNodeModules -Recurse -Force -ErrorAction SilentlyContinue
}

Write-Host "=== Cleanup Complete! ===" -ForegroundColor Green
Write-Host "The repository has been cleaned while preserving Supabase CLI files." -ForegroundColor Cyan
