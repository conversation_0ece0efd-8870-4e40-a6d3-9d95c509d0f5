/**
 * Venture Creation Integration
 * 
 * Integrates Agreement Generation V2 with the venture creation wizard flow.
 * Triggers agreement generation when ventures are created with contributors.
 */

import { AgreementIntegrationService } from './AgreementIntegrationService.js';
import { supabase } from '../../supabase/supabase.utils.js';

export class VentureCreationIntegration {
  constructor() {
    this.agreementService = new AgreementIntegrationService();
  }

  /**
   * Handle venture creation completion with agreement generation
   */
  async handleVentureCreationComplete(ventureData, allianceData, creatorData, options = {}) {
    console.log('🚀 Processing venture creation with agreement generation...');
    
    try {
      // Step 1: Create the venture in the database
      const createdVenture = await this.createVentureRecord(ventureData, allianceData.id, creatorData.id);
      
      // Step 2: Create milestone records if provided
      if (ventureData.milestones && ventureData.milestones.length > 0) {
        await this.createMilestoneRecords(createdVenture.id, ventureData.milestones);
      }
      
      // Step 3: Create project specifications if provided
      if (ventureData.specifications) {
        await this.createSpecificationRecord(createdVenture.id, ventureData.specifications);
      }
      
      // Step 4: Generate founder agreement (if requested)
      let founderAgreement = null;
      if (options.generateFounderAgreement !== false) {
        founderAgreement = await this.generateFounderAgreement(
          createdVenture,
          allianceData,
          creatorData,
          options
        );
      }
      
      // Step 5: Set up contributor invitation system
      await this.setupContributorInvitationSystem(createdVenture.id, allianceData.id);
      
      console.log('✅ Venture creation with agreement integration completed');
      
      return {
        venture: createdVenture,
        founderAgreement: founderAgreement,
        agreementSystemReady: true,
        nextSteps: {
          canInviteContributors: true,
          agreementGenerationEnabled: true,
          milestoneTrackingEnabled: ventureData.milestones?.length > 0
        }
      };
      
    } catch (error) {
      console.error('❌ Venture creation with agreement integration failed:', error);
      throw new Error(`Venture creation failed: ${error.message}`);
    }
  }

  /**
   * Create venture record in database
   */
  async createVentureRecord(ventureData, allianceId, creatorId) {
    console.log('📝 Creating venture record...');
    
    try {
      const ventureRecord = {
        name: ventureData.name,
        description: ventureData.description,
        venture_type: ventureData.projectType || ventureData.venture_type,
        team_id: allianceId,
        created_by: creatorId,
        status: 'active',
        start_date: ventureData.startDate || new Date().toISOString(),
        objectives: ventureData.objectives || [],
        deliverables: ventureData.deliverables || [],
        revenue_model: ventureData.revenueModel || 'revenue_sharing',
        agreement_generation_enabled: true,
        created_at: new Date().toISOString()
      };
      
      const { data: createdVenture, error } = await supabase
        .from('projects')
        .insert([ventureRecord])
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('✅ Venture record created');
      return createdVenture;
      
    } catch (error) {
      console.error('❌ Failed to create venture record:', error);
      throw new Error(`Failed to create venture record: ${error.message}`);
    }
  }

  /**
   * Create milestone records
   */
  async createMilestoneRecords(ventureId, milestones) {
    console.log('📅 Creating milestone records...');
    
    try {
      const milestoneRecords = milestones.map((milestone, index) => ({
        venture_id: ventureId,
        title: milestone.name || milestone.title,
        description: milestone.description,
        due_date: milestone.deadline || milestone.due_date,
        phase: milestone.phase || this.inferPhaseFromIndex(index, milestones.length),
        deliverables: milestone.deliverables || [],
        acceptance_criteria: milestone.acceptance_criteria || [],
        status: 'pending',
        order_index: index,
        created_at: new Date().toISOString()
      }));
      
      const { data: createdMilestones, error } = await supabase
        .from('venture_milestones')
        .insert(milestoneRecords)
        .select();
      
      if (error) throw error;
      
      console.log(`✅ ${createdMilestones.length} milestone records created`);
      return createdMilestones;
      
    } catch (error) {
      console.error('❌ Failed to create milestone records:', error);
      throw new Error(`Failed to create milestone records: ${error.message}`);
    }
  }

  /**
   * Create project specifications record
   */
  async createSpecificationRecord(ventureId, specifications) {
    console.log('📋 Creating project specifications...');
    
    try {
      const specRecord = {
        project_id: ventureId,
        core_features: specifications.coreFeatures || [],
        technical_requirements: specifications.technical || {},
        platforms: specifications.platforms || [],
        art_style: specifications.artStyle,
        audio_requirements: specifications.audioRequirements,
        deliverables: specifications.deliverables || [],
        created_at: new Date().toISOString()
      };
      
      const { data: createdSpec, error } = await supabase
        .from('project_specifications')
        .insert([specRecord])
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('✅ Project specifications created');
      return createdSpec;
      
    } catch (error) {
      console.error('❌ Failed to create project specifications:', error);
      throw new Error(`Failed to create project specifications: ${error.message}`);
    }
  }

  /**
   * Generate founder agreement
   */
  async generateFounderAgreement(venture, alliance, creator, options = {}) {
    console.log('📄 Generating founder agreement...');
    
    try {
      const result = await this.agreementService.generateForVentureCreation(
        venture,
        alliance,
        creator,
        {
          ...options,
          metadata: {
            ...options.metadata,
            agreementType: 'founder_agreement',
            generationContext: 'venture_creation'
          }
        }
      );
      
      console.log('✅ Founder agreement generated');
      return result;
      
    } catch (error) {
      console.error('❌ Failed to generate founder agreement:', error);
      // Don't throw - founder agreement is optional
      console.log('⚠️  Continuing without founder agreement');
      return null;
    }
  }

  /**
   * Set up contributor invitation system
   */
  async setupContributorInvitationSystem(ventureId, allianceId) {
    console.log('👥 Setting up contributor invitation system...');
    
    try {
      // Create invitation configuration
      const invitationConfig = {
        venture_id: ventureId,
        alliance_id: allianceId,
        auto_generate_agreements: true,
        agreement_template: 'standard_v2',
        invitation_workflow_enabled: true,
        created_at: new Date().toISOString()
      };
      
      const { data: config, error } = await supabase
        .from('venture_invitation_config')
        .insert([invitationConfig])
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('✅ Contributor invitation system configured');
      return config;
      
    } catch (error) {
      console.error('❌ Failed to setup contributor invitation system:', error);
      throw new Error(`Failed to setup contributor invitation system: ${error.message}`);
    }
  }

  /**
   * Handle venture update with agreement regeneration
   */
  async handleVentureUpdate(ventureId, updateData, options = {}) {
    console.log('🔄 Processing venture update with agreement regeneration...');
    
    try {
      // Step 1: Update venture record
      const { data: updatedVenture, error: updateError } = await supabase
        .from('projects')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', ventureId)
        .select()
        .single();
      
      if (updateError) throw updateError;
      
      // Step 2: Update specifications if provided
      if (updateData.specifications) {
        await this.updateSpecifications(ventureId, updateData.specifications);
      }
      
      // Step 3: Update milestones if provided
      if (updateData.milestones) {
        await this.updateMilestones(ventureId, updateData.milestones);
      }
      
      // Step 4: Regenerate existing agreements if requested
      if (options.regenerateAgreements !== false) {
        await this.regenerateVentureAgreements(ventureId);
      }
      
      console.log('✅ Venture update with agreement regeneration completed');
      
      return {
        venture: updatedVenture,
        agreementsRegenerated: options.regenerateAgreements !== false
      };
      
    } catch (error) {
      console.error('❌ Venture update failed:', error);
      throw new Error(`Venture update failed: ${error.message}`);
    }
  }

  /**
   * Regenerate all agreements for a venture
   */
  async regenerateVentureAgreements(ventureId) {
    console.log('🔄 Regenerating venture agreements...');
    
    try {
      // Get all agreements for this venture
      const { data: agreements, error } = await supabase
        .from('generated_agreements')
        .select('id')
        .eq('venture_id', ventureId)
        .eq('is_active', true);
      
      if (error) throw error;
      
      // Regenerate each agreement
      const regenerationPromises = agreements.map(agreement =>
        this.agreementService.regenerateAgreement(agreement.id)
      );
      
      const results = await Promise.allSettled(regenerationPromises);
      
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      console.log(`✅ Agreement regeneration completed: ${successful} successful, ${failed} failed`);
      
      return {
        total: agreements.length,
        successful,
        failed,
        results
      };
      
    } catch (error) {
      console.error('❌ Failed to regenerate venture agreements:', error);
      throw new Error(`Failed to regenerate venture agreements: ${error.message}`);
    }
  }

  /**
   * Update project specifications
   */
  async updateSpecifications(ventureId, specifications) {
    try {
      const { data, error } = await supabase
        .from('project_specifications')
        .upsert({
          project_id: ventureId,
          ...specifications,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) throw error;
      return data;
      
    } catch (error) {
      console.error('❌ Failed to update specifications:', error);
      throw error;
    }
  }

  /**
   * Update milestones
   */
  async updateMilestones(ventureId, milestones) {
    try {
      // Delete existing milestones
      await supabase
        .from('venture_milestones')
        .delete()
        .eq('venture_id', ventureId);
      
      // Create new milestones
      if (milestones.length > 0) {
        return await this.createMilestoneRecords(ventureId, milestones);
      }
      
      return [];
      
    } catch (error) {
      console.error('❌ Failed to update milestones:', error);
      throw error;
    }
  }

  /**
   * Helper method to infer phase from milestone index
   */
  inferPhaseFromIndex(index, total) {
    const percentage = (index + 1) / total;
    
    if (percentage <= 0.25) return 'Phase 1: Planning';
    if (percentage <= 0.5) return 'Phase 2: Development';
    if (percentage <= 0.75) return 'Phase 3: Testing';
    return 'Phase 4: Launch';
  }

  /**
   * Get venture creation integration status
   */
  getIntegrationStatus() {
    return {
      version: '2.0.0',
      capabilities: {
        ventureCreation: true,
        milestoneIntegration: true,
        specificationIntegration: true,
        founderAgreements: true,
        contributorInvitationSetup: true,
        agreementRegeneration: true
      },
      status: 'active'
    };
  }
}
