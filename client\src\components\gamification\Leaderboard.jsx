import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Chip, Avatar, Tabs, Tab } from '@heroui/react';

/**
 * Leaderboard Component
 * 
 * Enhanced leaderboard following wireframe specifications
 * Features rankings, time periods, filtering options, and streak tracking
 */
const Leaderboard = ({ 
  currentUser,
  leaderboardData = [],
  userRank = 4,
  totalUsers = 247,
  onViewFullLeaderboard,
  onViewAllianceRankings,
  className = ""
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Sample leaderboard data
  const defaultLeaderboard = [
    {
      rank: 1,
      user: { name: '<PERSON>', avatar: null },
      orbs: 2450,
      level: 15,
      streak: 12,
      change: '+1'
    },
    {
      rank: 2,
      user: { name: '<PERSON>', avatar: null },
      orbs: 2100,
      level: 14,
      streak: 8,
      change: '-1'
    },
    {
      rank: 3,
      user: { name: '<PERSON>', avatar: null },
      orbs: 1890,
      level: 13,
      streak: 15,
      change: '+2'
    },
    {
      rank: 4,
      user: { name: 'You', avatar: null },
      orbs: 1247,
      level: 12,
      streak: 5,
      change: '+2',
      isCurrentUser: true
    },
    {
      rank: 5,
      user: { name: 'David Kim', avatar: null },
      orbs: 1156,
      level: 10,
      streak: 3,
      change: '-1'
    },
    {
      rank: 6,
      user: { name: 'Lisa Wong', avatar: null },
      orbs: 1089,
      level: 11,
      streak: 7,
      change: '0'
    }
  ];

  const leaderboard = leaderboardData.length > 0 ? leaderboardData : defaultLeaderboard;

  // Get rank icon
  const getRankIcon = (rank) => {
    switch (rank) {
      case 1: return '👑';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return rank.toString();
    }
  };

  // Get rank color
  const getRankColor = (rank) => {
    switch (rank) {
      case 1: return 'warning'; // Gold
      case 2: return 'default'; // Silver
      case 3: return 'warning'; // Bronze
      default: return 'primary';
    }
  };

  // Get change indicator
  const getChangeIndicator = (change) => {
    if (change.startsWith('+')) {
      return <span className="text-success text-xs">⬆️ {change}</span>;
    } else if (change.startsWith('-')) {
      return <span className="text-danger text-xs">⬇️ {change}</span>;
    } else {
      return <span className="text-default-500 text-xs">➖</span>;
    }
  };

  // Get period label
  const getPeriodLabel = (period) => {
    const labels = {
      'week': 'This Week',
      'month': 'This Month',
      'alltime': 'All Time',
      'studio': 'Studio Only'
    };
    return labels[period] || 'This Month';
  };

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-amber-50 to-yellow-100 dark:from-amber-900/20 dark:to-yellow-800/20 border-2 border-amber-200 dark:border-amber-700 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🏅</span>
              <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-200">Leaderboards</h3>
            </div>
            <Chip color="warning" variant="flat" size="sm">
              #{userRank} You
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Period Selector */}
          <div className="mb-4">
            <Tabs 
              selectedKey={selectedPeriod}
              onSelectionChange={setSelectedPeriod}
              size="sm"
              variant="underlined"
              classNames={{
                tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                cursor: "w-full bg-primary",
                tab: "max-w-fit px-0 h-8",
                tabContent: "group-data-[selected=true]:text-primary text-xs"
              }}
            >
              <Tab key="month" title="📅 This Month" />
              <Tab key="week" title="⚡ This Week" />
              <Tab key="alltime" title="🏆 All Time" />
              <Tab key="studio" title="🏰 Studio" />
            </Tabs>
          </div>

          {/* Leaderboard List */}
          <div className="space-y-2 mb-4">
            {leaderboard.slice(0, 6).map((entry, idx) => (
              <motion.div
                key={idx}
                className={`flex items-center gap-3 p-2 rounded-lg transition-colors ${
                  entry.isCurrentUser 
                    ? 'bg-primary-100 dark:bg-primary-900/30 border border-primary-200' 
                    : 'bg-white/50 dark:bg-black/20 hover:bg-white/70 dark:hover:bg-black/30'
                }`}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: idx * 0.05 }}
              >
                {/* Rank */}
                <div className="w-8 text-center">
                  {entry.rank <= 3 ? (
                    <span className="text-lg">{getRankIcon(entry.rank)}</span>
                  ) : (
                    <Chip 
                      size="sm" 
                      color={entry.isCurrentUser ? 'primary' : 'default'}
                      variant="flat"
                    >
                      {entry.rank}
                    </Chip>
                  )}
                </div>

                {/* User Info */}
                <Avatar 
                  name={entry.user.name}
                  src={entry.user.avatar}
                  size="sm"
                />
                <div className="flex-1">
                  <div className={`text-sm font-medium ${entry.isCurrentUser ? 'text-primary' : ''}`}>
                    {entry.user.name}
                  </div>
                  <div className="text-xs text-default-500">
                    Level {entry.level}
                  </div>
                </div>

                {/* Stats */}
                <div className="text-right">
                  <div className="text-sm font-bold text-warning-600">
                    {entry.orbs.toLocaleString()} ORBs
                  </div>
                  <div className="text-xs text-default-500">
                    🔥 {entry.streak} days
                  </div>
                </div>

                {/* Change Indicator */}
                <div className="w-8 text-center">
                  {getChangeIndicator(entry.change)}
                </div>
              </motion.div>
            ))}
          </div>

          {/* User Rank Summary */}
          <div className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 rounded-lg p-3 mb-4">
            <div className="text-center">
              <div className="text-sm font-semibold text-primary">
                Your Rank: #{userRank} out of {totalUsers} users
              </div>
              <div className="text-xs text-default-600 mt-1">
                (+2 from last week) ⬆️
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 gap-2">
            <Button 
              color="warning" 
              variant="flat" 
              size="sm"
              onPress={() => onViewFullLeaderboard && onViewFullLeaderboard()}
            >
              🏆 View Full Leaderboard
            </Button>
            
            <div className="grid grid-cols-2 gap-2">
              <Button 
                color="primary" 
                variant="flat" 
                size="sm"
                onPress={() => onViewAllianceRankings && onViewAllianceRankings()}
              >
                🏰 Studio Rankings
              </Button>
              <Button 
                color="secondary" 
                variant="flat" 
                size="sm"
              >
                🎯 Skill Rankings
              </Button>
            </div>
          </div>

          {/* Streak Info */}
          <div className="mt-4 p-2 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 rounded-lg">
            <div className="text-xs text-center">
              <div className="font-semibold text-orange-700 dark:text-orange-300">
                🔥 Your Streak: 5 days
              </div>
              <div className="text-orange-600 dark:text-orange-400 mt-1">
                Keep it up! 2 more days for a weekly bonus
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default Leaderboard;
