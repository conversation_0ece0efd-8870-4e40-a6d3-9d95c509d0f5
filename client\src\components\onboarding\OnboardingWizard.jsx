import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button, Input, Checkbox } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { useSupabase } from '../../hooks/useSupabase.js';
import { toast } from 'react-hot-toast';

/**
 * OnboardingWizard Component
 * 
 * Main wizard container for the complete onboarding experience
 * Handles quick setup flows for Studio and Project creation
 * Follows immersive pattern with full-screen, minimal UI
 */
const OnboardingWizard = ({ 
  selectedPath, 
  onComplete, 
  onCancel 
}) => {
  const { currentUser } = useContext(UserContext);
  // supabase is imported directly
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    useQuickStart: true,
    customSettings: {}
  });

  // Initialize form data based on selected path
  useEffect(() => {
    if (selectedPath?.template) {
      setFormData(prev => ({
        ...prev,
        name: selectedPath.template.name || '',
        useQuickStart: true
      }));
    }
  }, [selectedPath]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleQuickStartToggle = (checked) => {
    setFormData(prev => ({
      ...prev,
      useQuickStart: checked
    }));
  };

  // Handle Studio creation (team path)
  const handleCreateAlliance = async () => {
    if (!currentUser) {
      toast.error('Please log in to create an studio');
      return;
    }

    if (!formData.name.trim()) {
      toast.error('Studio name is required');
      return;
    }

    setIsLoading(true);
    try {
      // Create studio in teams table
      const allianceData = {
        name: formData.name,
        description: `Studio created through onboarding`,
        studio_type: 'emerging',
        created_by: currentUser.id,
        max_members: 10,
        is_public: true,
        created_at: new Date().toISOString()
      };

      const { data: studio, error: allianceError } = await supabase
        .from('teams')
        .insert([allianceData])
        .select()
        .single();

      if (allianceError) throw allianceError;

      // Add creator as studio founder
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: studio.id,
          user_id: currentUser.id,
          role: 'founder',
          status: 'active',
          joined_at: new Date().toISOString()
        }]);

      if (memberError) throw memberError;

      // Apply quick start settings if enabled
      if (formData.useQuickStart) {
        // Set up default studio preferences
        const { error: prefsError } = await supabase
          .from('team_preferences')
          .insert([{
            team_id: studio.id,
            invite_members_enabled: true,
            shared_workspace_enabled: true,
            auto_venture_creation: true,
            created_at: new Date().toISOString()
          }]);

        if (prefsError) console.warn('Could not set studio preferences:', prefsError);
      }

      toast.success('Studio created successfully!');
      
      // Complete onboarding with studio data
      onComplete({
        type: 'alliance_created',
        studio,
        action: 'invite_members',
        redirectTo: `/studio/${studio.id}`
      });

    } catch (error) {
      console.error('Error creating studio:', error);
      toast.error('Failed to create studio. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Project creation (solo path)
  const handleCreateVenture = async () => {
    if (!currentUser) {
      toast.error('Please log in to create a project');
      return;
    }

    if (!formData.name.trim()) {
      toast.error('Project name is required');
      return;
    }

    setIsLoading(true);
    try {
      // Create project in projects table
      const ventureData = {
        name: formData.name,
        description: `Project created through onboarding`,
        project_type: selectedPath?.projectType || 'software',
        created_by: currentUser.id,
        is_public: true,
        estimated_duration: 6,
        start_date: new Date().toISOString(),
        status: 'active',
        created_at: new Date().toISOString()
      };

      const { data: project, error: ventureError } = await supabase
        .from('projects')
        .insert([ventureData])
        .select()
        .single();

      if (ventureError) throw ventureError;

      // Add creator as project owner
      const { error: contributorError } = await supabase
        .from('project_contributors')
        .insert([{
          project_id: project.id,
          user_id: currentUser.id,
          role: 'owner',
          status: 'active',
          joined_at: new Date().toISOString()
        }]);

      if (contributorError) throw contributorError;

      // Apply quick start settings if enabled
      if (formData.useQuickStart) {
        // Create first mission
        const { error: missionError } = await supabase
          .from('tasks')
          .insert([{
            project_id: project.id,
            title: 'Get Started',
            description: 'Set up your project and define your first goals',
            status: 'todo',
            priority: 'high',
            created_by: currentUser.id,
            assigned_to: currentUser.id,
            created_at: new Date().toISOString()
          }]);

        if (missionError) console.warn('Could not create first mission:', missionError);
      }

      toast.success('Project created successfully!');
      
      // Complete onboarding with project data
      onComplete({
        type: 'venture_created',
        project,
        action: 'create_mission',
        redirectTo: `/project/${project.id}`
      });

    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = () => {
    if (selectedPath?.teamChoice === 'team') {
      handleCreateAlliance();
    } else {
      handleCreateVenture();
    }
  };

  const isAlliance = selectedPath?.teamChoice === 'team';
  const entityType = isAlliance ? 'Studio' : 'Project';
  const entityIcon = isAlliance ? '👥' : '🚀';
  const defaultName = isAlliance ? 'Dream Team Studios' : 'My Awesome Software';

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      {/* Cancel Button */}
      <motion.div
        className="fixed top-4 right-4 z-60"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <Button
          onClick={onCancel}
          variant="ghost"
          className="text-white text-opacity-70 hover:text-opacity-100 bg-transparent hover:bg-white hover:bg-opacity-10"
          size="sm"
        >
          ✕
        </Button>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center max-w-2xl mx-auto px-8"
      >
        <motion.h1
          className="text-3xl md:text-5xl font-bold text-white mb-4"
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          {entityIcon} Let's create your {entityType.toLowerCase()}!
        </motion.h1>

        <motion.div
          className="bg-white bg-opacity-10 backdrop-blur-md rounded-lg p-8 border border-white border-opacity-20"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {/* Name Input */}
          <div className="mb-6">
            <label className="block text-white text-lg font-medium mb-3">
              {entityType} Name
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder={defaultName}
              className="text-lg"
              size="lg"
              variant="bordered"
              classNames={{
                input: "text-white placeholder:text-white/50",
                inputWrapper: "bg-white/10 border-white/30 hover:border-white/50 focus-within:border-white/70"
              }}
            />
          </div>

          {/* Quick Start Options */}
          <div className="mb-8">
            <div className="flex items-start space-x-3">
              <Checkbox
                isSelected={formData.useQuickStart}
                onValueChange={handleQuickStartToggle}
                classNames={{
                  wrapper: "bg-white/10 border-white/30",
                  icon: "text-white"
                }}
              />
              <div className="text-left">
                <div className="text-white font-medium mb-2">Quick Start</div>
                <div className="text-white/70 text-sm space-y-1">
                  {isAlliance ? (
                    <>
                      <div>☑ Invite team members</div>
                      <div>☑ Set up shared workspace</div>
                      <div>☑ Create first project</div>
                    </>
                  ) : (
                    <>
                      <div>☑ Use standard settings</div>
                      <div>☑ Set up basic tracking</div>
                      <div>☑ Create first mission</div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 justify-center">
            <Button
              onClick={handleSubmit}
              size="lg"
              className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-3 text-lg font-medium"
              isLoading={isLoading}
              disabled={isLoading || !formData.name.trim()}
            >
              {isLoading ? `Creating ${entityType}...` : `Create ${entityType}`}
            </Button>

            <Button
              onClick={onCancel}
              variant="ghost"
              className="text-white/70 hover:text-white hover:bg-white/10 px-6 py-3"
              size="lg"
            >
              Customize
            </Button>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default OnboardingWizard;
