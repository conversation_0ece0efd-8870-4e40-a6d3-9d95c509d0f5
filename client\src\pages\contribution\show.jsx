import axios from "axios";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";
import moment from "moment/moment";
import { getAuth } from "firebase/auth";
import LoadingAnimation from "../../components/layout/LoadingAnimation";

const ContributionPage = () => {
  const { id } = useParams();
  const auth = getAuth();
  const [contribution, setContribution] = useState(null);
  const [canEdit, setCanEdit] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchContribution = async () => {
      setError(null); // Reset error state
      try {
        const user = auth.currentUser;
        if (!user) {
          setError("User not authenticated. Please log in.");
          return;
        }

        const token = await user.getIdToken();
        const { data } = await axios.get(`/contribution/${id}`, {
          headers: { Authorization: `Bearer ${token}` },
        });

        setContribution(data.contribution);

        setCanEdit(
          data.user?.isAdmin || data.contribution?.createdBy === data.user?._id
        );
      } catch (error) {
        console.error("Error fetching contribution data:", error);
        setError("Failed to load contribution data.");
      }
    };

    fetchContribution();
  }, [id]); // Add `id` to the dependency array to refetch if it changes

  if (error) {
    return (
      <div className="alert alert-danger text-center" role="alert">
        {error}
      </div>
    );
  }

  return (
    <>
      {contribution ? (
        <div className="container mt-5">
          <div className="card shadow-lg border-0 rounded-lg">
            <div className="card-body">
              <h3 className="card-title text-center mb-4">
                Contribution Details
              </h3>

              <div className="mb-3">
                <strong>Contribution ID:</strong>
                <p className="mb-0">{contribution._id}</p>
              </div>

              <div className="mb-3">
                <strong>Contributor:</strong>
                <p className="mb-0">
                  <Link
                    to={`/user/${contribution.contributor?._id}`}
                    className="link-primary"
                  >
                    {contribution.contributor?.displayName || "[Deleted User]"}
                  </Link>
                </p>
              </div>

              <div className="mb-3">
                <strong>Hours:</strong>
                <p className="mb-0">{contribution.hours}</p>
              </div>

              <div className="mb-3">
                <strong>Project:</strong>
                {contribution.project && contribution.project !== "" ? (
                  <Link to={`/project/${contribution.project._id}`}>
                    <p className="mb-0">{contribution.project.title}</p>
                  </Link>
                ) : (
                  <p>[Deleted Project]</p>
                )}
              </div>

              <div className="mb-3">
                <strong>Date Added:</strong>
                <p className="mb-0">
                  {moment(contribution.dateCreated).format("l")}
                </p>
              </div>

              {canEdit && (
                <div className="mb-4">
                  <Link to={`/contribution/${id}/edit`}>Edit Contribution</Link>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <LoadingAnimation />
      )}
    </>
  );
};

export default ContributionPage;
