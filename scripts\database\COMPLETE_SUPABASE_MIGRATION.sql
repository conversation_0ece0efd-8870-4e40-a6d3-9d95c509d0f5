-- COMPLETE SUPABASE MIGRATION SCRIPT
-- Day 3 - Run this entire script in Supabase Dashboard SQL Editor
-- This will create all compliance tables and fix RLS policies

-- ============================================================================
-- STEP 1: FIX RLS POLICIES (Fix infinite recursion)
-- ============================================================================

-- Drop problematic team_members policies
DROP POLICY IF EXISTS "team_members_select_policy" ON public.team_members;
DROP POLICY IF EXISTS "team_members_insert_policy" ON public.team_members;
DROP POLICY IF EXISTS "team_members_update_policy" ON public.team_members;
DROP POLICY IF EXISTS "team_members_delete_policy" ON public.team_members;

-- Create simple, non-recursive team_members policies
CREATE POLICY "team_members_simple_policy" ON public.team_members
    FOR ALL USING (user_id = auth.uid());

-- ============================================================================
-- STEP 2: CREATE COMPANIES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    legal_name TEXT NOT NULL,
    tax_id TEXT NOT NULL UNIQUE,
    company_type TEXT NOT NULL CHECK (company_type IN ('corporation', 'llc', 'partnership', 'sole_proprietorship')),
    incorporation_state TEXT,
    incorporation_country TEXT DEFAULT 'US',
    incorporation_date DATE,
    doing_business_as TEXT,
    industry_classification TEXT,
    business_description TEXT,
    website_url TEXT,
    primary_address JSONB NOT NULL,
    mailing_address JSONB,
    primary_email TEXT NOT NULL,
    primary_phone TEXT,
    fiscal_year_end DATE DEFAULT (CURRENT_DATE + INTERVAL '1 year'),
    accounting_method TEXT DEFAULT 'accrual' CHECK (accounting_method IN ('accrual', 'cash')),
    base_currency TEXT DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    dissolution_date DATE,
    compliance_status TEXT DEFAULT 'active' CHECK (compliance_status IN ('active', 'suspended', 'dissolved')),
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable RLS and create simple policy
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
CREATE POLICY "companies_simple_policy" ON public.companies FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.companies TO authenticated;

-- ============================================================================
-- STEP 3: ADD COMPANY FIELDS TO TEAMS TABLE
-- ============================================================================

-- Add company-related columns to teams table
ALTER TABLE public.teams 
ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES public.companies(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS is_business_entity BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS alliance_type TEXT DEFAULT 'emerging' CHECK (alliance_type IN ('emerging', 'established', 'solo'));

-- ============================================================================
-- STEP 4: CREATE FINANCIAL TRANSACTIONS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.financial_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('commission', 'recurring_fee', 'royalty', 'expense', 'refund', 'bonus', 'salary')),
    transaction_category TEXT DEFAULT 'business_payment' CHECK (transaction_category IN ('business_payment', 'contractor_payment', 'employee_payment', 'expense_reimbursement')),
    gross_amount DECIMAL(12,2) NOT NULL CHECK (gross_amount >= 0),
    tax_amount DECIMAL(12,2) DEFAULT 0 CHECK (tax_amount >= 0),
    net_amount DECIMAL(12,2) NOT NULL CHECK (net_amount >= 0),
    currency TEXT DEFAULT 'USD',
    exchange_rate DECIMAL(10,6) DEFAULT 1.0,
    tax_category TEXT CHECK (tax_category IN ('1099-NEC', '1099-MISC', 'W2', 'exempt', 'international')),
    requires_1099 BOOLEAN DEFAULT false,
    requires_w2 BOOLEAN DEFAULT false,
    backup_withholding_rate DECIMAL(5,2) DEFAULT 0 CHECK (backup_withholding_rate >= 0 AND backup_withholding_rate <= 100),
    tax_year INTEGER DEFAULT EXTRACT(YEAR FROM CURRENT_DATE),
    payer_company_id UUID REFERENCES public.companies(id),
    payee_user_id UUID REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'processing', 'paid', 'failed', 'cancelled', 'disputed')),
    processed_at TIMESTAMP WITH TIME ZONE,
    payment_method TEXT CHECK (payment_method IN ('ach', 'wire', 'check', 'paypal', 'stripe', 'manual')),
    external_transaction_id TEXT,
    approval_required BOOLEAN DEFAULT true,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_notes TEXT,
    description TEXT NOT NULL,
    reference_number TEXT,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "financial_transactions_simple_policy" ON public.financial_transactions FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.financial_transactions TO authenticated;

-- ============================================================================
-- STEP 5: CREATE COMMISSION PAYMENTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.commission_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    financial_transaction_id UUID NOT NULL REFERENCES public.financial_transactions(id) ON DELETE CASCADE,
    sales_amount DECIMAL(12,2) NOT NULL CHECK (sales_amount >= 0),
    commission_rate DECIMAL(5,2) NOT NULL CHECK (commission_rate >= 0 AND commission_rate <= 100),
    commission_amount DECIMAL(12,2) NOT NULL CHECK (commission_amount >= 0),
    sale_date DATE NOT NULL,
    product_or_service TEXT NOT NULL,
    client_reference TEXT,
    sales_rep_id UUID NOT NULL REFERENCES auth.users(id),
    payment_due_date DATE,
    payment_terms TEXT DEFAULT 'net_30' CHECK (payment_terms IN ('immediate', 'net_15', 'net_30', 'net_60')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.commission_payments ENABLE ROW LEVEL SECURITY;
CREATE POLICY "commission_payments_simple_policy" ON public.commission_payments FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.commission_payments TO authenticated;

-- ============================================================================
-- STEP 6: CREATE RECURRING FEES TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.recurring_fees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    payee_user_id UUID NOT NULL REFERENCES auth.users(id),
    fee_type TEXT NOT NULL CHECK (fee_type IN ('talent_fee', 'subscription', 'retainer', 'maintenance')),
    amount DECIMAL(12,2) NOT NULL CHECK (amount >= 0),
    currency TEXT DEFAULT 'USD',
    frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'annually')),
    start_date DATE NOT NULL,
    end_date DATE,
    next_payment_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    paused_until DATE,
    description TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.recurring_fees ENABLE ROW LEVEL SECURITY;
CREATE POLICY "recurring_fees_simple_policy" ON public.recurring_fees FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.recurring_fees TO authenticated;

-- ============================================================================
-- STEP 7: CREATE TAX DOCUMENTS TABLE
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.tax_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    payee_user_id UUID NOT NULL REFERENCES auth.users(id),
    payee_company_id UUID REFERENCES public.companies(id),
    document_type TEXT NOT NULL CHECK (document_type IN ('1099-NEC', '1099-MISC', 'W2', '1042-S')),
    tax_year INTEGER NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL CHECK (total_amount >= 0),
    nonemployee_compensation DECIMAL(12,2) DEFAULT 0,
    rents DECIMAL(12,2) DEFAULT 0,
    royalties DECIMAL(12,2) DEFAULT 0,
    other_income DECIMAL(12,2) DEFAULT 0,
    backup_withholding DECIMAL(12,2) DEFAULT 0,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'generated', 'sent', 'corrected')),
    generated_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    document_url TEXT,
    correction_of UUID REFERENCES public.tax_documents(id),
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

ALTER TABLE public.tax_documents ENABLE ROW LEVEL SECURITY;
CREATE POLICY "tax_documents_simple_policy" ON public.tax_documents FOR ALL USING (true);
GRANT SELECT, INSERT, UPDATE ON public.tax_documents TO authenticated;

-- ============================================================================
-- STEP 8: CREATE PERFORMANCE INDEXES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_companies_tax_id ON public.companies(tax_id);
CREATE INDEX IF NOT EXISTS idx_companies_active ON public.companies(is_active);
CREATE INDEX IF NOT EXISTS idx_teams_company_id ON public.teams(company_id);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_company ON public.financial_transactions(company_id, tax_year);
CREATE INDEX IF NOT EXISTS idx_financial_transactions_payee ON public.financial_transactions(payee_user_id, tax_year);
CREATE INDEX IF NOT EXISTS idx_commission_payments_sales_rep ON public.commission_payments(sales_rep_id, sale_date);
CREATE INDEX IF NOT EXISTS idx_recurring_fees_company ON public.recurring_fees(company_id, is_active);
CREATE INDEX IF NOT EXISTS idx_recurring_fees_next_payment ON public.recurring_fees(next_payment_date) WHERE is_active = true;

-- ============================================================================
-- STEP 9: INSERT VRC SAMPLE DATA
-- ============================================================================

INSERT INTO public.companies (
    legal_name, 
    tax_id, 
    company_type, 
    incorporation_state, 
    incorporation_country,
    doing_business_as, 
    industry_classification, 
    business_description, 
    website_url,
    primary_address, 
    primary_email, 
    primary_phone, 
    fiscal_year_end, 
    accounting_method
) VALUES (
    'VRC Entertainment LLC', 
    '12-3456789', 
    'llc', 
    'CA', 
    'US',
    'VRC Films', 
    '512110', 
    'Independent film production and talent management company specializing in commission-based sales and recurring talent fees',
    'https://vrcfilms.com',
    '{"street": "123 Hollywood Blvd", "city": "Los Angeles", "state": "CA", "zip": "90028", "country": "US"}',
    '<EMAIL>', 
    '******-123-4567', 
    '2024-12-31', 
    'accrual'
) ON CONFLICT (tax_id) DO UPDATE SET
    business_description = EXCLUDED.business_description,
    updated_at = now();

-- ============================================================================
-- STEP 10: ADD COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE public.companies IS 'Legal business entities with full tax compliance for VRC and other clients';
COMMENT ON TABLE public.financial_transactions IS 'Tax-compliant financial transactions with automatic 1099 tracking';
COMMENT ON TABLE public.commission_payments IS 'Commission payment tracking for sales teams with VRC-specific features';
COMMENT ON TABLE public.recurring_fees IS 'Recurring fee management for talent and subscriptions';
COMMENT ON TABLE public.tax_documents IS 'Tax document generation and management for compliance reporting';

-- ============================================================================
-- VERIFICATION QUERIES (Run these to verify success)
-- ============================================================================

-- Check if all tables were created successfully
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('companies', 'financial_transactions', 'commission_payments', 'recurring_fees', 'tax_documents')
ORDER BY tablename;

-- Check if VRC company was inserted
SELECT 
    legal_name,
    tax_id,
    company_type,
    primary_email,
    created_at
FROM public.companies 
WHERE tax_id = '12-3456789';

-- Check teams table has new columns
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'teams' 
AND column_name IN ('company_id', 'is_business_entity', 'alliance_type')
ORDER BY column_name;
