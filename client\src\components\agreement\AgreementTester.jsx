import React, { useState, useEffect } from 'react';

import { toast } from 'react-hot-toast';
import { generateTestAgreement, TEST_PROJECTS } from '../../utils/agreement/testAgreementGenerator';
import { TEMPLATE_TYPES } from '../../utils/agreement/templateManager';
import PDFPreview from './PDFPreview';
import { markdownToHTML } from '../../utils/pdf/pdfGenerator';

/**
 * Agreement Tester Component
 *
 * This component provides a UI for testing agreement generation with different project types.
 */
const AgreementTester = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedProjectType, setSelectedProjectType] = useState('game');
  const [selectedTemplate, setSelectedTemplate] = useState(TEMPLATE_TYPES.STANDARD);
  const [agreementText, setAgreementText] = useState('');
  const [htmlContent, setHtmlContent] = useState('');
  const [verificationResults, setVerificationResults] = useState(null);
  const [customProject, setCustomProject] = useState(null);
  const [showCustomForm, setShowCustomForm] = useState(false);
  const [activeTab, setActiveTab] = useState('preview');

  // Form fields for custom project
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [projectType, setProjectType] = useState('game');
  const [companyName, setCompanyName] = useState('');
  const [companyAddress, setCompanyAddress] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zip, setZip] = useState('');

  // Generate agreement when project type or template changes
  useEffect(() => {
    if (!showCustomForm) {
      handleGenerateAgreement();
    }
  }, [selectedProjectType, selectedTemplate, showCustomForm]);

  // Convert markdown to HTML when agreement text changes
  useEffect(() => {
    if (agreementText) {
      const html = markdownToHTML(agreementText);
      setHtmlContent(html);
    }
  }, [agreementText]);

  // Handle project type change
  const handleProjectTypeChange = (e) => {
    setSelectedProjectType(e.target.value);
  };

  // Handle template change
  const handleTemplateChange = (e) => {
    setSelectedTemplate(e.target.value);
  };

  // Handle custom project form submission
  const handleCustomProjectSubmit = (e) => {
    e.preventDefault();

    // Create custom project object
    const custom = {
      name: projectName,
      description: projectDescription,
      projectType: projectType,
      company_name: companyName,
      address: companyAddress,
      contact_email: contactEmail,
      city: city,
      state: state,
      zip: zip
    };

    setCustomProject(custom);
    handleGenerateAgreement(custom);
  };

  // Generate agreement
  const handleGenerateAgreement = async (customProjectData = null) => {
    setLoading(true);
    setError(null);

    try {
      // Determine which project type to use
      const projectTypeToUse = customProjectData ? customProjectData.projectType : selectedProjectType;

      // Generate the agreement
      const result = await generateTestAgreement(projectTypeToUse, {
        templateType: selectedTemplate,
        projectOverrides: customProjectData || {},
        userName: 'Test User',
        userEmail: '<EMAIL>'
      });

      // Set the agreement text and verification results
      setAgreementText(result.agreement);
      setVerificationResults(result.verificationResults);

      toast.success('Agreement generated successfully');
    } catch (err) {
      console.error('Error generating agreement:', err);
      setError('Failed to generate agreement: ' + err.message);
      toast.error('Failed to generate agreement');
    } finally {
      setLoading(false);
    }
  };

  // Reset custom project form
  const handleResetCustomForm = () => {
    setProjectName('');
    setProjectDescription('');
    setProjectType('game');
    setCompanyName('');
    setCompanyAddress('');
    setContactEmail('');
    setCity('');
    setState('');
    setZip('');
    setCustomProject(null);
    setShowCustomForm(false);
  };

  return (
    <Card className="mb-4">
      <Card.Header>
        <h5 className="mb-0">Agreement Tester</h5>
      </Card.Header>
      <Card.Body>
        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Row className="mb-3">
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Project Type</Form.Label>
              <Form.Select
                value={selectedProjectType}
                onChange={handleProjectTypeChange}
                disabled={loading || showCustomForm}
              >
                <option value="game">Game</option>
                <option value="music">Music</option>
                <option value="software">Software</option>
                <option value="film">Film</option>
              </Form.Select>
            </Form.Group>
          </Col>
          <Col md={6}>
            <Form.Group className="mb-3">
              <Form.Label>Template Type</Form.Label>
              <Form.Select
                value={selectedTemplate}
                onChange={handleTemplateChange}
                disabled={loading}
              >
                <option value={TEMPLATE_TYPES.STANDARD}>Standard</option>
                <option value={TEMPLATE_TYPES.SIMPLIFIED}>Simplified</option>
                <option value={TEMPLATE_TYPES.DETAILED}>Detailed</option>
              </Form.Select>
            </Form.Group>
          </Col>
        </Row>

        <div className="mb-3">
          <Button
            variant={showCustomForm ? "secondary" : "primary"}
            onClick={() => setShowCustomForm(!showCustomForm)}
            className="me-2"
          >
            {showCustomForm ? "Use Predefined Project" : "Use Custom Project"}
          </Button>

          <Button
            variant="primary"
            onClick={() => handleGenerateAgreement(customProject)}
            disabled={loading}
          >
            {loading ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                Generating...
              </>
            ) : "Generate Agreement"}
          </Button>
        </div>

        {showCustomForm && (
          <Card className="mb-3">
            <Card.Header>Custom Project Details</Card.Header>
            <Card.Body>
              <Form onSubmit={handleCustomProjectSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Project Name</Form.Label>
                      <Form.Control
                        type="text"
                        value={projectName}
                        onChange={(e) => setProjectName(e.target.value)}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Project Type</Form.Label>
                      <Form.Select
                        value={projectType}
                        onChange={(e) => setProjectType(e.target.value)}
                      >
                        <option value="game">Game</option>
                        <option value="music">Music</option>
                        <option value="software">Software</option>
                        <option value="film">Film</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Project Description</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    value={projectDescription}
                    onChange={(e) => setProjectDescription(e.target.value)}
                    required
                  />
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Company Name</Form.Label>
                      <Form.Control
                        type="text"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Contact Email</Form.Label>
                      <Form.Control
                        type="email"
                        value={contactEmail}
                        onChange={(e) => setContactEmail(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Company Address</Form.Label>
                  <Form.Control
                    type="text"
                    value={companyAddress}
                    onChange={(e) => setCompanyAddress(e.target.value)}
                  />
                </Form.Group>

                <Row>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>City</Form.Label>
                      <Form.Control
                        type="text"
                        value={city}
                        onChange={(e) => setCity(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>State</Form.Label>
                      <Form.Control
                        type="text"
                        value={state}
                        onChange={(e) => setState(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>ZIP Code</Form.Label>
                      <Form.Control
                        type="text"
                        value={zip}
                        onChange={(e) => setZip(e.target.value)}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex justify-content-end">
                  <Button variant="secondary" onClick={handleResetCustomForm} className="me-2">
                    Reset
                  </Button>
                  <Button variant="primary" type="submit">
                    Apply Custom Project
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        )}

        {verificationResults && (
          <Alert variant={verificationResults.verified ? "success" : "warning"} className="mb-3">
            <Alert.Heading>{verificationResults.verified ? "Project Verified" : "Project Verification Issues"}</Alert.Heading>
            <p>{verificationResults.message}</p>
            {verificationResults.discrepancies && verificationResults.discrepancies.length > 0 && (
              <>
                <hr />
                <p className="mb-0">Discrepancies found:</p>
                <ul>
                  {verificationResults.discrepancies.map((discrepancy, index) => (
                    <li key={index}>{discrepancy}</li>
                  ))}
                </ul>
              </>
            )}
          </Alert>
        )}

        <Tabs
          activeKey={activeTab}
          onSelect={(k) => setActiveTab(k)}
          className="mb-3"
        >
          <Tab eventKey="preview" title="PDF Preview">
            {htmlContent ? (
              <PDFPreview htmlContent={htmlContent} />
            ) : (
              <div className="text-center p-5">
                <p>No agreement generated yet</p>
              </div>
            )}
          </Tab>
          <Tab eventKey="markdown" title="Markdown">
            <div className="border p-3 bg-light" style={{ maxHeight: '500px', overflow: 'auto' }}>
              <pre>{agreementText}</pre>
            </div>
          </Tab>
        </Tabs>
      </Card.Body>
    </Card>
  );
};

export default AgreementTester;
