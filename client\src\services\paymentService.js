// PaymentService - Frontend integration for payment system
import { supabase } from '../utils/supabase/supabase.utils';

class PaymentService {
  constructor() {
    this.baseUrl = '/.netlify/functions';
    this.listeners = new Set();
    this.currentUser = null;
  }

  // Get auth headers for API calls
  async getAuthHeaders() {
    const { data: { session } } = await supabase.auth.getSession();
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session?.access_token}`
    };
  }

  // Initialize payment service with user context
  async initialize(user) {
    this.currentUser = user;
    if (user) {
      await this.loadPaymentMethods();
    }
  }

  // Teller Integration Methods
  async createLinkToken(products = ['auth', 'transactions', 'transfer']) {
    try {
      const headers = await this.getAuthHeaders();

      const response = await fetch(`${this.baseUrl}/teller-link/create-link-token`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          products,
          country_codes: ['US'],
          language: 'en'
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.link_token;
    } catch (error) {
      console.error('Error creating link token:', error);
      throw error;
    }
  }

  async exchangePublicToken(publicToken) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/teller-link/exchange-token`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          public_token: publicToken
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('payment_method_added', result);
      
      return result;
    } catch (error) {
      console.error('Error exchanging public token:', error);
      throw error;
    }
  }

  async loadPaymentMethods() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/teller-payments/accounts`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.accounts;
    } catch (error) {
      console.error('Error loading payment methods:', error);
      throw error;
    }
  }

  // Payment Processing Methods
  async initiatePayment(paymentData) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/teller-payments/initiate`, {
        method: 'POST',
        headers,
        body: JSON.stringify(paymentData)
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('payment_initiated', result);
      
      return result;
    } catch (error) {
      console.error('Error initiating payment:', error);
      throw error;
    }
  }

  async getPaymentStatus(paymentId) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/teller-payments/status/${paymentId}`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.payment;
    } catch (error) {
      console.error('Error getting payment status:', error);
      throw error;
    }
  }

  // Escrow Management Methods
  async createEscrowAccount(escrowData) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/escrow-management`, {
        method: 'POST',
        headers,
        body: JSON.stringify(escrowData)
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('escrow_created', result);
      
      return result;
    } catch (error) {
      console.error('Error creating escrow account:', error);
      throw error;
    }
  }

  async getEscrowAccounts() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/escrow-management`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.escrows;
    } catch (error) {
      console.error('Error getting escrow accounts:', error);
      throw error;
    }
  }

  async releaseEscrowFunds(escrowId, releaseData) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/escrow-releases`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          escrow_id: escrowId,
          ...releaseData
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('escrow_released', result);
      
      return result;
    } catch (error) {
      console.error('Error releasing escrow funds:', error);
      throw error;
    }
  }

  // Financial Transaction Methods
  async getTransactionHistory(filters = {}) {
    try {
      const headers = await this.getAuthHeaders();
      
      const queryParams = new URLSearchParams(filters);
      const response = await fetch(`${this.baseUrl}/financial-transactions?${queryParams}`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.transactions;
    } catch (error) {
      console.error('Error getting transaction history:', error);
      throw error;
    }
  }

  async getFinancialSummary() {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/financial-transactions/summary`, {
        method: 'GET',
        headers
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.summary;
    } catch (error) {
      console.error('Error getting financial summary:', error);
      throw error;
    }
  }

  // Revenue Distribution Methods
  async calculateRevenue(projectId, revenueData) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/revenue-distribution/calculate`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          project_id: projectId,
          ...revenueData
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      return result.distribution;
    } catch (error) {
      console.error('Error calculating revenue distribution:', error);
      throw error;
    }
  }

  async distributeRevenue(distributionId) {
    try {
      const headers = await this.getAuthHeaders();
      
      const response = await fetch(`${this.baseUrl}/revenue-distribution/distribute`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          distribution_id: distributionId
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      this.notifyListeners('revenue_distributed', result);
      
      return result;
    } catch (error) {
      console.error('Error distributing revenue:', error);
      throw error;
    }
  }

  // Utility Methods
  formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount);
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getTransactionStatusColor(status) {
    const colors = {
      completed: 'success',
      processing: 'warning',
      pending: 'default',
      failed: 'danger',
      cancelled: 'default'
    };
    return colors[status] || 'default';
  }

  // Event listener management
  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  notifyListeners(event, data) {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in payment service listener:', error);
      }
    });
  }

  // Cleanup
  cleanup() {
    this.listeners.clear();
    this.currentUser = null;
  }

  // Validation helpers
  validatePaymentAmount(amount) {
    const numAmount = parseFloat(amount);
    return numAmount > 0 && numAmount <= 10000; // Max $10k per transaction
  }

  validateBankAccount(accountData) {
    return accountData.account_id && 
           accountData.routing_number && 
           accountData.account_number;
  }

  // Error handling helpers
  handlePaymentError(error) {
    const errorMessages = {
      'INSUFFICIENT_FUNDS': 'Insufficient funds in account',
      'INVALID_ACCOUNT': 'Invalid bank account information',
      'TRANSACTION_LIMIT_EXCEEDED': 'Transaction limit exceeded',
      'ACCOUNT_LOCKED': 'Account is temporarily locked',
      'NETWORK_ERROR': 'Network connection error'
    };

    return errorMessages[error.code] || error.message || 'An unexpected error occurred';
  }
}

// Create singleton instance
const paymentService = new PaymentService();

export default paymentService;
