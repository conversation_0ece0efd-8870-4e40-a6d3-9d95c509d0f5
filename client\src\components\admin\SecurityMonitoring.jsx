import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { secureApiRequest } from '../../utils/security/securityUtils';
import VulnerabilityScanner from '../../utils/security/vulnerabilityScanner';
import { toast } from 'react-hot-toast';

/**
 * Security Monitoring Dashboard
 * 
 * Real-time security monitoring and threat detection interface
 * for administrators to monitor platform security status
 */
const SecurityMonitoring = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [securityData, setSecurityData] = useState({
    overview: {
      threatLevel: 'low',
      activeThreats: 0,
      blockedAttacks: 156,
      securityScore: 92,
      lastScan: '2 hours ago'
    },
    recentEvents: [],
    vulnerabilities: [],
    systemHealth: {
      authSystem: 'healthy',
      apiSecurity: 'healthy',
      databaseSecurity: 'healthy',
      networkSecurity: 'warning'
    }
  });
  
  const [scanInProgress, setScanInProgress] = useState(false);
  const [scanResults, setScanResults] = useState(null);

  // Load security monitoring data
  const loadSecurityData = async () => {
    try {
      setLoading(true);
      
      // Load security events
      const eventsResponse = await secureApiRequest('/.netlify/functions/security-events');
      
      // Load vulnerability data
      const vulnResponse = await secureApiRequest('/.netlify/functions/vulnerability-scan');
      
      setSecurityData(prevData => ({
        ...prevData,
        recentEvents: eventsResponse.events || [],
        vulnerabilities: vulnResponse.vulnerabilities || []
      }));
      
    } catch (error) {
      console.error('Error loading security data:', error);
      toast.error('Failed to load security monitoring data');
    } finally {
      setLoading(false);
    }
  };

  // Run security vulnerability scan
  const runSecurityScan = async () => {
    try {
      setScanInProgress(true);
      toast.info('Starting comprehensive security scan...');
      
      const scanner = new VulnerabilityScanner();
      const results = await scanner.runFullScan();
      
      setScanResults(results);
      toast.success('Security scan completed successfully');
      
      // Update security data with scan results
      setSecurityData(prevData => ({
        ...prevData,
        vulnerabilities: results.vulnerabilities.critical.concat(
          results.vulnerabilities.high,
          results.vulnerabilities.medium
        ),
        overview: {
          ...prevData.overview,
          securityScore: Math.max(0, 100 - results.summary.riskScore),
          lastScan: 'Just now'
        }
      }));
      
    } catch (error) {
      console.error('Error running security scan:', error);
      toast.error('Security scan failed');
    } finally {
      setScanInProgress(false);
    }
  };

  // Get threat level color
  const getThreatLevelColor = (level) => {
    const colors = {
      'low': 'success',
      'medium': 'warning',
      'high': 'danger',
      'critical': 'danger'
    };
    return colors[level] || 'default';
  };

  // Get system health color
  const getHealthColor = (status) => {
    const colors = {
      'healthy': 'success',
      'warning': 'warning',
      'critical': 'danger'
    };
    return colors[status] || 'default';
  };

  // Get vulnerability severity color
  const getSeverityColor = (severity) => {
    const colors = {
      'critical': 'danger',
      'high': 'warning',
      'medium': 'primary',
      'low': 'default'
    };
    return colors[severity] || 'default';
  };

  useEffect(() => {
    if (currentUser) {
      loadSecurityData();
      
      // Set up real-time updates
      const interval = setInterval(loadSecurityData, 30000); // Update every 30 seconds
      return () => clearInterval(interval);
    }
  }, [currentUser]);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">🛡️</span>
                <Chip color={getThreatLevelColor(securityData.overview.threatLevel)} variant="flat" size="sm">
                  {securityData.overview.threatLevel.toUpperCase()}
                </Chip>
              </div>
              <div>
                <div className="text-sm text-default-600">Threat Level</div>
                <div className="text-2xl font-bold text-green-600">
                  {securityData.overview.threatLevel}
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">🔒</span>
                <Badge color="primary" content={securityData.overview.securityScore}>
                  <span></span>
                </Badge>
              </div>
              <div>
                <div className="text-sm text-default-600">Security Score</div>
                <div className="text-2xl font-bold text-blue-600">
                  {securityData.overview.securityScore}/100
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">🚫</span>
                <Chip color="warning" variant="flat" size="sm">Blocked</Chip>
              </div>
              <div>
                <div className="text-sm text-default-600">Blocked Attacks</div>
                <div className="text-2xl font-bold text-orange-600">
                  {securityData.overview.blockedAttacks.toLocaleString()}
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-800/20">
            <CardBody className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-2xl">⚠️</span>
                <Chip color="secondary" variant="flat" size="sm">Active</Chip>
              </div>
              <div>
                <div className="text-sm text-default-600">Active Threats</div>
                <div className="text-2xl font-bold text-purple-600">
                  {securityData.overview.activeThreats}
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Security Actions */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <h3 className="text-lg font-semibold">🔍 Security Actions</h3>
            <div className="flex gap-2">
              <Button
                color="primary"
                variant="flat"
                onClick={runSecurityScan}
                isLoading={scanInProgress}
                startContent={<span>🔍</span>}
              >
                {scanInProgress ? 'Scanning...' : 'Run Security Scan'}
              </Button>
              <Button
                color="secondary"
                variant="flat"
                onClick={loadSecurityData}
                startContent={<span>🔄</span>}
              >
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardBody>
          <div className="text-sm text-default-600">
            Last security scan: {securityData.overview.lastScan}
          </div>
        </CardBody>
      </Card>

      {/* System Health */}
      <Card>
        <CardHeader className="pb-3">
          <h3 className="text-lg font-semibold">💚 System Health</h3>
        </CardHeader>
        <CardBody className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(securityData.systemHealth).map(([system, status]) => (
              <div key={system} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium capitalize">{system.replace(/([A-Z])/g, ' $1')}</div>
                  <div className="text-sm text-default-600">Security Status</div>
                </div>
                <Chip color={getHealthColor(status)} variant="flat" size="sm">
                  {status}
                </Chip>
              </div>
            ))}
          </div>
        </CardBody>
      </Card>

      {/* Vulnerabilities */}
      {securityData.vulnerabilities.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">🚨 Security Vulnerabilities</h3>
              <Badge color="danger" content={securityData.vulnerabilities.length}>
                <span></span>
              </Badge>
            </div>
          </CardHeader>
          <CardBody className="space-y-3">
            {securityData.vulnerabilities.slice(0, 5).map((vuln, index) => (
              <motion.div
                key={vuln.id || index}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="p-3 border rounded-lg"
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <div className="font-medium">{vuln.type?.replace(/_/g, ' ')}</div>
                    <div className="text-sm text-default-600">{vuln.description}</div>
                  </div>
                  <Chip color={getSeverityColor(vuln.severity)} variant="flat" size="sm">
                    {vuln.severity}
                  </Chip>
                </div>
                {vuln.recommendation && (
                  <div className="text-sm text-default-500 mt-2">
                    💡 {vuln.recommendation}
                  </div>
                )}
              </motion.div>
            ))}
          </CardBody>
        </Card>
      )}

      {/* Recent Security Events */}
      <Card>
        <CardHeader className="pb-3">
          <h3 className="text-lg font-semibold">📋 Recent Security Events</h3>
        </CardHeader>
        <CardBody className="space-y-3">
          {securityData.recentEvents.length > 0 ? (
            securityData.recentEvents.slice(0, 10).map((event, index) => (
              <motion.div
                key={event.id || index}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className="p-3 border rounded-lg"
              >
                <div className="flex items-start justify-between">
                  <div>
                    <div className="font-medium">{event.event_type?.replace(/_/g, ' ')}</div>
                    <div className="text-sm text-default-600">{event.event_description}</div>
                    <div className="text-xs text-default-500 mt-1">
                      {new Date(event.created_at).toLocaleString()}
                    </div>
                  </div>
                  <Chip color={getSeverityColor(event.severity)} variant="flat" size="sm">
                    {event.severity}
                  </Chip>
                </div>
              </motion.div>
            ))
          ) : (
            <div className="text-center text-default-500 py-8">
              No recent security events
            </div>
          )}
        </CardBody>
      </Card>

      {/* Scan Results Modal */}
      {scanResults && (
        <Card className="border-2 border-primary">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">🔍 Security Scan Results</h3>
              <Button
                color="default"
                variant="light"
                size="sm"
                onClick={() => setScanResults(null)}
              >
                ✕
              </Button>
            </div>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{scanResults.summary.criticalCount}</div>
                <div className="text-sm text-default-600">Critical</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{scanResults.summary.highCount}</div>
                <div className="text-sm text-default-600">High</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{scanResults.summary.mediumCount}</div>
                <div className="text-sm text-default-600">Medium</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{scanResults.summary.lowCount}</div>
                <div className="text-sm text-default-600">Low</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{scanResults.summary.riskScore}</div>
                <div className="text-sm text-default-600">Risk Score</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Recommendations:</h4>
              {scanResults.recommendations.map((rec, index) => (
                <div key={index} className="text-sm text-default-600">
                  {rec}
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default SecurityMonitoring;
