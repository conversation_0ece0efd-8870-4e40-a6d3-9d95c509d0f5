import { test, expect } from '@playwright/test';

// Test configuration
const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  
  // Check if we need to authenticate
  const needsAuth = await page.locator('input[type="email"]').isVisible();
  
  if (needsAuth) {
    console.log('📝 Filling in credentials...');
    await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
    await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Wait for auth to complete
    
    // Verify authentication worked
    const stillNeedsAuth = await page.locator('input[type="email"]').isVisible();
    if (stillNeedsAuth) {
      throw new Error('Authentication failed');
    }
    
    console.log('✅ Authentication successful');
    return true;
  }
  
  console.log('✅ Already authenticated');
  return true;
}

// Helper function to check for console errors
async function checkConsoleErrors(page) {
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  return errors;
}

test.describe('Comprehensive Platform Audit', () => {
  let authWorking = false;
  let consoleErrors = [];

  test.beforeEach(async ({ page }) => {
    // Set up console error tracking
    consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    try {
      authWorking = await authenticate(page);
    } catch (error) {
      console.log('❌ Authentication failed:', error.message);
      authWorking = false;
    }
  });

  test('1. Authentication System', async ({ page }) => {
    expect(authWorking).toBe(true);
    console.log('✅ Authentication system working');
  });

  test('2. Dashboard Loading', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for main dashboard elements
    const dashboardElements = [
      '[data-testid="dashboard"]',
      '.bento-grid',
      '[class*="dashboard"]',
      'main',
      'nav'
    ];
    
    let foundDashboard = false;
    for (const selector of dashboardElements) {
      if (await page.locator(selector).count() > 0) {
        foundDashboard = true;
        console.log(`✅ Dashboard found with selector: ${selector}`);
        break;
      }
    }
    
    expect(foundDashboard).toBe(true);
  });

  test('3. Navigation System', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Test main navigation links
    const navLinks = [
      { text: 'Start', expected: '/start' },
      { text: 'Track', expected: '/track' },
      { text: 'Earn', expected: '/earn' }
    ];
    
    for (const link of navLinks) {
      const linkElement = page.locator(`text="${link.text}"`).first();
      if (await linkElement.count() > 0) {
        console.log(`✅ Found navigation link: ${link.text}`);
        
        // Test clicking the link
        await linkElement.click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        console.log(`📍 Navigated to: ${currentUrl}`);
        
        // Check if page loaded without errors
        const hasContent = await page.locator('body').count() > 0;
        expect(hasContent).toBe(true);
      } else {
        console.log(`⚠️ Navigation link not found: ${link.text}`);
      }
    }
  });

  test('4. Project Creation Flow', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(`${PRODUCTION_URL}/start`);
    await page.waitForLoadState('networkidle');
    
    // Look for project creation elements
    const projectElements = [
      'text="Create Project"',
      'text="New Project"',
      'text="Start Project"',
      '[data-testid="create-project"]',
      'button:has-text("Create")'
    ];
    
    let foundProjectCreation = false;
    for (const selector of projectElements) {
      if (await page.locator(selector).count() > 0) {
        foundProjectCreation = true;
        console.log(`✅ Project creation found with: ${selector}`);
        break;
      }
    }
    
    if (foundProjectCreation) {
      console.log('✅ Project creation flow accessible');
    } else {
      console.log('⚠️ Project creation flow not immediately visible');
    }
  });

  test('5. Studio/Team Management', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Test studio/team pages
    const studioUrls = [
      '/studios',
      '/teams',
      '/studios/create',
      '/teams/create'
    ];
    
    for (const url of studioUrls) {
      try {
        await page.goto(`${PRODUCTION_URL}${url}`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const hasContent = await page.locator('body').count() > 0;
        const hasError = await page.locator('text="Error"').count() > 0;
        
        if (hasContent && !hasError) {
          console.log(`✅ Studio page accessible: ${url}`);
        } else {
          console.log(`⚠️ Studio page issue: ${url}`);
        }
      } catch (error) {
        console.log(`❌ Studio page error: ${url} - ${error.message}`);
      }
    }
  });

  test('6. Database Connectivity', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for database-related errors in console
    await page.waitForTimeout(5000); // Wait for any async operations
    
    const dbErrors = consoleErrors.filter(error => 
      error.includes('404') || 
      error.includes('400') || 
      error.includes('user_activity') ||
      error.includes('team_members') ||
      error.includes('projects')
    );
    
    console.log(`📊 Database-related errors found: ${dbErrors.length}`);
    if (dbErrors.length > 0) {
      console.log('❌ Database errors:', dbErrors);
    } else {
      console.log('✅ No database connectivity issues detected');
    }
    
    expect(dbErrors.length).toBeLessThan(3); // Allow some minor errors
  });

  test('7. Console Error Analysis', async ({ page }) => {
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000);
    
    console.log(`📊 Total console errors: ${consoleErrors.length}`);
    
    // Categorize errors
    const criticalErrors = consoleErrors.filter(error => 
      error.includes('Failed to load') ||
      error.includes('Network error') ||
      error.includes('TypeError') ||
      error.includes('ReferenceError')
    );
    
    const warningErrors = consoleErrors.filter(error => 
      error.includes('Warning') ||
      error.includes('deprecated') ||
      error.includes('chunk')
    );
    
    console.log(`🚨 Critical errors: ${criticalErrors.length}`);
    console.log(`⚠️ Warning errors: ${warningErrors.length}`);
    
    if (criticalErrors.length > 0) {
      console.log('Critical errors found:', criticalErrors.slice(0, 5));
    }
    
    // Expect minimal critical errors
    expect(criticalErrors.length).toBeLessThan(5);
  });

  test('8. Performance Check', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    console.log(`⏱️ Page load time: ${loadTime}ms`);
    
    // Check for performance issues
    expect(loadTime).toBeLessThan(10000); // 10 seconds max
    
    if (loadTime < 3000) {
      console.log('✅ Excellent performance');
    } else if (loadTime < 5000) {
      console.log('✅ Good performance');
    } else {
      console.log('⚠️ Slow performance detected');
    }
  });

  test('9. Mobile Responsiveness', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check if content is visible and accessible
    const bodyWidth = await page.locator('body').boundingBox();
    const hasOverflow = bodyWidth && bodyWidth.width > 375;
    
    if (!hasOverflow) {
      console.log('✅ Mobile layout looks good');
    } else {
      console.log('⚠️ Potential mobile layout issues');
    }
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('10. Feature Availability Audit', async ({ page }) => {
    test.skip(!authWorking, 'Authentication required');
    
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
    
    // Check for key features
    const features = [
      { name: 'Projects', selectors: ['text="Projects"', 'text="Project"'] },
      { name: 'Teams', selectors: ['text="Teams"', 'text="Studio"'] },
      { name: 'Tasks', selectors: ['text="Tasks"', 'text="Mission"'] },
      { name: 'Revenue', selectors: ['text="Revenue"', 'text="Earn"'] },
      { name: 'Analytics', selectors: ['text="Analytics"', 'text="Insights"'] }
    ];
    
    for (const feature of features) {
      let found = false;
      for (const selector of feature.selectors) {
        if (await page.locator(selector).count() > 0) {
          found = true;
          break;
        }
      }
      
      if (found) {
        console.log(`✅ Feature available: ${feature.name}`);
      } else {
        console.log(`⚠️ Feature not visible: ${feature.name}`);
      }
    }
  });
});
