import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, Button, Input, Textarea, Chip, Card, CardBody } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Bounty Application Modal Component - Application Submission Interface
 * 
 * Features:
 * - Comprehensive application form with skill assessment
 * - Portfolio and experience validation
 * - Estimated timeline and budget proposal
 * - Skill compatibility analysis
 * - Application preview and submission
 */
const BountyApplicationModal = ({ bounty, isOpen, onClose, onSubmit, currentUser }) => {
  const [applicationData, setApplicationData] = useState({
    coverLetter: '',
    proposedTimeline: '',
    proposedBudget: bounty.value,
    portfolioLinks: [''],
    relevantExperience: '',
    skillsHighlight: [],
    questions: ''
  });

  const [skillCompatibility, setSkillCompatibility] = useState(null);

  // Mock user skills for compatibility analysis
  const userSkills = [
    { name: 'React', level: 'Expert', verified: true },
    { name: 'Python', level: 'Advanced', verified: true },
    { name: 'AI/ML', level: 'Intermediate', verified: false },
    { name: 'APIs', level: 'Expert', verified: true },
    { name: 'Data Science', level: 'Beginner', verified: false }
  ];

  // Calculate skill compatibility
  React.useEffect(() => {
    if (bounty && userSkills) {
      const requiredSkills = bounty.skillsRequired;
      const matchedSkills = [];
      const missingSkills = [];
      
      requiredSkills.forEach(required => {
        const userSkill = userSkills.find(skill => 
          skill.name.toLowerCase().includes(required.toLowerCase()) ||
          required.toLowerCase().includes(skill.name.toLowerCase())
        );
        
        if (userSkill) {
          matchedSkills.push({ required, user: userSkill });
        } else {
          missingSkills.push(required);
        }
      });
      
      const compatibilityScore = Math.round((matchedSkills.length / requiredSkills.length) * 100);
      
      setSkillCompatibility({
        score: compatibilityScore,
        matched: matchedSkills,
        missing: missingSkills
      });
    }
  }, [bounty]);

  // Handle form submission
  const handleSubmit = () => {
    if (!applicationData.coverLetter.trim()) {
      alert('Please provide a cover letter');
      return;
    }
    
    if (!applicationData.proposedTimeline.trim()) {
      alert('Please provide your proposed timeline');
      return;
    }

    onSubmit({
      ...applicationData,
      skillCompatibility,
      submittedAt: new Date()
    });
  };

  // Add portfolio link
  const addPortfolioLink = () => {
    setApplicationData(prev => ({
      ...prev,
      portfolioLinks: [...prev.portfolioLinks, '']
    }));
  };

  // Update portfolio link
  const updatePortfolioLink = (index, value) => {
    setApplicationData(prev => ({
      ...prev,
      portfolioLinks: prev.portfolioLinks.map((link, i) => i === index ? value : link)
    }));
  };

  // Remove portfolio link
  const removePortfolioLink = (index) => {
    setApplicationData(prev => ({
      ...prev,
      portfolioLinks: prev.portfolioLinks.filter((_, i) => i !== index)
    }));
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="4xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-2xl font-bold">Apply for Bounty</h2>
          <p className="text-default-600 font-normal">{bounty.title}</p>
        </ModalHeader>
        
        <ModalBody>
          <div className="space-y-6">
            {/* Bounty Summary */}
            <Card>
              <CardBody className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold">{bounty.title}</h3>
                    <p className="text-sm text-default-600">{bounty.poster.name}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-green-600">
                      {formatCurrency(bounty.value)}
                    </div>
                    <div className="text-sm text-default-500">{bounty.timeline}</div>
                  </div>
                </div>
                <p className="text-sm text-default-600 line-clamp-2">
                  {bounty.description}
                </p>
              </CardBody>
            </Card>

            {/* Skill Compatibility Analysis */}
            {skillCompatibility && (
              <Card>
                <CardBody className="p-4">
                  <h3 className="font-semibold mb-3">Skill Compatibility Analysis</h3>
                  
                  <div className="mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium">Compatibility Score:</span>
                      <Chip 
                        color={skillCompatibility.score >= 80 ? 'success' : skillCompatibility.score >= 60 ? 'warning' : 'danger'}
                        variant="flat"
                      >
                        {skillCompatibility.score}%
                      </Chip>
                    </div>
                  </div>

                  {skillCompatibility.matched.length > 0 && (
                    <div className="mb-3">
                      <div className="text-sm font-medium mb-2">Matched Skills:</div>
                      <div className="flex flex-wrap gap-2">
                        {skillCompatibility.matched.map((match, idx) => (
                          <Chip 
                            key={idx} 
                            color="success" 
                            variant="flat" 
                            size="sm"
                            startContent={match.user.verified ? '✓' : ''}
                          >
                            {match.required} ({match.user.level})
                          </Chip>
                        ))}
                      </div>
                    </div>
                  )}

                  {skillCompatibility.missing.length > 0 && (
                    <div>
                      <div className="text-sm font-medium mb-2">Skills to Highlight:</div>
                      <div className="flex flex-wrap gap-2">
                        {skillCompatibility.missing.map((skill, idx) => (
                          <Chip key={idx} color="warning" variant="flat" size="sm">
                            {skill}
                          </Chip>
                        ))}
                      </div>
                    </div>
                  )}
                </CardBody>
              </Card>
            )}

            {/* Cover Letter */}
            <div>
              <label className="block text-sm font-medium mb-2">Cover Letter *</label>
              <Textarea
                placeholder="Explain why you're the perfect fit for this bounty. Highlight your relevant experience and approach..."
                value={applicationData.coverLetter}
                onChange={(e) => setApplicationData(prev => ({ ...prev, coverLetter: e.target.value }))}
                minRows={4}
                maxRows={8}
              />
            </div>

            {/* Timeline and Budget */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Proposed Timeline *</label>
                <Input
                  placeholder="e.g., 6 weeks, 2 months"
                  value={applicationData.proposedTimeline}
                  onChange={(e) => setApplicationData(prev => ({ ...prev, proposedTimeline: e.target.value }))}
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Proposed Budget</label>
                <Input
                  type="number"
                  placeholder="USD"
                  value={applicationData.proposedBudget}
                  onChange={(e) => setApplicationData(prev => ({ ...prev, proposedBudget: parseInt(e.target.value) || bounty.value }))}
                  startContent="$"
                />
              </div>
            </div>

            {/* Portfolio Links */}
            <div>
              <label className="block text-sm font-medium mb-2">Portfolio Links</label>
              <div className="space-y-2">
                {applicationData.portfolioLinks.map((link, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="https://..."
                      value={link}
                      onChange={(e) => updatePortfolioLink(index, e.target.value)}
                      className="flex-1"
                    />
                    {applicationData.portfolioLinks.length > 1 && (
                      <Button
                        color="danger"
                        variant="flat"
                        size="sm"
                        onClick={() => removePortfolioLink(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  color="primary"
                  variant="flat"
                  size="sm"
                  onClick={addPortfolioLink}
                >
                  Add Portfolio Link
                </Button>
              </div>
            </div>

            {/* Relevant Experience */}
            <div>
              <label className="block text-sm font-medium mb-2">Relevant Experience</label>
              <Textarea
                placeholder="Describe your most relevant projects and achievements..."
                value={applicationData.relevantExperience}
                onChange={(e) => setApplicationData(prev => ({ ...prev, relevantExperience: e.target.value }))}
                minRows={3}
              />
            </div>

            {/* Questions */}
            <div>
              <label className="block text-sm font-medium mb-2">Questions for Client</label>
              <Textarea
                placeholder="Any questions about the project requirements, timeline, or deliverables?"
                value={applicationData.questions}
                onChange={(e) => setApplicationData(prev => ({ ...prev, questions: e.target.value }))}
                minRows={2}
              />
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button color="danger" variant="flat" onPress={onClose}>
            Cancel
          </Button>
          <Button color="primary" onPress={handleSubmit}>
            Submit Application
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default BountyApplicationModal;
