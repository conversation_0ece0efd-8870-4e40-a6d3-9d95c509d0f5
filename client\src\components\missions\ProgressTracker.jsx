import React from 'react';
import { Card, CardBody, CardHeader, Progress, Badge } from '@heroui/react';

const ProgressTracker = ({ userProgress }) => {
  return (
    <Card>
      <CardHeader>
        <h3 className="text-lg font-semibold">Mission Progress</h3>
      </CardHeader>
      <CardBody>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Level Progress</span>
              <Badge color="primary" variant="flat">
                Level {userProgress?.level || 1}
              </Badge>
            </div>
            <Progress 
              value={75} // TODO: Calculate actual progress to next level
              color="primary"
              size="sm"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-purple-600">
                {userProgress?.totalPoints || 0}
              </div>
              <div className="text-sm text-gray-600">Total Points</div>
            </div>
            
            <div>
              <div className="text-2xl font-bold text-green-600">
                {userProgress?.missionsCompleted || 0}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ProgressTracker;
