import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Progress, Badge, Button, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';

/**
 * Admin Analytics Component
 * 
 * Comprehensive platform analytics interface providing:
 * - User engagement and retention metrics
 * - Platform usage statistics
 * - Revenue and financial analytics
 * - Performance and system metrics
 * - Growth and conversion tracking
 * - Custom reporting and insights
 */
const AdminAnalytics = ({ currentUser, onDataRefresh }) => {
  const [analyticsData, setAnalyticsData] = useState({
    users: {
      total: 0,
      active: 0,
      retention: 0,
      growth: 0
    },
    platform: {
      projects: 0,
      completionRate: 0,
      averageValue: 0,
      satisfaction: 0
    },
    revenue: {
      total: 0,
      growth: 0,
      arpu: 0,
      churn: 0
    }
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Mock analytics data
      setAnalyticsData({
        users: {
          total: 1250,
          active: 890,
          retention: 78.5,
          growth: 12.3
        },
        platform: {
          projects: 456,
          completionRate: 92.1,
          averageValue: 2850,
          satisfaction: 4.7
        },
        revenue: {
          total: 125000,
          growth: 18.7,
          arpu: 140,
          churn: 3.2
        }
      });
    } catch (error) {
      console.error('Error loading analytics data:', error);
      toast.error('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="admin-analytics">
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div>
              <h3 className="text-lg font-semibold">Platform Analytics</h3>
              <p className="text-sm text-default-600">
                Comprehensive platform insights and metrics
              </p>
            </div>
            <Button
              size="sm"
              variant="flat"
              onClick={loadAnalyticsData}
              isLoading={loading}
            >
              Refresh
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* User Analytics */}
      <Card className="mb-6">
        <CardHeader>
          <h3 className="text-lg font-semibold">User Analytics</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">
                    {analyticsData.users.total.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Total Users</div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-success">
                    {analyticsData.users.active.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Active Users</div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-warning">
                    {analyticsData.users.retention}%
                  </div>
                  <div className="text-sm text-default-600">Retention Rate</div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-success">
                    +{analyticsData.users.growth}%
                  </div>
                  <div className="text-sm text-default-600">Growth Rate</div>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </CardBody>
      </Card>

      {/* Platform Analytics */}
      <Card className="mb-6">
        <CardHeader>
          <h3 className="text-lg font-semibold">Platform Performance</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Project Completion Rate</span>
                <span>{analyticsData.platform.completionRate}%</span>
              </div>
              <Progress
                value={analyticsData.platform.completionRate}
                color="success"
                size="sm"
              />
            </div>
            
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>User Satisfaction</span>
                <span>{analyticsData.platform.satisfaction}/5.0</span>
              </div>
              <Progress
                value={(analyticsData.platform.satisfaction / 5) * 100}
                color="primary"
                size="sm"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {analyticsData.platform.projects}
              </div>
              <div className="text-sm text-default-600">Active Projects</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-success">
                ${analyticsData.platform.averageValue.toLocaleString()}
              </div>
              <div className="text-sm text-default-600">Average Project Value</div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Revenue Analytics */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Revenue Analytics</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-success">
                    ${analyticsData.revenue.total.toLocaleString()}
                  </div>
                  <div className="text-sm text-default-600">Total Revenue</div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-success">
                    +{analyticsData.revenue.growth}%
                  </div>
                  <div className="text-sm text-default-600">Revenue Growth</div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">
                    ${analyticsData.revenue.arpu}
                  </div>
                  <div className="text-sm text-default-600">ARPU</div>
                </CardBody>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <Card>
                <CardBody className="p-4 text-center">
                  <div className="text-2xl font-bold text-warning">
                    {analyticsData.revenue.churn}%
                  </div>
                  <div className="text-sm text-default-600">Churn Rate</div>
                </CardBody>
              </Card>
            </motion.div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default AdminAnalytics;
