import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Navigate } from 'react-router-dom';
import LoadingAnimation from '../../components/layout/LoadingAnimation';

const BugManager = () => {
  const { currentUser } = useContext(UserContext);
  const [bugs, setBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [filter, setFilter] = useState('all'); // 'all', 'open', 'acknowledged', 'in-progress', 'fixed'
  const [users, setUsers] = useState({});

  // Check if user is admin and fetch bugs
  useEffect(() => {
    const checkAdminAndFetchBugs = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Check if user is admin
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', currentUser.id)
          .single();

        if (userError) throw userError;

        if (!userData?.is_admin) {
          setIsAdmin(false);
          setLoading(false);
          return;
        }

        setIsAdmin(true);

        // Fetch bugs
        let query = supabase
          .from('bug_reports')
          .select('*')
          .order('created_at', { ascending: false });

        // Apply filter if not 'all'
        if (filter !== 'all') {
          query = query.eq('status', filter);
        }

        const { data, error } = await query;

        if (error) throw error;

        setBugs(data || []);

        // Fetch user data for reporters and acknowledgers
        await fetchUserData(data);

      } catch (error) {
        console.error('Error in admin check or fetching bugs:', error);
        toast.error('Failed to load bug reports');
      } finally {
        setLoading(false);
      }
    };

    checkAdminAndFetchBugs();
  }, [currentUser, filter]);

  // Fetch user data for bug reporters and acknowledgers
  const fetchUserData = async (bugsData) => {
    if (!bugsData || bugsData.length === 0) return;

    try {
      // Get unique user IDs
      const userIds = [...new Set([
        ...bugsData.map(bug => bug.reported_by).filter(Boolean),
        ...bugsData.map(bug => bug.acknowledged_by).filter(Boolean)
      ])];

      if (userIds.length === 0) return;

      const { data, error } = await supabase
        .from('users')
        .select('id, email, display_name')
        .in('id', userIds);

      if (error) throw error;

      // Create a map of user data
      const userMap = {};
      data.forEach(user => {
        userMap[user.id] = user;
      });

      setUsers(userMap);
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  // Handle bug status update
  const handleStatusUpdate = async (bugId, newStatus) => {
    try {
      const updateData = {
        status: newStatus,
        updated_at: new Date()
      };

      // If acknowledging, set acknowledged_by and acknowledged_at
      if (newStatus === 'acknowledged' && bugs.find(b => b.id === bugId)?.status === 'open') {
        updateData.acknowledged_by = currentUser.id;
        updateData.acknowledged_at = new Date();
      }

      // If fixing, set fixed_at
      if (newStatus === 'fixed') {
        updateData.fixed_at = new Date();
      }

      const { error } = await supabase
        .from('bug_reports')
        .update(updateData)
        .eq('id', bugId);

      if (error) throw error;

      // Update local state
      setBugs(prev => prev.map(bug =>
        bug.id === bugId ? { ...bug, ...updateData } : bug
      ));

      toast.success(`Bug status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating bug status:', error);
      toast.error('Failed to update bug status');
    }
  };

  // Toggle public visibility
  const togglePublicVisibility = async (bugId, currentVisibility) => {
    try {
      const { error } = await supabase
        .from('bug_reports')
        .update({ is_public: !currentVisibility })
        .eq('id', bugId);

      if (error) throw error;

      // Update local state
      setBugs(prev => prev.map(bug =>
        bug.id === bugId ? { ...bug, is_public: !bug.is_public } : bug
      ));

      toast.success(`Bug is now ${!currentVisibility ? 'public' : 'private'}`);
    } catch (error) {
      console.error('Error toggling bug visibility:', error);
      toast.error('Failed to update bug visibility');
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get user display name
  const getUserName = (userId) => {
    if (!userId) return '';
    const user = users[userId];
    if (!user) return 'Unknown User';
    return user.display_name || user.email;
  };

  if (loading) {
    return <LoadingAnimation />;
  }

  if (!isAdmin) {
    return <Navigate to="/" />;
  }

  return (
    <div className="bug-manager-container">
      <div className="bug-manager-header">
        <h1>Bug Reports Manager</h1>
        <p className="text-muted">Manage and respond to user-reported bugs</p>
      </div>

      <div className="bug-filters">
        <button
          className={`filter-button ${filter === 'all' ? 'active' : ''}`}
          onClick={() => setFilter('all')}
        >
          All
        </button>
        <button
          className={`filter-button ${filter === 'open' ? 'active' : ''}`}
          onClick={() => setFilter('open')}
        >
          Open
        </button>
        <button
          className={`filter-button ${filter === 'acknowledged' ? 'active' : ''}`}
          onClick={() => setFilter('acknowledged')}
        >
          Acknowledged
        </button>
        <button
          className={`filter-button ${filter === 'in-progress' ? 'active' : ''}`}
          onClick={() => setFilter('in-progress')}
        >
          In Progress
        </button>
        <button
          className={`filter-button ${filter === 'fixed' ? 'active' : ''}`}
          onClick={() => setFilter('fixed')}
        >
          Fixed
        </button>
      </div>

      {bugs.length === 0 ? (
        <div className="no-bugs-found">
          <p>No bug reports found for the selected filter.</p>
        </div>
      ) : (
        <div className="bugs-table-container">
          <table className="bugs-table">
            <thead>
              <tr>
                <th>Title</th>
                <th>Status</th>
                <th>Reported By</th>
                <th>Date</th>
                <th>Public</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {bugs.map(bug => (
                <tr key={bug.id} className={bug.status}>
                  <td className="bug-title-cell">
                    <div className="bug-title">{bug.title}</div>
                    <div className="bug-description">{bug.description}</div>
                    {bug.solution && (
                      <div className="bug-solution">
                        <strong>Solution:</strong> {bug.solution}
                      </div>
                    )}
                  </td>
                  <td>
                    <span className={`status-badge ${bug.status}`}>
                      {bug.status}
                    </span>
                  </td>
                  <td>{getUserName(bug.reported_by)}</td>
                  <td>{formatDate(bug.created_at)}</td>
                  <td>
                    <button
                      className={`visibility-toggle ${bug.is_public ? 'public' : 'private'}`}
                      onClick={() => togglePublicVisibility(bug.id, bug.is_public)}
                      title={bug.is_public ? 'Make private' : 'Make public'}
                    >
                      <i className={`bi ${bug.is_public ? 'bi-eye' : 'bi-eye-slash'}`}></i>
                    </button>
                  </td>
                  <td>
                    <div className="bug-actions">
                      {bug.status === 'open' && (
                        <button
                          className="action-button acknowledge"
                          onClick={() => handleStatusUpdate(bug.id, 'acknowledged')}
                          title="Acknowledge bug"
                        >
                          <i className="bi bi-check"></i>
                        </button>
                      )}

                      {(bug.status === 'open' || bug.status === 'acknowledged') && (
                        <button
                          className="action-button progress"
                          onClick={() => handleStatusUpdate(bug.id, 'in-progress')}
                          title="Mark as in progress"
                        >
                          <i className="bi bi-tools"></i>
                        </button>
                      )}

                      {bug.status !== 'fixed' && (
                        <button
                          className="action-button fix"
                          onClick={() => handleStatusUpdate(bug.id, 'fixed')}
                          title="Mark as fixed"
                        >
                          <i className="bi bi-check-circle"></i>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default BugManager;
