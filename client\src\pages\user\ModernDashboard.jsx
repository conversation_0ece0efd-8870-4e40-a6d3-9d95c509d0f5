import React, { useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import BentoDashboard from '../../components/dashboard/BentoDashboard';

/**
 * Modern Dashboard with Bento Grid Layout and Experimental Navigation
 */
const ModernDashboard = () => {
  const { currentUser, userData } = useContext(UserContext);

  const displayName = userData?.display_name ||
                     userData?.full_name ||
                     currentUser?.user_metadata?.full_name ||
                     currentUser?.email?.split('@')[0] ||
                     'User';

  return (
    <BentoDashboard
      currentUser={currentUser}
      displayName={displayName}
    />
  );
};

export default ModernDashboard;