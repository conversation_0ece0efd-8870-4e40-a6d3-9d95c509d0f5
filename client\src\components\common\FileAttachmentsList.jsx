import React from 'react';

const FileAttachmentsList = ({ files, compact = false }) => {
  if (!files || files.length === 0) {
    return null;
  }

  // Helper function to get appropriate icon for file type
  const getFileIcon = (fileType) => {
    if (!fileType) return 'bi bi-file-earmark';
    
    if (fileType.startsWith('image/')) {
      return 'bi bi-file-image';
    } else if (fileType === 'application/pdf') {
      return 'bi bi-file-pdf';
    } else if (fileType.includes('word') || fileType.includes('document')) {
      return 'bi bi-file-word';
    } else if (fileType.includes('excel') || fileType.includes('spreadsheet')) {
      return 'bi bi-file-excel';
    } else if (fileType.includes('powerpoint') || fileType.includes('presentation')) {
      return 'bi bi-file-ppt';
    } else if (fileType.includes('text/')) {
      return 'bi bi-file-text';
    } else {
      return 'bi bi-file-earmark';
    }
  };

  // Helper function to format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return '';
    
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(1) + ' KB';
    } else {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
  };

  return (
    <div className={`file-attachments-list ${compact ? 'compact' : ''}`}>
      {!compact && <h6>Attachments</h6>}
      <ul className="attachments-list">
        {files.map((file, index) => (
          <li key={index} className="attachment-item">
            <i className={getFileIcon(file.type)}></i>
            <a 
              href={file.url} 
              target="_blank" 
              rel="noopener noreferrer" 
              className="attachment-name"
              title={file.name}
            >
              {file.name}
            </a>
            {!compact && <span className="attachment-size">({formatFileSize(file.size)})</span>}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FileAttachmentsList;
