// AnalyticsSettings - Comprehensive analytics configuration and preferences
// Implements analytics settings following system specifications
import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Switch, Select, SelectItem, Input, Slider, Divider, Badge } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { 
  Settings, 
  Eye, 
  EyeOff,
  Bell,
  Shield,
  Database,
  Clock,
  Palette,
  BarChart3,
  Users,
  Lock,
  Globe,
  Save,
  RefreshCw
} from 'lucide-react';

const AnalyticsSettings = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [settings, setSettings] = useState({
    privacy: {
      shareAnalytics: true,
      publicProfile: false,
      showEarnings: false,
      showProjects: true,
      allowBenchmarking: true
    },
    notifications: {
      weeklyReports: true,
      monthlyReports: true,
      performanceAlerts: true,
      goalReminders: true,
      emailFrequency: 'weekly'
    },
    display: {
      defaultPeriod: '30d',
      chartType: 'line',
      colorScheme: 'default',
      showPredictions: true,
      compactMode: false
    },
    data: {
      retentionPeriod: '2y',
      autoBackup: true,
      includeDeleted: false,
      exportFormat: 'csv'
    },
    performance: {
      realTimeUpdates: true,
      cacheData: true,
      refreshInterval: 300, // seconds
      maxDataPoints: 1000
    }
  });

  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      
      // Simulate API call to save settings
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      alert('Settings saved successfully!');
      
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to default?')) {
      // Reset to default settings
      setSettings({
        privacy: {
          shareAnalytics: true,
          publicProfile: false,
          showEarnings: false,
          showProjects: true,
          allowBenchmarking: true
        },
        notifications: {
          weeklyReports: true,
          monthlyReports: true,
          performanceAlerts: true,
          goalReminders: true,
          emailFrequency: 'weekly'
        },
        display: {
          defaultPeriod: '30d',
          chartType: 'line',
          colorScheme: 'default',
          showPredictions: true,
          compactMode: false
        },
        data: {
          retentionPeriod: '2y',
          autoBackup: true,
          includeDeleted: false,
          exportFormat: 'csv'
        },
        performance: {
          realTimeUpdates: true,
          cacheData: true,
          refreshInterval: 300,
          maxDataPoints: 1000
        }
      });
      setHasChanges(true);
    }
  };

  return (
    <div className={`analytics-settings space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Settings className="text-gray-600" size={28} />
            Analytics Settings
          </h2>
          <p className="text-gray-600">Configure your analytics preferences and privacy settings</p>
        </div>
        
        {hasChanges && (
          <Badge color="warning" variant="flat">
            Unsaved Changes
          </Badge>
        )}
      </div>

      {/* Privacy Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Shield className="text-blue-500" size={20} />
            <h3 className="text-lg font-semibold">Privacy & Visibility</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Share Analytics Data</h4>
                <p className="text-sm text-gray-600">Allow platform to use anonymized data for insights</p>
              </div>
              <Switch
                isSelected={settings.privacy.shareAnalytics}
                onChange={(value) => handleSettingChange('privacy', 'shareAnalytics', value)}
              />
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Public Profile Analytics</h4>
                <p className="text-sm text-gray-600">Show analytics on your public profile</p>
              </div>
              <Switch
                isSelected={settings.privacy.publicProfile}
                onChange={(value) => handleSettingChange('privacy', 'publicProfile', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Show Earnings</h4>
                <p className="text-sm text-gray-600">Display earnings information publicly</p>
              </div>
              <Switch
                isSelected={settings.privacy.showEarnings}
                onChange={(value) => handleSettingChange('privacy', 'showEarnings', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Show Projects</h4>
                <p className="text-sm text-gray-600">Display project analytics publicly</p>
              </div>
              <Switch
                isSelected={settings.privacy.showProjects}
                onChange={(value) => handleSettingChange('privacy', 'showProjects', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Allow Benchmarking</h4>
                <p className="text-sm text-gray-600">Include your data in platform benchmarks</p>
              </div>
              <Switch
                isSelected={settings.privacy.allowBenchmarking}
                onChange={(value) => handleSettingChange('privacy', 'allowBenchmarking', value)}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Notification Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Bell className="text-yellow-500" size={20} />
            <h3 className="text-lg font-semibold">Notifications & Reports</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Weekly Reports</h4>
                <p className="text-sm text-gray-600">Receive weekly performance summaries</p>
              </div>
              <Switch
                isSelected={settings.notifications.weeklyReports}
                onChange={(value) => handleSettingChange('notifications', 'weeklyReports', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Monthly Reports</h4>
                <p className="text-sm text-gray-600">Receive detailed monthly analytics</p>
              </div>
              <Switch
                isSelected={settings.notifications.monthlyReports}
                onChange={(value) => handleSettingChange('notifications', 'monthlyReports', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Performance Alerts</h4>
                <p className="text-sm text-gray-600">Get notified of significant changes</p>
              </div>
              <Switch
                isSelected={settings.notifications.performanceAlerts}
                onChange={(value) => handleSettingChange('notifications', 'performanceAlerts', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Goal Reminders</h4>
                <p className="text-sm text-gray-600">Reminders about your goals and targets</p>
              </div>
              <Switch
                isSelected={settings.notifications.goalReminders}
                onChange={(value) => handleSettingChange('notifications', 'goalReminders', value)}
              />
            </div>
            
            <Divider />
            
            <div>
              <h4 className="font-medium mb-2">Email Frequency</h4>
              <Select
                selectedKeys={[settings.notifications.emailFrequency]}
                onSelectionChange={(keys) => handleSettingChange('notifications', 'emailFrequency', Array.from(keys)[0])}
                className="w-full"
              >
                <SelectItem key="daily">Daily</SelectItem>
                <SelectItem key="weekly">Weekly</SelectItem>
                <SelectItem key="monthly">Monthly</SelectItem>
                <SelectItem key="never">Never</SelectItem>
              </Select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Display Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Palette className="text-purple-500" size={20} />
            <h3 className="text-lg font-semibold">Display & Interface</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Default Time Period</h4>
              <Select
                selectedKeys={[settings.display.defaultPeriod]}
                onSelectionChange={(keys) => handleSettingChange('display', 'defaultPeriod', Array.from(keys)[0])}
                className="w-full"
              >
                <SelectItem key="7d">Last 7 Days</SelectItem>
                <SelectItem key="30d">Last 30 Days</SelectItem>
                <SelectItem key="90d">Last 90 Days</SelectItem>
                <SelectItem key="1y">Last Year</SelectItem>
              </Select>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Default Chart Type</h4>
              <Select
                selectedKeys={[settings.display.chartType]}
                onSelectionChange={(keys) => handleSettingChange('display', 'chartType', Array.from(keys)[0])}
                className="w-full"
              >
                <SelectItem key="line">Line Charts</SelectItem>
                <SelectItem key="bar">Bar Charts</SelectItem>
                <SelectItem key="area">Area Charts</SelectItem>
                <SelectItem key="mixed">Mixed Charts</SelectItem>
              </Select>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Color Scheme</h4>
              <Select
                selectedKeys={[settings.display.colorScheme]}
                onSelectionChange={(keys) => handleSettingChange('display', 'colorScheme', Array.from(keys)[0])}
                className="w-full"
              >
                <SelectItem key="default">Default</SelectItem>
                <SelectItem key="blue">Blue Theme</SelectItem>
                <SelectItem key="green">Green Theme</SelectItem>
                <SelectItem key="purple">Purple Theme</SelectItem>
                <SelectItem key="dark">Dark Theme</SelectItem>
              </Select>
            </div>
            
            <Divider />
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Show Predictions</h4>
                <p className="text-sm text-gray-600">Display AI-powered predictions and forecasts</p>
              </div>
              <Switch
                isSelected={settings.display.showPredictions}
                onChange={(value) => handleSettingChange('display', 'showPredictions', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Compact Mode</h4>
                <p className="text-sm text-gray-600">Use compact layout for more data density</p>
              </div>
              <Switch
                isSelected={settings.display.compactMode}
                onChange={(value) => handleSettingChange('display', 'compactMode', value)}
              />
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Performance Settings */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <BarChart3 className="text-green-500" size={20} />
            <h3 className="text-lg font-semibold">Performance & Data</h3>
          </div>
        </CardHeader>
        <CardBody className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Real-time Updates</h4>
                <p className="text-sm text-gray-600">Update analytics data in real-time</p>
              </div>
              <Switch
                isSelected={settings.performance.realTimeUpdates}
                onChange={(value) => handleSettingChange('performance', 'realTimeUpdates', value)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Cache Data</h4>
                <p className="text-sm text-gray-600">Cache data locally for faster loading</p>
              </div>
              <Switch
                isSelected={settings.performance.cacheData}
                onChange={(value) => handleSettingChange('performance', 'cacheData', value)}
              />
            </div>
            
            <div>
              <h4 className="font-medium mb-3">Refresh Interval (seconds)</h4>
              <Slider
                value={[settings.performance.refreshInterval]}
                onValueChange={(value) => handleSettingChange('performance', 'refreshInterval', value[0])}
                minValue={60}
                maxValue={3600}
                step={60}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-gray-600 mt-1">
                <span>1 min</span>
                <span className="font-medium">{Math.round(settings.performance.refreshInterval / 60)} min</span>
                <span>60 min</span>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Data Retention Period</h4>
              <Select
                selectedKeys={[settings.data.retentionPeriod]}
                onSelectionChange={(keys) => handleSettingChange('data', 'retentionPeriod', Array.from(keys)[0])}
                className="w-full"
              >
                <SelectItem key="1y">1 Year</SelectItem>
                <SelectItem key="2y">2 Years</SelectItem>
                <SelectItem key="5y">5 Years</SelectItem>
                <SelectItem key="forever">Forever</SelectItem>
              </Select>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button
          variant="light"
          color="danger"
          startContent={<RefreshCw size={18} />}
          onPress={handleResetSettings}
        >
          Reset to Defaults
        </Button>
        
        <div className="flex gap-3">
          <Button
            variant="light"
            onPress={() => setHasChanges(false)}
            isDisabled={!hasChanges}
          >
            Cancel
          </Button>
          <Button
            color="primary"
            startContent={<Save size={18} />}
            onPress={handleSaveSettings}
            isLoading={isSaving}
            isDisabled={!hasChanges}
          >
            Save Settings
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsSettings;
