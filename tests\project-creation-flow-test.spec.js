import { test, expect } from '@playwright/test';

/**
 * Comprehensive Project Creation Flow Test
 *
 * Tests the complete project creation pipeline including:
 * - Question flow navigation
 * - Legal agreement generation
 * - Agreement display and download
 * - Project creation completion
 */

// Test credentials
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TestPassword123!';

test.describe('Project Creation Flow with Legal Agreement Generation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the site and authenticate
    await page.goto('https://royalty.technology');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test('should complete project creation flow with legal agreement generation', async ({ page }) => {
    console.log('🚀 Starting project creation flow test...');

    // Navigate to project creation
    await page.click('[data-testid="create-project"], [href*="project"], button:has-text("Create"), button:has-text("Project")');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Step 1: Welcome Screen
    console.log('📋 Step 1: Welcome screen');
    await expect(page.locator('h1')).toContainText(['Create Your Project', 'Welcome', 'Start']);

    // Click start button
    await page.click('button:has-text("Start"), button:has-text("Begin"), button:has-text("Continue")');
    await page.waitForTimeout(1000);

    // Step 2: Question Flow
    console.log('📋 Step 2: Question flow');

    // Question 1: What's your project?
    await page.fill('input[placeholder*="TaskMaster"], input[name="projectName"]', 'Test Project Pro');
    await page.selectOption('select[name="projectCategory"], select:has(option[value="software"])', 'software');
    await page.fill('textarea[name="projectDescription"], textarea[placeholder*="description"]', 'A comprehensive test project for validating the legal agreement generation system');
    await page.click('button:has-text("Next"), button:has-text("Continue")');
    await page.waitForTimeout(1000);

    // Question 2: How will you work together?
    await page.selectOption('select[name="timeline"], select:has(option[value="medium"])', 'medium');
    await page.selectOption('select[name="targetAudience"], select:has(option[value="business"])', 'business');
    await page.click('button:has-text("Next"), button:has-text("Continue")');
    await page.waitForTimeout(1000);

    // Question 3: How will you fund this?
    await page.selectOption('select[name="budget"], select:has(option[value="bootstrapped"])', 'bootstrapped');
    await page.selectOption('select[name="revenueSharing"], select:has(option[value="contribution"])', 'contribution');
    await page.click('button:has-text("Next"), button:has-text("Continue")');
    await page.waitForTimeout(1000);

    // Question 4: What are your goals?
    await page.selectOption('select[name="successMetrics"], select:has(option[value="revenue"])', 'revenue');
    
    // Add tags
    const tagOptions = ['productivity', 'automation', 'business'];
    for (const tag of tagOptions) {
      const tagButton = page.locator(`button:has-text("${tag}"), [data-tag="${tag}"]`);
      if (await tagButton.isVisible()) {
        await tagButton.click();
        await page.waitForTimeout(500);
      }
    }
    
    await page.click('button:has-text("Next"), button:has-text("Continue")');
    await page.waitForTimeout(1000);

    // Question 5: Legal & Protection
    await page.selectOption('select[name="agreementType"], select:has(option[value="business"])', 'business');
    await page.selectOption('select[name="ipOwnership"], select:has(option[value="shared"])', 'shared');
    await page.selectOption('select[name="disputeResolution"], select:has(option[value="mediation"])', 'mediation');
    await page.click('button:has-text("Complete"), button:has-text("Finish"), button:has-text("Review")');
    await page.waitForTimeout(3000);

    // Step 3: Review Screen with Legal Agreement
    console.log('📋 Step 3: Review screen with legal agreement');
    
    // Verify review screen elements
    await expect(page.locator('h1')).toContainText(['Review Your Project', 'Review', 'Summary']);
    await expect(page.locator('text=Test Project Pro')).toBeVisible();
    
    // Verify agreement preview section
    await expect(page.locator('text=Generated Agreement, text=Legal Agreement, text=Agreement')).toBeVisible();
    await expect(page.locator('text=CITY OF GAMERS INC., text=CONTRIBUTOR AGREEMENT')).toBeVisible();
    
    // Test "View Full Agreement" functionality
    const viewAgreementButton = page.locator('button:has-text("View Full Agreement"), button:has-text("View Agreement")');
    if (await viewAgreementButton.isVisible()) {
      console.log('📄 Testing full agreement view...');
      await viewAgreementButton.click();
      await page.waitForTimeout(2000);
      
      // Verify modal with full agreement
      await expect(page.locator('text=Legal Collaboration Agreement, text=CITY OF GAMERS INC.')).toBeVisible();
      await expect(page.locator('text=Test Project Pro')).toBeVisible();
      await expect(page.locator('text=Recitals, text=Definitions')).toBeVisible();
      await expect(page.locator('text=SCHEDULE A, text=SCHEDULE B')).toBeVisible();
      
      // Close modal
      await page.click('button:has-text("Close"), button:has-text("Cancel")');
      await page.waitForTimeout(1000);
    }
    
    // Test download functionality
    const downloadButton = page.locator('button:has-text("Download"), button:has-text("Download Agreement")');
    if (await downloadButton.isVisible()) {
      console.log('📥 Testing agreement download...');
      
      // Set up download handler
      const downloadPromise = page.waitForEvent('download');
      await downloadButton.click();
      const download = await downloadPromise;
      
      // Verify download
      expect(download.suggestedFilename()).toMatch(/test-venture-pro.*\.md/i);
      console.log('✅ Agreement download successful:', download.suggestedFilename());
    }

    // Step 4: Complete Venture Creation
    console.log('📋 Step 4: Complete venture creation');
    
    // Click create venture button
    const createButton = page.locator('button:has-text("Create Venture"), button:has-text("Create"), button:has-text("Confirm")');
    await createButton.click();
    await page.waitForTimeout(5000);
    
    // Verify success
    const successIndicators = [
      'Venture created successfully',
      'Success',
      'Created',
      'Welcome to your venture',
      'Project Detail',
      'Test Venture Pro'
    ];
    
    let successFound = false;
    for (const indicator of successIndicators) {
      if (await page.locator(`text=${indicator}`).isVisible()) {
        console.log(`✅ Success indicator found: ${indicator}`);
        successFound = true;
        break;
      }
    }
    
    if (!successFound) {
      console.log('⚠️ No clear success indicator found, checking URL...');
      const currentUrl = page.url();
      if (currentUrl.includes('/project/') || currentUrl.includes('/venture/')) {
        console.log('✅ Redirected to project/venture page, creation likely successful');
        successFound = true;
      }
    }
    
    expect(successFound).toBe(true);
    
    console.log('🎉 Venture creation flow completed successfully!');
  });

  test('should handle agreement generation errors gracefully', async ({ page }) => {
    console.log('🔧 Testing error handling in venture creation...');
    
    // Navigate to venture creation
    await page.click('[data-testid="create-venture"], [href*="venture"], button:has-text("Create")');
    await page.waitForLoadState('networkidle');
    
    // Fill minimal data to trigger potential errors
    await page.click('button:has-text("Start")');
    await page.waitForTimeout(1000);
    
    // Fill incomplete data
    await page.fill('input[name="ventureName"]', 'Error Test');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);
    
    // Skip through quickly to test error handling
    for (let i = 0; i < 4; i++) {
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue"), button:has-text("Complete")');
      if (await nextButton.isVisible()) {
        await nextButton.click();
        await page.waitForTimeout(500);
      }
    }
    
    // Should reach review screen even with minimal data
    await page.waitForTimeout(3000);
    
    // Verify error handling doesn't break the flow
    const errorMessages = page.locator('text=Error, text=Failed, text=Something went wrong');
    if (await errorMessages.isVisible()) {
      console.log('⚠️ Error message displayed, but flow continues');
    }
    
    // Should still show some form of agreement or fallback
    const agreementSection = page.locator('text=Agreement, text=Legal, text=Generated');
    await expect(agreementSection).toBeVisible();
    
    console.log('✅ Error handling test completed');
  });
});
