import React, { createContext, useContext, useState, useCallback } from 'react';

/**
 * Enhanced Data Synchronization Context
 *
 * Production-ready cross-tile data synchronization with:
 * - Real-time data synchronization between all tiles
 * - Cross-tile navigation state management
 * - Unified data caching and invalidation
 * - Performance optimization with selective updates
 * - Event-driven architecture for seamless integration
 */

const DataSyncContext = createContext();

export const useDataSync = () => {
  const context = useContext(DataSyncContext);
  if (!context) {
    throw new Error('useDataSync must be used within a DataSyncProvider');
  }
  return context;
};

export const DataSyncProvider = ({ children }) => {
  // Enhanced sync triggers for all tile types
  const [syncTriggers, setSyncTriggers] = useState({
    contributions: 0,
    earnings: 0,
    projects: 0,
    payments: 0,
    users: 0,
    teams: 0,
    analytics: 0,
    system: 0,
    admin: 0,
    navigation: 0
  });

  // Cross-tile shared state
  const [sharedState, setSharedState] = useState({
    currentProject: null,
    selectedTimeRange: '30d',
    activeFilters: {},
    userPreferences: {},
    navigationContext: {},
    crossTileData: {}
  });

  // Trigger refresh for specific data types
  const triggerSync = useCallback((dataType) => {
    setSyncTriggers(prev => ({
      ...prev,
      [dataType]: prev[dataType] + 1
    }));
  }, []);

  // Trigger multiple data types at once
  const triggerMultipleSync = useCallback((dataTypes) => {
    setSyncTriggers(prev => {
      const newTriggers = { ...prev };
      dataTypes.forEach(type => {
        newTriggers[type] = prev[type] + 1;
      });
      return newTriggers;
    });
  }, []);

  // Specific trigger functions for common operations
  const triggerContributionSync = useCallback(() => {
    // When contributions change, earnings should also update
    triggerMultipleSync(['contributions', 'earnings']);
  }, [triggerMultipleSync]);

  const triggerEarningsSync = useCallback(() => {
    triggerSync('earnings');
  }, [triggerSync]);

  const triggerProjectSync = useCallback(() => {
    // When projects change, contributions and earnings might also need updates
    triggerMultipleSync(['projects', 'contributions', 'earnings']);
  }, [triggerMultipleSync]);

  const triggerPaymentSync = useCallback(() => {
    // When payments change, earnings should also update
    triggerMultipleSync(['payments', 'earnings']);
  }, [triggerMultipleSync]);

  // Enhanced cross-tile synchronization functions
  const triggerAdminSync = useCallback(() => {
    triggerMultipleSync(['admin', 'users', 'system', 'analytics']);
  }, [triggerMultipleSync]);

  const triggerSystemSync = useCallback(() => {
    triggerMultipleSync(['system', 'admin']);
  }, [triggerMultipleSync]);

  const triggerNavigationSync = useCallback(() => {
    triggerSync('navigation');
  }, [triggerSync]);

  // Shared state management
  const updateSharedState = useCallback((key, value) => {
    setSharedState(prev => ({
      ...prev,
      [key]: value
    }));
    // Trigger navigation sync when shared state changes
    triggerNavigationSync();
  }, [triggerNavigationSync]);

  const updateCrossTileData = useCallback((tileId, data) => {
    setSharedState(prev => ({
      ...prev,
      crossTileData: {
        ...prev.crossTileData,
        [tileId]: data
      }
    }));
  }, []);

  // Cross-tile navigation helpers
  const navigateWithContext = useCallback((targetTile, context = {}) => {
    updateSharedState('navigationContext', {
      from: context.from || 'unknown',
      to: targetTile,
      timestamp: Date.now(),
      data: context.data || {},
      preserveState: context.preserveState || false
    });
  }, [updateSharedState]);

  // Global data invalidation
  const invalidateAllData = useCallback(() => {
    setSyncTriggers(prev => {
      const newTriggers = {};
      Object.keys(prev).forEach(key => {
        newTriggers[key] = prev[key] + 1;
      });
      return newTriggers;
    });
  }, []);

  const value = {
    // Original sync functionality
    syncTriggers,
    triggerSync,
    triggerMultipleSync,
    triggerContributionSync,
    triggerEarningsSync,
    triggerProjectSync,
    triggerPaymentSync,

    // Enhanced cross-tile functionality
    triggerAdminSync,
    triggerSystemSync,
    triggerNavigationSync,
    invalidateAllData,

    // Shared state management
    sharedState,
    updateSharedState,
    updateCrossTileData,
    navigateWithContext
  };

  return (
    <DataSyncContext.Provider value={value}>
      {children}
    </DataSyncContext.Provider>
  );
};

export default DataSyncContext;
