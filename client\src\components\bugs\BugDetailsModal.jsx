import React from 'react';

const BugDetailsModal = ({ bug, isOpen, onClose }) => {
  if (!isOpen || !bug) return null;
  
  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  return (
    <div className="bug-details-modal-overlay" onClick={onClose}>
      <div className="bug-details-modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">{bug.title}</h3>
          <button 
            className="close-button"
            onClick={onClose}
            aria-label="Close"
          >
            <i className="bi bi-x-lg"></i>
          </button>
        </div>
        
        <div className="modal-body">
          <div className="bug-status-container">
            <span className={`bug-status-badge ${bug.status}`}>
              {bug.status}
            </span>
          </div>
          
          <div className="bug-section">
            <h4 className="bug-section-title">Description</h4>
            <p className="bug-section-content">{bug.description}</p>
          </div>
          
          {bug.solution && (
            <div className="bug-section solution-section">
              <h4 className="bug-section-title">Solution</h4>
              <p className="bug-section-content">{bug.solution}</p>
            </div>
          )}
          
          <div className="bug-meta-info">
            <div className="bug-meta-item">
              <span className="bug-meta-label">Reported:</span>
              <span className="bug-meta-value">{formatDate(bug.created_at)}</span>
            </div>
            
            {bug.acknowledged_at && (
              <div className="bug-meta-item">
                <span className="bug-meta-label">Acknowledged:</span>
                <span className="bug-meta-value">{formatDate(bug.acknowledged_at)}</span>
              </div>
            )}
            
            {bug.fixed_at && (
              <div className="bug-meta-item">
                <span className="bug-meta-label">Fixed:</span>
                <span className="bug-meta-value">{formatDate(bug.fixed_at)}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BugDetailsModal;
