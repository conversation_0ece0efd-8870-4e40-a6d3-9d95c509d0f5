/**
 * Agreement Generator V2 - Complete Rewrite
 * 
 * This is a complete rewrite of the agreement generation system
 * designed to achieve 95%+ accuracy against lawyer-approved templates
 * with zero tolerance for hardcoded content or unreplaced variables.
 * 
 * Key Features:
 * - Single-pass replacement system
 * - Comprehensive input validation
 * - Strict output validation
 * - Zero hardcoded content
 * - Legal accuracy focused
 */

import { DataValidator } from './DataValidator.js';
import { TemplateLoader } from './TemplateLoader.js';
import { ReplacementEngine } from './ReplacementEngine.js';
import { OutputValidator } from './OutputValidator.js';
import { AgreementError, ValidationError } from './errors/AgreementErrors.js';

export class AgreementGeneratorV2 {
  constructor() {
    this.validator = new DataValidator();
    this.templateLoader = new TemplateLoader();
    this.replacementEngine = new ReplacementEngine();
    this.outputValidator = new OutputValidator();
    
    // Configuration
    this.config = {
      minAccuracyScore: 95,
      enableStrictValidation: true,
      enableDebugLogging: process.env.NODE_ENV === 'development'
    };
  }

  /**
   * Generate a legal agreement with comprehensive validation
   * 
   * @param {string} templateType - Type of agreement template to use
   * @param {Object} userData - Complete user data for agreement generation
   * @returns {Promise<Object>} Generated agreement with metadata
   */
  async generateAgreement(templateType, userData) {
    const startTime = Date.now();
    
    try {
      this._log('Starting agreement generation', { templateType, userData: this._sanitizeForLog(userData) });

      // STEP 1: Validate input data (fail fast approach)
      this._log('Step 1: Validating input data');
      const validatedData = await this.validator.validateInputData(userData);
      this._log('Input validation successful', { validatedFields: Object.keys(validatedData) });

      // STEP 2: Load and validate template
      this._log('Step 2: Loading template', { templateType });
      const template = await this.templateLoader.loadTemplate(templateType);
      this._log('Template loaded successfully', { templateLength: template.length });

      // STEP 3: Single-pass replacement processing
      this._log('Step 3: Processing template replacements');
      const processedAgreement = await this.replacementEngine.processTemplate(template, validatedData);
      this._log('Template processing completed', { outputLength: processedAgreement.length });

      // STEP 4: Comprehensive output validation
      this._log('Step 4: Validating output');
      const validatedAgreement = await this.outputValidator.validateOutput(processedAgreement, template, validatedData);
      this._log('Output validation completed', { accuracyScore: validatedAgreement.validation.accuracyScore });

      // STEP 5: Final accuracy check
      if (validatedAgreement.validation.accuracyScore < this.config.minAccuracyScore) {
        throw new ValidationError(
          `Agreement accuracy ${validatedAgreement.validation.accuracyScore}% below required ${this.config.minAccuracyScore}%`,
          validatedAgreement.validation
        );
      }

      const generationTime = Date.now() - startTime;
      
      return {
        success: true,
        agreement: validatedAgreement.content,
        metadata: {
          templateType,
          generatedAt: new Date().toISOString(),
          generationTimeMs: generationTime,
          accuracyScore: validatedAgreement.validation.accuracyScore,
          version: '2.0.0',
          validationResults: validatedAgreement.validation
        }
      };

    } catch (error) {
      const generationTime = Date.now() - startTime;
      
      this._log('Agreement generation failed', { error: error.message, stack: error.stack });
      
      return {
        success: false,
        error: error.message,
        errorType: error.constructor.name,
        details: error.details || [],
        metadata: {
          templateType,
          failedAt: new Date().toISOString(),
          generationTimeMs: generationTime,
          version: '2.0.0'
        }
      };
    }
  }

  /**
   * Validate agreement data without generating the full agreement
   * Useful for form validation and pre-flight checks
   */
  async validateAgreementData(userData) {
    try {
      const validatedData = await this.validator.validateInputData(userData);
      return {
        valid: true,
        data: validatedData,
        issues: []
      };
    } catch (error) {
      return {
        valid: false,
        data: null,
        issues: error.details || [{ message: error.message }]
      };
    }
  }

  /**
   * Get available template types
   */
  async getAvailableTemplates() {
    return await this.templateLoader.getAvailableTemplates();
  }

  /**
   * Get template preview with variable placeholders
   */
  async getTemplatePreview(templateType) {
    const template = await this.templateLoader.loadTemplate(templateType);
    const variables = this.replacementEngine.extractVariables(template);
    
    return {
      template,
      variables,
      requiredVariables: variables.filter(v => v.required),
      optionalVariables: variables.filter(v => !v.required)
    };
  }

  /**
   * Test agreement generation with sample data
   * Useful for development and testing
   */
  async testGeneration(templateType, testScenario = 'default') {
    const testData = this._getTestData(testScenario);
    return await this.generateAgreement(templateType, testData);
  }

  /**
   * Get system health and configuration
   */
  getSystemInfo() {
    return {
      version: '2.0.0',
      config: this.config,
      components: {
        validator: this.validator.constructor.name,
        templateLoader: this.templateLoader.constructor.name,
        replacementEngine: this.replacementEngine.constructor.name,
        outputValidator: this.outputValidator.constructor.name
      },
      timestamp: new Date().toISOString()
    };
  }

  // Private helper methods

  _log(message, data = {}) {
    if (this.config.enableDebugLogging) {
      console.log(`[AgreementGeneratorV2] ${message}`, data);
    }
  }

  _sanitizeForLog(userData) {
    // Remove sensitive information from logs
    const sanitized = { ...userData };
    if (sanitized.contributor?.email) {
      sanitized.contributor.email = sanitized.contributor.email.replace(/(.{2}).*(@.*)/, '$1***$2');
    }
    if (sanitized.company?.billingEmail) {
      sanitized.company.billingEmail = sanitized.company.billingEmail.replace(/(.{2}).*(@.*)/, '$1***$2');
    }
    return sanitized;
  }

  _getTestData(scenario) {
    const testData = {
      default: {
        company: {
          name: 'TechCorp Solutions Inc.',
          address: '123 Innovation Drive, Orlando, FL 32801',
          state: 'Florida',
          city: 'Orlando',
          signerName: 'John Smith',
          signerTitle: 'CEO',
          billingEmail: '<EMAIL>'
        },
        project: {
          name: 'AI Analytics Platform',
          description: 'Advanced AI-powered analytics platform for enterprise data insights',
          projectType: 'software'
        },
        contributor: {
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          address: '456 Developer Lane, Orlando, FL 32802'
        }
      },
      game: {
        company: {
          name: 'GameStudio Collective LLC',
          address: '789 Creative Blvd, Los Angeles, CA 90210',
          state: 'California',
          city: 'Los Angeles',
          signerName: 'Alex Thompson',
          signerTitle: 'Creative Director',
          billingEmail: '<EMAIL>'
        },
        project: {
          name: 'Mystic Realms RPG',
          description: 'Fantasy role-playing game with immersive storytelling and strategic combat',
          projectType: 'game'
        },
        contributor: {
          name: 'Jordan Williams',
          email: '<EMAIL>',
          address: '321 Artist Ave, Los Angeles, CA 90211'
        }
      }
    };

    return testData[scenario] || testData.default;
  }
}

// Export singleton instance for backward compatibility
export const agreementGeneratorV2 = new AgreementGeneratorV2();
