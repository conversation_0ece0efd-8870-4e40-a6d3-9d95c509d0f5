import { test, expect } from '@playwright/test';

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate using the immersive auth flow
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  const hasSignInButton = await page.locator('text="Sign In"').isVisible();
  
  if (!hasSignInButton) {
    console.log('✅ Already authenticated');
    return true;
  }
  
  console.log('📝 Need to authenticate - clicking Sign In...');
  await page.click('text="Sign In"');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  const hasLoginButton = await page.locator('text="LOGIN"').isVisible();
  if (!hasLoginButton) {
    throw new Error('LOGIN button not found on auth landing page');
  }
  
  console.log('📝 Clicking LOGIN button...');
  await page.click('text="LOGIN"');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  const hasEmailInput = await page.locator('input[type="email"]').isVisible();
  const hasPasswordInput = await page.locator('input[type="password"]').isVisible();
  
  if (!hasEmailInput || !hasPasswordInput) {
    throw new Error('Login form not found after clicking LOGIN');
  }
  
  console.log('📝 Filling in credentials...');
  await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
  await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
  
  const submitButton = page.locator('button[type="submit"]').first();
  await submitButton.click();
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  const stillHasSignInButton = await page.locator('text="Sign In"').isVisible();
  if (stillHasSignInButton) {
    throw new Error('Authentication failed - Sign In button still visible');
  }
  
  console.log('✅ Authentication successful');
  return true;
}

test.describe('Debug Start Page', () => {
  test('Debug what page loads when clicking Start', async ({ page }) => {
    await authenticate(page);
    
    // Check current URL after authentication
    const authUrl = page.url();
    console.log(`📍 After authentication URL: ${authUrl}`);
    
    // Take screenshot after auth
    await page.screenshot({ path: 'test-results/debug-after-auth.png', fullPage: true });
    console.log('📸 After auth screenshot saved');
    
    // Click Start button
    console.log('🔄 Clicking Start button...');
    await page.click('text="Start"');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(5000); // Wait longer for any dynamic content
    
    // Check URL after clicking Start
    const startUrl = page.url();
    console.log(`📍 After clicking Start URL: ${startUrl}`);
    
    // Take screenshot after clicking Start
    await page.screenshot({ path: 'test-results/debug-after-start-click.png', fullPage: true });
    console.log('📸 After Start click screenshot saved');
    
    // Get all text content on the page
    const pageText = await page.textContent('body');
    console.log('📄 Page text content (first 500 chars):');
    console.log(pageText.substring(0, 500));
    
    // Check for specific elements that should be there
    const allButtons = await page.locator('button').allTextContents();
    console.log('🔘 All buttons on page:', allButtons);
    
    const allHeadings = await page.locator('h1, h2, h3, h4, h5, h6').allTextContents();
    console.log('📝 All headings on page:', allHeadings);
    
    // Try different routes that might show the project wizard
    const routesToTry = [
      '/start',
      '/project/create',
      '/project/wizard',
      '/projects/new',
      '/create'
    ];
    
    for (const route of routesToTry) {
      console.log(`🔄 Trying route: ${route}`);
      await page.goto(route);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      const routeUrl = page.url();
      console.log(`📍 Route ${route} actual URL: ${routeUrl}`);
      
      // Check for project wizard elements
      const hasCreateProject = await page.locator('text="Create Your Project"').isVisible();
      const hasTraditionalWizard = await page.locator('text="Use Traditional Wizard"').isVisible();
      const hasEnhancedWizard = await page.locator('text="Enhanced Project Wizard"').isVisible();
      
      if (hasCreateProject || hasTraditionalWizard || hasEnhancedWizard) {
        console.log(`✅ FOUND PROJECT WIZARD on route: ${route}`);
        console.log(`  - Create Your Project: ${hasCreateProject}`);
        console.log(`  - Use Traditional Wizard: ${hasTraditionalWizard}`);
        console.log(`  - Enhanced Project Wizard: ${hasEnhancedWizard}`);
        
        await page.screenshot({ path: `test-results/debug-wizard-found-${route.replace('/', '-')}.png`, fullPage: true });
        break;
      } else {
        console.log(`❌ No project wizard found on route: ${route}`);
      }
    }
    
    // Check if there are any redirects happening
    console.log('🔄 Checking for redirects...');
    await page.goto('/start');
    await page.waitForTimeout(1000);
    const finalUrl = page.url();
    console.log(`📍 Final URL after /start: ${finalUrl}`);
    
    if (finalUrl !== 'http://localhost:5173/start') {
      console.log(`⚠️ REDIRECT DETECTED: /start redirects to ${finalUrl}`);
    }
  });
});
