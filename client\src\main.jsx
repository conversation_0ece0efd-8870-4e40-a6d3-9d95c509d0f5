import React, { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter as Router } from "react-router-dom";
import { HeroUIProvider } from "@heroui/react";
import { UserProvider } from "./contexts/supabase-auth.context.jsx";
import Theme<PERSON>rovider from "./contexts/theme.context.jsx";
import FeatureFlagProvider from "./contexts/feature-flags.context.jsx";
import App from "./App.jsx";
import ExtensionErrorBoundary from "./components/error/ExtensionErrorBoundary.jsx";

// Import CSS
import "./styles/tailwind.css";

// Import and initialize browser extension error handler
import { initBrowserExtensionHandler } from "./utils/browserExtensionHandler.js";

// Initialize browser extension error handler early
initBrowserExtensionHandler();

// Simple test component
function TestApp() {
  return (
    <div>
      <h1>🎉 React is Working!</h1>
      <p>This is a minimal React app to test if the flat import issue is resolved.</p>
    </div>
  );
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ExtensionErrorBoundary>
      <Router>
        <UserProvider>
          <ThemeProvider>
            <HeroUIProvider>
              <FeatureFlagProvider>
                <App />
              </FeatureFlagProvider>
            </HeroUIProvider>
          </ThemeProvider>
        </UserProvider>
      </Router>
    </ExtensionErrorBoundary>
  </StrictMode>
);
