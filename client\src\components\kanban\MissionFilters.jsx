import React, { useState } from 'react';
import { 
  Card, 
  CardBody, 
  Input, 
  Select, 
  SelectItem, 
  Button, 
  Chip,
  Slider,
  Switch,
  Popover,
  PopoverTrigger,
  PopoverContent
} from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * MissionFilters Component
 * 
 * Advanced filtering system for mission board
 * Follows wireframe specifications with comprehensive filter options
 */
const MissionFilters = ({ 
  searchTerm, 
  onSearchChange,
  filters, 
  onFiltersChange,
  savedFilters = [],
  onSaveFilter,
  onLoadFilter,
  onClearFilters,
  className = ""
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [rewardRange, setRewardRange] = useState([0, 2000]);
  const [filterName, setFilterName] = useState('');

  // Filter options
  const statusOptions = [
    { key: 'all', label: 'All Status' },
    { key: 'available', label: 'Available' },
    { key: 'active', label: 'In Progress' },
    { key: 'review', label: 'In Review' },
    { key: 'completed', label: 'Completed' },
    { key: 'blocked', label: 'Blocked' }
  ];

  const difficultyOptions = [
    { key: 'all', label: 'All Levels' },
    { key: 'easy', label: '⭐⭐⭐ Easy' },
    { key: 'medium', label: '⭐⭐⭐⭐⭐ Medium' },
    { key: 'hard', label: '⭐⭐⭐⭐⭐⭐⭐ Hard' },
    { key: 'expert', label: '⭐⭐⭐⭐⭐⭐⭐⭐⭐ Expert' }
  ];

  const typeOptions = [
    { key: 'all', label: 'All Types' },
    { key: 'bug', label: '🔥 Bug Fix' },
    { key: 'feature', label: '⚡ Feature' },
    { key: 'design', label: '🎨 Design' },
    { key: 'documentation', label: '📋 Documentation' },
    { key: 'testing', label: '🧪 Testing' },
    { key: 'general', label: '⚔️ General' }
  ];

  const timelineOptions = [
    { key: 'all', label: 'All Timelines' },
    { key: 'urgent', label: '🔥 Urgent (< 1 day)' },
    { key: 'short', label: '⚡ Short (1-3 days)' },
    { key: 'medium', label: '📅 Medium (1-2 weeks)' },
    { key: 'long', label: '📆 Long (2+ weeks)' },
    { key: 'flexible', label: '🌊 Flexible' }
  ];

  const assigneeOptions = [
    { key: 'all', label: 'All Missions' },
    { key: 'mine', label: 'My Missions' },
    { key: 'unassigned', label: 'Unassigned' },
    { key: 'team', label: 'Team Missions' }
  ];

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  // Handle reward range change
  const handleRewardRangeChange = (value) => {
    setRewardRange(value);
    handleFilterChange('rewardRange', value);
  };

  // Save current filter preset
  const handleSaveFilter = () => {
    if (filterName.trim()) {
      onSaveFilter({
        name: filterName,
        filters: { ...filters, searchTerm, rewardRange }
      });
      setFilterName('');
    }
  };

  // Clear all filters
  const handleClearAll = () => {
    onClearFilters();
    setRewardRange([0, 2000]);
    setShowAdvancedFilters(false);
  };

  // Check if any filters are active
  const hasActiveFilters = () => {
    return searchTerm || 
           Object.values(filters).some(value => value !== 'all' && value !== '') ||
           (rewardRange[0] !== 0 || rewardRange[1] !== 2000);
  };

  return (
    <Card className={`mission-filters ${className}`}>
      <CardBody className="p-4">
        {/* Main Search and Quick Filters */}
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="flex gap-2">
            <Input
              placeholder="🔍 Search missions, skills, keywords..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              startContent={<i className="bi bi-search text-default-400"></i>}
              className="flex-1"
              size="lg"
            />
            <Button
              variant="flat"
              onPress={() => setShowAdvancedFilters(!showAdvancedFilters)}
              startContent={<i className="bi bi-funnel"></i>}
            >
              Filters
            </Button>
          </div>

          {/* Quick Filter Row */}
          <div className="flex flex-wrap gap-2">
            <Select
              placeholder="Status"
              selectedKeys={[filters.status || 'all']}
              onSelectionChange={(keys) => handleFilterChange('status', Array.from(keys)[0])}
              className="w-32"
              size="sm"
            >
              {statusOptions.map(option => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            <Select
              placeholder="Difficulty"
              selectedKeys={[filters.difficulty || 'all']}
              onSelectionChange={(keys) => handleFilterChange('difficulty', Array.from(keys)[0])}
              className="w-40"
              size="sm"
            >
              {difficultyOptions.map(option => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            <Select
              placeholder="Type"
              selectedKeys={[filters.type || 'all']}
              onSelectionChange={(keys) => handleFilterChange('type', Array.from(keys)[0])}
              className="w-36"
              size="sm"
            >
              {typeOptions.map(option => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>

            <Select
              placeholder="Assignee"
              selectedKeys={[filters.assignee || 'all']}
              onSelectionChange={(keys) => handleFilterChange('assignee', Array.from(keys)[0])}
              className="w-32"
              size="sm"
            >
              {assigneeOptions.map(option => (
                <SelectItem key={option.key}>{option.label}</SelectItem>
              ))}
            </Select>
          </div>

          {/* Advanced Filters */}
          <AnimatePresence>
            {showAdvancedFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="border-t border-default-200 pt-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Timeline Filter */}
                  <div>
                    <label className="text-sm font-medium text-default-700 mb-2 block">
                      ⏰ Timeline
                    </label>
                    <Select
                      selectedKeys={[filters.timeline || 'all']}
                      onSelectionChange={(keys) => handleFilterChange('timeline', Array.from(keys)[0])}
                      size="sm"
                    >
                      {timelineOptions.map(option => (
                        <SelectItem key={option.key}>{option.label}</SelectItem>
                      ))}
                    </Select>
                  </div>

                  {/* Reward Range */}
                  <div>
                    <label className="text-sm font-medium text-default-700 mb-2 block">
                      💰 Reward Range: ${rewardRange[0]} - ${rewardRange[1]}
                    </label>
                    <Slider
                      step={50}
                      minValue={0}
                      maxValue={2000}
                      value={rewardRange}
                      onChange={handleRewardRangeChange}
                      className="w-full"
                      color="primary"
                    />
                  </div>

                  {/* Skill Matching */}
                  <div>
                    <label className="text-sm font-medium text-default-700 mb-2 block">
                      🎯 Skill Matching
                    </label>
                    <div className="flex items-center space-x-4">
                      <Switch
                        isSelected={filters.skillMatch}
                        onValueChange={(value) => handleFilterChange('skillMatch', value)}
                        size="sm"
                      >
                        Only show matches
                      </Switch>
                    </div>
                  </div>

                  {/* Show Completed */}
                  <div>
                    <label className="text-sm font-medium text-default-700 mb-2 block">
                      📊 Display Options
                    </label>
                    <div className="flex items-center space-x-4">
                      <Switch
                        isSelected={filters.showCompleted}
                        onValueChange={(value) => handleFilterChange('showCompleted', value)}
                        size="sm"
                      >
                        Show completed
                      </Switch>
                    </div>
                  </div>
                </div>

                {/* Filter Actions */}
                <div className="flex justify-between items-center mt-4 pt-4 border-t border-default-200">
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="flat"
                      color="danger"
                      onPress={handleClearAll}
                      startContent={<i className="bi bi-x-circle"></i>}
                    >
                      Clear All
                    </Button>
                    
                    <Popover>
                      <PopoverTrigger>
                        <Button
                          size="sm"
                          variant="flat"
                          startContent={<i className="bi bi-bookmark"></i>}
                        >
                          Save Filter
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent>
                        <div className="p-4 space-y-2">
                          <Input
                            placeholder="Filter name"
                            value={filterName}
                            onChange={(e) => setFilterName(e.target.value)}
                            size="sm"
                          />
                          <Button
                            size="sm"
                            color="primary"
                            onPress={handleSaveFilter}
                            className="w-full"
                          >
                            Save
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Saved Filters */}
                  {savedFilters.length > 0 && (
                    <div className="flex gap-2">
                      <span className="text-sm text-default-500">Saved:</span>
                      {savedFilters.slice(0, 3).map((saved, idx) => (
                        <Chip
                          key={idx}
                          size="sm"
                          variant="flat"
                          className="cursor-pointer"
                          onClick={() => onLoadFilter(saved)}
                        >
                          {saved.name}
                        </Chip>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Active Filters Display */}
          {hasActiveFilters() && (
            <div className="flex flex-wrap gap-2 pt-2 border-t border-default-200">
              <span className="text-sm text-default-500">Active filters:</span>
              
              {searchTerm && (
                <Chip size="sm" onClose={() => onSearchChange('')}>
                  Search: "{searchTerm}"
                </Chip>
              )}
              
              {Object.entries(filters).map(([key, value]) => {
                if (value && value !== 'all') {
                  return (
                    <Chip 
                      key={key} 
                      size="sm" 
                      onClose={() => handleFilterChange(key, 'all')}
                    >
                      {key}: {value}
                    </Chip>
                  );
                }
                return null;
              })}
              
              {(rewardRange[0] !== 0 || rewardRange[1] !== 2000) && (
                <Chip 
                  size="sm" 
                  onClose={() => setRewardRange([0, 2000])}
                >
                  Reward: ${rewardRange[0]}-${rewardRange[1]}
                </Chip>
              )}
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default MissionFilters;
