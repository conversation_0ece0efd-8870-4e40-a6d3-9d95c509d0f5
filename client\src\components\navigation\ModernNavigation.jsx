import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Avatar, Dropdown, DropdownTrigger, DropdownMenu, DropdownI<PERSON>, Badge, Drawer, Drawer<PERSON>ontent, Drawer<PERSON>eader, DrawerBody } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  BarChart3,
  DollarSign,
  Bell,
  Settings,
  User,
  LogOut,
  ChevronDown,
  Menu,
  X
} from 'lucide-react';

/**
 * Modern Navigation Component
 * 
 * Clean, focused navigation with:
 * - Core user journey: Start → Track → Earn
 * - Essential user actions: Notifications, Settings, Profile
 * - Everything else accessible through clever secondary navigation
 */
const ModernNavigation = ({ currentUser, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [notifications] = useState(3); // Mock notification count
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Core navigation items - the main user journey
  const coreNavItems = [
    {
      id: 'start',
      label: 'Start',
      path: '/start',
      icon: Play,
      description: 'Create projects and get started'
    },
    {
      id: 'track',
      label: 'Track',
      path: '/track',
      icon: BarChart3,
      description: 'Monitor progress and contributions'
    },
    {
      id: 'earn',
      label: 'Earn',
      path: '/earn',
      icon: DollarSign,
      description: 'View earnings and revenue'
    }
  ];

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false); // Close mobile menu after navigation
  };

  const handleUserAction = (action) => {
    switch (action) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        if (onLogout) {
          onLogout();
        }
        break;
      default:
        break;
    }
  };

  return (
    <>
      {/* Mobile Navigation Header */}
      <div className="md:hidden fixed top-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center justify-between px-4 h-16">
          {/* Mobile Menu Button */}
          <Button
            isIconOnly
            variant="light"
            onClick={() => setIsMobileMenuOpen(true)}
            aria-label="Open navigation menu"
            className="text-gray-700 dark:text-gray-300"
          >
            <Menu size={24} />
          </Button>

          {/* Logo/Brand */}
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
              <span className="text-white font-bold text-sm">R</span>
            </div>
            <span className="font-bold text-lg text-gray-900 dark:text-white">Royaltea</span>
          </div>

          {/* Profile Button */}
          {currentUser ? (
            <Dropdown placement="bottom-end">
              <DropdownTrigger>
                <Avatar
                  size="sm"
                  src={currentUser.avatar_url}
                  name={currentUser.full_name || currentUser.email}
                  className="cursor-pointer"
                />
              </DropdownTrigger>
              <DropdownMenu aria-label="Profile Actions">
                <DropdownItem key="profile" onClick={() => handleNavigation('/profile')}>
                  <div className="flex items-center gap-2">
                    <User size={16} />
                    Profile
                  </div>
                </DropdownItem>
                <DropdownItem key="settings" onClick={() => handleNavigation('/settings')}>
                  <div className="flex items-center gap-2">
                    <Settings size={16} />
                    Settings
                  </div>
                </DropdownItem>
                <DropdownItem key="logout" color="danger" onClick={onLogout}>
                  <div className="flex items-center gap-2">
                    <LogOut size={16} />
                    Sign Out
                  </div>
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          ) : (
            <Button
              size="sm"
              color="primary"
              onClick={() => handleNavigation('/login')}
            >
              Sign In
            </Button>
          )}
        </div>
      </div>

      {/* Desktop Navigation */}
      <nav className="hidden md:block sticky top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
          
          {/* Logo/Brand */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center gap-3"
          >
            <button
              onClick={() => navigate('/')}
              className="flex items-center gap-2 text-gray-900 dark:text-white hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
            >
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="font-bold text-xl hidden sm:block">Royaltea</span>
            </button>
          </motion.div>

          {/* Core Navigation - Main User Journey */}
          <div className="hidden md:flex items-center gap-2">
            {coreNavItems.map((item, index) => {
              const Icon = item.icon;
              const active = isActive(item.path);
              
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Button
                    variant={active ? "solid" : "light"}
                    color={active ? "primary" : "default"}
                    onClick={() => handleNavigation(item.path)}
                    className={`
                      ${active 
                        ? 'bg-purple-600 text-white shadow-lg' 
                        : 'text-gray-700 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-purple-900/20'
                      }
                      transition-all duration-200 font-medium
                    `}
                    startContent={<Icon size={18} />}
                  >
                    {item.label}
                  </Button>
                </motion.div>
              );
            })}
          </div>

          {/* Right Side - User Actions */}
          <div className="flex items-center gap-3">
            
            {/* Notifications */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Badge content={notifications} color="danger" size="sm" isInvisible={notifications === 0}>
                <Button
                  isIconOnly
                  variant="light"
                  onClick={() => navigate('/notifications')}
                  className="text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400"
                >
                  <Bell size={20} />
                </Button>
              </Badge>
            </motion.div>

            {/* User Menu */}
            {currentUser ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Dropdown placement="bottom-end">
                  <DropdownTrigger>
                    <Button
                      variant="light"
                      className="p-0 min-w-0 h-auto gap-2"
                    >
                      <Avatar
                        src={currentUser.avatar_url}
                        name={currentUser.display_name || currentUser.email}
                        size="sm"
                        className="cursor-pointer"
                      />
                      <ChevronDown size={16} className="text-gray-500" />
                    </Button>
                  </DropdownTrigger>
                  <DropdownMenu
                    aria-label="User menu"
                    onAction={handleUserAction}
                  >
                    <DropdownItem
                      key="profile"
                      startContent={<User size={16} />}
                      description="View and edit your profile"
                    >
                      Profile
                    </DropdownItem>
                    <DropdownItem
                      key="settings"
                      startContent={<Settings size={16} />}
                      description="Account and app settings"
                    >
                      Settings
                    </DropdownItem>
                    <DropdownItem
                      key="logout"
                      color="danger"
                      startContent={<LogOut size={16} />}
                      description="Sign out of your account"
                    >
                      Logout
                    </DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  color="primary"
                  onClick={() => navigate('/login')}
                  className="font-medium"
                >
                  Sign In
                </Button>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      </nav>

      {/* Mobile Navigation Drawer */}
      <Drawer
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        placement="left"
        size="sm"
        backdrop="blur"
        className="md:hidden z-50"
        classNames={{
          base: "z-50",
          backdrop: "z-40",
          wrapper: "z-50"
        }}
      >
        <DrawerContent>
          <DrawerHeader className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                <span className="text-white font-bold text-sm">R</span>
              </div>
              <span className="font-bold text-lg">Royaltea</span>
            </div>
            <Button
              isIconOnly
              variant="light"
              onClick={() => setIsMobileMenuOpen(false)}
              aria-label="Close navigation menu"
            >
              <X size={20} />
            </Button>
          </DrawerHeader>

          <DrawerBody className="px-4">
            <div className="space-y-6">
              {/* Main Navigation */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3 uppercase tracking-wider">
                  Main Journey
                </h3>
                <div className="space-y-2">
                  {coreNavItems.map((item) => {
                    const Icon = item.icon;
                    const active = isActive(item.path);

                    return (
                      <motion.div
                        key={item.id}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Button
                          variant={active ? "flat" : "light"}
                          color={active ? "primary" : "default"}
                          onClick={() => handleNavigation(item.path)}
                          className={`
                            w-full justify-start h-12 px-4
                            ${active
                              ? 'bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300'
                              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                            }
                          `}
                          startContent={<Icon size={20} />}
                        >
                          <div className="flex flex-col items-start">
                            <span className="font-medium">{item.label}</span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">{item.description}</span>
                          </div>
                        </Button>
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Quick Actions */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3 uppercase tracking-wider">
                  Quick Actions
                </h3>
                <div className="space-y-2">
                  <Button
                    variant="light"
                    onClick={() => handleNavigation('/notifications')}
                    className="w-full justify-start h-10 px-4 text-gray-700 dark:text-gray-300"
                    startContent={
                      <div className="relative">
                        <Bell size={18} />
                        {notifications > 0 && (
                          <Badge
                            content={notifications}
                            size="sm"
                            color="danger"
                            className="absolute -top-1 -right-1"
                          />
                        )}
                      </div>
                    }
                  >
                    Notifications
                  </Button>

                  <Button
                    variant="light"
                    onClick={() => handleNavigation('/settings')}
                    className="w-full justify-start h-10 px-4 text-gray-700 dark:text-gray-300"
                    startContent={<Settings size={18} />}
                  >
                    Settings
                  </Button>
                </div>
              </div>

              {/* User Actions */}
              {currentUser && (
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex items-center gap-3 mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Avatar
                      size="sm"
                      src={currentUser.avatar_url}
                      name={currentUser.full_name || currentUser.email}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {currentUser.full_name || 'User'}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {currentUser.email}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Button
                      variant="light"
                      onClick={() => handleNavigation('/profile')}
                      className="w-full justify-start h-10 px-4 text-gray-700 dark:text-gray-300"
                      startContent={<User size={18} />}
                    >
                      Profile
                    </Button>

                    <Button
                      variant="light"
                      color="danger"
                      onClick={() => {
                        setIsMobileMenuOpen(false);
                        onLogout?.();
                      }}
                      className="w-full justify-start h-10 px-4"
                      startContent={<LogOut size={18} />}
                    >
                      Sign Out
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </DrawerBody>
        </DrawerContent>
      </Drawer>

      {/* Mobile Bottom Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 z-[60] bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-800 safe-area-inset-bottom">
        <div className="flex items-center justify-around py-3 px-4">
          {coreNavItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);

            return (
              <motion.div
                key={item.id}
                whileTap={{ scale: 0.9 }}
                className="flex-1 flex justify-center"
              >
                <Button
                  variant="light"
                  onClick={() => handleNavigation(item.path)}
                  className={`
                    h-14 w-14 rounded-xl flex flex-col items-center justify-center gap-1 p-2
                    ${active
                      ? 'text-purple-600 bg-purple-50 dark:bg-purple-900/20'
                      : 'text-gray-600 dark:text-gray-400'
                    }
                    transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800
                  `}
                  aria-label={item.label}
                >
                  <Icon size={18} />
                  <span className="text-xs font-medium leading-none">{item.label}</span>
                </Button>
              </motion.div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default ModernNavigation;
