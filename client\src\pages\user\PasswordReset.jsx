import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { sendResetPasswordEmail } from "../../../utils/firebase/firebase.utils";
import toast from "react-hot-toast";
const PasswordReset = () => {
  const [email, setEmail] = useState("");
  const navigate = useNavigate();

  const handlePasswordReset = async (e) => {
    e.preventDefault();
    try {
      await sendResetPasswordEmail(email);
      navigate(-1);
      toast.success("Email Sent! It may take a few minutes.");
    } catch (error) {
      toast.error("Error sending reset email", error);
      console.log(error);
    }
  };
  return (
    <div className="container-sm mt-5">
      <div className="row justify-content-center">
        <div className="col-md-6" style={{ maxWidth: "500px" }}>
          <div className="card shadow-sm border-0 rounded-lg">
            <div className="card-body p-4 mx-auto">
              <h3 className="text-center mb-4">Send Reset Password Email</h3>
              <form onSubmit={handlePasswordReset} className="text-start">
                <div className="mb-3">
                  <label htmlFor="email" className="form-label">
                    Account Email
                  </label>
                  <input
                    type="email"
                    className="form-control"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                <div className="w-50 mx-auto">
                  <button
                    type="submit"
                    className="btn btn-primary btn-sm w-100 mx-auto d-block fw-semibold py-2 mt-4"
                  >
                    Reset
                  </button>
                  <button
                    type="button"
                    className="btn btn-secondary btn-sm w-100 mx-auto d-block fw-semibold py-2 mt-2"
                    onClick={() => navigate(-1)}
                  >
                    Cancel
                  </button>
                </div>
                <div className="mt-3 text-center">
                  No account yet? <Link to="/register">Sign up now!</Link>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordReset;
