import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import IntegrationStatusMonitor from '../../components/track/IntegrationStatusMonitor';

describe('IntegrationStatusMonitor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders all integration services', () => {
    render(<IntegrationStatusMonitor />);

    // Check for all expected integrations
    expect(screen.getByText('GitHub')).toBeInTheDocument();
    expect(screen.getByText('Slack')).toBeInTheDocument();
    expect(screen.getByText('Trello')).toBeInTheDocument();
    expect(screen.getByText('Linear')).toBeInTheDocument();
    expect(screen.getByText('Jira')).toBeInTheDocument();
    expect(screen.getByText('Discord')).toBeInTheDocument();
  });

  it('displays integration status correctly', () => {
    render(<IntegrationStatusMonitor />);

    // Check for status indicators
    expect(screen.getByText('Connected')).toBeInTheDocument();
    expect(screen.getByText('Sync Issues')).toBeInTheDocument();
    expect(screen.getByText('Disconnected')).toBeInTheDocument();
  });

  it('shows sync buttons for connected services', () => {
    render(<IntegrationStatusMonitor />);

    // Check for sync buttons
    const syncButtons = screen.getAllByText('Sync Now');
    expect(syncButtons.length).toBeGreaterThan(0);
  });

  it('shows connect buttons for disconnected services', () => {
    render(<IntegrationStatusMonitor />);

    // Check for connect buttons
    const connectButtons = screen.getAllByText('Connect');
    expect(connectButtons.length).toBeGreaterThan(0);
  });

  it('displays last sync timestamps', () => {
    render(<IntegrationStatusMonitor />);

    // Check for timestamp text
    expect(screen.getByText(/Last sync:/)).toBeInTheDocument();
  });

  it('handles sync button clicks', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    render(<IntegrationStatusMonitor />);

    const syncButton = screen.getAllByText('Sync Now')[0];
    fireEvent.click(syncButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Syncing'));
    });

    consoleSpy.mockRestore();
  });

  it('handles connect button clicks', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    
    render(<IntegrationStatusMonitor />);

    const connectButton = screen.getAllByText('Connect')[0];
    fireEvent.click(connectButton);

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('Connecting to'));
    });

    consoleSpy.mockRestore();
  });

  it('displays connection percentages', () => {
    render(<IntegrationStatusMonitor />);

    // Check for percentage indicators
    expect(screen.getByText('98%')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
    expect(screen.getByText('0%')).toBeInTheDocument();
  });

  it('applies correct status colors', () => {
    render(<IntegrationStatusMonitor />);

    // Check for status color classes (these would be applied via Tailwind)
    const statusElements = screen.getAllByText(/Connected|Sync Issues|Disconnected/);
    expect(statusElements.length).toBeGreaterThan(0);
  });

  it('is responsive on different screen sizes', () => {
    // Test mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    const { rerender } = render(<IntegrationStatusMonitor />);

    // Component should render on mobile
    expect(screen.getByText('Integration Status')).toBeInTheDocument();

    // Test desktop view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    rerender(<IntegrationStatusMonitor />);

    // Component should still render on desktop
    expect(screen.getByText('Integration Status')).toBeInTheDocument();
  });

  it('displays proper grid layout', () => {
    render(<IntegrationStatusMonitor />);

    // Check that the grid container exists
    const gridContainer = screen.getByText('Integration Status').closest('div');
    expect(gridContainer).toBeInTheDocument();
  });

  it('shows integration icons', () => {
    render(<IntegrationStatusMonitor />);

    // Check for mock icons (our test setup mocks lucide-react icons)
    const icons = screen.getAllByTestId('mock-icon');
    expect(icons.length).toBeGreaterThan(0);
  });

  it('handles error states gracefully', () => {
    // This test would be more meaningful with actual error handling
    // For now, we'll just verify the component renders without crashing
    expect(() => render(<IntegrationStatusMonitor />)).not.toThrow();
  });

  it('displays integration descriptions', () => {
    render(<IntegrationStatusMonitor />);

    // Check for service descriptions
    expect(screen.getByText('Code repository and issue tracking')).toBeInTheDocument();
    expect(screen.getByText('Team communication and notifications')).toBeInTheDocument();
    expect(screen.getByText('Project management and task boards')).toBeInTheDocument();
  });

  it('shows proper loading states during sync', async () => {
    render(<IntegrationStatusMonitor />);

    const syncButton = screen.getAllByText('Sync Now')[0];
    fireEvent.click(syncButton);

    // In a real implementation, we'd check for loading states
    // For now, we verify the button interaction works
    expect(syncButton).toBeInTheDocument();
  });

  it('maintains accessibility standards', () => {
    render(<IntegrationStatusMonitor />);

    // Check for proper heading structure
    expect(screen.getByRole('heading', { name: 'Integration Status' })).toBeInTheDocument();

    // Check for button accessibility
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
  });
});
