import React from 'react';
import { Card, CardBody, CardHeader, Avatar, Chip } from '@heroui/react';
import { motion } from 'framer-motion';

/**
 * Top Performers Widget - 2x1 Bento Grid Component
 * 
 * Features:
 * - Top performing users/projects ranking
 * - Performance metrics and earnings
 * - Visual ranking indicators
 * - Performance trends and badges
 */
const TopPerformers = ({ period, className = "" }) => {
  
  // Mock top performers data
  const topPerformers = [
    {
      id: 1,
      name: 'TaskMaster Pro',
      type: 'project',
      avatar: null,
      earnings: 18400,
      percentage: 39,
      missions: 23,
      rating: 4.9,
      trend: '+12%',
      badge: '🥇'
    },
    {
      id: 2,
      name: 'Creative Studio',
      type: 'project',
      avatar: null,
      earnings: 8200,
      percentage: 17,
      missions: 15,
      rating: 4.8,
      trend: '+8%',
      badge: '🥈'
    },
    {
      id: 3,
      name: 'Testing Tool',
      type: 'project',
      avatar: null,
      earnings: 6800,
      percentage: 14,
      missions: 12,
      rating: 4.7,
      trend: '+5%',
      badge: '🥉'
    }
  ];

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get trend color
  const getTrendColor = (trend) => {
    if (trend.startsWith('+')) return 'success';
    if (trend.startsWith('-')) return 'danger';
    return 'default';
  };

  return (
    <div className={`top-performers ${className}`}>
      <Card className="bg-gradient-to-br from-orange-50 to-red-100 dark:from-orange-900/20 dark:to-red-800/20 border-2 border-orange-200 dark:border-orange-700 h-full">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🔥</span>
              <h3 className="text-lg font-semibold">Top Performers</h3>
            </div>
            <Chip color="warning" variant="flat" size="sm">
              {period.toUpperCase()}
            </Chip>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          <div className="space-y-4">
            {topPerformers.map((performer, index) => (
              <motion.div
                key={performer.id}
                className="flex items-center gap-3 p-3 bg-white/50 dark:bg-slate-800/50 rounded-lg"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {/* Rank Badge */}
                <div className="flex-shrink-0">
                  <span className="text-2xl">{performer.badge}</span>
                </div>

                {/* Performer Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold text-sm truncate">
                      {performer.name}
                    </h4>
                    <Chip 
                      color={getTrendColor(performer.trend)} 
                      variant="flat" 
                      size="sm"
                    >
                      {performer.trend}
                    </Chip>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-default-600 mb-2">
                    <span>{formatCurrency(performer.earnings)} ({performer.percentage}%)</span>
                    <div className="flex items-center gap-1">
                      <span>{performer.rating}</span>
                      <span className="text-yellow-500">⭐</span>
                    </div>
                  </div>

                  {/* Performance Bar */}
                  <div className="w-full bg-default-200 rounded-full h-2">
                    <motion.div
                      className="bg-orange-500 h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${performer.percentage * 2.5}%` }}
                      transition={{ duration: 1, delay: index * 0.2 }}
                    />
                  </div>

                  <div className="text-xs text-default-500 mt-1">
                    {performer.missions} missions completed
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Summary Stats */}
          <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
            <div className="grid grid-cols-2 gap-3 text-center">
              <div>
                <div className="text-lg font-bold text-orange-600">
                  {formatCurrency(topPerformers.reduce((sum, p) => sum + p.earnings, 0))}
                </div>
                <div className="text-xs text-default-600">Combined Earnings</div>
              </div>
              <div>
                <div className="text-lg font-bold text-orange-600">
                  {topPerformers.reduce((sum, p) => sum + p.missions, 0)}
                </div>
                <div className="text-xs text-default-600">Total Missions</div>
              </div>
            </div>
          </div>

          {/* Performance Insights */}
          <div className="mt-3 text-xs text-default-600">
            <div className="flex items-center gap-1 mb-1">
              <span>💡</span>
              <span className="font-medium">Top performer earns 2.3x average</span>
            </div>
            <div className="flex items-center gap-1">
              <span>📈</span>
              <span className="font-medium">All top 3 showing positive growth</span>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

export default TopPerformers;
