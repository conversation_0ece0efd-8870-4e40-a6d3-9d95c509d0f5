// Profile Service API
// Integration & Services Agent: Enhanced user profile management system

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Get comprehensive user profile
const getUserProfile = async (user, targetUserId = null) => {
  try {
    const profileUserId = targetUserId || user.id;
    const isOwnProfile = profileUserId === user.id;

    // Update view count if viewing someone else's profile
    if (!isOwnProfile) {
      await supabase.rpc('update_profile_view_count', { profile_user_id: profileUserId });
    }

    // Get basic user info
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        display_name,
        email,
        avatar_url,
        created_at,
        is_premium
      `)
      .eq('id', profileUserId)
      .single();

    if (userError) {
      throw new Error(`Failed to fetch user data: ${userError.message}`);
    }

    // Get extended profile
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', profileUserId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      throw new Error(`Failed to fetch profile data: ${profileError.message}`);
    }

    // Get user skills
    const { data: skills, error: skillsError } = await supabase
      .from('user_skills')
      .select('*')
      .eq('user_id', profileUserId)
      .order('skill_level', { ascending: false })
      .order('years_experience', { ascending: false });

    if (skillsError) {
      console.error('Failed to fetch skills:', skillsError);
    }

    // Get portfolio items
    const { data: portfolio, error: portfolioError } = await supabase
      .from('portfolio_items')
      .select('*')
      .eq('user_id', profileUserId)
      .eq('is_public', true)
      .order('is_featured', { ascending: false })
      .order('display_order', { ascending: true });

    if (portfolioError) {
      console.error('Failed to fetch portfolio:', portfolioError);
    }

    // Get achievements
    const { data: achievements, error: achievementsError } = await supabase
      .from('user_achievements')
      .select('*')
      .eq('user_id', profileUserId)
      .order('is_featured', { ascending: false })
      .order('earned_date', { ascending: false });

    if (achievementsError) {
      console.error('Failed to fetch achievements:', achievementsError);
    }

    // Get skill summary
    const { data: skillSummary, error: summaryError } = await supabase
      .rpc('get_user_skill_summary', { target_user_id: profileUserId });

    if (summaryError) {
      console.error('Failed to fetch skill summary:', summaryError);
    }

    // Get analytics (only for own profile)
    let analytics = null;
    if (isOwnProfile) {
      const { data: analyticsData, error: analyticsError } = await supabase
        .from('profile_analytics')
        .select('*')
        .eq('user_id', profileUserId)
        .order('metric_date', { ascending: false })
        .limit(30);

      if (!analyticsError) {
        analytics = analyticsData;
      }
    }

    return {
      user: userData,
      profile: profileData,
      skills: skills || [],
      portfolio: portfolio || [],
      achievements: achievements || [],
      skill_summary: skillSummary?.[0] || {
        total_skills: 0,
        verified_skills: 0,
        expert_skills: 0,
        total_endorsements: 0
      },
      analytics: analytics,
      is_own_profile: isOwnProfile
    };

  } catch (error) {
    console.error('Get user profile error:', error);
    throw error;
  }
};

// Update user profile
const updateUserProfile = async (user, profileData) => {
  try {
    const {
      display_name,
      professional_title,
      bio,
      location,
      website_url,
      linkedin_url,
      github_url,
      twitter_url,
      portfolio_url,
      availability_status,
      profile_visibility,
      years_experience,
      hourly_rate,
      currency,
      timezone,
      languages,
      education,
      work_experience
    } = profileData;

    // Update basic user info
    if (display_name !== undefined) {
      const { error: userError } = await supabase
        .from('users')
        .update({ display_name })
        .eq('id', user.id);

      if (userError) {
        throw new Error(`Failed to update user data: ${userError.message}`);
      }
    }

    // Update or create extended profile
    const profileUpdates = {};
    if (professional_title !== undefined) profileUpdates.professional_title = professional_title;
    if (bio !== undefined) profileUpdates.bio = bio;
    if (location !== undefined) profileUpdates.location = location;
    if (website_url !== undefined) profileUpdates.website_url = website_url;
    if (linkedin_url !== undefined) profileUpdates.linkedin_url = linkedin_url;
    if (github_url !== undefined) profileUpdates.github_url = github_url;
    if (twitter_url !== undefined) profileUpdates.twitter_url = twitter_url;
    if (portfolio_url !== undefined) profileUpdates.portfolio_url = portfolio_url;
    if (availability_status !== undefined) profileUpdates.availability_status = availability_status;
    if (profile_visibility !== undefined) profileUpdates.profile_visibility = profile_visibility;
    if (years_experience !== undefined) profileUpdates.years_experience = years_experience;
    if (hourly_rate !== undefined) profileUpdates.hourly_rate = hourly_rate;
    if (currency !== undefined) profileUpdates.currency = currency;
    if (timezone !== undefined) profileUpdates.timezone = timezone;
    if (languages !== undefined) profileUpdates.languages = languages;
    if (education !== undefined) profileUpdates.education = education;
    if (work_experience !== undefined) profileUpdates.work_experience = work_experience;

    if (Object.keys(profileUpdates).length > 0) {
      const { error: profileError } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: user.id,
          ...profileUpdates
        });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }
    }

    return {
      success: true,
      message: 'Profile updated successfully'
    };

  } catch (error) {
    console.error('Update user profile error:', error);
    throw error;
  }
};

// Manage user skills
const manageUserSkills = async (user, action, skillData) => {
  try {
    switch (action) {
      case 'add':
        const {
          skill_name,
          skill_category,
          skill_level = 'beginner',
          years_experience = 0,
          proficiency_score
        } = skillData;

        if (!skill_name) {
          throw new Error('skill_name is required');
        }

        const { data: newSkill, error: addError } = await supabase
          .from('user_skills')
          .insert({
            user_id: user.id,
            skill_name,
            skill_category,
            skill_level,
            years_experience,
            proficiency_score
          })
          .select()
          .single();

        if (addError) {
          throw new Error(`Failed to add skill: ${addError.message}`);
        }

        return { success: true, skill: newSkill };

      case 'update':
        const { skill_id, ...updateData } = skillData;

        if (!skill_id) {
          throw new Error('skill_id is required');
        }

        const { data: updatedSkill, error: updateError } = await supabase
          .from('user_skills')
          .update(updateData)
          .eq('id', skill_id)
          .eq('user_id', user.id)
          .select()
          .single();

        if (updateError) {
          throw new Error(`Failed to update skill: ${updateError.message}`);
        }

        return { success: true, skill: updatedSkill };

      case 'delete':
        const { skill_id: deleteId } = skillData;

        if (!deleteId) {
          throw new Error('skill_id is required');
        }

        const { error: deleteError } = await supabase
          .from('user_skills')
          .delete()
          .eq('id', deleteId)
          .eq('user_id', user.id);

        if (deleteError) {
          throw new Error(`Failed to delete skill: ${deleteError.message}`);
        }

        return { success: true, message: 'Skill deleted successfully' };

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Manage user skills error:', error);
    throw error;
  }
};

// Endorse user skill
const endorseUserSkill = async (user, endorsementData) => {
  try {
    const {
      endorsed_id,
      skill_name,
      endorsement_level,
      message,
      relationship_context,
      project_context
    } = endorsementData;

    if (!endorsed_id || !skill_name || !endorsement_level) {
      throw new Error('endorsed_id, skill_name, and endorsement_level are required');
    }

    if (endorsed_id === user.id) {
      throw new Error('Cannot endorse your own skills');
    }

    // Check if skill exists for the user
    const { data: skill, error: skillError } = await supabase
      .from('user_skills')
      .select('id')
      .eq('user_id', endorsed_id)
      .eq('skill_name', skill_name)
      .single();

    if (skillError || !skill) {
      throw new Error('Skill not found for this user');
    }

    const { data: endorsement, error } = await supabase
      .from('skill_endorsements')
      .insert({
        endorser_id: user.id,
        endorsed_id,
        skill_name,
        endorsement_level,
        message,
        relationship_context,
        project_context
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create endorsement: ${error.message}`);
    }

    return {
      success: true,
      endorsement_id: endorsement.id,
      message: 'Skill endorsed successfully'
    };

  } catch (error) {
    console.error('Endorse user skill error:', error);
    throw error;
  }
};

// Manage portfolio items
const managePortfolioItem = async (user, action, itemData) => {
  try {
    switch (action) {
      case 'add':
        const {
          title,
          description,
          project_url,
          repository_url,
          demo_url,
          image_url,
          images = [],
          technologies = [],
          skills_demonstrated = [],
          project_type,
          project_status = 'completed',
          collaboration_type,
          team_size,
          role_in_project,
          start_date,
          completion_date,
          client_name,
          is_featured = false,
          is_public = true
        } = itemData;

        if (!title) {
          throw new Error('title is required');
        }

        const { data: newItem, error: addError } = await supabase
          .from('portfolio_items')
          .insert({
            user_id: user.id,
            title,
            description,
            project_url,
            repository_url,
            demo_url,
            image_url,
            images,
            technologies,
            skills_demonstrated,
            project_type,
            project_status,
            collaboration_type,
            team_size,
            role_in_project,
            start_date,
            completion_date,
            client_name,
            is_featured,
            is_public
          })
          .select()
          .single();

        if (addError) {
          throw new Error(`Failed to add portfolio item: ${addError.message}`);
        }

        return { success: true, item: newItem };

      case 'update':
        const { item_id, ...updateData } = itemData;

        if (!item_id) {
          throw new Error('item_id is required');
        }

        const { data: updatedItem, error: updateError } = await supabase
          .from('portfolio_items')
          .update(updateData)
          .eq('id', item_id)
          .eq('user_id', user.id)
          .select()
          .single();

        if (updateError) {
          throw new Error(`Failed to update portfolio item: ${updateError.message}`);
        }

        return { success: true, item: updatedItem };

      case 'delete':
        const { item_id: deleteId } = itemData;

        if (!deleteId) {
          throw new Error('item_id is required');
        }

        const { error: deleteError } = await supabase
          .from('portfolio_items')
          .delete()
          .eq('id', deleteId)
          .eq('user_id', user.id);

        if (deleteError) {
          throw new Error(`Failed to delete portfolio item: ${deleteError.message}`);
        }

        return { success: true, message: 'Portfolio item deleted successfully' };

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Manage portfolio item error:', error);
    throw error;
  }
};

// Get user settings
const getUserSettings = async (user) => {
  try {
    const { data: settings, error } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to fetch user settings: ${error.message}`);
    }

    return settings || {
      theme: 'system',
      language: 'en',
      timezone: null,
      currency: 'USD',
      date_format: 'MM/DD/YYYY',
      time_format: '12h',
      email_notifications: {
        project_updates: true,
        messages: true,
        endorsements: true,
        achievements: true,
        marketing: false
      },
      push_notifications: {
        project_updates: true,
        messages: true,
        endorsements: true,
        achievements: true
      },
      privacy_settings: {
        profile_visibility: 'public',
        show_email: false,
        show_location: true,
        show_experience: true,
        show_hourly_rate: false,
        show_availability: true,
        allow_endorsements: true,
        allow_contact: true
      },
      interface_preferences: {
        dashboard_layout: 'default',
        sidebar_collapsed: false,
        show_tooltips: true,
        animation_speed: 'normal'
      }
    };

  } catch (error) {
    console.error('Get user settings error:', error);
    throw error;
  }
};

// Update user settings
const updateUserSettings = async (user, settingsData) => {
  try {
    const { error } = await supabase
      .from('user_settings')
      .upsert({
        user_id: user.id,
        ...settingsData
      });

    if (error) {
      throw new Error(`Failed to update settings: ${error.message}`);
    }

    return {
      success: true,
      message: 'Settings updated successfully'
    };

  } catch (error) {
    console.error('Update user settings error:', error);
    throw error;
  }
};

// Get profile analytics
const getProfileAnalytics = async (user, timeRange = '30d') => {
  try {
    const daysBack = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : 30;
    const startDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const { data: analytics, error } = await supabase
      .from('profile_analytics')
      .select('*')
      .eq('user_id', user.id)
      .gte('metric_date', startDate)
      .order('metric_date', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch analytics: ${error.message}`);
    }

    // Calculate totals and trends
    const totals = analytics.reduce((acc, day) => ({
      profile_views: acc.profile_views + day.profile_views,
      portfolio_views: acc.portfolio_views + day.portfolio_views,
      skill_endorsements_received: acc.skill_endorsements_received + day.skill_endorsements_received,
      connection_requests: acc.connection_requests + day.connection_requests,
      project_inquiries: acc.project_inquiries + day.project_inquiries,
      search_appearances: acc.search_appearances + day.search_appearances
    }), {
      profile_views: 0,
      portfolio_views: 0,
      skill_endorsements_received: 0,
      connection_requests: 0,
      project_inquiries: 0,
      search_appearances: 0
    });

    return {
      analytics: analytics || [],
      totals,
      time_range: timeRange
    };

  } catch (error) {
    console.error('Get profile analytics error:', error);
    throw error;
  }
};

// Search profiles
const searchProfiles = async (user, queryParams) => {
  try {
    const searchTerm = queryParams.get('q') || '';
    const skills = queryParams.get('skills') ? queryParams.get('skills').split(',') : [];
    const location = queryParams.get('location') || '';
    const availability = queryParams.get('availability') || '';
    const limit = Math.min(parseInt(queryParams.get('limit')) || 20, 50);
    const offset = parseInt(queryParams.get('offset')) || 0;

    let query = supabase
      .from('user_profiles')
      .select(`
        user_id,
        professional_title,
        bio,
        location,
        availability_status,
        years_experience,
        users!inner (
          id,
          display_name,
          avatar_url,
          is_premium
        )
      `)
      .eq('profile_visibility', 'public')
      .range(offset, offset + limit - 1);

    // Apply filters
    if (searchTerm) {
      query = query.or(`professional_title.ilike.%${searchTerm}%,bio.ilike.%${searchTerm}%,users.display_name.ilike.%${searchTerm}%`);
    }

    if (location) {
      query = query.ilike('location', `%${location}%`);
    }

    if (availability) {
      query = query.eq('availability_status', availability);
    }

    const { data: profiles, error } = await query;

    if (error) {
      throw new Error(`Failed to search profiles: ${error.message}`);
    }

    // If skills filter is provided, get users with those skills
    let filteredProfiles = profiles || [];
    if (skills.length > 0) {
      const userIds = filteredProfiles.map(p => p.user_id);

      const { data: userSkills, error: skillsError } = await supabase
        .from('user_skills')
        .select('user_id, skill_name')
        .in('user_id', userIds)
        .in('skill_name', skills);

      if (!skillsError) {
        const usersWithSkills = new Set(userSkills.map(s => s.user_id));
        filteredProfiles = filteredProfiles.filter(p => usersWithSkills.has(p.user_id));
      }
    }

    return filteredProfiles;

  } catch (error) {
    console.error('Search profiles error:', error);
    throw error;
  }
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'profile':
        if (httpMethod === 'GET') {
          const targetUserId = queryParams.get('user_id');
          result = await getUserProfile(user, targetUserId);
        } else if (httpMethod === 'PUT') {
          result = await updateUserProfile(user, body);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'skills':
        if (httpMethod === 'POST') {
          const skillAction = body.action || 'add';
          result = await manageUserSkills(user, skillAction, body);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'endorse':
        if (httpMethod === 'POST') {
          result = await endorseUserSkill(user, body);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'portfolio':
        if (httpMethod === 'POST') {
          const portfolioAction = body.action || 'add';
          result = await managePortfolioItem(user, portfolioAction, body);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'settings':
        if (httpMethod === 'GET') {
          result = await getUserSettings(user);
        } else if (httpMethod === 'PUT') {
          result = await updateUserSettings(user, body);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'analytics':
        if (httpMethod === 'GET') {
          const timeRange = queryParams.get('time_range') || '30d';
          result = await getProfileAnalytics(user, timeRange);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      case 'search':
        if (httpMethod === 'GET') {
          result = await searchProfiles(user, queryParams);
        } else {
          throw new Error('Method not allowed');
        }
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Profile Service API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
