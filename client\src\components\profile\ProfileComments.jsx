import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { getProfileComments, addProfileComment } from '../../utils/profile/profile.utils';

const ProfileComments = ({ 
  profileId, 
  currentUser = null,
  isOwnProfile = false
}) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [newComment, setNewComment] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [showAllComments, setShowAllComments] = useState(false);
  
  // Fetch comments
  useEffect(() => {
    const fetchComments = async () => {
      try {
        setLoading(true);
        const data = await getProfileComments(profileId);
        setComments(data);
      } catch (err) {
        console.error('Error fetching comments:', err);
        setError('Failed to load comments. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    if (profileId) {
      fetchComments();
    }
  }, [profileId]);
  
  // Handle comment submission
  const handleSubmitComment = async (e) => {
    e.preventDefault();
    
    if (!newComment.trim() || !currentUser) return;
    
    try {
      setSubmitting(true);
      
      const comment = await addProfileComment(
        profileId,
        currentUser.id,
        newComment.trim()
      );
      
      // Add the new comment to the list with author details
      setComments([
        {
          ...comment,
          author: {
            id: currentUser.id,
            display_name: currentUser.display_name,
            avatar_url: currentUser.avatar_url
          }
        },
        ...comments
      ]);
      
      // Clear the input
      setNewComment('');
      
    } catch (err) {
      console.error('Error adding comment:', err);
      setError('Failed to add comment. Please try again later.');
    } finally {
      setSubmitting(false);
    }
  };
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else if (diffDays < 30) {
      return `${Math.floor(diffDays / 7)} weeks ago`;
    } else if (diffDays < 365) {
      return `${Math.floor(diffDays / 30)} months ago`;
    } else {
      return `${Math.floor(diffDays / 365)} years ago`;
    }
  };
  
  // Limit comments to display
  const displayComments = showAllComments ? comments : comments.slice(0, 3);
  
  return (
    <div className="profile-comments-section">
      <div className="section-header">
        <h3>Comments ({comments.length})</h3>
      </div>
      
      {/* Comment Form */}
      {currentUser && !isOwnProfile && (
        <div className="comment-form-container">
          <div className="comment-form-avatar">
            <img
              src={currentUser.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(currentUser.display_name || 'User')}&background=random`}
              alt={currentUser.display_name}
            />
          </div>
          
          <form className="comment-form" onSubmit={handleSubmitComment}>
            <textarea
              placeholder="Write a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              disabled={submitting}
              required
            ></textarea>
            
            <div className="comment-form-actions">
              <button 
                type="submit" 
                className="post-comment-btn"
                disabled={submitting || !newComment.trim()}
              >
                {submitting ? 'Posting...' : 'Post'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Comments List */}
      <div className="comments-list">
        {loading ? (
          <div className="comments-loading">Loading comments...</div>
        ) : error ? (
          <div className="comments-error">{error}</div>
        ) : comments.length === 0 ? (
          <div className="no-comments">
            <i className="bi bi-chat-square-text"></i>
            <p>No comments yet</p>
            {!isOwnProfile && currentUser && (
              <p className="be-first">Be the first to leave a comment!</p>
            )}
          </div>
        ) : (
          <>
            {displayComments.map((comment) => (
              <div key={comment.id} className="comment-item">
                <div className="comment-avatar">
                  <Link to={`/profile/${comment.author.id}`}>
                    <img
                      src={comment.author.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.author.display_name || 'User')}&background=random`}
                      alt={comment.author.display_name}
                    />
                  </Link>
                </div>
                
                <div className="comment-content">
                  <div className="comment-header">
                    <Link to={`/profile/${comment.author.id}`} className="comment-author">
                      {comment.author.display_name}
                    </Link>
                    <span className="comment-date">{formatDate(comment.created_at)}</span>
                  </div>
                  
                  <div className="comment-text">
                    {comment.content}
                  </div>
                  
                  {!comment.is_approved && (
                    <div className="comment-pending">
                      <i className="bi bi-clock"></i>
                      <span>Pending approval</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {comments.length > 3 && (
              <button 
                className="show-more-btn"
                onClick={() => setShowAllComments(!showAllComments)}
              >
                {showAllComments ? 'Show less' : `Show all ${comments.length} comments`}
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProfileComments;
