import React, { useState, useEffect, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { Card, CardBody, CardHeader, Avatar, Chip } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { formatDistanceToNow } from 'date-fns';

/**
 * Recent Activity Section
 *
 * Shows recent activity across all projects and contributions.
 * Part of the Home canvas in the experimental navigation system.
 */
const RecentActivity = () => {
  const { currentUser } = useContext(UserContext);
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch recent activities
  useEffect(() => {
    const fetchRecentActivities = async () => {
      if (!currentUser) return;

      try {
        // Try to fetch recent contributions
        let contributions = [];
        try {
          const { data } = await supabase
            .from('contributions')
            .select(`
              id,
              title,
              status,
              created_at,
              projects (name, id)
            `)
            .eq('user_id', currentUser.id)
            .order('created_at', { ascending: false })
            .limit(5);
          contributions = data || [];
        } catch (error) {
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Contributions table access issue (this is normal if database is not set up yet)');
          } else {
            console.error('Error fetching contributions:', error);
          }
        }

        // Try to fetch recent project updates (using created_by instead of owner_id)
        let projects = [];
        try {
          const { data } = await supabase
            .from('projects')
            .select('id, name, updated_at, status')
            .eq('created_by', currentUser.id)
            .order('updated_at', { ascending: false })
            .limit(3);
          projects = data || [];
        } catch (error) {
          if (error.message?.includes('401') || error.message?.includes('permission') || error.message?.includes('relation') || error.message?.includes('does not exist')) {
            console.warn('Projects table access issue (this is normal if database is not set up yet)');
          } else {
            console.error('Error fetching projects:', error);
          }
        }

        // Combine and format activities
        const formattedActivities = [];

        // Add contributions
        contributions?.forEach(contribution => {
          formattedActivities.push({
            id: `contribution-${contribution.id}`,
            type: 'contribution',
            title: contribution.title,
            subtitle: `in ${contribution.projects?.name || 'Unknown Project'}`,
            status: contribution.status,
            timestamp: contribution.created_at,
            icon: getContributionIcon(contribution.status),
            color: getContributionColor(contribution.status)
          });
        });

        // Add project updates
        projects?.forEach(project => {
          formattedActivities.push({
            id: `project-${project.id}`,
            type: 'project',
            title: `Project: ${project.name}`,
            subtitle: 'Updated',
            status: project.status,
            timestamp: project.updated_at,
            icon: '📁',
            color: 'from-blue-500 to-cyan-500'
          });
        });

        // Sort by timestamp
        formattedActivities.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        setActivities(formattedActivities.slice(0, 8)); // Show top 8 activities
      } catch (error) {
        console.error('Error fetching recent activities:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentActivities();
  }, [currentUser]);

  // Helper functions
  const getContributionIcon = (status) => {
    switch (status) {
      case 'approved': return '✅';
      case 'pending': return '⏳';
      case 'rejected': return '❌';
      default: return '📝';
    }
  };

  const getContributionColor = (status) => {
    switch (status) {
      case 'approved': return 'from-green-500 to-emerald-500';
      case 'pending': return 'from-yellow-500 to-orange-500';
      case 'rejected': return 'from-red-500 to-pink-500';
      default: return 'from-gray-500 to-slate-500';
    }
  };

  const getStatusChipColor = (status) => {
    switch (status) {
      case 'approved': return 'success';
      case 'pending': return 'warning';
      case 'rejected': return 'danger';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full"
        />
      </div>
    );
  }

  return (
    <div className="p-6">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-white/10 backdrop-blur-md border-white/20">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                <span className="text-xl">📈</span>
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">Recent Activity</h2>
                <p className="text-white/60 text-sm">Your latest project updates and contributions</p>
              </div>
            </div>
          </CardHeader>
          <CardBody>
            {activities.length === 0 ? (
              <div className="text-center py-8">
                <span className="text-6xl mb-4 block">🌟</span>
                <h3 className="text-white text-lg font-medium mb-2">No recent activity</h3>
                <p className="text-white/60">Start contributing to projects to see your activity here!</p>
              </div>
            ) : (
              <div className="space-y-4">
                {activities.map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                    className="flex items-center gap-4 p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-all duration-200"
                  >
                    {/* Activity Icon */}
                    <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${activity.color} flex items-center justify-center flex-shrink-0`}>
                      <span className="text-xl">{activity.icon}</span>
                    </div>

                    {/* Activity Details */}
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium truncate">{activity.title}</h4>
                      <p className="text-white/60 text-sm truncate">{activity.subtitle}</p>
                      <p className="text-white/40 text-xs mt-1">
                        {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                      </p>
                    </div>

                    {/* Status Chip */}
                    <div className="flex-shrink-0">
                      <Chip
                        color={getStatusChipColor(activity.status)}
                        variant="flat"
                        size="sm"
                      >
                        {activity.status}
                      </Chip>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default RecentActivity;
