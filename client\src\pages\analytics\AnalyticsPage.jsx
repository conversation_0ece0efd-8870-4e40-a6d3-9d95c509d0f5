import React, { useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import AnalyticsDashboard from '../../components/analytics/AnalyticsDashboard';

/**
 * Analytics Page - Main page for comprehensive analytics and insights
 *
 * Features:
 * - Full-screen analytics dashboard
 * - Three-column bento grid layout
 * - Integration with experimental navigation
 * - Responsive design for all screen sizes
 * - Context-aware helper buttons
 */
const AnalyticsPage = () => {
  const { currentUser } = useContext(UserContext);

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-default-600">Please log in to access the Analytics Dashboard</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
      {/* Three-Column Bento Grid Layout */}
      <div className="flex min-h-screen">
        {/* Left Helper Sidebar */}
        <div className="w-16 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-4 space-y-4">
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Notifications"
          >
            🔔
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Messages"
          >
            📧
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Quick Tasks"
          >
            📋
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Social Network"
          >
            👥
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Settings"
          >
            ⚙️
          </button>
        </div>

        {/* Center Content Area */}
        <div className="flex-1 p-6">
          <AnalyticsDashboard />
        </div>

        {/* Right Context Sidebar */}
        <div className="w-16 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-4 space-y-4">
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Custom Reports"
          >
            📊
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Data Export"
          >
            📤
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Forecasting"
          >
            📈
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Alert Settings"
          >
            🔔
          </button>
          <button
            className="p-3 rounded-lg hover:bg-default-100 transition-colors"
            title="Analytics Settings"
          >
            ⚙️
          </button>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
