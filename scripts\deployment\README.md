# Deployment Scripts

This directory contains deployment and build scripts for the Royaltea platform.

## 🚀 Primary Deployment

**Use the main deployment script in the root directory:**
```powershell
# From root directory
./deploy-netlify-cli.ps1 -RunBuild -NonInteractive
```

## 📋 Scripts in This Directory

### **Build Scripts**
- `build-netlify-deploy.ps1` - Build and prepare for deployment
- `build-and-deploy.ps1` - Legacy build and deploy method

### **Cleanup Scripts**
- `cleanup-migrations.ps1` - Clean up migration files
- `cleanup-all-test-data.js` - Remove test data (moved from root)

### **Organization Scripts**
- `organize-migrations.ps1` - Organize migration files

## 🔧 Environment Configuration

### **Required Environment Variables**
```bash
# Supabase Configuration
SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
SUPABASE_SERVICE_KEY=[service-key]
VITE_SUPABASE_URL=https://hqqlrrqvjcetoxbdjgzx.supabase.co
VITE_SUPABASE_ANON_KEY=[anon-key]

# Site Configuration
SITE_URL=https://royalty.technology
```

### **Environment Files**
- **Production**: Netlify environment variables
- **Development**: `client/.env.local`

## 🌐 Deployment Environments

### **Production**
- **URL**: https://royalty.technology
- **Hosting**: Netlify
- **Domain**: GoDaddy → Netlify
- **Build Command**: `npm run build`
- **Publish Directory**: `client/dist`

### **Development**
- **URL**: http://localhost:5173
- **Command**: `npm run dev`

## 🔍 Troubleshooting

### **Build Failures**
```bash
# Check dependencies
npm install
npm audit fix

# Clear cache and rebuild
rm -rf node_modules package-lock.json
npm install
npm run build
```

### **Deployment Issues**
```bash
# Re-authenticate with Netlify
netlify logout
netlify login

# Check site status
netlify status
```

## ⚠️ Important Notes

- Always use the main `deploy-netlify-cli.ps1` script from root directory
- These scripts are alternatives or utilities
- Test deployments in draft mode first
- Verify environment variables before deployment

## 📚 Related Documentation

- [DEPLOYMENT_DATABASE_GUIDE.md](../../DEPLOYMENT_DATABASE_GUIDE.md) - Complete deployment procedures
- [Netlify Documentation](https://docs.netlify.com/)
- [Deployment Infrastructure](../../docs/deployment-infrastructure.md)
