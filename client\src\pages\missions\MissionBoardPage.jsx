import React, { useState, useContext } from 'react';
import { Card, CardBody, Button, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import MissionBoard from '../../components/kanban/MissionBoard';
import { LoadingButton } from '../../components/common/LoadingStates';
import { useNavigate } from 'react-router-dom';

/**
 * MissionBoardPage Component
 *
 * Main page for the Mission Board - where users discover and claim missions
 * Integrates the comprehensive MissionBoard component with enhanced navigation
 */
const MissionBoardPage = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all-missions');
  const [selectedProject, setSelectedProject] = useState(null);

  const handleCreateMission = () => {
    navigate('/project/wizard');
  };

  const handleViewMyMissions = () => {
    setActiveTab('my-missions');
  };

  const handleViewAnalytics = () => {
    navigate('/analytics/contributions');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20">
      {/* Enhanced Three-Column Layout */}
      <div className="flex min-h-screen">
        {/* Left Quick Actions Sidebar */}
        <motion.div
          initial={{ x: -50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-r border-default-200 flex flex-col items-center py-6 space-y-4"
        >
          <div className="text-2xl mb-4">🎯</div>

          <LoadingButton
            size="sm"
            variant="flat"
            onClick={handleCreateMission}
            className="p-3 rounded-lg hover:bg-primary/10 transition-colors"
            title="Create Mission"
          >
            ➕
          </LoadingButton>

          <Button
            size="sm"
            variant="flat"
            onClick={handleViewMyMissions}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="My Missions"
          >
            👤
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={handleViewAnalytics}
            className="p-3 rounded-lg hover:bg-warning/10 transition-colors"
            title="Mission Analytics"
          >
            📊
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/teams')}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="Team Missions"
          >
            👥
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/earn')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="Earnings"
          >
            💰
          </Button>
        </motion.div>

        {/* Center Content Area */}
        <div className="flex-1 p-6">
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.1 }}
          >
            {/* Mission Board Tabs */}
            <Card className="mb-6">
              <CardBody className="p-4">
                <Tabs
                  selectedKey={activeTab}
                  onSelectionChange={setActiveTab}
                  variant="underlined"
                  color="primary"
                >
                  <Tab key="all-missions" title="🌟 All Missions" />
                  <Tab key="my-missions" title="👤 My Missions" />
                  <Tab key="available" title="🎯 Available" />
                  <Tab key="in-progress" title="⚡ In Progress" />
                  <Tab key="completed" title="✅ Completed" />
                </Tabs>
              </CardBody>
            </Card>

            {/* Mission Board Component */}
            <MissionBoard
              projectId={selectedProject}
              className="mission-board-container"
              filterByUser={activeTab === 'my-missions' ? currentUser?.id : null}
              filterByStatus={
                activeTab === 'available' ? 'todo' :
                activeTab === 'in-progress' ? 'in_progress' :
                activeTab === 'completed' ? 'done' : null
              }
            />
          </motion.div>
        </div>

        {/* Right Context Sidebar */}
        <motion.div
          initial={{ x: 50, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="w-20 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-l border-default-200 flex flex-col items-center py-6 space-y-4"
        >
          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/projects')}
            className="p-3 rounded-lg hover:bg-primary/10 transition-colors"
            title="All Projects"
          >
            📁
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/contributions')}
            className="p-3 rounded-lg hover:bg-warning/10 transition-colors"
            title="Track Time"
          >
            ⏱️
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/validation/metrics')}
            className="p-3 rounded-lg hover:bg-success/10 transition-colors"
            title="Validation"
          >
            ✅
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/profile')}
            className="p-3 rounded-lg hover:bg-secondary/10 transition-colors"
            title="Profile"
          >
            👤
          </Button>

          <Button
            size="sm"
            variant="flat"
            onClick={() => navigate('/settings')}
            className="p-3 rounded-lg hover:bg-default/10 transition-colors"
            title="Settings"
          >
            ⚙️
          </Button>
        </motion.div>
      </div>
    </div>
  );
};

export default MissionBoardPage;
