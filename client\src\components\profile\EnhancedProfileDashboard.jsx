// Enhanced Profile Dashboard Component
// Integration & Services Agent: Comprehensive profile management interface

import React, { useState, useEffect, useContext } from 'react';
import { 
  User, 
  Settings, 
  Award, 
  Briefcase, 
  Star, 
  Eye, 
  TrendingUp,
  Edit3,
  Plus,
  MapPin,
  Globe,
  Calendar,
  Users,
  Target,
  BarChart3
} from 'lucide-react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const EnhancedProfileDashboard = ({ userId = null }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  const isOwnProfile = !userId || userId === currentUser?.id;
  const targetUserId = userId || currentUser?.id;

  useEffect(() => {
    if (targetUserId) {
      loadProfileData();
    }
  }, [targetUserId]);

  const loadProfileData = async () => {
    try {
      setLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const url = `/.netlify/functions/profile-service/profile${userId ? `?user_id=${userId}` : ''}`;
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to load profile data');
      }

      setProfileData(result.data);
    } catch (err) {
      console.error('Failed to load profile data:', err);
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadProfileData();
    setRefreshing(false);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  const getAvailabilityColor = (status) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'busy': return 'bg-yellow-100 text-yellow-800';
      case 'unavailable': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSkillLevelColor = (level) => {
    switch (level) {
      case 'expert': return 'bg-purple-100 text-purple-800';
      case 'advanced': return 'bg-blue-100 text-blue-800';
      case 'intermediate': return 'bg-green-100 text-green-800';
      case 'beginner': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <p className="font-bold">Error Loading Profile</p>
            <p>Failed to load profile data</p>
          </div>
          <button
            onClick={loadProfileData}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const { user, profile, skills, portfolio, achievements, skill_summary, analytics } = profileData;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Profile Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center space-x-6">
              {/* Avatar */}
              <div className="flex-shrink-0">
                <img
                  src={user.avatar_url || '/default-avatar.png'}
                  alt={user.display_name}
                  className="h-24 w-24 rounded-full object-cover border-4 border-white shadow-lg"
                />
              </div>

              {/* User Info */}
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <h1 className="text-3xl font-bold text-gray-900">{user.display_name}</h1>
                  {user.is_premium && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      <Star className="h-3 w-3 mr-1" />
                      Premium
                    </span>
                  )}
                </div>
                
                {profile?.professional_title && (
                  <p className="text-xl text-gray-600 mt-1">{profile.professional_title}</p>
                )}

                <div className="flex items-center space-x-4 mt-3">
                  {profile?.location && (
                    <span className="flex items-center text-sm text-gray-500">
                      <MapPin className="h-4 w-4 mr-1" />
                      {profile.location}
                    </span>
                  )}
                  
                  {profile?.availability_status && (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAvailabilityColor(profile.availability_status)}`}>
                      {profile.availability_status.charAt(0).toUpperCase() + profile.availability_status.slice(1)}
                    </span>
                  )}

                  <span className="flex items-center text-sm text-gray-500">
                    <Calendar className="h-4 w-4 mr-1" />
                    Joined {new Date(user.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3">
                {isOwnProfile ? (
                  <>
                    <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                      <Edit3 className="h-4 w-4 mr-2" />
                      Edit Profile
                    </button>
                    <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </button>
                  </>
                ) : (
                  <>
                    <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                      <Users className="h-4 w-4 mr-2" />
                      Connect
                    </button>
                    <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                      <Briefcase className="h-4 w-4 mr-2" />
                      Hire
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Skills */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Target className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Skills</p>
                <p className="text-2xl font-bold text-gray-900">
                  {skill_summary.total_skills}
                </p>
                <p className="text-sm text-blue-600">
                  {skill_summary.verified_skills} verified
                </p>
              </div>
            </div>
          </div>

          {/* Portfolio */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Briefcase className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Portfolio</p>
                <p className="text-2xl font-bold text-gray-900">
                  {portfolio.length}
                </p>
                <p className="text-sm text-green-600">
                  {portfolio.filter(p => p.is_featured).length} featured
                </p>
              </div>
            </div>
          </div>

          {/* Achievements */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Achievements</p>
                <p className="text-2xl font-bold text-gray-900">
                  {achievements.length}
                </p>
                <p className="text-sm text-yellow-600">
                  {achievements.filter(a => a.is_featured).length} featured
                </p>
              </div>
            </div>
          </div>

          {/* Endorsements */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Star className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Endorsements</p>
                <p className="text-2xl font-bold text-gray-900">
                  {skill_summary.total_endorsements}
                </p>
                <p className="text-sm text-purple-600">
                  From peers
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: User },
                { id: 'skills', name: 'Skills', icon: Target },
                { id: 'portfolio', name: 'Portfolio', icon: Briefcase },
                { id: 'achievements', name: 'Achievements', icon: Award },
                ...(isOwnProfile ? [{ id: 'analytics', name: 'Analytics', icon: BarChart3 }] : [])
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Bio */}
                {profile?.bio && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">About</h3>
                    <p className="text-gray-700 leading-relaxed">{profile.bio}</p>
                  </div>
                )}

                {/* Quick Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Professional Info</h3>
                    <div className="space-y-2">
                      {profile?.years_experience && (
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Experience:</span> {profile.years_experience} years
                        </p>
                      )}
                      {profile?.hourly_rate && (
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Rate:</span> ${profile.hourly_rate}/{profile.currency || 'USD'} per hour
                        </p>
                      )}
                      {profile?.languages && profile.languages.length > 0 && (
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Languages:</span> {profile.languages.join(', ')}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Links</h3>
                    <div className="space-y-2">
                      {profile?.website_url && (
                        <a href={profile.website_url} target="_blank" rel="noopener noreferrer" className="flex items-center text-sm text-blue-600 hover:text-blue-800">
                          <Globe className="h-4 w-4 mr-2" />
                          Website
                        </a>
                      )}
                      {profile?.linkedin_url && (
                        <a href={profile.linkedin_url} target="_blank" rel="noopener noreferrer" className="flex items-center text-sm text-blue-600 hover:text-blue-800">
                          <Users className="h-4 w-4 mr-2" />
                          LinkedIn
                        </a>
                      )}
                      {profile?.github_url && (
                        <a href={profile.github_url} target="_blank" rel="noopener noreferrer" className="flex items-center text-sm text-blue-600 hover:text-blue-800">
                          <Briefcase className="h-4 w-4 mr-2" />
                          GitHub
                        </a>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'skills' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Skills & Expertise</h3>
                  {isOwnProfile && (
                    <button className="flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Skill
                    </button>
                  )}
                </div>

                {skills && skills.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {skills.map((skill, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{skill.skill_name}</h4>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSkillLevelColor(skill.skill_level)}`}>
                            {skill.skill_level}
                          </span>
                        </div>
                        {skill.skill_category && (
                          <p className="text-sm text-gray-500 mb-2">{skill.skill_category}</p>
                        )}
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>{skill.years_experience} years</span>
                          {skill.is_verified && (
                            <span className="text-green-600">✓ Verified</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No skills added yet</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'portfolio' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-medium text-gray-900">Portfolio</h3>
                  {isOwnProfile && (
                    <button className="flex items-center px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Project
                    </button>
                  )}
                </div>

                {portfolio && portfolio.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {portfolio.map((item, index) => (
                      <div key={index} className="bg-white border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                        {item.image_url && (
                          <img
                            src={item.image_url}
                            alt={item.title}
                            className="w-full h-48 object-cover"
                          />
                        )}
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{item.title}</h4>
                            {item.is_featured && (
                              <Star className="h-4 w-4 text-yellow-500" />
                            )}
                          </div>
                          {item.description && (
                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                          )}
                          {item.technologies && item.technologies.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {item.technologies.slice(0, 3).map((tech, techIndex) => (
                                <span key={techIndex} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {tech}
                                </span>
                              ))}
                              {item.technologies.length > 3 && (
                                <span className="text-xs text-gray-500">+{item.technologies.length - 3} more</span>
                              )}
                            </div>
                          )}
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">{item.project_type}</span>
                            <div className="flex items-center space-x-2">
                              {item.project_url && (
                                <a href={item.project_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                                  <Globe className="h-4 w-4" />
                                </a>
                              )}
                              {item.repository_url && (
                                <a href={item.repository_url} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-gray-800">
                                  <Briefcase className="h-4 w-4" />
                                </a>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Briefcase className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No portfolio items yet</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'achievements' && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-6">Achievements & Recognition</h3>

                {achievements && achievements.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {achievements.map((achievement, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center space-x-3 mb-3">
                          {achievement.achievement_icon && (
                            <span className="text-2xl">{achievement.achievement_icon}</span>
                          )}
                          <div>
                            <h4 className="font-medium text-gray-900">{achievement.achievement_name}</h4>
                            <p className="text-sm text-gray-500">{achievement.issuer}</p>
                          </div>
                        </div>
                        {achievement.achievement_description && (
                          <p className="text-sm text-gray-600 mb-2">{achievement.achievement_description}</p>
                        )}
                        <p className="text-xs text-gray-500">
                          Earned {new Date(achievement.earned_date).toLocaleDateString()}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Award className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No achievements yet</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'analytics' && isOwnProfile && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-6">Profile Analytics</h3>
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Analytics coming soon</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedProfileDashboard;
