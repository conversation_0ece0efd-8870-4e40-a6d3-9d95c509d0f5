# Build and deploy script for Royaltea

# Function to check if a command exists
function Test-Command {
    param (
        [string]$Command
    )
    
    $exists = $null -ne (Get-Command $Command -ErrorAction SilentlyContinue)
    return $exists
}

# Check if Node.js is installed
if (-not (Test-Command "node")) {
    Write-Error "Node.js is not installed. Please install Node.js and try again."
    exit 1
}

# Check if pnpm is installed
if (-not (Test-Command "pnpm")) {
    Write-Host "pnpm is not installed. Installing pnpm..."
    npm install -g pnpm
}

# Check if Netlify CLI is installed
if (-not (Test-Command "netlify")) {
    Write-Host "Netlify CLI is not installed. Installing Netlify CLI..."
    npm install -g netlify-cli
}

# Navigate to the client directory
Set-Location -Path "client"

# Install dependencies
Write-Host "Installing dependencies..."
pnpm install

# Build the project
Write-Host "Building the project..."
pnpm build

# Check if build was successful
if (-not (Test-Path -Path "dist")) {
    Write-Error "Build failed. Please check the error messages above."
    exit 1
}

# Navigate back to the root directory
Set-Location -Path ".."

# Deploy to Netlify
Write-Host "Deploying to Netlify..."
netlify deploy --prod --dir="client/dist"

Write-Host "Deployment completed successfully!"
