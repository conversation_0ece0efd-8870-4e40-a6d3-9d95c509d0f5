// Enhanced Admin Dashboard Component
// Integration & Services Agent: Comprehensive admin and moderation interface

import React, { useState, useEffect, useContext } from 'react';
import { 
  Users, 
  Shield, 
  AlertTriangle, 
  Ticket, 
  Activity, 
  Settings,
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  UserX,
  MessageSquare,
  TrendingUp,
  Server,
  Database
} from 'lucide-react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const EnhancedAdminDashboard = () => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/.netlify/functions/admin-service/dashboard', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to load admin dashboard');
      }

      setDashboardData(result.data);
    } catch (err) {
      console.error('Failed to load admin dashboard:', err);
      toast.error(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'normal': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'normal': return 'text-blue-600 bg-blue-100';
      case 'low': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <p className="font-bold">Error Loading Dashboard</p>
            <p>Failed to load admin dashboard data</p>
          </div>
          <button
            onClick={loadDashboardData}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const { statistics, recent_actions, system_metrics } = dashboardData;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="mt-1 text-sm text-gray-500">
                Platform administration and moderation center
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                <Activity className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          {/* Total Users */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(statistics.total_users)}
                </p>
              </div>
            </div>
          </div>

          {/* Active Users */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active (24h)</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(statistics.active_users_24h)}
                </p>
              </div>
            </div>
          </div>

          {/* Pending Moderation */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Shield className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(statistics.pending_moderation)}
                </p>
              </div>
            </div>
          </div>

          {/* Open Tickets */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Ticket className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Open Tickets</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(statistics.open_tickets)}
                </p>
              </div>
            </div>
          </div>

          {/* Critical Issues */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Critical Issues</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatNumber(statistics.critical_issues)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: BarChart3 },
                { id: 'users', name: 'User Management', icon: Users },
                { id: 'moderation', name: 'Content Moderation', icon: Shield },
                { id: 'support', name: 'Support Tickets', icon: Ticket },
                { id: 'monitoring', name: 'System Monitoring', icon: Server }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Recent Admin Actions */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Admin Actions</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    {recent_actions && recent_actions.length > 0 ? (
                      <div className="space-y-3">
                        {recent_actions.slice(0, 10).map((action, index) => (
                          <div key={index} className="flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0">
                            <div className="flex items-center space-x-3">
                              <div className="flex-shrink-0">
                                {action.action_type.includes('suspend') && <UserX className="h-4 w-4 text-red-500" />}
                                {action.action_type.includes('moderate') && <Shield className="h-4 w-4 text-yellow-500" />}
                                {action.action_type.includes('ticket') && <Ticket className="h-4 w-4 text-blue-500" />}
                                {!action.action_type.includes('suspend') && !action.action_type.includes('moderate') && !action.action_type.includes('ticket') && <Activity className="h-4 w-4 text-gray-500" />}
                              </div>
                              <div>
                                <p className="text-sm font-medium text-gray-900 capitalize">
                                  {action.action_type.replace('_', ' ')}
                                </p>
                                <p className="text-xs text-gray-500">
                                  by {action.admin?.email || 'Unknown'} • {new Date(action.created_at).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                            <div className="text-xs text-gray-500 capitalize">
                              {action.target_type}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-4">No recent admin actions</p>
                    )}
                  </div>
                </div>

                {/* System Health Overview */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">System Health</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {system_metrics && system_metrics.length > 0 ? (
                      system_metrics.slice(0, 6).map((metric, index) => (
                        <div key={index} className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-gray-900 capitalize">
                                {metric.metric_name || metric.metric_type}
                              </p>
                              <p className="text-lg font-bold text-gray-900">
                                {metric.metric_value} {metric.metric_unit}
                              </p>
                            </div>
                            <div className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(metric.status)}`}>
                              {metric.status}
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="col-span-3 text-center py-8 text-gray-500">
                        <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No system metrics available</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'users' && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">User Management</h3>
                <p className="text-gray-500 mb-4">Comprehensive user administration tools</p>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                  Open User Management
                </button>
              </div>
            )}

            {activeTab === 'moderation' && (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Content Moderation</h3>
                <p className="text-gray-500 mb-4">Review and moderate platform content</p>
                <button className="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700">
                  Open Moderation Queue
                </button>
              </div>
            )}

            {activeTab === 'support' && (
              <div className="text-center py-8">
                <Ticket className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Support Tickets</h3>
                <p className="text-gray-500 mb-4">Manage user support requests</p>
                <button className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                  Open Support Center
                </button>
              </div>
            )}

            {activeTab === 'monitoring' && (
              <div className="text-center py-8">
                <Server className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">System Monitoring</h3>
                <p className="text-gray-500 mb-4">Platform health and performance metrics</p>
                <button className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                  Open Monitoring Dashboard
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50">
              <Eye className="h-5 w-5 mr-2 text-gray-500" />
              View All Users
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50">
              <Shield className="h-5 w-5 mr-2 text-gray-500" />
              Review Content
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50">
              <MessageSquare className="h-5 w-5 mr-2 text-gray-500" />
              Support Queue
            </button>
            <button className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md hover:bg-gray-50">
              <Settings className="h-5 w-5 mr-2 text-gray-500" />
              System Settings
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAdminDashboard;
