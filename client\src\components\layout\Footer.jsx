import { Link } from "react-router-dom";

const Footer = (props) => {
  const { navLinks } = props;
  return (
    <footer className="bg-light text-center text-lg-start mt-4">
      <div className="container p-4">
        <div className="row">
          <div className="col-lg-6 col-md-12 mb-4 mb-md-0">
            <h5 className="text-uppercase">Royaltea</h5>
            <p>
              Empowering creators with fair compensation and transparent royalty tracking.
              Build together, earn together, and transform how creative work is valued.
            </p>
          </div>
          <div className="col-lg-6 col-md-12 mb-4 mb-md-0">
            <h5 className="text-uppercase">Quick Links</h5>
            <ul className="list-unstyled mb-0">
              {navLinks.map((link, index) => {
                return <li key={index}>
                  <Link to={link.path} className="text-dark">{link.title}</Link>
                </li>
              })}
            </ul>
          </div>
        </div>
      </div>
      <div className="text-center p-3" style={{ backgroundColor: '#f1f1f1' }}>
        &copy; {new Date().getFullYear()} Royaltea. All rights reserved.
      </div>
    </footer>
  );
}

export default Footer;
