import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Select, SelectItem, Avatar, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * ProjectInsights Component - Project Performance Analysis and Insights
 * 
 * Features:
 * - Comprehensive project performance tracking and analysis
 * - Timeline efficiency and budget performance monitoring
 * - Team collaboration metrics and individual contributions
 * - Success rate analysis and bottleneck identification
 * - Project comparison and benchmarking
 * - Predictive project success modeling
 */
const ProjectInsights = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState('all');
  const [projectData, setProjectData] = useState({});

  // Load project insights data
  const loadProjectData = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Mock data for development - in production this would call the project analytics API
      const mockData = {
        overview: {
          totalProjects: 24,
          completedProjects: 22,
          activeProjects: 2,
          successRate: 91.7,
          avgCompletionTime: 18.5,
          onTimeDelivery: 86.4,
          budgetEfficiency: 94.2
        },
        recentProjects: [
          {
            id: 'proj_001',
            name: 'E-commerce Platform Redesign',
            status: 'completed',
            completionDate: '2025-01-10',
            duration: 45,
            plannedDuration: 50,
            budget: 15000,
            actualCost: 14200,
            efficiency: 98,
            rating: 4.9,
            team: [
              { id: 'u1', name: 'Sarah Chen', role: 'Designer', avatar: null, contribution: 35 },
              { id: 'u2', name: 'Mike Johnson', role: 'Developer', avatar: null, contribution: 45 },
              { id: 'u3', name: 'Alex Kim', role: 'PM', avatar: null, contribution: 20 }
            ],
            milestones: [
              { name: 'Design Phase', planned: 15, actual: 12, status: 'completed' },
              { name: 'Development', planned: 25, actual: 28, status: 'completed' },
              { name: 'Testing', planned: 7, actual: 5, status: 'completed' },
              { name: 'Deployment', planned: 3, actual: 3, status: 'completed' }
            ]
          },
          {
            id: 'proj_002',
            name: 'Mobile App Development',
            status: 'active',
            startDate: '2024-12-15',
            duration: 32,
            plannedDuration: 40,
            budget: 22000,
            currentCost: 16800,
            efficiency: 85,
            rating: null,
            team: [
              { id: 'u4', name: 'Lisa Wang', role: 'Lead Dev', avatar: null, contribution: 40 },
              { id: 'u5', name: 'Tom Brown', role: 'UI/UX', avatar: null, contribution: 30 },
              { id: 'u6', name: 'Emma Davis', role: 'QA', avatar: null, contribution: 30 }
            ],
            milestones: [
              { name: 'Planning', planned: 8, actual: 7, status: 'completed' },
              { name: 'Development', planned: 20, actual: 18, status: 'active' },
              { name: 'Testing', planned: 8, actual: 0, status: 'pending' },
              { name: 'Launch', planned: 4, actual: 0, status: 'pending' }
            ]
          }
        ],
        performanceMetrics: {
          timelinePerformance: {
            onTime: 19,
            early: 3,
            late: 2,
            avgDelay: 2.3
          },
          budgetPerformance: {
            underBudget: 18,
            onBudget: 4,
            overBudget: 2,
            avgSavings: 5.8
          },
          qualityMetrics: {
            avgRating: 4.7,
            clientSatisfaction: 94,
            reworkRate: 8,
            defectRate: 2.1
          }
        },
        insights: [
          {
            type: 'success',
            title: 'Excellent Timeline Management',
            description: 'Your projects are consistently delivered on time with 86% on-time delivery rate.',
            impact: 'high'
          },
          {
            type: 'warning',
            title: 'Development Phase Bottleneck',
            description: 'Development phases tend to take 15% longer than planned. Consider better estimation.',
            impact: 'medium'
          },
          {
            type: 'info',
            title: 'Strong Team Collaboration',
            description: 'Team satisfaction scores are 20% above platform average.',
            impact: 'medium'
          }
        ]
      };

      setProjectData(mockData);
    } catch (error) {
      console.error('Error loading project data:', error);
      toast.error('Failed to load project insights');
    } finally {
      setLoading(false);
    }
  };

  // Format duration
  const formatDuration = (days) => {
    if (days < 7) return `${days} days`;
    if (days < 30) return `${Math.round(days / 7)} weeks`;
    return `${Math.round(days / 30)} months`;
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'completed': 'success',
      'active': 'primary',
      'pending': 'warning',
      'delayed': 'danger'
    };
    return colors[status] || 'default';
  };

  // Get efficiency color
  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 95) return 'success';
    if (efficiency >= 85) return 'primary';
    if (efficiency >= 75) return 'warning';
    return 'danger';
  };

  // Initialize component
  useEffect(() => {
    loadProjectData();
  }, [selectedProject]);

  if (loading) {
    return (
      <Card className={className}>
        <CardBody className="p-6 text-center">
          <div className="animate-spin text-2xl mb-2">🔄</div>
          <div>Loading project insights...</div>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className={`project-insights ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 mb-6">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🎯</span>
              <div>
                <h2 className="text-2xl font-bold">Project Insights</h2>
                <p className="text-default-600">Performance analysis and optimization recommendations</p>
              </div>
            </div>
            <Select
              selectedKeys={[selectedProject]}
              onSelectionChange={(keys) => setSelectedProject(Array.from(keys)[0])}
              className="w-48"
              size="sm"
            >
              <SelectItem key="all">All Projects</SelectItem>
              <SelectItem key="completed">Completed Only</SelectItem>
              <SelectItem key="active">Active Only</SelectItem>
            </Select>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-primary">{projectData.overview?.totalProjects}</div>
              <div className="text-sm text-default-600">Total Projects</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-success">{projectData.overview?.successRate}%</div>
              <div className="text-sm text-default-600">Success Rate</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-warning">{projectData.overview?.onTimeDelivery}%</div>
              <div className="text-sm text-default-600">On-Time Delivery</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardBody className="p-4 text-center">
              <div className="text-2xl font-bold text-secondary">{formatDuration(projectData.overview?.avgCompletionTime)}</div>
              <div className="text-sm text-default-600">Avg Duration</div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Recent Projects */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mb-6"
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">📊 Recent Projects</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-6">
              {projectData.recentProjects?.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="border border-default-200 rounded-lg p-4"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h4 className="font-semibold text-lg">{project.name}</h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Chip color={getStatusColor(project.status)} size="sm" variant="flat">
                          {project.status}
                        </Chip>
                        {project.rating && (
                          <Chip color="warning" size="sm" variant="flat">
                            ⭐ {project.rating}
                          </Chip>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-default-600">Efficiency</div>
                      <div className={`text-lg font-bold text-${getEfficiencyColor(project.efficiency)}`}>
                        {project.efficiency}%
                      </div>
                    </div>
                  </div>

                  {/* Project Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <div className="text-sm text-default-600">Duration</div>
                      <div className="font-medium">
                        {formatDuration(project.duration)} / {formatDuration(project.plannedDuration)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">Budget</div>
                      <div className="font-medium">
                        {formatCurrency(project.actualCost || project.currentCost)} / {formatCurrency(project.budget)}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">Team Size</div>
                      <div className="font-medium">{project.team.length} members</div>
                    </div>
                    <div>
                      <div className="text-sm text-default-600">Progress</div>
                      <div className="font-medium">
                        {project.status === 'completed' ? '100%' : 
                         Math.round((project.duration / project.plannedDuration) * 100)}%
                      </div>
                    </div>
                  </div>

                  {/* Team Members */}
                  <div className="mb-4">
                    <div className="text-sm font-medium mb-2">Team Members</div>
                    <div className="flex items-center gap-3">
                      {project.team.map((member) => (
                        <div key={member.id} className="flex items-center gap-2">
                          <Avatar 
                            src={member.avatar}
                            name={member.name}
                            size="sm"
                          />
                          <div className="text-xs">
                            <div className="font-medium">{member.name}</div>
                            <div className="text-default-500">{member.role}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Milestones */}
                  <div>
                    <div className="text-sm font-medium mb-2">Milestones</div>
                    <div className="space-y-2">
                      {project.milestones.map((milestone, idx) => (
                        <div key={idx} className="flex items-center gap-3">
                          <div className="w-32 text-sm">{milestone.name}</div>
                          <div className="flex-1">
                            <Progress 
                              value={milestone.status === 'completed' ? 100 : 
                                     milestone.status === 'active' ? 60 : 0} 
                              color={getStatusColor(milestone.status)} 
                              size="sm"
                            />
                          </div>
                          <div className="text-xs text-default-500 w-20">
                            {milestone.actual || 0}/{milestone.planned} days
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>

      {/* Performance Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        {/* Timeline Performance */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">⏰ Timeline Performance</h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">On Time</span>
                  <div className="flex items-center gap-2">
                    <Progress value={(projectData.performanceMetrics?.timelinePerformance?.onTime / 24) * 100} color="success" size="sm" className="w-16" />
                    <span className="text-sm font-medium">{projectData.performanceMetrics?.timelinePerformance?.onTime}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Early</span>
                  <div className="flex items-center gap-2">
                    <Progress value={(projectData.performanceMetrics?.timelinePerformance?.early / 24) * 100} color="primary" size="sm" className="w-16" />
                    <span className="text-sm font-medium">{projectData.performanceMetrics?.timelinePerformance?.early}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Late</span>
                  <div className="flex items-center gap-2">
                    <Progress value={(projectData.performanceMetrics?.timelinePerformance?.late / 24) * 100} color="danger" size="sm" className="w-16" />
                    <span className="text-sm font-medium">{projectData.performanceMetrics?.timelinePerformance?.late}</span>
                  </div>
                </div>
                <div className="text-xs text-default-500 mt-2">
                  Avg delay: {projectData.performanceMetrics?.timelinePerformance?.avgDelay} days
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Budget Performance */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">💰 Budget Performance</h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Under Budget</span>
                  <div className="flex items-center gap-2">
                    <Progress value={(projectData.performanceMetrics?.budgetPerformance?.underBudget / 24) * 100} color="success" size="sm" className="w-16" />
                    <span className="text-sm font-medium">{projectData.performanceMetrics?.budgetPerformance?.underBudget}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">On Budget</span>
                  <div className="flex items-center gap-2">
                    <Progress value={(projectData.performanceMetrics?.budgetPerformance?.onBudget / 24) * 100} color="primary" size="sm" className="w-16" />
                    <span className="text-sm font-medium">{projectData.performanceMetrics?.budgetPerformance?.onBudget}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Over Budget</span>
                  <div className="flex items-center gap-2">
                    <Progress value={(projectData.performanceMetrics?.budgetPerformance?.overBudget / 24) * 100} color="danger" size="sm" className="w-16" />
                    <span className="text-sm font-medium">{projectData.performanceMetrics?.budgetPerformance?.overBudget}</span>
                  </div>
                </div>
                <div className="text-xs text-default-500 mt-2">
                  Avg savings: {projectData.performanceMetrics?.budgetPerformance?.avgSavings}%
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Quality Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">⭐ Quality Metrics</h3>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Avg Rating</span>
                  <span className="text-sm font-medium">{projectData.performanceMetrics?.qualityMetrics?.avgRating}/5.0</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Client Satisfaction</span>
                  <span className="text-sm font-medium">{projectData.performanceMetrics?.qualityMetrics?.clientSatisfaction}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Rework Rate</span>
                  <span className="text-sm font-medium">{projectData.performanceMetrics?.qualityMetrics?.reworkRate}%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Defect Rate</span>
                  <span className="text-sm font-medium">{projectData.performanceMetrics?.qualityMetrics?.defectRate}%</span>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Insights and Recommendations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9 }}
      >
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">💡 Insights & Recommendations</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-3">
              {projectData.insights?.map((insight, index) => (
                <div key={index} className={`p-3 rounded-lg border-l-4 ${
                  insight.type === 'success' ? 'border-success bg-success-50 dark:bg-success-900/20' :
                  insight.type === 'warning' ? 'border-warning bg-warning-50 dark:bg-warning-900/20' :
                  'border-primary bg-primary-50 dark:bg-primary-900/20'
                }`}>
                  <div className="flex items-start gap-3">
                    <span className="text-lg">
                      {insight.type === 'success' ? '✅' : insight.type === 'warning' ? '⚠️' : 'ℹ️'}
                    </span>
                    <div>
                      <h4 className="font-medium">{insight.title}</h4>
                      <p className="text-sm text-default-600 mt-1">{insight.description}</p>
                      <Chip size="sm" variant="flat" color={
                        insight.impact === 'high' ? 'danger' : 
                        insight.impact === 'medium' ? 'warning' : 'default'
                      } className="mt-2">
                        {insight.impact} impact
                      </Chip>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default ProjectInsights;
