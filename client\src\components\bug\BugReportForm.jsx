import React, { useState, useContext } from 'react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const BugReportForm = ({ onSuccess, onCancel }) => {
  const { currentUser } = useContext(UserContext);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [steps, setSteps] = useState('');
  const [expected, setExpected] = useState('');
  const [actual, setActual] = useState('');
  const [severity, setSeverity] = useState('medium');
  const [area, setArea] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get browser and device info
  const getBrowserInfo = () => {
    return `${navigator.userAgent}`;
  };

  const getDeviceInfo = () => {
    return `Screen: ${window.screen.width}x${window.screen.height}, Window: ${window.innerWidth}x${window.innerHeight}, DPR: ${window.devicePixelRatio}`;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!title.trim() || !description.trim()) {
      toast.error('Please provide both a title and description');
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const bugReport = {
        title: title.trim(),
        description: description.trim(),
        status: 'open',
        reported_by: currentUser?.id,
        severity,
        affected_area: area.trim(),
        browser_info: getBrowserInfo(),
        device_info: getDeviceInfo(),
        steps_to_reproduce: steps.trim(),
        expected_behavior: expected.trim(),
        actual_behavior: actual.trim(),
        is_public: false
      };
      
      const { data, error } = await supabase
        .from('bug_reports')
        .insert(bugReport)
        .select();
      
      if (error) throw error;
      
      toast.success('Bug report submitted successfully');
      
      if (onSuccess) {
        onSuccess(data[0]);
      }
      
      // Reset form
      setTitle('');
      setDescription('');
      setSteps('');
      setExpected('');
      setActual('');
      setSeverity('medium');
      setArea('');
    } catch (error) {
      console.error('Error submitting bug report:', error);
      toast.error('Failed to submit bug report');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bug-report-form-container">
      <div className="bug-report-form-header">
        <h2>Report a Bug</h2>
        <p>Help us improve by reporting any issues you encounter</p>
      </div>
      
      <form onSubmit={handleSubmit} className="bug-report-form">
        <div className="form-group">
          <label htmlFor="bug-title">Title *</label>
          <input
            id="bug-title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Brief description of the issue"
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="bug-description">Description *</label>
          <textarea
            id="bug-description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Detailed description of what happened"
            rows={4}
            required
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="bug-severity">Severity</label>
            <select
              id="bug-severity"
              value={severity}
              onChange={(e) => setSeverity(e.target.value)}
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
          
          <div className="form-group">
            <label htmlFor="bug-area">Affected Area</label>
            <input
              id="bug-area"
              type="text"
              value={area}
              onChange={(e) => setArea(e.target.value)}
              placeholder="Which part of the app? (e.g., Profile, Projects)"
            />
          </div>
        </div>
        
        <div className="form-group">
          <label htmlFor="bug-steps">Steps to Reproduce</label>
          <textarea
            id="bug-steps"
            value={steps}
            onChange={(e) => setSteps(e.target.value)}
            placeholder="1. Go to...\n2. Click on...\n3. Observe that..."
            rows={3}
          />
        </div>
        
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="bug-expected">Expected Behavior</label>
            <textarea
              id="bug-expected"
              value={expected}
              onChange={(e) => setExpected(e.target.value)}
              placeholder="What should have happened?"
              rows={2}
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="bug-actual">Actual Behavior</label>
            <textarea
              id="bug-actual"
              value={actual}
              onChange={(e) => setActual(e.target.value)}
              placeholder="What actually happened?"
              rows={2}
            />
          </div>
        </div>
        
        <div className="form-note">
          <p>
            <strong>Note:</strong> Your browser and device information will be automatically included to help us diagnose the issue.
          </p>
        </div>
        
        <div className="form-actions">
          <button 
            type="button" 
            className="cancel-button"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button 
            type="submit" 
            className="submit-button"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Report'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default BugReportForm;
