import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Progress, Avatar, Tabs, Tab } from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import MissionCard from './MissionCard';
import MissionCreator from './MissionCreator';
import ProgressTracker from './ProgressTracker';

/**
 * Mission Board Component - Mission Display and Management
 * 
 * Features:
 * - Bento grid layout for mission cards
 * - Mission filtering and categorization
 * - Progress tracking and achievement system
 * - Mission creation and management
 * - Real-time updates and notifications
 */
const MissionBoard = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [missionData, setMissionData] = useState({
    availableMissions: [],
    activeMissions: [],
    completedMissions: [],
    userProgress: {
      level: 1,
      totalPoints: 0,
      missionsCompleted: 0,
      currentStreak: 0
    }
  });
  
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('available');
  const [showCreator, setShowCreator] = useState(false);

  // Load mission data
  const loadMissionData = async () => {
    try {
      setLoading(true);
      
      // Fetch available missions
      const availableResponse = await fetch('/api/missions?status=available', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (availableResponse.ok) {
        const availableData = await availableResponse.json();
        setMissionData(prev => ({
          ...prev,
          availableMissions: availableData.data || []
        }));
      }
      
      // Fetch active missions
      const activeResponse = await fetch('/api/missions?status=active', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (activeResponse.ok) {
        const activeData = await activeResponse.json();
        setMissionData(prev => ({
          ...prev,
          activeMissions: activeData.data || []
        }));
      }
      
      // Fetch completed missions
      const completedResponse = await fetch('/api/missions?status=completed', {
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (completedResponse.ok) {
        const completedData = await completedResponse.json();
        setMissionData(prev => ({
          ...prev,
          completedMissions: completedData.data || []
        }));
      }
      
    } catch (error) {
      console.error('Error loading mission data:', error);
      toast.error('Failed to load missions');
    } finally {
      setLoading(false);
    }
  };

  // Initialize data loading
  useEffect(() => {
    if (currentUser) {
      loadMissionData();
    }
  }, [currentUser]);

  const handleStartMission = async (missionId) => {
    try {
      const response = await fetch(`/api/missions/${missionId}/start`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        toast.success('Mission started!');
        loadMissionData();
      } else {
        throw new Error('Failed to start mission');
      }
    } catch (error) {
      console.error('Error starting mission:', error);
      toast.error('Failed to start mission');
    }
  };

  const handleCompleteMission = async (missionId) => {
    try {
      const response = await fetch(`/api/missions/${missionId}/complete`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${currentUser?.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        toast.success('Mission completed! 🎉');
        loadMissionData();
      } else {
        throw new Error('Failed to complete mission');
      }
    } catch (error) {
      console.error('Error completing mission:', error);
      toast.error('Failed to complete mission');
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'danger';
      case 'expert': return 'secondary';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading missions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`max-w-7xl mx-auto p-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Mission Board</h1>
          <p className="text-gray-600">
            Complete missions to earn points, unlock achievements, and grow your skills
          </p>
        </div>
        
        <div className="flex gap-3 mt-4 md:mt-0">
          <Button
            onClick={() => setShowCreator(true)}
            color="primary"
            variant="flat"
          >
            Create Mission
          </Button>
        </div>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl mb-2">🏆</div>
              <div className="text-2xl font-bold text-purple-600">
                Level {missionData.userProgress.level}
              </div>
              <div className="text-sm text-gray-600">Current Level</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl mb-2">⭐</div>
              <div className="text-2xl font-bold text-yellow-600">
                {missionData.userProgress.totalPoints}
              </div>
              <div className="text-sm text-gray-600">Total Points</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl mb-2">✅</div>
              <div className="text-2xl font-bold text-green-600">
                {missionData.userProgress.missionsCompleted}
              </div>
              <div className="text-sm text-gray-600">Missions Completed</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl mb-2">🔥</div>
              <div className="text-2xl font-bold text-orange-600">
                {missionData.userProgress.currentStreak}
              </div>
              <div className="text-sm text-gray-600">Current Streak</div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Mission Tabs */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={setActiveTab}
        className="mb-6"
      >
        <Tab key="available" title={`Available (${missionData.availableMissions.length})`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {missionData.availableMissions.map((mission, index) => (
              <MissionCard
                key={mission.id}
                mission={mission}
                index={index}
                type="available"
                onStart={() => handleStartMission(mission.id)}
                getDifficultyColor={getDifficultyColor}
              />
            ))}
          </div>
        </Tab>

        <Tab key="active" title={`Active (${missionData.activeMissions.length})`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {missionData.activeMissions.map((mission, index) => (
              <MissionCard
                key={mission.id}
                mission={mission}
                index={index}
                type="active"
                onComplete={() => handleCompleteMission(mission.id)}
                getDifficultyColor={getDifficultyColor}
              />
            ))}
          </div>
        </Tab>

        <Tab key="completed" title={`Completed (${missionData.completedMissions.length})`}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {missionData.completedMissions.map((mission, index) => (
              <MissionCard
                key={mission.id}
                mission={mission}
                index={index}
                type="completed"
                getDifficultyColor={getDifficultyColor}
              />
            ))}
          </div>
        </Tab>
      </Tabs>

      {/* Empty States */}
      {missionData.availableMissions.length === 0 && activeTab === 'available' && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">🎯</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No available missions</h3>
          <p className="text-gray-600 mb-6">Check back later for new missions or create your own!</p>
          <Button
            onClick={() => setShowCreator(true)}
            color="primary"
          >
            Create Mission
          </Button>
        </div>
      )}

      {/* Mission Creator Modal */}
      {showCreator && (
        <MissionCreator
          onClose={() => setShowCreator(false)}
          onMissionCreated={loadMissionData}
        />
      )}
    </div>
  );
};

export default MissionBoard;
