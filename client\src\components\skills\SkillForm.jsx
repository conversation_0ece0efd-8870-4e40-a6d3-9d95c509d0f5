import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>oot<PERSON>,
  Button,
  Input,
  Select,
  SelectItem,
  Slider,
  Switch
} from '../ui/heroui';
import { Loader2 } from 'lucide-react';
import { getAllSkills, addUserSkill, updateUserSkill } from '../../utils/skills/skills.utils';

const SkillForm = ({ show, onHide, userId, userSkill = null, onSave }) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [allSkills, setAllSkills] = useState([]);
  const [filteredSkills, setFilteredSkills] = useState([]);
  const [categories, setCategories] = useState([]);
  const [areas, setAreas] = useState([]);
  const [specificSkills, setSpecificSkills] = useState([]);
  const [microSkills, setMicroSkills] = useState([]);

  const [formData, setFormData] = useState({
    category: '',
    area: '',
    name: '',
    micro_skill: '',
    skill_id: '',
    proficiency_score: 50,
    is_public: true
  });

  // Load all skills on component mount
  useEffect(() => {
    const loadSkills = async () => {
      try {
        setLoading(true);
        const skills = await getAllSkills();
        setAllSkills(skills);

        // Extract unique categories
        const uniqueCategories = [...new Set(skills.map(skill => skill.category))];
        setCategories(uniqueCategories);

        // If editing an existing skill, populate the form
        if (userSkill) {
          const skill = userSkill.skill;
          setFormData({
            category: skill.category || '',
            area: skill.area || '',
            name: skill.name || '',
            micro_skill: skill.micro_skill || '',
            skill_id: skill.id,
            proficiency_score: userSkill.proficiency_score || 50,
            is_public: userSkill.is_public !== undefined ? userSkill.is_public : true
          });

          // Trigger the cascading filters
          handleCategoryChange({ target: { value: skill.category } });
        }
      } catch (error) {
        console.error('Error loading skills:', error);
        setError('Failed to load skills. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    loadSkills();
  }, [userSkill]);

  // Filter areas based on selected category
  const handleCategoryChange = (e) => {
    const category = e.target.value;
    setFormData({ ...formData, category, area: '', name: '', micro_skill: '', skill_id: '' });

    if (category) {
      const filteredAreas = [...new Set(
        allSkills
          .filter(skill => skill.category === category)
          .map(skill => skill.area)
      )];
      setAreas(filteredAreas);
    } else {
      setAreas([]);
    }

    setSpecificSkills([]);
    setMicroSkills([]);
  };

  // Filter specific skills based on selected area
  const handleAreaChange = (e) => {
    const area = e.target.value;
    setFormData({ ...formData, area, name: '', micro_skill: '', skill_id: '' });

    if (area) {
      const filteredSkills = [...new Set(
        allSkills
          .filter(skill => skill.category === formData.category && skill.area === area)
          .map(skill => skill.name)
      )].filter(Boolean); // Remove null/undefined values

      setSpecificSkills(filteredSkills);
    } else {
      setSpecificSkills([]);
    }

    setMicroSkills([]);
  };

  // Filter micro-skills based on selected specific skill
  const handleNameChange = (e) => {
    const name = e.target.value;
    setFormData({ ...formData, name, micro_skill: '', skill_id: '' });

    if (name) {
      const filteredMicroSkills = [...new Set(
        allSkills
          .filter(skill =>
            skill.category === formData.category &&
            skill.area === formData.area &&
            skill.name === name
          )
          .map(skill => skill.micro_skill)
      )].filter(Boolean); // Remove null/undefined values

      setMicroSkills(filteredMicroSkills);
    } else {
      setMicroSkills([]);
    }
  };

  // Set the skill ID when a micro-skill is selected
  const handleMicroSkillChange = (e) => {
    const microSkill = e.target.value;
    setFormData({ ...formData, micro_skill: microSkill });

    // Find the matching skill to get its ID
    if (microSkill) {
      const matchingSkill = allSkills.find(skill =>
        skill.category === formData.category &&
        skill.area === formData.area &&
        skill.name === formData.name &&
        skill.micro_skill === microSkill
      );

      if (matchingSkill) {
        setFormData(prev => ({ ...prev, skill_id: matchingSkill.id }));
      }
    } else {
      // If no micro-skill is selected, find the skill without a micro-skill
      const matchingSkill = allSkills.find(skill =>
        skill.category === formData.category &&
        skill.area === formData.area &&
        skill.name === formData.name &&
        !skill.micro_skill
      );

      if (matchingSkill) {
        setFormData(prev => ({ ...prev, skill_id: matchingSkill.id }));
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      if (!formData.skill_id) {
        // If no micro-skill was selected, find the skill without a micro-skill
        const matchingSkill = allSkills.find(skill =>
          skill.category === formData.category &&
          skill.area === formData.area &&
          skill.name === formData.name &&
          !skill.micro_skill
        );

        if (matchingSkill) {
          formData.skill_id = matchingSkill.id;
        } else {
          throw new Error('Please select a valid skill');
        }
      }

      let result;

      if (userSkill) {
        // Update existing skill
        result = await updateUserSkill(userSkill.id, {
          proficiency_score: formData.proficiency_score,
          is_public: formData.is_public
        });
      } else {
        // Add new skill
        result = await addUserSkill(userId, formData.skill_id, {
          proficiency_score: formData.proficiency_score,
          is_public: formData.is_public
        });
      }

      if (onSave) {
        onSave(result);
      }

      onHide();
    } catch (error) {
      console.error('Error saving skill:', error);
      setError(error.message || 'Failed to save skill. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal isOpen={show} onOpenChange={onHide} size="2xl" scrollBehavior="inside">
      <ModalContent>
        <ModalHeader>
          <h2 className="text-lg font-semibold">{userSkill ? 'Edit Skill' : 'Add New Skill'}</h2>
        </ModalHeader>
        <ModalBody>
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <div className="p-3 bg-red-100 border border-red-300 rounded-md">
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            )}

            <div className="space-y-2">
              <Select
                label="Skill Category"
                selectedKeys={formData.category ? [formData.category] : []}
                onSelectionChange={(keys) => handleCategoryChange({ target: { value: Array.from(keys)[0] } })}
                isDisabled={userSkill !== null}
                placeholder="Select a category"
              >
                {categories.map(category => (
                  <SelectItem key={category} value={category}>{category}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <Select
                label="Skill Area"
                selectedKeys={formData.area ? [formData.area] : []}
                onSelectionChange={(keys) => handleAreaChange({ target: { value: Array.from(keys)[0] } })}
                isDisabled={!formData.category || userSkill !== null}
                placeholder="Select an area"
              >
                {areas.map(area => (
                  <SelectItem key={area} value={area}>{area}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <Select
                label="Specific Skill"
                selectedKeys={formData.name ? [formData.name] : []}
                onSelectionChange={(keys) => handleNameChange({ target: { value: Array.from(keys)[0] } })}
                isDisabled={!formData.area || userSkill !== null}
                placeholder="Select a skill"
              >
                {specificSkills.map(name => (
                  <SelectItem key={name} value={name}>{name}</SelectItem>
                ))}
              </Select>
            </div>

            {microSkills.length > 0 && (
              <div className="space-y-2">
                <Select
                  label="Micro-Skill (Optional)"
                  selectedKeys={formData.micro_skill ? [formData.micro_skill] : ['']}
                  onSelectionChange={(keys) => handleMicroSkillChange({ target: { value: Array.from(keys)[0] } })}
                  isDisabled={!formData.name || userSkill !== null}
                  placeholder="General knowledge (no specific micro-skill)"
                >
                  <SelectItem key="" value="">General knowledge (no specific micro-skill)</SelectItem>
                  {microSkills.map(microSkill => (
                    <SelectItem key={microSkill} value={microSkill}>{microSkill}</SelectItem>
                  ))}
                </Select>
              </div>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">Proficiency Level: {formData.proficiency_score}%</label>
              <Slider
                value={formData.proficiency_score}
                onChange={(value) => setFormData({ ...formData, proficiency_score: value })}
                maxValue={100}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Beginner</span>
                <span>Intermediate</span>
                <span>Expert</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                isSelected={formData.is_public}
                onValueChange={(checked) => setFormData({ ...formData, is_public: checked })}
              />
              <label className="text-sm">Display this skill on my public profile</label>
            </div>

            <div className="skill-form-note mt-4 p-3 bg-muted rounded">
              <p className="mb-1 font-medium">Note:</p>
              <p className="text-sm text-muted-foreground">
                Self-assessed skills start at the "Unverified" level. To increase your verification level,
                you can add evidence such as completed courses, project contributions, or peer endorsements.
              </p>
            </div>
          </form>
        )}
        </ModalBody>
        <ModalFooter>
          <Button variant="bordered" onPress={onHide} isDisabled={saving}>
            Cancel
          </Button>
          <Button
            onPress={handleSubmit}
            isDisabled={loading || saving || !formData.category || !formData.area || !formData.name}
          >
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              userSkill ? 'Update Skill' : 'Add Skill'
            )}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SkillForm;
