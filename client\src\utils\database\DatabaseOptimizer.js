// Database Optimizer Utility
// Integration & Services Agent: Advanced database optimization and query management

import { createClient } from '@supabase/supabase-js';

class DatabaseOptimizer {
  constructor() {
    this.queryCache = new Map();
    this.queryStats = new Map();
    this.optimizationRules = new Map();
    this.isEnabled = process.env.NODE_ENV === 'development';
    
    this.initializeOptimizationRules();
  }

  // Initialize optimization rules
  initializeOptimizationRules() {
    this.optimizationRules.set('SELECT', {
      maxRows: 1000,
      timeout: 30000,
      cacheTTL: 5 * 60 * 1000, // 5 minutes
      indexHints: ['created_at', 'updated_at', 'user_id']
    });

    this.optimizationRules.set('INSERT', {
      batchSize: 100,
      timeout: 10000,
      retryAttempts: 3
    });

    this.optimizationRules.set('UPDATE', {
      batchSize: 50,
      timeout: 15000,
      retryAttempts: 2
    });

    this.optimizationRules.set('DELETE', {
      batchSize: 25,
      timeout: 20000,
      retryAttempts: 1
    });
  }

  // Optimize query based on operation type
  optimizeQuery(query, operation = 'SELECT') {
    const rules = this.optimizationRules.get(operation) || {};
    let optimizedQuery = query;

    // Add LIMIT for SELECT queries if not present
    if (operation === 'SELECT' && \!query.toLowerCase().includes('limit')) {
      optimizedQuery += ` LIMIT ${rules.maxRows || 1000}`;
    }

    return optimizedQuery;
  }

  // Track query performance
  trackQueryPerformance(query, duration, rowCount = 0, operation = 'SELECT') {
    const queryHash = this.hashQuery(query);
    
    if (\!this.queryStats.has(queryHash)) {
      this.queryStats.set(queryHash, {
        query: query.substring(0, 200),
        operation,
        executions: 0,
        totalDuration: 0,
        avgDuration: 0,
        slowQueries: 0
      });
    }

    const stats = this.queryStats.get(queryHash);
    stats.executions++;
    stats.totalDuration += duration;
    stats.avgDuration = stats.totalDuration / stats.executions;

    // Track slow queries (>1 second)
    if (duration > 1000) {
      stats.slowQueries++;
    }

    if (this.isEnabled && duration > 1000) {
      console.warn(`🐌 Slow query detected (${duration}ms):`, query.substring(0, 100));
    }
  }

  // Generate query hash
  hashQuery(query) {
    let hash = 0;
    for (let i = 0; i < query.length; i++) {
      const char = query.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString();
  }

  // Get optimization recommendations
  getOptimizationRecommendations() {
    const stats = Array.from(this.queryStats.values());
    const recommendations = [];

    const slowQueries = stats.filter(stat => stat.slowQueries > 0);
    if (slowQueries.length > 0) {
      recommendations.push({
        type: 'slow-queries',
        severity: 'warning',
        message: `Found ${slowQueries.length} slow queries`,
        suggestion: 'Consider adding indexes or optimizing query structure'
      });
    }

    return recommendations;
  }

  // Batch insert operations
  async batchInsert(supabaseClient, table, records, batchSize = 100) {
    const results = [];
    
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);
      
      try {
        const { data, error } = await supabaseClient
          .from(table)
          .insert(batch);
        
        if (error) throw error;
        results.push(...(data || []));
      } catch (error) {
        console.error(`Batch insert failed for table ${table}:`, error);
        throw error;
      }
    }

    return results;
  }

  // Batch update operations
  async batchUpdate(supabaseClient, table, updates, batchSize = 50) {
    const results = [];
    
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      const batchPromises = batch.map(update => 
        supabaseClient
          .from(table)
          .update(update.values)
          .eq(update.column, update.value)
      );

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        console.error(`Batch update failed for table ${table}:`, error);
        throw error;
      }
    }

    return results;
  }

  // Optimize database connection
  optimizeConnection(supabaseClient) {
    const originalFrom = supabaseClient.from.bind(supabaseClient);
    
    supabaseClient.from = (table) => {
      const query = originalFrom(table);
      
      const originalSelect = query.select.bind(query);
      query.select = (columns = '*', options = {}) => {
        const startTime = Date.now();
        const result = originalSelect(columns, options);
        
        const originalThen = result.then.bind(result);
        result.then = (onFulfilled, onRejected) => {
          return originalThen((data) => {
            const duration = Date.now() - startTime;
            this.trackQueryPerformance(
              `SELECT ${columns} FROM ${table}`,
              duration,
              data?.data?.length || 0,
              'SELECT'
            );
            return onFulfilled ? onFulfilled(data) : data;
          }, onRejected);
        };
        
        return result;
      };

      return query;
    };

    return supabaseClient;
  }
}

// Create singleton instance
const databaseOptimizer = new DatabaseOptimizer();

// Enhanced Supabase client with optimization
export const createOptimizedSupabaseClient = (supabaseUrl, supabaseKey, options = {}) => {
  const client = createClient(supabaseUrl, supabaseKey, options);
  return databaseOptimizer.optimizeConnection(client);
};

// Export utilities
export const trackQueryPerformance = (query, duration, rowCount, operation) => 
  databaseOptimizer.trackQueryPerformance(query, duration, rowCount, operation);

export const getOptimizationRecommendations = () => 
  databaseOptimizer.getOptimizationRecommendations();

export const batchInsert = (client, table, records, batchSize) => 
  databaseOptimizer.batchInsert(client, table, records, batchSize);

export const batchUpdate = (client, table, updates, batchSize) => 
  databaseOptimizer.batchUpdate(client, table, updates, batchSize);

export default databaseOptimizer;
