import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Input, Select, SelectItem, Switch, Chip, Badge, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { toast } from 'react-hot-toast';
import AnalyticsDataService from '../../services/AnalyticsDataService';

/**
 * Automated Report Scheduler Component
 * 
 * Features:
 * - Schedule automated report generation and delivery
 * - Multiple delivery methods (email, dashboard, API)
 * - Flexible scheduling options (daily, weekly, monthly, custom)
 * - Report template management
 * - Delivery status tracking
 */
const AutomatedReportScheduler = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [scheduledReports, setScheduledReports] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [newSchedule, setNewSchedule] = useState({
    name: '',
    reportType: 'overview',
    schedule: 'weekly',
    deliveryMethod: 'email',
    recipients: '',
    isActive: true,
    customSchedule: {
      frequency: 'weekly',
      dayOfWeek: 1, // Monday
      timeOfDay: '09:00',
      timezone: 'UTC'
    }
  });

  // Report types available for scheduling
  const reportTypes = [
    { key: 'overview', label: 'Analytics Overview', description: 'Comprehensive performance summary' },
    { key: 'financial', label: 'Financial Report', description: 'Revenue, expenses, and profit analysis' },
    { key: 'projects', label: 'Project Insights', description: 'Project performance and completion metrics' },
    { key: 'performance', label: 'Performance Report', description: 'Individual performance and ratings' },
    { key: 'custom', label: 'Custom Report', description: 'User-defined report configuration' }
  ];

  // Schedule frequency options
  const scheduleOptions = [
    { key: 'daily', label: 'Daily', description: 'Every day at specified time' },
    { key: 'weekly', label: 'Weekly', description: 'Every week on specified day' },
    { key: 'monthly', label: 'Monthly', description: 'Every month on specified date' },
    { key: 'quarterly', label: 'Quarterly', description: 'Every 3 months' },
    { key: 'custom', label: 'Custom', description: 'Custom schedule pattern' }
  ];

  // Delivery methods
  const deliveryMethods = [
    { key: 'email', label: 'Email', icon: '📧', description: 'Send via email' },
    { key: 'dashboard', label: 'Dashboard', icon: '📊', description: 'Save to dashboard' },
    { key: 'api', label: 'API Webhook', icon: '🔗', description: 'POST to webhook URL' },
    { key: 'download', label: 'Auto Download', icon: '💾', description: 'Generate download link' }
  ];

  // Load scheduled reports
  const loadScheduledReports = async () => {
    if (!currentUser) return;
    
    try {
      setLoading(true);
      
      // Mock data for now - replace with actual API call
      const mockScheduledReports = [
        {
          id: '1',
          name: 'Weekly Performance Summary',
          reportType: 'overview',
          schedule: 'weekly',
          deliveryMethod: 'email',
          recipients: '<EMAIL>',
          isActive: true,
          nextRun: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
          lastRun: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active'
        },
        {
          id: '2',
          name: 'Monthly Financial Report',
          reportType: 'financial',
          schedule: 'monthly',
          deliveryMethod: 'dashboard',
          recipients: '',
          isActive: true,
          nextRun: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          lastRun: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'active'
        }
      ];
      
      setScheduledReports(mockScheduledReports);
    } catch (error) {
      console.error('Error loading scheduled reports:', error);
      toast.error('Failed to load scheduled reports');
    } finally {
      setLoading(false);
    }
  };

  // Create new scheduled report
  const handleCreateSchedule = async () => {
    if (!newSchedule.name.trim()) {
      toast.error('Please enter a report name');
      return;
    }

    try {
      setLoading(true);
      
      // Call analytics service to schedule report
      const scheduleConfig = {
        name: newSchedule.name,
        config: {
          reportType: newSchedule.reportType,
          period: '30d', // Default period
          format: 'pdf'
        },
        schedule: {
          frequency: newSchedule.schedule,
          ...newSchedule.customSchedule
        },
        deliveryMethod: newSchedule.deliveryMethod,
        recipients: newSchedule.recipients.split(',').map(email => email.trim()).filter(Boolean)
      };

      await AnalyticsDataService.scheduleReport(currentUser.id, scheduleConfig);
      
      toast.success('Report scheduled successfully!');
      setShowCreateModal(false);
      
      // Reset form
      setNewSchedule({
        name: '',
        reportType: 'overview',
        schedule: 'weekly',
        deliveryMethod: 'email',
        recipients: '',
        isActive: true,
        customSchedule: {
          frequency: 'weekly',
          dayOfWeek: 1,
          timeOfDay: '09:00',
          timezone: 'UTC'
        }
      });
      
      // Reload scheduled reports
      loadScheduledReports();
    } catch (error) {
      console.error('Error creating scheduled report:', error);
      toast.error('Failed to schedule report');
    } finally {
      setLoading(false);
    }
  };

  // Toggle report active status
  const toggleReportStatus = async (reportId, isActive) => {
    try {
      // Update report status via API
      setScheduledReports(prev => 
        prev.map(report => 
          report.id === reportId 
            ? { ...report, isActive, status: isActive ? 'active' : 'paused' }
            : report
        )
      );
      
      toast.success(`Report ${isActive ? 'activated' : 'paused'} successfully`);
    } catch (error) {
      console.error('Error updating report status:', error);
      toast.error('Failed to update report status');
    }
  };

  // Delete scheduled report
  const deleteScheduledReport = async (reportId) => {
    try {
      setScheduledReports(prev => prev.filter(report => report.id !== reportId));
      toast.success('Scheduled report deleted successfully');
    } catch (error) {
      console.error('Error deleting scheduled report:', error);
      toast.error('Failed to delete scheduled report');
    }
  };

  // Format next run time
  const formatNextRun = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 7) return `In ${diffDays} days`;
    return date.toLocaleDateString();
  };

  // Initialize component
  useEffect(() => {
    loadScheduledReports();
  }, [currentUser]);

  return (
    <div className={`automated-report-scheduler ${className}`}>
      {/* Header */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-2xl">⏰</span>
              <div>
                <h2 className="text-xl font-bold">Automated Reports</h2>
                <p className="text-sm text-default-600">
                  Schedule and manage automated report generation
                </p>
              </div>
            </div>
            
            <Button
              color="primary"
              onPress={() => setShowCreateModal(true)}
              startContent={<span>➕</span>}
            >
              Schedule Report
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Scheduled Reports List */}
      <div className="space-y-4">
        {loading ? (
          <Card>
            <CardBody className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-default-600">Loading scheduled reports...</p>
            </CardBody>
          </Card>
        ) : scheduledReports.length === 0 ? (
          <Card>
            <CardBody className="p-8 text-center">
              <span className="text-4xl mb-4 block">📅</span>
              <h3 className="text-lg font-semibold mb-2">No Scheduled Reports</h3>
              <p className="text-default-600 mb-4">
                Create your first automated report to get started
              </p>
              <Button
                color="primary"
                variant="flat"
                onPress={() => setShowCreateModal(true)}
              >
                Schedule Your First Report
              </Button>
            </CardBody>
          </Card>
        ) : (
          <AnimatePresence>
            {scheduledReports.map((report, index) => (
              <motion.div
                key={report.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card>
                  <CardBody className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <span className="text-xl">
                            {deliveryMethods.find(m => m.key === report.deliveryMethod)?.icon || '📊'}
                          </span>
                          <div>
                            <h3 className="font-semibold">{report.name}</h3>
                            <div className="flex items-center gap-2 mt-1">
                              <Chip size="sm" variant="flat" color="primary">
                                {reportTypes.find(t => t.key === report.reportType)?.label}
                              </Chip>
                              <Chip size="sm" variant="flat" color="secondary">
                                {scheduleOptions.find(s => s.key === report.schedule)?.label}
                              </Chip>
                              <Badge
                                color={report.status === 'active' ? 'success' : 'warning'}
                                variant="flat"
                                size="sm"
                              >
                                {report.status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-right text-sm">
                          <div className="text-default-600">Next run:</div>
                          <div className="font-medium">{formatNextRun(report.nextRun)}</div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Switch
                            size="sm"
                            isSelected={report.isActive}
                            onValueChange={(isActive) => toggleReportStatus(report.id, isActive)}
                          />
                          <Button
                            size="sm"
                            variant="light"
                            color="danger"
                            onPress={() => deleteScheduledReport(report.id)}
                          >
                            🗑️
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        )}
      </div>

      {/* Create Schedule Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        size="2xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <span className="text-xl">⏰</span>
              <span>Schedule New Report</span>
            </div>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Report Name"
                placeholder="Weekly Performance Summary"
                value={newSchedule.name}
                onChange={(e) => setNewSchedule(prev => ({ ...prev, name: e.target.value }))}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Report Type"
                  selectedKeys={[newSchedule.reportType]}
                  onSelectionChange={(keys) => setNewSchedule(prev => ({
                    ...prev,
                    reportType: Array.from(keys)[0]
                  }))}
                >
                  {reportTypes.map(type => (
                    <SelectItem key={type.key} description={type.description}>
                      {type.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  label="Schedule"
                  selectedKeys={[newSchedule.schedule]}
                  onSelectionChange={(keys) => setNewSchedule(prev => ({
                    ...prev,
                    schedule: Array.from(keys)[0]
                  }))}
                >
                  {scheduleOptions.map(option => (
                    <SelectItem key={option.key} description={option.description}>
                      {option.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
              
              <Select
                label="Delivery Method"
                selectedKeys={[newSchedule.deliveryMethod]}
                onSelectionChange={(keys) => setNewSchedule(prev => ({
                  ...prev,
                  deliveryMethod: Array.from(keys)[0]
                }))}
              >
                {deliveryMethods.map(method => (
                  <SelectItem 
                    key={method.key} 
                    description={method.description}
                    startContent={<span>{method.icon}</span>}
                  >
                    {method.label}
                  </SelectItem>
                ))}
              </Select>
              
              {newSchedule.deliveryMethod === 'email' && (
                <Input
                  label="Recipients"
                  placeholder="<EMAIL>, <EMAIL>"
                  description="Comma-separated email addresses"
                  value={newSchedule.recipients}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, recipients: e.target.value }))}
                />
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleCreateSchedule}
              isLoading={loading}
              isDisabled={!newSchedule.name.trim()}
            >
              Schedule Report
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AutomatedReportScheduler;
