-- Create the missing tables that the Track page needs
-- Based on the check, we need: tasks and active_timers

-- ============================================================================
-- CREATE TASKS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'done', 'blocked')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    assignee_id UUID,
    created_by UUID,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    due_date TIMESTAMPTZ,
    estimated_hours INTEGER,
    actual_hours INTEGER,
    tags TEXT[],
    task_type TEXT DEFAULT 'feature',
    difficulty TEXT DEFAULT 'medium',
    contribution_points INTEGER DEFAULT 1,
    position INTEGER DEFAULT 0
);

-- Add basic indexes for performance
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);

-- Enable RLS
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;

-- Create simple, permissive policies for tasks (matching your existing setup)
-- Wait to create policies until after table is fully created
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view all tasks" ON public.tasks;
    DROP POLICY IF EXISTS "Users can create tasks" ON public.tasks;
    DROP POLICY IF EXISTS "Users can update tasks" ON public.tasks;

    -- Create new policies
    CREATE POLICY "Users can view all tasks"
    ON public.tasks FOR SELECT
    USING (true);  -- Very permissive like your existing projects policy

    CREATE POLICY "Users can create tasks"
    ON public.tasks FOR INSERT
    WITH CHECK (true);  -- Simplified to avoid column reference issues

    CREATE POLICY "Users can update tasks"
    ON public.tasks FOR UPDATE
    USING (true)  -- Very permissive
    WITH CHECK (true);
END $$;

-- ============================================================================
-- CREATE ACTIVE_TIMERS TABLE
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.active_timers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add basic indexes
CREATE INDEX IF NOT EXISTS idx_active_timers_user_id ON public.active_timers(user_id);
CREATE INDEX IF NOT EXISTS idx_active_timers_task_id ON public.active_timers(task_id);

-- Enable RLS
ALTER TABLE public.active_timers ENABLE ROW LEVEL SECURITY;

-- Create policies for active_timers
DROP POLICY IF EXISTS "Users can manage their own timers" ON public.active_timers;
CREATE POLICY "Users can manage their own timers" 
ON public.active_timers FOR ALL 
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================
GRANT SELECT, INSERT, UPDATE, DELETE ON public.tasks TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.active_timers TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- CREATE HELPFUL FUNCTIONS
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
DROP TRIGGER IF EXISTS update_tasks_updated_at ON public.tasks;
CREATE TRIGGER update_tasks_updated_at 
    BEFORE UPDATE ON public.tasks 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_active_timers_updated_at ON public.active_timers;
CREATE TRIGGER update_active_timers_updated_at 
    BEFORE UPDATE ON public.active_timers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;
