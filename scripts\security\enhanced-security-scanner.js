#!/usr/bin/env node

/**
 * Enhanced Security Scanner
 */

const fs = require('fs');
const path = require('path');
const { scanFileForSecrets, SECURITY_CONFIG } = require('./security-config');

class EnhancedSecurityScanner {
  constructor() {
    this.results = {
      critical: [],
      suspicious: [],
      safe: [],
      summary: {
        totalFiles: 0,
        criticalIssues: 0,
        suspiciousIssues: 0,
        safeFiles: 0,
        score: 100
      }
    };
  }

  /**
   * Run comprehensive security scan
   */
  async runScan(directory = 'client/dist') {
    console.log('🔒 ENHANCED SECURITY SCAN');
    console.log('=' .repeat(50));
    console.log(`Scanning directory: ${directory}`);
    console.log('');

    if (!fs.existsSync(directory)) {
      console.log('❌ Directory not found. Run build first.');
      return this.results;
    }

    try {
      const files = this.getAllFiles(directory);
      console.log(`📁 Found ${files.length} files to scan`);
      
      for (const file of files) {
        await this.scanFile(file);
      }
      
      this.calculateScore();
      this.displayResults();
      this.generateReport();
      
      return this.results;
      
    } catch (error) {
      console.error('❌ Security scan failed:', error.message);
      throw error;
    }
  }

  /**
   * Get all files in directory recursively
   */
  getAllFiles(dir, extensions = ['.js', '.html', '.css', '.json']) {
    let files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files = files.concat(this.getAllFiles(fullPath, extensions));
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * Scan individual file for security issues
   */
  async scanFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const fileSize = Buffer.byteLength(content, 'utf8');
      const relativePath = path.relative('client/dist', filePath);
      
      this.results.summary.totalFiles++;
      
      // Skip very large files (likely legitimate minified code)
      if (fileSize > 2 * 1024 * 1024) { // 2MB
        this.results.safe.push({
          file: relativePath,
          reason: 'Large file - likely legitimate minified code',
          size: this.formatBytes(fileSize)
        });
        this.results.summary.safeFiles++;
        return;
      }
      
      // Scan for secrets
      const issues = scanFileForSecrets(filePath, content);
      
      if (issues.length === 0) {
        this.results.safe.push({
          file: relativePath,
          reason: 'No security issues detected',
          size: this.formatBytes(fileSize)
        });
        this.results.summary.safeFiles++;
      } else {
        issues.forEach(issue => {
          if (issue.type === 'critical') {
            this.results.critical.push({
              ...issue,
              file: relativePath
            });
            this.results.summary.criticalIssues++;
          } else {
            this.results.suspicious.push({
              ...issue,
              file: relativePath
            });
            this.results.summary.suspiciousIssues++;
          }
        });
      }
      
    } catch (error) {
      console.warn(`⚠️ Could not scan ${filePath}: ${error.message}`);
    }
  }

  /**
   * Calculate security score
   */
  calculateScore() {
    let score = 100;
    
    // Critical issues are major security risks
    score -= this.results.summary.criticalIssues * 25;
    
    // Suspicious issues are potential risks
    score -= this.results.summary.suspiciousIssues * 5;
    
    // Bonus for having many safe files
    if (this.results.summary.safeFiles > 10) {
      score += 5;
    }
    
    this.results.summary.score = Math.max(0, Math.min(100, score));
  }

  /**
   * Display scan results
   */
  displayResults() {
    console.log('\n📊 SECURITY SCAN RESULTS');
    console.log('=' .repeat(50));
    
    // Overall score
    const scoreColor = this.results.summary.score >= 90 ? '🟢' :
                      this.results.summary.score >= 80 ? '🟡' :
                      this.results.summary.score >= 60 ? '🟠' : '🔴';
    
    console.log(`Security Score: ${scoreColor} ${this.results.summary.score}/100`);
    console.log(`Files Scanned: ${this.results.summary.totalFiles}`);
    console.log(`Safe Files: ✅ ${this.results.summary.safeFiles}`);
    console.log(`Critical Issues: ❌ ${this.results.summary.criticalIssues}`);
    console.log(`Suspicious Issues: ⚠️ ${this.results.summary.suspiciousIssues}`);
    console.log('');

    // Critical issues
    if (this.results.critical.length > 0) {
      console.log('🚨 CRITICAL SECURITY ISSUES:');
      this.results.critical.forEach(issue => {
        console.log(`  ❌ ${issue.file}: ${issue.pattern} (${issue.confidence} confidence)`);
      });
      console.log('');
    }

    // Safe files summary
    if (this.results.safe.length > 0) {
      console.log('✅ SAFE FILES:');
      console.log(`  ${this.results.safe.length} files passed security scan`);
      console.log('');
    }

    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    if (this.results.summary.criticalIssues > 0) {
      console.log('  🚨 URGENT: Review and remove critical security issues immediately');
    } else if (this.results.summary.score >= 95) {
      console.log('  ✅ Excellent security - ready for production deployment');
    } else if (this.results.summary.score >= 90) {
      console.log('  ✅ Good security - ready for deployment');
    } else {
      console.log('  🟡 Security acceptable - consider improvements');
    }
    console.log('');
  }

  /**
   * Generate detailed security report
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.results.summary,
      critical: this.results.critical,
      safe: this.results.safe.slice(0, 20), // Limit to prevent huge reports
    };

    fs.writeFileSync('security-scan-results.json', JSON.stringify(report, null, 2));
    console.log('📄 Detailed report saved: security-scan-results.json');
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Run scanner if called directly
if (require.main === module) {
  const scanner = new EnhancedSecurityScanner();
  scanner.runScan().catch(error => {
    console.error('Security scan failed:', error);
    process.exit(1);
  });
}

module.exports = EnhancedSecurityScanner;
