// ActivityFeed - Simplified activity feed component
import React, { useState, useEffect, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, Button, Textarea } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import socialService from '../../services/socialService';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  Send,
  TrendingUp,
  Trophy,
  Star,
  Users,
  Briefcase
} from 'lucide-react';

const ActivityFeed = ({ className = "" }) => {
  const { currentUser } = useContext(UserContext);
  const [activities, setActivities] = useState([]);
  const [newUpdate, setNewUpdate] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (currentUser) {
      loadActivityFeed();
    }
  }, [currentUser]);

  const loadActivityFeed = async () => {
    try {
      setIsLoading(true);
      const feedData = await socialService.getActivityFeed();
      setActivities(feedData);
    } catch (error) {
      console.error('Error loading activity feed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const postUpdate = async () => {
    if (!newUpdate.trim()) return;

    try {
      await socialService.createActivity('social_update', newUpdate);
      setNewUpdate('');
      await loadActivityFeed();
    } catch (error) {
      console.error('Error posting update:', error);
    }
  };

  const getActivityIcon = (activityType) => {
    const iconMap = {
      achievement: <Trophy className="text-yellow-500" size={20} />,
      project_completion: <Star className="text-green-500" size={20} />,
      alliance_joined: <Users className="text-purple-500" size={20} />,
      venture_created: <Briefcase className="text-indigo-500" size={20} />,
      social_update: <MessageCircle className="text-gray-500" size={20} />
    };
    return iconMap[activityType] || <TrendingUp className="text-gray-500" size={20} />;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Share Update */}
      <Card>
        <CardBody className="p-6">
          <div className="space-y-4">
            <Textarea
              placeholder="What's happening in your work?"
              value={newUpdate}
              onChange={(e) => setNewUpdate(e.target.value)}
              minRows={3}
              className="w-full"
            />
            <div className="flex justify-end">
              <Button
                color="primary"
                onPress={postUpdate}
                isDisabled={!newUpdate.trim()}
                startContent={<Send size={16} />}
              >
                Share Update
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Activity Feed */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-500">Loading activity feed...</p>
          </div>
        ) : activities.length === 0 ? (
          <Card>
            <CardBody className="p-8 text-center">
              <TrendingUp size={48} className="mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Activity Yet</h3>
              <p className="text-gray-500 mb-4">
                Connect with others and start sharing your achievements to see activity here.
              </p>
              <Button color="primary" size="sm">
                Find Connections
              </Button>
            </CardBody>
          </Card>
        ) : (
          activities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.activity_type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-gray-900 mb-1">
                        {activity.title}
                      </h4>
                      
                      {activity.description && (
                        <p className="text-gray-700 mb-3">
                          {activity.description}
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <Button
                            size="sm"
                            variant="light"
                            startContent={<Heart size={16} />}
                            className="text-gray-600 hover:text-red-500"
                          >
                            {activity.likes_count || 0}
                          </Button>
                          <Button
                            size="sm"
                            variant="light"
                            startContent={<MessageCircle size={16} />}
                            className="text-gray-600 hover:text-blue-500"
                          >
                            {activity.comments_count || 0}
                          </Button>
                          <Button
                            size="sm"
                            variant="light"
                            startContent={<Share2 size={16} />}
                            className="text-gray-600 hover:text-green-500"
                          >
                            Share
                          </Button>
                        </div>
                        
                        <p className="text-xs text-gray-500">
                          {new Date(activity.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </motion.div>
          ))
        )}
      </div>
    </div>
  );
};

export default ActivityFeed;
