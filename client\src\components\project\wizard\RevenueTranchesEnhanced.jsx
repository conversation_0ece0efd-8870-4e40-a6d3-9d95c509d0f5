import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import DatePicker from 'react-datepicker';
import {
  Card,
  CardBody,
  Input,
  Button,
  Select,
  SelectItem,
  Textarea,
  Chip,
  Divider
} from '@heroui/react';
import { motion } from 'framer-motion';

const RevenueTranches = ({ projectData, setProjectData, onNext, onBack }) => {
  const [showTrancheForm, setShowTrancheForm] = useState(false);
  const [editingIndex, setEditingIndex] = useState(null);
  const [newTranche, setNewTranche] = useState({
    name: '',
    description: '',
    start_date: null,
    end_date: null,
    revenue_sources: [],
    distribution_thresholds: {
      minimum_revenue: 0,
      maximum_payout: null,
      per_contributor_minimum: 0,
      per_contributor_maximum: null
    },
    rollover_config: 'none'
  });
  const [newSource, setNewSource] = useState('');

  // Initialize with existing tranches if available
  useEffect(() => {
    if (!projectData.revenue_tranches || projectData.revenue_tranches.length === 0) {
      // Set default tranche if none exists
      setProjectData({
        ...projectData,
        revenue_tranches: [{
          name: 'Initial Release',
          description: '',
          start_date: null,
          end_date: null,
          revenue_sources: [],
          distribution_thresholds: {
            minimum_revenue: 0,
            maximum_payout: null,
            per_contributor_minimum: 0,
            per_contributor_maximum: null
          },
          rollover_config: 'none'
        }]
      });
    }
  }, []);

  // Revenue source options
  const revenueSourceOptions = [
    { value: 'app_store', label: 'App Store' },
    { value: 'google_play', label: 'Google Play' },
    { value: 'steam', label: 'Steam' },
    { value: 'epic_games', label: 'Epic Games Store' },
    { value: 'direct_sales', label: 'Direct Sales' },
    { value: 'subscriptions', label: 'Subscriptions' },
    { value: 'in_app_purchases', label: 'In-App Purchases' },
    { value: 'advertising', label: 'Advertising' },
    { value: 'licensing', label: 'Licensing' },
    { value: 'merchandise', label: 'Merchandise' },
    { value: 'crowdfunding', label: 'Crowdfunding' },
    { value: 'donations', label: 'Donations' },
    { value: 'other', label: 'Other' }
  ];

  // Rollover options
  const rolloverOptions = [
    { value: 'none', label: 'No rollover' },
    { value: 'next_tranche', label: 'Roll over to next tranche' },
    { value: 'proportional', label: 'Proportional distribution' },
    { value: 'equal', label: 'Equal distribution' }
  ];

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTranche({ ...newTranche, [name]: value });
  };

  // Handle distribution thresholds changes
  const handleThresholdChange = (e) => {
    const { name, value } = e.target;
    const parsedValue = value === '' ? null : parseCurrencyInput(value);

    setNewTranche({
      ...newTranche,
      distribution_thresholds: {
        ...newTranche.distribution_thresholds,
        [name]: parsedValue
      }
    });
  };

  // Save tranche
  const saveTrancheHandler = () => {
    if (!newTranche.name) {
      toast.error('Tranche name is required');
      return;
    }

    const updatedTranches = [...projectData.revenue_tranches];

    if (editingIndex !== null) {
      // Update existing tranche
      updatedTranches[editingIndex] = newTranche;
    } else {
      // Add new tranche
      updatedTranches.push(newTranche);
    }

    setProjectData({
      ...projectData,
      revenue_tranches: updatedTranches
    });

    setShowTrancheForm(false);
    setEditingIndex(null);
    setNewTranche({
      name: '',
      description: '',
      start_date: null,
      end_date: null,
      revenue_sources: [],
      platform_fee_config: {
        apply_before: true,
        percentage: 5
      },
      distribution_thresholds: {
        minimum_revenue: 0,
        maximum_payout: null,
        per_contributor_minimum: 0,
        per_contributor_maximum: null
      },
      rollover_config: 'none'
    });

    toast.success(
      editingIndex !== null
        ? 'Tranche updated successfully'
        : 'Tranche added successfully'
    );
  };

  // Edit tranche
  const editTrancheHandler = (index) => {
    setNewTranche(projectData.revenue_tranches[index]);
    setEditingIndex(index);
    setShowTrancheForm(true);
  };

  // Delete tranche
  const deleteTrancheHandler = (index) => {
    const updatedTranches = [...projectData.revenue_tranches];
    updatedTranches.splice(index, 1);

    setProjectData({
      ...projectData,
      revenue_tranches: updatedTranches
    });

    toast.success('Tranche deleted successfully');
  };

  // Add revenue source
  const addRevenueSource = () => {
    if (!newSource) return;

    if (newTranche.revenue_sources.includes(newSource)) {
      toast.error('This revenue source is already added');
      return;
    }

    setNewTranche({
      ...newTranche,
      revenue_sources: [...newTranche.revenue_sources, newSource]
    });

    setNewSource('');
  };

  // Remove revenue source
  const removeRevenueSource = (source) => {
    setNewTranche({
      ...newTranche,
      revenue_sources: newTranche.revenue_sources.filter((s) => s !== source)
    });
  };

  // Format currency
  const formatCurrency = (value) => {
    if (value === null || value === undefined) return '';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  // Parse currency input
  const parseCurrencyInput = (value) => {
    if (!value) return 0;
    return parseFloat(value.replace(/,/g, ''));
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold text-foreground mb-2">Revenue Tranches Setup</h2>
        <p className="text-default-500 mb-6">
          Configure how revenue will be distributed over time. Tranches allow you to define different revenue periods with specific rules.
        </p>
      </div>

      {!showTrancheForm ? (
        <>
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">
              Revenue Tranches ({projectData.revenue_tranches.length})
            </h3>
            <Button
              color="primary"
              onPress={() => setShowTrancheForm(true)}
              startContent={<span>➕</span>}
            >
              Add Tranche
            </Button>
          </div>

          {projectData.revenue_tranches.length === 0 ? (
            <Card className="bg-default-50">
              <CardBody className="p-6 text-center">
                <p className="text-default-500">
                  No revenue tranches added yet. Add at least one tranche to configure how
                  revenue will be distributed.
                </p>
              </CardBody>
            </Card>
          ) : (
            <div className="space-y-4">
              {projectData.revenue_tranches.map((tranche, index) => (
                <Card key={index} className="bg-content1">
                  <CardBody className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-2">{tranche.name}</h3>
                        {tranche.description && (
                          <p className="text-default-500 text-sm mb-3">{tranche.description}</p>
                        )}
                        <div className="flex flex-wrap gap-2">
                          <Chip
                            size="sm"
                            variant="flat"
                            color="primary"
                            startContent={<span className="text-xs">📅</span>}
                          >
                            {tranche.start_date
                              ? new Date(tranche.start_date).toLocaleDateString()
                              : 'No start date'}
                            {tranche.end_date
                              ? ` to ${new Date(tranche.end_date).toLocaleDateString()}`
                              : ''}
                          </Chip>
                          <Chip
                            size="sm"
                            variant="flat"
                            color="secondary"
                            startContent={<span className="text-xs">🏷️</span>}
                          >
                            {tranche.revenue_sources.length > 0
                              ? `${tranche.revenue_sources.length} sources`
                              : 'No sources'}
                          </Chip>
                          <Chip
                            size="sm"
                            variant="flat"
                            color="default"
                            startContent={<span className="text-xs">🔄</span>}
                          >
                            {rolloverOptions.find((option) => option.value === tranche.rollover_config)?.label || 'No rollover'}
                          </Chip>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="bordered"
                          color="primary"
                          onPress={() => editTrancheHandler(index)}
                          startContent={<span className="text-xs">✏️</span>}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="bordered"
                          color="danger"
                          onPress={() => {
                            if (window.confirm('Are you sure you want to delete this tranche?')) {
                              deleteTrancheHandler(index);
                            }
                          }}
                          startContent={<span className="text-xs">🗑️</span>}
                        >
                          Delete
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card className="bg-default-50">
                        <CardBody className="p-4">
                          <h4 className="text-sm font-medium text-foreground mb-3">Revenue Sources</h4>
                          {tranche.revenue_sources.length > 0 ? (
                            <div className="space-y-1">
                              {tranche.revenue_sources.map((source, i) => (
                                <div key={i} className="flex items-center text-sm text-default-600">
                                  <span className="w-2 h-2 bg-primary rounded-full mr-2"></span>
                                  {revenueSourceOptions.find(opt => opt.value === source)?.label || source}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-default-400 text-sm">No revenue sources defined</p>
                          )}
                        </CardBody>
                      </Card>
                      <Card className="bg-default-50">
                        <CardBody className="p-4">
                          <h4 className="text-sm font-medium text-foreground mb-3">Distribution Thresholds</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-default-600">Min Revenue:</span>
                              <span className="font-medium">${formatCurrency(tranche.distribution_thresholds.minimum_revenue)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-600">Max Payout:</span>
                              <span className="font-medium">
                                {tranche.distribution_thresholds.maximum_payout
                                  ? `$${formatCurrency(tranche.distribution_thresholds.maximum_payout)}`
                                  : 'No limit'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-600">Per Contributor Min:</span>
                              <span className="font-medium">${formatCurrency(tranche.distribution_thresholds.per_contributor_minimum)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-600">Per Contributor Max:</span>
                              <span className="font-medium">
                                {tranche.distribution_thresholds.per_contributor_maximum
                                  ? `$${formatCurrency(tranche.distribution_thresholds.per_contributor_maximum)}`
                                  : 'No limit'}
                              </span>
                            </div>
                          </div>
                        </CardBody>
                      </Card>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          )}

          <div className="flex justify-between mt-8">
            <Button
              variant="flat"
              onPress={onBack}
              size="lg"
            >
              Back
            </Button>
            <Button
              color="primary"
              onPress={onNext}
              size="lg"
            >
              Next
            </Button>
          </div>
        </>
      ) : (
        <Card className="bg-content1">
          <CardBody className="p-6">
            <h3 className="text-lg font-medium text-foreground mb-6">
              {editingIndex !== null ? 'Edit Tranche' : 'Add New Tranche'}
            </h3>

            <div className="space-y-6">
              <Input
                label="Tranche Name"
                placeholder="Enter tranche name"
                value={newTranche.name}
                onChange={handleInputChange}
                name="name"
                variant="bordered"
                size="lg"
                isRequired
                errorMessage={!newTranche.name ? "Tranche name is required" : ""}
              />

              <Textarea
                label="Description"
                placeholder="Enter description (optional)"
                value={newTranche.description}
                onChange={handleInputChange}
                name="description"
                variant="bordered"
                minRows={2}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    Start Date
                  </label>
                  <DatePicker
                    selected={newTranche.start_date ? new Date(newTranche.start_date) : null}
                    onChange={(date) =>
                      setNewTranche({ ...newTranche, start_date: date })
                    }
                    className="w-full px-3 py-2 border border-default-300 rounded-lg focus:border-primary focus:outline-none"
                    placeholderText="Select start date"
                    dateFormat="MM/dd/yyyy"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">
                    End Date
                  </label>
                  <DatePicker
                    selected={newTranche.end_date ? new Date(newTranche.end_date) : null}
                    onChange={(date) =>
                      setNewTranche({ ...newTranche, end_date: date })
                    }
                    className="w-full px-3 py-2 border border-default-300 rounded-lg focus:border-primary focus:outline-none"
                    placeholderText="Select end date"
                    dateFormat="MM/dd/yyyy"
                    minDate={newTranche.start_date ? new Date(newTranche.start_date) : null}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Revenue Sources
                </label>
                <div className="flex gap-2 mb-3">
                  <Select
                    placeholder="Select a revenue source"
                    selectedKeys={newSource ? [newSource] : []}
                    onSelectionChange={(keys) => setNewSource(Array.from(keys)[0] || '')}
                    variant="bordered"
                    className="flex-1"
                  >
                    {revenueSourceOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </Select>
                  <Button
                    color="primary"
                    variant="bordered"
                    onPress={addRevenueSource}
                    isDisabled={!newSource}
                  >
                    Add
                  </Button>
                </div>

                {newTranche.revenue_sources.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {newTranche.revenue_sources.map((source, index) => (
                      <Chip
                        key={index}
                        onClose={() => removeRevenueSource(source)}
                        variant="flat"
                        color="primary"
                      >
                        {revenueSourceOptions.find((option) => option.value === source)?.label || source}
                      </Chip>
                    ))}
                  </div>
                ) : (
                  <p className="text-default-400 text-sm">No revenue sources added</p>
                )}
              </div>

              <Card className="bg-default-50">
                <CardBody className="p-4">
                  <h4 className="text-sm font-medium text-foreground mb-4">Distribution Thresholds</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <Input
                      label="Minimum Revenue"
                      placeholder="0"
                      value={formatCurrency(newTranche.distribution_thresholds.minimum_revenue)}
                      onChange={handleThresholdChange}
                      name="minimum_revenue"
                      variant="bordered"
                      startContent={<span className="text-default-400">$</span>}
                      description="Minimum revenue before distribution begins"
                    />
                    <Input
                      label="Maximum Payout"
                      placeholder="No limit"
                      value={formatCurrency(newTranche.distribution_thresholds.maximum_payout)}
                      onChange={handleThresholdChange}
                      name="maximum_payout"
                      variant="bordered"
                      startContent={<span className="text-default-400">$</span>}
                      description="Maximum total payout (leave empty for no limit)"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Per Contributor Minimum"
                      placeholder="0"
                      value={formatCurrency(newTranche.distribution_thresholds.per_contributor_minimum)}
                      onChange={handleThresholdChange}
                      name="per_contributor_minimum"
                      variant="bordered"
                      startContent={<span className="text-default-400">$</span>}
                      description="Minimum payout per contributor"
                    />
                    <Input
                      label="Per Contributor Maximum"
                      placeholder="No limit"
                      value={formatCurrency(newTranche.distribution_thresholds.per_contributor_maximum)}
                      onChange={handleThresholdChange}
                      name="per_contributor_maximum"
                      variant="bordered"
                      startContent={<span className="text-default-400">$</span>}
                      description="Maximum payout per contributor (leave empty for no limit)"
                    />
                  </div>
                </CardBody>
              </Card>

              <Select
                label="Rollover Configuration"
                placeholder="Select rollover option"
                selectedKeys={[newTranche.rollover_config]}
                onSelectionChange={(keys) =>
                  setNewTranche({
                    ...newTranche,
                    rollover_config: Array.from(keys)[0]
                  })
                }
                variant="bordered"
                description="How to handle excess revenue after maximum payouts"
              >
                {rolloverOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </Select>

              <div className="flex justify-between gap-4 pt-4">
                <Button
                  variant="flat"
                  onPress={() => {
                    setShowTrancheForm(false);
                    setEditingIndex(null);
                    setNewTranche({
                      name: '',
                      description: '',
                      start_date: null,
                      end_date: null,
                      revenue_sources: [],
                      distribution_thresholds: {
                        minimum_revenue: 0,
                        maximum_payout: null,
                        per_contributor_minimum: 0,
                        per_contributor_maximum: null
                      },
                      rollover_config: 'none'
                    });
                  }}
                  size="lg"
                >
                  Cancel
                </Button>
                <Button
                  color="primary"
                  onPress={saveTrancheHandler}
                  size="lg"
                >
                  {editingIndex !== null ? 'Update Tranche' : 'Add Tranche'}
                </Button>
              </div>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default RevenueTranches;
