import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Chip } from '@heroui/react';

/**
 * Navigation Context Component
 * 
 * Provides spatial awareness and context indicators for the experimental navigation system.
 * Shows current location, zoom level, and available interactions in a non-intrusive way.
 */
const NavigationContext = ({ 
  viewMode, 
  zoomLevel, 
  currentCanvas, 
  canvases, 
  isVisible = true,
  className = "" 
}) => {
  const [showDetails, setShowDetails] = useState(false);

  // Get current canvas info
  const currentCanvasInfo = canvases[currentCanvas];

  // Format zoom level for display
  const formatZoomLevel = (zoom) => {
    return `${Math.round(zoom * 100)}%`;
  };

  // Get view mode display info
  const getViewModeInfo = () => {
    switch (viewMode) {
      case 'grid':
        return { 
          label: 'Grid View', 
          icon: '⊞', 
          description: 'Strategic Overview',
          color: 'from-blue-500 to-cyan-500'
        };
      case 'overworld':
        return { 
          label: 'World View', 
          icon: '🗺️', 
          description: 'Spatial Navigation',
          color: 'from-purple-500 to-pink-500'
        };
      case 'content':
        return { 
          label: currentCanvasInfo?.title || 'Content', 
          icon: currentCanvasInfo?.icon || '📄', 
          description: 'Deep Focus',
          color: currentCanvasInfo?.color || 'from-gray-500 to-slate-500'
        };
      default:
        return { 
          label: 'Navigation', 
          icon: '🧭', 
          description: 'Exploring',
          color: 'from-gray-500 to-slate-500'
        };
    }
  };

  const viewInfo = getViewModeInfo();

  if (!isVisible) return null;

  return (
    <motion.div
      className={`fixed top-4 right-4 z-50 ${className}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      <Card 
        className="bg-black/20 backdrop-blur-md border border-white/10 hover:bg-black/30 transition-all duration-300"
        onMouseEnter={() => setShowDetails(true)}
        onMouseLeave={() => setShowDetails(false)}
      >
        <CardBody className="p-3">
          <div className="flex items-center gap-3">
            {/* View Mode Indicator */}
            <div className="flex items-center gap-2">
              <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${viewInfo.color} flex items-center justify-center text-white text-sm`}>
                {viewInfo.icon}
              </div>
              <div className="text-white">
                <div className="text-sm font-medium">{viewInfo.label}</div>
                <AnimatePresence>
                  {showDetails && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="text-xs text-white/70"
                    >
                      {viewInfo.description}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Zoom Level Indicator */}
            <div className="flex items-center gap-1 text-white/80">
              <span className="text-xs">🔍</span>
              <span className="text-sm font-mono">{formatZoomLevel(zoomLevel)}</span>
            </div>
          </div>

          {/* Expanded Details */}
          <AnimatePresence>
            {showDetails && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-3 pt-3 border-t border-white/10"
              >
                <div className="space-y-2">
                  {/* Navigation Breadcrumbs */}
                  <div className="flex items-center gap-1 text-xs text-white/70">
                    <span>Grid</span>
                    <span>→</span>
                    <span>World</span>
                    {viewMode === 'content' && (
                      <>
                        <span>→</span>
                        <span className="text-white">{currentCanvasInfo?.title}</span>
                      </>
                    )}
                  </div>

                  {/* Quick Actions */}
                  <div className="flex gap-1">
                    <Chip size="sm" variant="flat" className="bg-white/10 text-white text-xs">
                      Space: Toggle
                    </Chip>
                    <Chip size="sm" variant="flat" className="bg-white/10 text-white text-xs">
                      G: Grid
                    </Chip>
                    <Chip size="sm" variant="flat" className="bg-white/10 text-white text-xs">
                      R: Reset
                    </Chip>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardBody>
      </Card>
    </motion.div>
  );
};

export default NavigationContext;
