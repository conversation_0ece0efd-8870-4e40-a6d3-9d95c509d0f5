// Secure Error Handler
// Authentication & Security Agent: Secure error handling to prevent information disclosure
// Created: January 16, 2025

import { logSecurityEvent } from './securityUtils';

/**
 * Secure Error Handler
 * 
 * Prevents information disclosure through error messages while providing
 * useful feedback to users and comprehensive logging for developers
 */

// Error severity levels
export const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

// Error categories for classification
export const ERROR_CATEGORIES = {
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  VALIDATION: 'validation',
  NETWORK: 'network',
  DATABASE: 'database',
  SYSTEM: 'system',
  USER_INPUT: 'user_input',
  SECURITY: 'security'
};

// Safe error messages for production
const SAFE_ERROR_MESSAGES = {
  [ERROR_CATEGORIES.AUTHENTICATION]: {
    default: 'Authentication failed. Please check your credentials and try again.',
    session_expired: 'Your session has expired. Please log in again.',
    invalid_credentials: 'Invalid email or password. Please try again.',
    account_locked: 'Your account has been temporarily locked. Please contact support.',
    mfa_required: 'Multi-factor authentication is required to continue.'
  },
  [ERROR_CATEGORIES.AUTHORIZATION]: {
    default: 'You do not have permission to perform this action.',
    insufficient_permissions: 'Insufficient permissions to access this resource.',
    admin_required: 'Administrator privileges are required for this action.',
    resource_forbidden: 'Access to this resource is not allowed.'
  },
  [ERROR_CATEGORIES.VALIDATION]: {
    default: 'The information provided is not valid. Please check and try again.',
    required_field: 'This field is required.',
    invalid_format: 'The format of this field is not valid.',
    value_too_long: 'This value is too long.',
    value_too_short: 'This value is too short.'
  },
  [ERROR_CATEGORIES.NETWORK]: {
    default: 'A network error occurred. Please check your connection and try again.',
    timeout: 'The request timed out. Please try again.',
    connection_failed: 'Unable to connect to the server. Please try again later.',
    rate_limited: 'Too many requests. Please wait a moment and try again.'
  },
  [ERROR_CATEGORIES.DATABASE]: {
    default: 'A data error occurred. Please try again later.',
    not_found: 'The requested information was not found.',
    conflict: 'This action conflicts with existing data.',
    constraint_violation: 'This action violates data requirements.'
  },
  [ERROR_CATEGORIES.SYSTEM]: {
    default: 'A system error occurred. Please try again later.',
    maintenance: 'The system is currently under maintenance. Please try again later.',
    overloaded: 'The system is currently busy. Please try again in a few moments.',
    configuration_error: 'A configuration error occurred. Please contact support.'
  },
  [ERROR_CATEGORIES.USER_INPUT]: {
    default: 'Invalid input provided. Please check your information and try again.',
    malicious_input: 'Invalid characters detected. Please use only allowed characters.',
    file_too_large: 'The file you selected is too large.',
    unsupported_format: 'This file format is not supported.'
  },
  [ERROR_CATEGORIES.SECURITY]: {
    default: 'A security error occurred. This incident has been logged.',
    suspicious_activity: 'Suspicious activity detected. This incident has been logged.',
    blocked_request: 'This request has been blocked for security reasons.',
    csrf_token_invalid: 'Security token is invalid. Please refresh the page and try again.'
  }
};

// Sensitive information patterns to redact
const SENSITIVE_PATTERNS = [
  /password/gi,
  /token/gi,
  /secret/gi,
  /key/gi,
  /credential/gi,
  /authorization/gi,
  /session/gi,
  /cookie/gi,
  /bearer/gi,
  /api[_-]?key/gi,
  /access[_-]?token/gi,
  /refresh[_-]?token/gi,
  /private[_-]?key/gi,
  /client[_-]?secret/gi,
  /database[_-]?url/gi,
  /connection[_-]?string/gi,
  /email/gi,
  /phone/gi,
  /ssn/gi,
  /social[_-]?security/gi,
  /credit[_-]?card/gi,
  /\b\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}\b/g, // Credit card numbers
  /\b\d{3}-\d{2}-\d{4}\b/g, // SSN format
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g // Email addresses
];

/**
 * Secure Error Handler Class
 */
class SecureErrorHandler {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.errorCounts = new Map();
    this.lastErrorTime = new Map();
  }

  /**
   * Handle error securely
   */
  handleError(error, context = {}) {
    const errorInfo = this.analyzeError(error, context);
    const safeMessage = this.getSafeErrorMessage(errorInfo);
    
    // Log error securely
    this.logError(errorInfo, context);
    
    // Check for potential security issues
    this.checkSecurityImplications(errorInfo);
    
    // Return safe error for user display
    return {
      message: safeMessage,
      code: errorInfo.code,
      severity: errorInfo.severity,
      category: errorInfo.category,
      timestamp: new Date().toISOString(),
      requestId: this.generateRequestId()
    };
  }

  /**
   * Analyze error to determine category and severity
   */
  analyzeError(error, context) {
    const errorInfo = {
      originalError: error,
      message: error.message || 'Unknown error',
      stack: error.stack,
      code: error.code || 'UNKNOWN_ERROR',
      status: error.status || error.statusCode || 500,
      category: ERROR_CATEGORIES.SYSTEM,
      severity: ERROR_SEVERITY.MEDIUM,
      context: context
    };

    // Categorize error based on status code and message
    if (errorInfo.status === 401 || errorInfo.message.toLowerCase().includes('unauthorized')) {
      errorInfo.category = ERROR_CATEGORIES.AUTHENTICATION;
      errorInfo.severity = ERROR_SEVERITY.MEDIUM;
    } else if (errorInfo.status === 403 || errorInfo.message.toLowerCase().includes('forbidden')) {
      errorInfo.category = ERROR_CATEGORIES.AUTHORIZATION;
      errorInfo.severity = ERROR_SEVERITY.MEDIUM;
    } else if (errorInfo.status === 400 || errorInfo.message.toLowerCase().includes('validation')) {
      errorInfo.category = ERROR_CATEGORIES.VALIDATION;
      errorInfo.severity = ERROR_SEVERITY.LOW;
    } else if (errorInfo.status === 429 || errorInfo.message.toLowerCase().includes('rate limit')) {
      errorInfo.category = ERROR_CATEGORIES.NETWORK;
      errorInfo.severity = ERROR_SEVERITY.LOW;
    } else if (errorInfo.status === 404) {
      errorInfo.category = ERROR_CATEGORIES.DATABASE;
      errorInfo.severity = ERROR_SEVERITY.LOW;
    } else if (errorInfo.status >= 500) {
      errorInfo.category = ERROR_CATEGORIES.SYSTEM;
      errorInfo.severity = ERROR_SEVERITY.HIGH;
    }

    // Check for security-related errors
    if (this.isSecurityError(error)) {
      errorInfo.category = ERROR_CATEGORIES.SECURITY;
      errorInfo.severity = ERROR_SEVERITY.HIGH;
    }

    return errorInfo;
  }

  /**
   * Check if error has security implications
   */
  isSecurityError(error) {
    const securityKeywords = [
      'xss', 'injection', 'csrf', 'malicious', 'attack', 'exploit',
      'unauthorized access', 'privilege escalation', 'data breach',
      'suspicious activity', 'blocked request', 'security violation'
    ];

    const errorText = (error.message || '').toLowerCase();
    return securityKeywords.some(keyword => errorText.includes(keyword));
  }

  /**
   * Get safe error message for user display
   */
  getSafeErrorMessage(errorInfo) {
    const categoryMessages = SAFE_ERROR_MESSAGES[errorInfo.category];
    
    if (!categoryMessages) {
      return 'An unexpected error occurred. Please try again later.';
    }

    // Try to find specific message based on error details
    const errorMessage = errorInfo.message.toLowerCase();
    
    for (const [key, message] of Object.entries(categoryMessages)) {
      if (key !== 'default' && errorMessage.includes(key.replace('_', ' '))) {
        return message;
      }
    }

    return categoryMessages.default;
  }

  /**
   * Log error securely (redact sensitive information)
   */
  logError(errorInfo, context) {
    const logData = {
      timestamp: new Date().toISOString(),
      category: errorInfo.category,
      severity: errorInfo.severity,
      code: errorInfo.code,
      status: errorInfo.status,
      message: this.redactSensitiveInfo(errorInfo.message),
      context: this.redactSensitiveInfo(JSON.stringify(context)),
      userAgent: context.userAgent,
      url: context.url,
      userId: context.userId,
      sessionId: context.sessionId
    };

    // Include stack trace only in development
    if (this.isDevelopment) {
      logData.stack = errorInfo.stack;
      logData.originalMessage = errorInfo.message;
    }

    // Log to console in development
    if (this.isDevelopment) {
      console.error('Secure Error Handler:', logData);
    }

    // Log security events for security-related errors
    if (errorInfo.category === ERROR_CATEGORIES.SECURITY || errorInfo.severity === ERROR_SEVERITY.CRITICAL) {
      logSecurityEvent(
        'application_error',
        errorInfo.severity,
        context.userId,
        `${errorInfo.category} error: ${this.redactSensitiveInfo(errorInfo.message)}`,
        {
          error_code: errorInfo.code,
          error_category: errorInfo.category,
          error_severity: errorInfo.severity,
          context: this.redactSensitiveInfo(JSON.stringify(context))
        },
        this.calculateRiskScore(errorInfo)
      );
    }

    return logData;
  }

  /**
   * Redact sensitive information from strings
   */
  redactSensitiveInfo(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }

    let redactedText = text;

    // Apply sensitive pattern redaction
    SENSITIVE_PATTERNS.forEach(pattern => {
      redactedText = redactedText.replace(pattern, '[REDACTED]');
    });

    // Redact potential file paths
    redactedText = redactedText.replace(/\/[^\s]+/g, '[PATH_REDACTED]');

    // Redact potential URLs with sensitive info
    redactedText = redactedText.replace(/https?:\/\/[^\s]+/g, '[URL_REDACTED]');

    return redactedText;
  }

  /**
   * Check for security implications and take action
   */
  checkSecurityImplications(errorInfo) {
    // Track error frequency for potential attacks
    const errorKey = `${errorInfo.category}_${errorInfo.code}`;
    const now = Date.now();
    
    if (!this.errorCounts.has(errorKey)) {
      this.errorCounts.set(errorKey, 0);
    }
    
    this.errorCounts.set(errorKey, this.errorCounts.get(errorKey) + 1);
    this.lastErrorTime.set(errorKey, now);

    // Check for potential attack patterns
    const errorCount = this.errorCounts.get(errorKey);
    const timeWindow = 5 * 60 * 1000; // 5 minutes
    
    if (errorCount > 10 && (now - this.lastErrorTime.get(errorKey)) < timeWindow) {
      // Potential attack detected
      logSecurityEvent(
        'potential_attack_detected',
        'warning',
        null,
        `High frequency of ${errorInfo.category} errors detected`,
        {
          error_type: errorKey,
          error_count: errorCount,
          time_window: timeWindow
        },
        60
      );
    }

    // Clean up old error counts
    this.cleanupErrorCounts();
  }

  /**
   * Calculate risk score for security events
   */
  calculateRiskScore(errorInfo) {
    let riskScore = 0;

    // Base score by severity
    switch (errorInfo.severity) {
      case ERROR_SEVERITY.CRITICAL:
        riskScore += 40;
        break;
      case ERROR_SEVERITY.HIGH:
        riskScore += 30;
        break;
      case ERROR_SEVERITY.MEDIUM:
        riskScore += 20;
        break;
      case ERROR_SEVERITY.LOW:
        riskScore += 10;
        break;
    }

    // Additional score by category
    switch (errorInfo.category) {
      case ERROR_CATEGORIES.SECURITY:
        riskScore += 30;
        break;
      case ERROR_CATEGORIES.AUTHENTICATION:
      case ERROR_CATEGORIES.AUTHORIZATION:
        riskScore += 20;
        break;
      default:
        riskScore += 10;
    }

    return Math.min(100, riskScore);
  }

  /**
   * Generate unique request ID for error tracking
   */
  generateRequestId() {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Clean up old error counts to prevent memory leaks
   */
  cleanupErrorCounts() {
    const now = Date.now();
    const cleanupThreshold = 60 * 60 * 1000; // 1 hour

    for (const [key, lastTime] of this.lastErrorTime.entries()) {
      if (now - lastTime > cleanupThreshold) {
        this.errorCounts.delete(key);
        this.lastErrorTime.delete(key);
      }
    }
  }
}

// Create singleton instance
const secureErrorHandler = new SecureErrorHandler();

/**
 * Main error handling function
 */
export const handleSecureError = (error, context = {}) => {
  return secureErrorHandler.handleError(error, context);
};

/**
 * React Error Boundary compatible error handler
 */
export const handleReactError = (error, errorInfo, context = {}) => {
  const enhancedContext = {
    ...context,
    componentStack: errorInfo.componentStack,
    errorBoundary: true
  };
  
  return secureErrorHandler.handleError(error, enhancedContext);
};

/**
 * API error handler
 */
export const handleApiError = (error, request = {}) => {
  const context = {
    url: request.url,
    method: request.method,
    headers: request.headers ? Object.keys(request.headers) : [],
    apiError: true
  };
  
  return secureErrorHandler.handleError(error, context);
};

/**
 * Validation error handler
 */
export const handleValidationError = (validationErrors, context = {}) => {
  const error = new Error('Validation failed');
  error.code = 'VALIDATION_ERROR';
  error.status = 400;
  error.validationErrors = validationErrors;
  
  return secureErrorHandler.handleError(error, {
    ...context,
    validationErrors: validationErrors,
    validationType: true
  });
};

export default secureErrorHandler;
