import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, CardHeader, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>eader, <PERSON>dal<PERSON><PERSON>, <PERSON>dalFooter } from '@heroui/react';
import { toast } from 'react-hot-toast';

/**
 * AchievementGallery Component
 * 
 * Enhanced achievement gallery following wireframe specifications
 * Features recent unlocks, progress tracking, and celebration animations
 */
const AchievementGallery = ({ 
  achievements = [],
  recentUnlocks = [],
  onShareAchievement,
  onViewAll,
  className = ""
}) => {
  const [selectedAchievement, setSelectedAchievement] = useState(null);
  const [showCelebration, setShowCelebration] = useState(false);

  // Achievement definitions
  const achievementTypes = {
    'first_alliance': {
      icon: '🥇',
      title: 'First Studio Created',
      description: 'Created your first studio',
      category: 'Collaboration',
      rarity: 'Common',
      xpReward: 100
    },
    'tasks_10': {
      icon: '🎯',
      title: '10 Tasks Completed',
      description: 'Completed 10 tasks successfully',
      category: 'Productivity',
      rarity: 'Common',
      xpReward: 150
    },
    'collaborations_5': {
      icon: '🤝',
      title: '5 Successful Collaborations',
      description: 'Collaborated successfully with 5 different people',
      category: 'Teamwork',
      rarity: 'Uncommon',
      xpReward: 200
    },
    'skill_master_react': {
      icon: '⚛️',
      title: 'React Master',
      description: 'Achieved mastery level in React development',
      category: 'Skills',
      rarity: 'Rare',
      xpReward: 500
    },
    'revenue_1000': {
      icon: '💰',
      title: 'First $1000 Earned',
      description: 'Earned your first $1000 on the platform',
      category: 'Financial',
      rarity: 'Epic',
      xpReward: 750
    }
  };

  // Get achievement details
  const getAchievementDetails = (achievementId) => {
    return achievementTypes[achievementId] || {
      icon: '🏆',
      title: 'Unknown Achievement',
      description: 'Achievement details not found',
      category: 'General',
      rarity: 'Common',
      xpReward: 50
    };
  };

  // Get rarity color
  const getRarityColor = (rarity) => {
    const colors = {
      'Common': 'default',
      'Uncommon': 'primary',
      'Rare': 'secondary',
      'Epic': 'warning',
      'Legendary': 'danger'
    };
    return colors[rarity] || 'default';
  };

  // Handle achievement sharing
  const handleShare = (achievement) => {
    const details = getAchievementDetails(achievement.achievement_id);
    const shareText = `🎉 I just unlocked "${details.title}" on Royaltea! ${details.icon}`;
    
    if (navigator.share) {
      navigator.share({
        title: 'Achievement Unlocked!',
        text: shareText,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast.success('Achievement copied to clipboard!');
    }
    
    if (onShareAchievement) {
      onShareAchievement(achievement);
    }
  };

  // Trigger celebration animation
  const triggerCelebration = () => {
    setShowCelebration(true);
    setTimeout(() => setShowCelebration(false), 3000);
  };

  return (
    <div className={className}>
      <Card className="bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-purple-900/20 dark:to-indigo-800/20 border-2 border-purple-200 dark:border-purple-700 h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🏆</span>
              <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-200">Achievements</h3>
            </div>
            <Badge content={achievements.length} color="secondary">
              <Button 
                size="sm" 
                variant="flat"
                onPress={() => onViewAll && onViewAll()}
              >
                View All
              </Button>
            </Badge>
          </div>
        </CardHeader>
        
        <CardBody className="pt-0">
          {/* Recent Unlocks */}
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-default-700 mb-3">Recent Unlocks:</h4>
            <div className="space-y-3">
              {recentUnlocks.slice(0, 3).map((achievement, idx) => {
                const details = getAchievementDetails(achievement.achievement_id);
                return (
                  <motion.div
                    key={idx}
                    className="flex items-center gap-3 p-2 bg-white/50 dark:bg-black/20 rounded-lg cursor-pointer hover:bg-white/70 dark:hover:bg-black/30 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    onPress={() => setSelectedAchievement(achievement)}
                  >
                    <div className="text-2xl">{details.icon}</div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{details.title}</div>
                      <div className="text-xs text-default-500">{details.category}</div>
                    </div>
                    <Chip 
                      size="sm" 
                      color={getRarityColor(details.rarity)}
                      variant="flat"
                    >
                      {details.rarity}
                    </Chip>
                  </motion.div>
                );
              })}
              
              {recentUnlocks.length === 0 && (
                <div className="text-sm text-default-500 text-center py-4">
                  No recent achievements
                  <div className="text-xs mt-1">Complete tasks to unlock achievements!</div>
                </div>
              )}
            </div>
          </div>

          {/* Achievement Progress */}
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-default-700 mb-3">Progress:</h4>
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span>🎯 Task Master (15/25)</span>
                <span className="text-primary">60%</span>
              </div>
              <div className="w-full bg-default-200 rounded-full h-2">
                <div className="bg-primary h-2 rounded-full" style={{ width: '60%' }}></div>
              </div>
              
              <div className="flex justify-between items-center text-sm">
                <span>🤝 Team Player (3/10)</span>
                <span className="text-warning">30%</span>
              </div>
              <div className="w-full bg-default-200 rounded-full h-2">
                <div className="bg-warning h-2 rounded-full" style={{ width: '30%' }}></div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              color="secondary" 
              variant="flat" 
              size="sm" 
              className="flex-1"
              onPress={() => onViewAll && onViewAll()}
            >
              🏆 View All
            </Button>
            <Button 
              color="primary" 
              variant="flat" 
              size="sm" 
              className="flex-1"
              onPress={triggerCelebration}
            >
              🎉 Share Progress
            </Button>
          </div>

          {/* Celebration Animation */}
          <AnimatePresence>
            {showCelebration && (
              <motion.div
                className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <motion.div
                  className="text-6xl"
                  animate={{ 
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{ 
                    duration: 1,
                    repeat: 2
                  }}
                >
                  🎉
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardBody>
      </Card>

      {/* Achievement Details Modal */}
      <Modal 
        isOpen={!!selectedAchievement} 
        onClose={() => setSelectedAchievement(null)}
        size="md"
      >
        <ModalContent>
          {selectedAchievement && (
            <>
              <ModalHeader>
                <div className="flex items-center gap-3">
                  <span className="text-3xl">
                    {getAchievementDetails(selectedAchievement.achievement_id).icon}
                  </span>
                  <div>
                    <h3 className="text-xl font-semibold">
                      {getAchievementDetails(selectedAchievement.achievement_id).title}
                    </h3>
                    <Chip 
                      size="sm" 
                      color={getRarityColor(getAchievementDetails(selectedAchievement.achievement_id).rarity)}
                    >
                      {getAchievementDetails(selectedAchievement.achievement_id).rarity}
                    </Chip>
                  </div>
                </div>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-4">
                  <p className="text-default-600">
                    {getAchievementDetails(selectedAchievement.achievement_id).description}
                  </p>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-default-500">Category:</span>
                    <span className="font-medium">
                      {getAchievementDetails(selectedAchievement.achievement_id).category}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-default-500">XP Reward:</span>
                    <span className="font-medium text-primary">
                      +{getAchievementDetails(selectedAchievement.achievement_id).xpReward} XP
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-default-500">Unlocked:</span>
                    <span className="font-medium">
                      {new Date(selectedAchievement.unlocked_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button 
                  variant="light" 
                  onPress={() => setSelectedAchievement(null)}
                >
                  Close
                </Button>
                <Button 
                  color="primary" 
                  onPress={() => handleShare(selectedAchievement)}
                >
                  🎉 Share Achievement
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AchievementGallery;
