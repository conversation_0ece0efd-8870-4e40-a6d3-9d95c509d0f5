import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { supabase } from '../../utils/supabase/supabase.utils';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { toast } from 'react-hot-toast';
import { PieChart } from 'react-minimal-pie-chart';
import SimpleLoading from '../../components/layout/SimpleLoading';
import ContributionManager from '../../components/contribution/ContributionManager';
import MilestoneManager from '../../components/milestone/MilestoneManager';
import RoyaltyDistributionList from '../../components/revenue/RoyaltyDistributionList';
import ProjectActivityFeed from '../../components/project/ProjectActivityFeed';
import ProjectAnalyticsDashboard from '../../components/project/ProjectAnalyticsDashboard';

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);

  const [project, setProject] = useState(null);
  const [contributors, setContributors] = useState([]);
  const [milestones, setMilestones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [revenueSubTab, setRevenueSubTab] = useState('overview');
  const [userRole, setUserRole] = useState(null);
  const [showRoyaltyExplanation, setShowRoyaltyExplanation] = useState(false);

  // Sample data for royalty model explanation
  const [previewData] = useState({
    contributors: [
      { name: 'Alice', tasks: 15, hours: 40, difficulty: 3.5 },
      { name: 'Bob', tasks: 8, hours: 30, difficulty: 4.2 },
      { name: 'Charlie', tasks: 20, hours: 25, difficulty: 2.8 }
    ]
  });

  // Fetch project data
  useEffect(() => {
    const fetchProjectData = async () => {
      if (!id) return;

      try {
        setLoading(true);
        console.log('🔍 PROJECT DETAIL: Fetching project data for ID:', id);
        console.log('🔍 PROJECT DETAIL: Current user:', currentUser?.id);

        // Fetch project details
        const { data: projectData, error: projectError } = await supabase
          .from('projects')
          .select('*')
          .eq('id', id)
          .single();

        if (projectError) {
          console.error('Error fetching project:', projectError);
          throw projectError;
        }

        console.log('Project data fetched successfully:', projectData);

        // Fetch royalty model from royalty_models table
        const { data: royaltyModelData, error: royaltyModelError } = await supabase
          .from('royalty_models')
          .select('*')
          .eq('project_id', id)
          .maybeSingle();

        if (!royaltyModelError) {
          // Add royalty model to project data
          projectData.royalty_model = royaltyModelData || null;
          console.log('Fetched royalty model:', projectData.royalty_model);
        } else if (royaltyModelError.code !== 'PGRST116') {
          console.error('Error fetching royalty model:', royaltyModelError);
        } else {
          console.log('No royalty model found for project:', id);
          projectData.royalty_model = null;
        }

        setProject(projectData);

        // Fetch project contributors
        const { data: contributorsData, error: contributorsError } = await supabase
          .from('project_contributors')
          .select('*')
          .eq('project_id', id);

        // If we have contributors, fetch their user details separately
        let contributorsWithUserDetails = [];
        if (contributorsData && contributorsData.length > 0) {
          // Get all user IDs
          const userIds = contributorsData.map(c => c.user_id);

          // Fetch user details
          const { data: usersData, error: usersError } = await supabase
            .from('users')
            .select('id, email, display_name')
            .in('id', userIds);

          if (!usersError && usersData) {
            // Combine the data
            contributorsWithUserDetails = contributorsData.map(contributor => {
              const userData = usersData.find(user => user.id === contributor.user_id);
              return {
                ...contributor,
                users: userData || null
              };
            });
          } else {
            console.error('Error fetching user details:', usersError);
            contributorsWithUserDetails = contributorsData.map(contributor => ({
              ...contributor,
              users: null
            }));
          }
        } else {
          contributorsWithUserDetails = contributorsData || [];
        }

        if (contributorsError) throw contributorsError;
        setContributors(contributorsWithUserDetails);

        // Fetch project milestones
        const { data: milestonesData, error: milestonesError } = await supabase
          .from('milestones')
          .select('*')
          .eq('project_id', id)
          .order('created_at', { ascending: true });

        if (milestonesError) throw milestonesError;
        setMilestones(milestonesData || []);

        // Check user's role in the project
        if (currentUser) {
          const userContributor = contributorsWithUserDetails.find(c => c.user_id === currentUser.id);
          if (userContributor) {
            setUserRole(userContributor.is_admin ? 'admin' : 'contributor');
          } else {
            // Default to admin for testing purposes
            // In production, this should be 'viewer' for non-contributors
            setUserRole('admin');
          }
        } else {
          // If no user is logged in, set a default role for testing
          setUserRole('admin');
        }

      } catch (error) {
        console.error('Error fetching project data:', error);
        toast.error('Failed to load project data');
        setProject(null);
        setLoading(false);

        // Don't navigate away automatically - let the user see the error
        // and use the "Back to Projects" button in the UI
      } finally {
        setLoading(false);
      }
    };

    fetchProjectData();
  }, [id, currentUser, navigate]);

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Get contributor name
  const getContributorName = (contributor) => {
    if (!contributor.users) return 'Unknown';
    return contributor.users.display_name || contributor.users.email || 'Unknown';
  };

  // Show loading indicator when fetching project data
  if (loading) {
    console.log('🔍 PROJECT DETAIL: Showing loading indicator for project ID:', id);
    return <SimpleLoading text="PROJECT DETAIL: Loading project..." fullPage={true} />;
  }

  if (!project) {
    return (
      <div className="project-not-found">
        <h2>Project Not Found</h2>
        <p>The project you're looking for doesn't exist or you don't have permission to view it.</p>
        <Link to="/projects" className="btn btn-primary">Back to Projects</Link>
      </div>
    );
  }

  // Calculate royalty distribution based on the selected model
  const calculateRoyaltyDistribution = () => {
    if (!project || !project.royalty_model) return [];

    const { contributors } = previewData;
    let distribution = [];

    if (project.royalty_model.model_type === 'equal') {
      // Equal split model
      const equalShare = 100 / contributors.length;
      distribution = contributors.map(contributor => ({
        name: contributor.name,
        percentage: parseFloat(equalShare.toFixed(2))
      }));
    } else if (project.royalty_model.model_type === 'custom') {
      // Custom model - simplified for preview
      distribution = [
        { name: 'Alice', percentage: 50 },
        { name: 'Bob', percentage: 30 },
        { name: 'Charlie', percentage: 20 }
      ];
    } else if (project.royalty_model.model_type === 'cog') {
      // CoG model (Tasks-Hours-Difficulty)
      const tasksWeight = project.royalty_model.configuration?.tasks_weight || 33.33;
      const hoursWeight = project.royalty_model.configuration?.hours_weight || 33.33;
      const difficultyWeight = project.royalty_model.configuration?.difficulty_weight || 33.34;

      const totalTasks = contributors.reduce((sum, c) => sum + c.tasks, 0);
      const totalHours = contributors.reduce((sum, c) => sum + c.hours, 0);
      const totalAvgDifficulty = contributors.reduce((sum, c) => sum + c.difficulty, 0);

      const scores = contributors.map(contributor => {
        // Task score - normalized to reduce impact of high task counts
        const taskScore = totalTasks > 0 ?
          (Math.sqrt(contributor.tasks) / contributors.reduce((sum, c) => sum + Math.sqrt(c.tasks), 0)) *
          (tasksWeight / 100) : 0;

        // Hour score - standard calculation
        const hourScore = totalHours > 0 ?
          (contributor.hours / totalHours) * (hoursWeight / 100) : 0;

        // Difficulty score - using average difficulty
        const difficultyScore = totalAvgDifficulty > 0 ?
          (contributor.difficulty / totalAvgDifficulty) * (difficultyWeight / 100) : 0;

        return {
          name: contributor.name,
          score: taskScore + hourScore + difficultyScore
        };
      });

      const totalScore = scores.reduce((sum, s) => sum + s.score, 0);

      distribution = scores.map(score => ({
        name: score.name,
        percentage: parseFloat(((score.score / totalScore) * 100).toFixed(2))
      }));
    }

    return distribution;
  };

  return (
    <div className="project-detail-container">
      <div className="project-header">
        <div className="project-title-section">
          <div className="project-title-container">
            {project.thumbnail_url && (
              <div className="project-thumbnail">
                <img src={project.thumbnail_url} alt={project.name} />
              </div>
            )}
            <div>
              <h1 className="project-title">{project.name}</h1>
              <div className="project-meta">
                <span className="project-type">{project.project_type}</span>
                <span className="project-status">{project.is_active ? 'Active' : 'Inactive'}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="project-actions">
          {userRole === 'admin' && (
            <>
              <Link to={`/project/${id}/edit`} className="btn btn-primary me-2">
                <i className="bi bi-pencil"></i> Edit Project
              </Link>
              <Link to={`/project/${id}/tasks`} className="btn btn-secondary me-2">
                <i className="bi bi-kanban"></i> Kanban Board
              </Link>
              <Link to={`/project/${id}/contributions`} className="btn btn-success me-2">
                <i className="bi bi-clock-history"></i> Track Contributions
              </Link>
              <Link to={`/project/${id}/revenue`} className="btn btn-info me-2">
                <i className="bi bi-cash-coin"></i> Manage Revenue
              </Link>
              <Link to={`/project/${id}/agreements`} className="btn btn-dark me-2">
                <i className="bi bi-file-earmark-text"></i> Agreements
              </Link>
              <Link to={`/validation/metrics/${id}`} className="btn btn-primary me-2">
                <i className="bi bi-graph-up"></i> Validation Metrics
              </Link>
              <Link to={`/analytics/contributions/${id}`} className="btn btn-info">
                <i className="bi bi-bar-chart-line"></i> Analytics
              </Link>
            </>
          )}
          {userRole === 'contributor' && (
            <>
              <Link to={`/project/${id}/tasks`} className="btn btn-secondary me-2">
                <i className="bi bi-kanban"></i> Kanban Board
              </Link>
              <Link to={`/project/${id}/contributions`} className="btn btn-success me-2">
                <i className="bi bi-clock-history"></i> Track Contributions
              </Link>
              <Link to={`/project/${id}/revenue`} className="btn btn-info me-2">
                <i className="bi bi-cash-coin"></i> View Revenue
              </Link>
              <Link to={`/project/${id}/agreements`} className="btn btn-dark me-2">
                <i className="bi bi-file-earmark-text"></i> Agreements
              </Link>
              <Link to={`/analytics/contributions/${id}`} className="btn btn-info">
                <i className="bi bi-bar-chart-line"></i> Analytics
              </Link>
            </>
          )}
        </div>
      </div>

      <div className="project-description">
        {project.description || 'No description provided.'}
      </div>

      <div className="project-tabs">
        <button
          className={`tab-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`tab-button ${activeTab === 'contributions' ? 'active' : ''}`}
          onClick={() => setActiveTab('contributions')}
        >
          Contributions
        </button>
        <button
          className={`tab-button ${activeTab === 'revenue' ? 'active' : ''}`}
          onClick={() => setActiveTab('revenue')}
        >
          Revenue
        </button>
        <button
          className={`tab-button ${activeTab === 'milestones' ? 'active' : ''}`}
          onClick={() => setActiveTab('milestones')}
        >
          Milestones
        </button>
        <button
          className={`tab-button ${activeTab === 'team' ? 'active' : ''}`}
          onClick={() => setActiveTab('team')}
        >
          Team
        </button>
        <button
          className={`tab-button ${activeTab === 'activity' ? 'active' : ''}`}
          onClick={() => setActiveTab('activity')}
        >
          Activity
        </button>
        <button
          className={`tab-button ${activeTab === 'analytics' ? 'active' : ''}`}
          onClick={() => setActiveTab('analytics')}
        >
          Analytics
        </button>
      </div>

      <div className="project-content">
        {activeTab === 'overview' && (
          <div className="project-overview">
            <div className="overview-section">
              <h3>Project Details</h3>
              <div className="details-grid">
                <div className="detail-item">
                  <div className="detail-label">Start Date</div>
                  <div className="detail-value">{formatDate(project.start_date)}</div>
                </div>
                <div className="detail-item">
                  <div className="detail-label">Launch Date</div>
                  <div className="detail-value">{formatDate(project.launch_date)}</div>
                </div>
                <div className="detail-item">
                  <div className="detail-label">Estimated Duration</div>
                  <div className="detail-value">{project.estimated_duration} months</div>
                </div>
                <div className="detail-item">
                  <div className="detail-label">Visibility</div>
                  <div className="detail-value">{project.is_public ? 'Public' : 'Private'}</div>
                </div>
              </div>
            </div>

            <div className="overview-section">
              <h3>Royalty Model</h3>
              <div className="royalty-model">
                <div className="model-type">
                  <strong>Model Type:</strong> {project.royalty_model?.model_type === 'cog' ? 'CoG Model' :
                                              project.royalty_model?.model_type === 'equal' ? 'Equal Split' :
                                              project.royalty_model?.model_type === 'custom' ? 'Custom Model' :
                                              project.royalty_model?.model_type || 'Not set'}
                </div>
                {(project.royalty_model?.model_schema === 'cog' || project.royalty_model?.model_type === 'cog') && (
                  <div className="model-weights">
                    <div className="weight-item">
                      <div className="weight-label">Tasks</div>
                      <div className="weight-value">{project.royalty_model?.configuration?.tasks_weight || 0}%</div>
                    </div>
                    <div className="weight-item">
                      <div className="weight-label">Hours</div>
                      <div className="weight-value">{project.royalty_model?.configuration?.hours_weight || 0}%</div>
                    </div>
                    <div className="weight-item">
                      <div className="weight-label">Difficulty</div>
                      <div className="weight-value">{project.royalty_model?.configuration?.difficulty_weight || 0}%</div>
                    </div>
                  </div>
                )}
                <button
                  className="learn-more-button"
                  onClick={() => setShowRoyaltyExplanation(!showRoyaltyExplanation)}
                >
                  {showRoyaltyExplanation ? 'Hide Details' : 'Learn More'}
                </button>

                {showRoyaltyExplanation && (
                  <div className="royalty-explanation">
                    <h4>How {project.royalty_model?.model_type === 'cog' ? 'CoG Model' :
                           project.royalty_model?.model_type === 'equal' ? 'Equal Split' :
                           'Custom Model'} Works</h4>

                    {project.royalty_model?.model_type === 'cog' && (
                      <>
                        <div className="explanation-text">
                          <p>The CoG (City of Gamers) model calculates royalties based on three factors:</p>
                          <ul>
                            <li><strong>Tasks:</strong> Number of tasks completed (using square root normalization)</li>
                            <li><strong>Hours:</strong> Time spent on contributions</li>
                            <li><strong>Difficulty:</strong> Average complexity of completed tasks</li>
                          </ul>
                          <p>This balanced approach ensures fair compensation based on both quantity and quality of work.</p>
                        </div>

                        <div className="model-visualization">
                          <div className="pie-chart-container">
                            <h5>Weight Distribution</h5>
                            <PieChart
                              data={[
                                { title: 'Tasks', value: project.royalty_model?.configuration?.tasks_weight || 33.33, color: '#3b82f6' },
                                { title: 'Hours', value: project.royalty_model?.configuration?.hours_weight || 33.33, color: '#10b981' },
                                { title: 'Difficulty', value: project.royalty_model?.configuration?.difficulty_weight || 33.34, color: '#f59e0b' }
                              ]}
                              lineWidth={60}
                              paddingAngle={2}
                              rounded
                              label={({ dataEntry }) => `${dataEntry.title}`}
                              labelStyle={{
                                fontSize: '5px',
                                fontFamily: 'sans-serif',
                                fill: '#fff'
                              }}
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {project.royalty_model?.model_type === 'equal' && (
                      <div className="explanation-text">
                        <p>The Equal Split model divides royalties equally among all active contributors.</p>
                        <p>For example, if there are 5 contributors, each will receive 20% of the royalties.</p>
                      </div>
                    )}

                    {project.royalty_model?.model_type === 'custom' && (
                      <div className="explanation-text">
                        <p>The Custom model allows the project owner to define their own royalty calculation formula.</p>
                        <p>This can be based on roles, specific contribution types, or other custom metrics.</p>
                      </div>
                    )}

                    <div className="interactive-example">
                      <h5>Interactive Example</h5>

                      <div className="example-data">
                        <div className="example-table">
                          <h6>Sample Contributor Data</h6>
                          <table>
                            <thead>
                              <tr>
                                <th>Contributor</th>
                                <th>Tasks</th>
                                <th>Hours</th>
                                <th>Avg. Difficulty</th>
                              </tr>
                            </thead>
                            <tbody>
                              {previewData.contributors.map((contributor, index) => (
                                <tr key={index}>
                                  <td>{contributor.name}</td>
                                  <td>{contributor.tasks}</td>
                                  <td>{contributor.hours}</td>
                                  <td>{contributor.difficulty}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>

                        <div className="example-results">
                          <h6>Calculated Distribution</h6>
                          <table>
                            <thead>
                              <tr>
                                <th>Contributor</th>
                                <th>Percentage</th>
                                <th>Visual</th>
                              </tr>
                            </thead>
                            <tbody>
                              {calculateRoyaltyDistribution().map((distribution, index) => (
                                <tr key={index}>
                                  <td>{distribution.name}</td>
                                  <td>{distribution.percentage}%</td>
                                  <td>
                                    <div className="progress">
                                      <div
                                        className="progress-bar"
                                        role="progressbar"
                                        style={{
                                          width: `${distribution.percentage}%`,
                                          backgroundColor: index === 0 ? '#3b82f6' : index === 1 ? '#10b981' : '#f59e0b'
                                        }}
                                      ></div>
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="overview-section">
              <h3>Quick Stats</h3>
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-value">{contributors.length}</div>
                  <div className="stat-label">Team Members</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">{milestones.length}</div>
                  <div className="stat-label">Milestones</div>
                </div>
                <div className="stat-card">
                  <div className="stat-value">{milestones.filter(m => m.status === 'completed').length}</div>
                  <div className="stat-label">Completed Milestones</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'contributions' && (
          <div className="project-contributions">
            <div className="contributions-header">
              <h3>Contributions</h3>
              <Link to={`/project/${id}/contributions`} className="btn btn-primary">
                <i className="bi bi-arrow-up-right-square"></i> Open Full Contribution Tracker
              </Link>
            </div>
            <ContributionManager projectId={id} />
          </div>
        )}

        {activeTab === 'revenue' && (
          <div className="project-revenue">
            <div className="revenue-header">
              <h3>Revenue</h3>
              <Link to={`/project/${id}/revenue`} className="btn btn-primary">
                <i className="bi bi-arrow-up-right-square"></i> Open Full Revenue Manager
              </Link>
            </div>
            <p className="section-description">
              Track and manage project revenue and royalty distributions.
            </p>

            <div className="revenue-tabs">
              <button
                className={`revenue-tab-button ${revenueSubTab === 'overview' ? 'active' : ''}`}
                onClick={() => setRevenueSubTab('overview')}
              >
                Overview
              </button>
              <button
                className={`revenue-tab-button ${revenueSubTab === 'distributions' ? 'active' : ''}`}
                onClick={() => setRevenueSubTab('distributions')}
              >
                Royalty Distributions
              </button>
            </div>

            {revenueSubTab === 'overview' && (
              <div className="revenue-preview">
                <Link to={`/project/${id}/revenue`} className="revenue-preview-link">
                  <i className="bi bi-cash-stack"></i>
                  <span>Click here to manage project revenue</span>
                  <i className="bi bi-arrow-right"></i>
                </Link>
              </div>
            )}

            {revenueSubTab === 'distributions' && (
              <div className="royalty-distributions">
                <RoyaltyDistributionList projectId={id} />
              </div>
            )}
          </div>
        )}

        {activeTab === 'milestones' && (
          <div className="project-milestones">
            <MilestoneManager projectId={id} />
          </div>
        )}

        {activeTab === 'team' && (
          <div className="project-team">
            <h3>Project Team</h3>
            {contributors.length === 0 ? (
              <div className="empty-state">
                <p>No team members have been added to this project yet.</p>
                {userRole === 'admin' && (
                  <Link to={`/project/${id}/edit`} className="btn btn-primary">
                    Add Team Members
                  </Link>
                )}
              </div>
            ) : (
              <div className="contributors-list">
                {contributors.map((contributor) => (
                  <div key={contributor.id} className="contributor-card">
                    <div className="contributor-avatar">
                      <i className="bi bi-person-circle"></i>
                    </div>
                    <div className="contributor-info">
                      <div className="contributor-name">{getContributorName(contributor)}</div>
                      <div className="contributor-role">{contributor.role || 'Team Member'}</div>
                      {contributor.is_admin && (
                        <div className="contributor-badge admin">Admin</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="project-activity">
            <ProjectActivityFeed projectId={id} />
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="project-analytics">
            <ProjectAnalyticsDashboard projectId={id} />
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectDetail;
