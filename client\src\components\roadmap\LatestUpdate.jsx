import React from 'react';

import { formatDistanceToNow } from 'date-fns';

const LatestUpdate = ({ latestUpdate }) => {
  if (!latestUpdate) {
    return null;
  }

  const { title, description, date, version, author } = latestUpdate;

  // Format the date as "X days/hours ago"
  const formattedDate = formatDistanceToNow(new Date(date), { addSuffix: true });

  return (
    <Card className="latest-update-card">
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h5 className="mb-0">
          <i className="bi bi-lightning-charge-fill me-2 text-warning"></i>
          Latest Update
        </h5>
        {version && (
          <Badge bg="primary" pill>v{version}</Badge>
        )}
      </Card.Header>
      <Card.Body>
        <Card.Title>{title}</Card.Title>
        <Card.Text>{description}</Card.Text>
        <div className="latest-update-footer">
          <small className="text-muted">
            <i className="bi bi-clock me-1"></i> {formattedDate}
          </small>
          {author && (
            <small className="text-muted">
              <i className="bi bi-person me-1"></i> {author}
            </small>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default LatestUpdate;
