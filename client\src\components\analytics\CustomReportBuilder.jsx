import React, { useState } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Input, Select, SelectItem, Checkbox } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * CustomReportBuilder Component
 * 
 * Advanced report builder with drag-and-drop interface
 * Allows users to create custom analytics reports with various widgets
 */
const CustomReportBuilder = ({ 
  isOpen, 
  onClose, 
  onSaveReport,
  className = ""
}) => {
  const [reportName, setReportName] = useState('');
  const [selectedWidgets, setSelectedWidgets] = useState([]);
  const [reportSettings, setReportSettings] = useState({
    timeRange: '30d',
    format: 'dashboard',
    schedule: 'manual',
    recipients: []
  });

  // Available widgets for custom reports
  const availableWidgets = [
    {
      id: 'revenue-metrics',
      name: 'Revenue Metrics',
      icon: '💰',
      description: 'Total revenue, growth, and financial KPIs',
      category: 'Financial',
      size: '2x2'
    },
    {
      id: 'performance-score',
      name: 'Performance Score',
      icon: '🎯',
      description: 'Overall performance rating and trends',
      category: 'Performance',
      size: '1x1'
    },
    {
      id: 'success-rate',
      name: 'Success Rate',
      icon: '⚡',
      description: 'Mission completion and quality metrics',
      category: 'Performance',
      size: '1x1'
    },
    {
      id: 'growth-trends',
      name: 'Growth Trends',
      icon: '📈',
      description: 'Interactive charts showing growth over time',
      category: 'Analytics',
      size: '2x2'
    },
    {
      id: 'top-performers',
      name: 'Top Performers',
      icon: '🔥',
      description: 'Ranking of best performing users/projects',
      category: 'Performance',
      size: '2x1'
    },
    {
      id: 'ai-insights',
      name: 'AI Insights',
      icon: '💡',
      description: 'AI-powered recommendations and predictions',
      category: 'Intelligence',
      size: '2x1'
    },
    {
      id: 'detailed-breakdown',
      name: 'Detailed Breakdown',
      icon: '📊',
      description: 'Comprehensive data analysis by categories',
      category: 'Analytics',
      size: '6x2'
    },
    {
      id: 'user-engagement',
      name: 'User Engagement',
      icon: '👥',
      description: 'User activity and engagement metrics',
      category: 'Users',
      size: '2x1'
    }
  ];

  // Widget categories
  const categories = ['All', 'Financial', 'Performance', 'Analytics', 'Intelligence', 'Users'];
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Filter widgets by category
  const filteredWidgets = selectedCategory === 'All' 
    ? availableWidgets 
    : availableWidgets.filter(widget => widget.category === selectedCategory);

  // Handle widget selection
  const toggleWidget = (widgetId) => {
    setSelectedWidgets(prev => 
      prev.includes(widgetId) 
        ? prev.filter(id => id !== widgetId)
        : [...prev, widgetId]
    );
  };

  // Handle report save
  const handleSaveReport = () => {
    if (!reportName.trim() || selectedWidgets.length === 0) {
      return;
    }

    const reportConfig = {
      name: reportName,
      widgets: selectedWidgets,
      settings: reportSettings,
      createdAt: new Date().toISOString()
    };

    if (onSaveReport) {
      onSaveReport(reportConfig);
    }

    // Reset form
    setReportName('');
    setSelectedWidgets([]);
    setReportSettings({
      timeRange: '30d',
      format: 'dashboard',
      schedule: 'manual',
      recipients: []
    });

    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="5xl"
      scrollBehavior="inside"
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6"
      }}
    >
      <ModalContent>
        <ModalHeader>
          <div className="flex items-center gap-3">
            <span className="text-2xl">📊</span>
            <div>
              <h2 className="text-xl font-bold">Custom Report Builder</h2>
              <p className="text-sm text-default-500">Create personalized analytics reports</p>
            </div>
          </div>
        </ModalHeader>

        <ModalBody>
          <div className="space-y-6">
            {/* Report Configuration */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Report Configuration</h3>
              </CardHeader>
              <CardBody>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Report Name"
                    placeholder="Monthly Performance Report"
                    value={reportName}
                    onChange={(e) => setReportName(e.target.value)}
                  />
                  <Select 
                    label="Time Range"
                    selectedKeys={[reportSettings.timeRange]}
                    onSelectionChange={(keys) => setReportSettings(prev => ({
                      ...prev,
                      timeRange: Array.from(keys)[0]
                    }))}
                  >
                    <SelectItem key="7d">Last 7 Days</SelectItem>
                    <SelectItem key="30d">Last 30 Days</SelectItem>
                    <SelectItem key="90d">Last 90 Days</SelectItem>
                    <SelectItem key="1y">Last Year</SelectItem>
                  </Select>
                  <Select 
                    label="Output Format"
                    selectedKeys={[reportSettings.format]}
                    onSelectionChange={(keys) => setReportSettings(prev => ({
                      ...prev,
                      format: Array.from(keys)[0]
                    }))}
                  >
                    <SelectItem key="dashboard">Interactive Dashboard</SelectItem>
                    <SelectItem key="pdf">PDF Report</SelectItem>
                    <SelectItem key="excel">Excel Spreadsheet</SelectItem>
                    <SelectItem key="email">Email Summary</SelectItem>
                  </Select>
                  <Select 
                    label="Schedule"
                    selectedKeys={[reportSettings.schedule]}
                    onSelectionChange={(keys) => setReportSettings(prev => ({
                      ...prev,
                      schedule: Array.from(keys)[0]
                    }))}
                  >
                    <SelectItem key="manual">Manual Generation</SelectItem>
                    <SelectItem key="daily">Daily</SelectItem>
                    <SelectItem key="weekly">Weekly</SelectItem>
                    <SelectItem key="monthly">Monthly</SelectItem>
                  </Select>
                </div>
              </CardBody>
            </Card>

            {/* Widget Selection */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between w-full">
                  <h3 className="text-lg font-semibold">Select Widgets</h3>
                  <Chip color="primary" variant="flat">
                    {selectedWidgets.length} selected
                  </Chip>
                </div>
              </CardHeader>
              <CardBody>
                {/* Category Filter */}
                <div className="flex gap-2 mb-4 flex-wrap">
                  {categories.map(category => (
                    <Button
                      key={category}
                      size="sm"
                      variant={selectedCategory === category ? "solid" : "flat"}
                      color={selectedCategory === category ? "primary" : "default"}
                      onPress={() => setSelectedCategory(category)}
                    >
                      {category}
                    </Button>
                  ))}
                </div>

                {/* Widget Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredWidgets.map((widget, index) => (
                    <motion.div
                      key={widget.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <Card 
                        isPressable
                        onPress={() => toggleWidget(widget.id)}
                        className={`cursor-pointer transition-all ${
                          selectedWidgets.includes(widget.id)
                            ? 'border-2 border-primary bg-primary-50'
                            : 'border border-default-200 hover:border-primary-300'
                        }`}
                      >
                        <CardBody className="p-4">
                          <div className="flex items-start gap-3">
                            <span className="text-2xl">{widget.icon}</span>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-semibold text-sm">{widget.name}</h4>
                                <Chip size="sm" variant="flat" color="secondary">
                                  {widget.size}
                                </Chip>
                              </div>
                              <p className="text-xs text-default-600 mb-2">
                                {widget.description}
                              </p>
                              <Chip size="sm" variant="bordered" color="default">
                                {widget.category}
                              </Chip>
                            </div>
                            <Checkbox
                              isSelected={selectedWidgets.includes(widget.id)}
                              onChange={() => toggleWidget(widget.id)}
                            />
                          </div>
                        </CardBody>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </CardBody>
            </Card>

            {/* Preview */}
            {selectedWidgets.length > 0 && (
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Report Preview</h3>
                </CardHeader>
                <CardBody>
                  <div className="space-y-2">
                    <div className="text-sm text-default-600">
                      <strong>Report:</strong> {reportName || 'Untitled Report'}
                    </div>
                    <div className="text-sm text-default-600">
                      <strong>Widgets:</strong> {selectedWidgets.length} components
                    </div>
                    <div className="text-sm text-default-600">
                      <strong>Time Range:</strong> {reportSettings.timeRange}
                    </div>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {selectedWidgets.map(widgetId => {
                        const widget = availableWidgets.find(w => w.id === widgetId);
                        return (
                          <Chip key={widgetId} size="sm" variant="flat" color="primary">
                            {widget?.icon} {widget?.name}
                          </Chip>
                        );
                      })}
                    </div>
                  </div>
                </CardBody>
              </Card>
            )}
          </div>
        </ModalBody>

        <ModalFooter>
          <Button variant="light" onPress={onClose}>
            Cancel
          </Button>
          <Button 
            color="primary" 
            onPress={handleSaveReport}
            isDisabled={!reportName.trim() || selectedWidgets.length === 0}
          >
            Create Report
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default CustomReportBuilder;
