-- Create teams tables for team management and team-based project ownership
-- This migration adds support for teams, team members, and team projects

-- Check if the teams table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'teams'
    ) THEN
        -- Create the teams table
        CREATE TABLE public.teams (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            name TEXT NOT NULL,
            description TEXT,
            logo_url TEXT,
            created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );

        -- Add comments to the teams table
        COMMENT ON TABLE public.teams IS 'Teams that can own projects and have members';
        COMMENT ON COLUMN public.teams.id IS 'Unique identifier for the team';
        COMMENT ON COLUMN public.teams.name IS 'Name of the team';
        COMMENT ON COLUMN public.teams.description IS 'Description of the team';
        COMMENT ON COLUMN public.teams.logo_url IS 'URL to the team logo';
        COMMENT ON COLUMN public.teams.created_by IS 'User who created the team';
        COMMENT ON COLUMN public.teams.created_at IS 'Timestamp when the team was created';
        COMMENT ON COLUMN public.teams.updated_at IS 'Timestamp when the team was last updated';
    END IF;
END $$;

-- Check if the team_members table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'team_members'
    ) THEN
        -- Create the team_members table
        CREATE TABLE public.team_members (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
            user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            role TEXT NOT NULL DEFAULT 'member',
            is_admin BOOLEAN DEFAULT false,
            joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(team_id, user_id)
        );

        -- Add comments to the team_members table
        COMMENT ON TABLE public.team_members IS 'Members of teams';
        COMMENT ON COLUMN public.team_members.id IS 'Unique identifier for the team member';
        COMMENT ON COLUMN public.team_members.team_id IS 'Team the member belongs to';
        COMMENT ON COLUMN public.team_members.user_id IS 'User who is a member of the team';
        COMMENT ON COLUMN public.team_members.role IS 'Role of the member in the team (owner, admin, member)';
        COMMENT ON COLUMN public.team_members.is_admin IS 'Whether the member is an admin of the team';
        COMMENT ON COLUMN public.team_members.joined_at IS 'Timestamp when the member joined the team';
        COMMENT ON COLUMN public.team_members.created_at IS 'Timestamp when the record was created';
        COMMENT ON COLUMN public.team_members.updated_at IS 'Timestamp when the record was last updated';
    END IF;
END $$;

-- Check if the team_projects table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'team_projects'
    ) THEN
        -- Create the team_projects table
        CREATE TABLE public.team_projects (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
            project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            UNIQUE(team_id, project_id)
        );

        -- Add comments to the team_projects table
        COMMENT ON TABLE public.team_projects IS 'Projects owned by teams';
        COMMENT ON COLUMN public.team_projects.id IS 'Unique identifier for the team project';
        COMMENT ON COLUMN public.team_projects.team_id IS 'Team that owns the project';
        COMMENT ON COLUMN public.team_projects.project_id IS 'Project owned by the team';
        COMMENT ON COLUMN public.team_projects.created_at IS 'Timestamp when the record was created';
        COMMENT ON COLUMN public.team_projects.updated_at IS 'Timestamp when the record was last updated';
    END IF;
END $$;

-- Add team_id column to projects table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'projects'
        AND column_name = 'team_id'
    ) THEN
        ALTER TABLE public.projects ADD COLUMN team_id UUID REFERENCES public.teams(id) ON DELETE SET NULL;
        COMMENT ON COLUMN public.projects.team_id IS 'Team that owns the project (if owned by a team)';
    END IF;
END $$;

-- Create team invitations table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'team_invitations'
    ) THEN
        -- Create the team_invitations table
        CREATE TABLE public.team_invitations (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            team_id UUID NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
            invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            invited_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            invited_email TEXT,
            role TEXT NOT NULL DEFAULT 'member',
            status TEXT NOT NULL DEFAULT 'pending',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            expires_at TIMESTAMP WITH TIME ZONE DEFAULT (now() + interval '7 days'),
            CONSTRAINT check_invited_user CHECK (
                (invited_user_id IS NOT NULL) OR (invited_email IS NOT NULL)
            )
        );

        -- Add comments to the team_invitations table
        COMMENT ON TABLE public.team_invitations IS 'Invitations to join teams';
        COMMENT ON COLUMN public.team_invitations.id IS 'Unique identifier for the invitation';
        COMMENT ON COLUMN public.team_invitations.team_id IS 'Team the invitation is for';
        COMMENT ON COLUMN public.team_invitations.invited_by IS 'User who sent the invitation';
        COMMENT ON COLUMN public.team_invitations.invited_user_id IS 'User who is invited (if known)';
        COMMENT ON COLUMN public.team_invitations.invited_email IS 'Email of the invited user (if user not in system)';
        COMMENT ON COLUMN public.team_invitations.role IS 'Role the invited user will have';
        COMMENT ON COLUMN public.team_invitations.status IS 'Status of the invitation (pending, accepted, rejected)';
        COMMENT ON COLUMN public.team_invitations.created_at IS 'Timestamp when the invitation was created';
        COMMENT ON COLUMN public.team_invitations.updated_at IS 'Timestamp when the invitation was last updated';
        COMMENT ON COLUMN public.team_invitations.expires_at IS 'Timestamp when the invitation expires';
    END IF;
END $$;

-- Create RLS policies for teams
DO $$
BEGIN
    -- Teams policies
    DROP POLICY IF EXISTS "Teams are viewable by members" ON public.teams;
    CREATE POLICY "Teams are viewable by members" ON public.teams
        FOR SELECT
        USING (
            id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid()
            )
        );

    DROP POLICY IF EXISTS "Teams can be created by authenticated users" ON public.teams;
    CREATE POLICY "Teams can be created by authenticated users" ON public.teams
        FOR INSERT
        WITH CHECK (auth.uid() = created_by);

    DROP POLICY IF EXISTS "Teams can be updated by admins" ON public.teams;
    CREATE POLICY "Teams can be updated by admins" ON public.teams
        FOR UPDATE
        USING (
            id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    DROP POLICY IF EXISTS "Teams can be deleted by admins" ON public.teams;
    CREATE POLICY "Teams can be deleted by admins" ON public.teams
        FOR DELETE
        USING (
            id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    -- Team members policies
    DROP POLICY IF EXISTS "Team members are viewable by team members" ON public.team_members;
    CREATE POLICY "Team members are viewable by team members" ON public.team_members
        FOR SELECT
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid()
            )
        );

    DROP POLICY IF EXISTS "Team members can be added by team admins" ON public.team_members;
    CREATE POLICY "Team members can be added by team admins" ON public.team_members
        FOR INSERT
        WITH CHECK (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    DROP POLICY IF EXISTS "Team members can be updated by team admins" ON public.team_members;
    CREATE POLICY "Team members can be updated by team admins" ON public.team_members
        FOR UPDATE
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    DROP POLICY IF EXISTS "Team members can be deleted by team admins" ON public.team_members;
    CREATE POLICY "Team members can be deleted by team admins" ON public.team_members
        FOR DELETE
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    -- Team projects policies
    DROP POLICY IF EXISTS "Team projects are viewable by team members" ON public.team_projects;
    CREATE POLICY "Team projects are viewable by team members" ON public.team_projects
        FOR SELECT
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid()
            )
        );

    DROP POLICY IF EXISTS "Team projects can be added by team admins" ON public.team_projects;
    CREATE POLICY "Team projects can be added by team admins" ON public.team_projects
        FOR INSERT
        WITH CHECK (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    DROP POLICY IF EXISTS "Team projects can be updated by team admins" ON public.team_projects;
    CREATE POLICY "Team projects can be updated by team admins" ON public.team_projects
        FOR UPDATE
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    DROP POLICY IF EXISTS "Team projects can be deleted by team admins" ON public.team_projects;
    CREATE POLICY "Team projects can be deleted by team admins" ON public.team_projects
        FOR DELETE
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    -- Team invitations policies
    DROP POLICY IF EXISTS "Team invitations are viewable by team members" ON public.team_invitations;
    CREATE POLICY "Team invitations are viewable by team members" ON public.team_invitations
        FOR SELECT
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid()
            ) OR invited_user_id = auth.uid()
        );

    DROP POLICY IF EXISTS "Team invitations can be created by team admins" ON public.team_invitations;
    CREATE POLICY "Team invitations can be created by team admins" ON public.team_invitations
        FOR INSERT
        WITH CHECK (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );

    DROP POLICY IF EXISTS "Team invitations can be updated by team admins or invitees" ON public.team_invitations;
    CREATE POLICY "Team invitations can be updated by team admins or invitees" ON public.team_invitations
        FOR UPDATE
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            ) OR invited_user_id = auth.uid()
        );

    DROP POLICY IF EXISTS "Team invitations can be deleted by team admins" ON public.team_invitations;
    CREATE POLICY "Team invitations can be deleted by team admins" ON public.team_invitations
        FOR DELETE
        USING (
            team_id IN (
                SELECT team_id FROM public.team_members
                WHERE user_id = auth.uid() AND is_admin = true
            )
        );
END $$;

-- Enable RLS on all tables
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_invitations ENABLE ROW LEVEL SECURITY;
