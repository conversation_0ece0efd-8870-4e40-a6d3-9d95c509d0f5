import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { toast } from 'react-toastify';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import LoadingAnimation from '../layout/LoadingAnimation';

/**
 * TeamInvitations component displays and manages team invitations
 * for the current user
 */
const TeamInvitations = () => {
  const { currentUser } = useContext(UserContext);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch invitations on component mount
  useEffect(() => {
    if (currentUser) {
      fetchInvitations();
    }
  }, [currentUser]);

  // Fetch team invitations for the current user
  const fetchInvitations = async () => {
    try {
      setLoading(true);

      // Get invitations for the current user
      const { data: invitationsData, error: invitationsError } = await supabase
        .from('team_invitations')
        .select(`
          id,
          team_id,
          invited_by,
          role,
          status,
          created_at,
          teams:team_id(name, description),
          inviter:invited_by(email, user_metadata)
        `)
        .eq('invited_user_id', currentUser.id)
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (invitationsError) throw invitationsError;

      // Process invitations data
      const processedInvitations = invitationsData.map(invitation => ({
        id: invitation.id,
        teamId: invitation.team_id,
        teamName: invitation.teams.name,
        teamDescription: invitation.teams.description,
        invitedBy: invitation.inviter.user_metadata?.full_name || invitation.inviter.email,
        role: invitation.role,
        status: invitation.status,
        createdAt: invitation.created_at
      }));

      setInvitations(processedInvitations);
    } catch (error) {
      console.error('Error fetching invitations:', error);
      toast.error('Failed to load invitations');
    } finally {
      setLoading(false);
    }
  };

  // Handle accepting an invitation
  const handleAcceptInvitation = async (invitationId, teamId, teamName) => {
    try {
      setLoading(true);

      // First, update the invitation status
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({ status: 'accepted' })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      // Then, add the user as a team member
      const { error: memberError } = await supabase
        .from('team_members')
        .insert([{
          team_id: teamId,
          user_id: currentUser.id,
          role: 'member',
          is_admin: false
        }]);

      if (memberError) throw memberError;

      // Delete the notification if it exists
      const { error: notifError } = await supabase
        .from('notifications')
        .delete()
        .eq('type', 'team_invitation')
        .eq('related_id', teamId)
        .eq('user_id', currentUser.id);

      if (notifError) console.error('Error deleting notification:', notifError);

      toast.success(`You have joined ${teamName}`);
      fetchInvitations();
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast.error('Failed to accept invitation');
    } finally {
      setLoading(false);
    }
  };

  // Handle declining an invitation
  const handleDeclineInvitation = async (invitationId, teamName) => {
    try {
      setLoading(true);

      // Update the invitation status
      const { error: updateError } = await supabase
        .from('team_invitations')
        .update({ status: 'declined' })
        .eq('id', invitationId);

      if (updateError) throw updateError;

      toast.success(`You have declined the invitation to join ${teamName}`);
      fetchInvitations();
    } catch (error) {
      console.error('Error declining invitation:', error);
      toast.error('Failed to decline invitation');
    } finally {
      setLoading(false);
    }
  };

  if (loading && invitations.length === 0) {
    return <LoadingAnimation />;
  }

  return (
    <div className="team-invitations-container">
      <div className="team-invitations-header">
        <h2>Team Invitations</h2>
        <Link to="/teams" className="view-teams-button">
          View Your Teams
        </Link>
      </div>

      {invitations.length > 0 ? (
        <div className="invitations-list">
          {invitations.map(invitation => (
            <div key={invitation.id} className="invitation-card">
              <div className="invitation-content">
                <div className="invitation-header">
                  <h3>{invitation.teamName}</h3>
                  <span className="invitation-date">
                    Invited {new Date(invitation.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <p className="invitation-description">
                  {invitation.teamDescription || 'No description'}
                </p>
                <div className="invitation-meta">
                  <span className="invited-by">
                    Invited by: {invitation.invitedBy}
                  </span>
                  <span className="invitation-role">
                    Role: {invitation.role}
                  </span>
                </div>
              </div>
              <div className="invitation-actions">
                <button
                  className="accept-button"
                  onClick={() => handleAcceptInvitation(invitation.id, invitation.teamId, invitation.teamName)}
                >
                  Accept
                </button>
                <button
                  className="decline-button"
                  onClick={() => handleDeclineInvitation(invitation.id, invitation.teamName)}
                >
                  Decline
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="no-invitations">
          <p>You don't have any pending team invitations.</p>
          <Link to="/teams" className="view-teams-button">
            View Your Teams
          </Link>
        </div>
      )}
    </div>
  );
};

export default TeamInvitations;
