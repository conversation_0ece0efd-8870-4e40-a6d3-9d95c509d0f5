/**
 * Contributor Onboarding Integration
 * 
 * Integrates Agreement Generation V2 with the contributor invitation and onboarding flow.
 * Generates agreements when new contributors join ventures.
 */

import { AgreementIntegrationService } from './AgreementIntegrationService.js';
import { supabase } from '../../supabase/supabase.utils.js';

export class ContributorOnboardingIntegration {
  constructor() {
    this.agreementService = new AgreementIntegrationService();
  }

  /**
   * Handle contributor invitation with automatic agreement generation
   */
  async handleContributorInvitation(ventureId, inviteeEmail, inviterData, invitationData = {}) {
    console.log('👥 Processing contributor invitation with agreement generation...');
    
    try {
      // Step 1: Create or find contributor profile
      const contributor = await this.findOrCreateContributor(inviteeEmail, invitationData);
      
      // Step 2: Create invitation record
      const invitation = await this.createInvitationRecord(
        ventureId,
        contributor.id,
        inviterData.id,
        invitationData
      );
      
      // Step 3: Generate agreement for the invitation
      const agreement = await this.generateInvitationAgreement(
        ventureId,
        contributor,
        inviterData,
        invitationData
      );
      
      // Step 4: Send invitation email with agreement preview
      await this.sendInvitationEmail(invitation, agreement, contributor);
      
      console.log('✅ Contributor invitation with agreement generation completed');
      
      return {
        invitation,
        agreement,
        contributor,
        status: 'invitation_sent',
        nextSteps: {
          contributorCanReview: true,
          agreementGenerated: true,
          emailSent: true
        }
      };
      
    } catch (error) {
      console.error('❌ Contributor invitation failed:', error);
      throw new Error(`Contributor invitation failed: ${error.message}`);
    }
  }

  /**
   * Handle contributor acceptance of invitation
   */
  async handleContributorAcceptance(invitationId, contributorData = {}) {
    console.log('✅ Processing contributor acceptance...');
    
    try {
      // Step 1: Update invitation status
      const { data: invitation, error: invitationError } = await supabase
        .from('contributor_invitations')
        .update({
          status: 'accepted',
          accepted_at: new Date().toISOString(),
          contributor_data: contributorData
        })
        .eq('id', invitationId)
        .select(`
          *,
          venture:projects!contributor_invitations_venture_id_fkey(*),
          contributor:profiles!contributor_invitations_contributor_id_fkey(*)
        `)
        .single();
      
      if (invitationError) throw invitationError;
      
      // Step 2: Add contributor to venture team
      await this.addContributorToVentureTeam(
        invitation.venture_id,
        invitation.contributor_id,
        contributorData
      );
      
      // Step 3: Finalize agreement
      const finalizedAgreement = await this.finalizeContributorAgreement(
        invitation.agreement_id,
        contributorData
      );
      
      // Step 4: Set up contributor workspace
      await this.setupContributorWorkspace(invitation.venture_id, invitation.contributor_id);
      
      // Step 5: Send welcome email
      await this.sendWelcomeEmail(invitation, finalizedAgreement);
      
      console.log('✅ Contributor acceptance processing completed');
      
      return {
        invitation,
        agreement: finalizedAgreement,
        status: 'onboarded',
        nextSteps: {
          workspaceReady: true,
          agreementActive: true,
          canStartContributing: true
        }
      };
      
    } catch (error) {
      console.error('❌ Contributor acceptance processing failed:', error);
      throw new Error(`Contributor acceptance failed: ${error.message}`);
    }
  }

  /**
   * Find or create contributor profile
   */
  async findOrCreateContributor(email, invitationData = {}) {
    console.log('👤 Finding or creating contributor profile...');
    
    try {
      // Try to find existing profile
      const { data: existingProfile, error: findError } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', email)
        .single();
      
      if (existingProfile) {
        console.log('✅ Found existing contributor profile');
        return existingProfile;
      }
      
      // Create new profile if not found
      const newProfile = {
        email: email,
        full_name: invitationData.name || '[Contributor Name]',
        role: 'contributor',
        status: 'invited',
        created_at: new Date().toISOString(),
        invitation_data: invitationData
      };
      
      const { data: createdProfile, error: createError } = await supabase
        .from('profiles')
        .insert([newProfile])
        .select()
        .single();
      
      if (createError) throw createError;
      
      console.log('✅ Created new contributor profile');
      return createdProfile;
      
    } catch (error) {
      console.error('❌ Failed to find or create contributor:', error);
      throw new Error(`Failed to find or create contributor: ${error.message}`);
    }
  }

  /**
   * Create invitation record
   */
  async createInvitationRecord(ventureId, contributorId, inviterId, invitationData) {
    console.log('📝 Creating invitation record...');
    
    try {
      const invitationRecord = {
        venture_id: ventureId,
        contributor_id: contributorId,
        inviter_id: inviterId,
        status: 'pending',
        invitation_type: invitationData.type || 'contributor',
        role: invitationData.role || 'contributor',
        permissions: invitationData.permissions || [],
        message: invitationData.message,
        expires_at: invitationData.expiresAt || this.getDefaultExpirationDate(),
        created_at: new Date().toISOString()
      };
      
      const { data: invitation, error } = await supabase
        .from('contributor_invitations')
        .insert([invitationRecord])
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('✅ Invitation record created');
      return invitation;
      
    } catch (error) {
      console.error('❌ Failed to create invitation record:', error);
      throw new Error(`Failed to create invitation record: ${error.message}`);
    }
  }

  /**
   * Generate agreement for invitation
   */
  async generateInvitationAgreement(ventureId, contributor, inviter, invitationData) {
    console.log('📄 Generating invitation agreement...');
    
    try {
      const agreement = await this.agreementService.generateForContributorInvitation(
        ventureId,
        contributor,
        inviter,
        {
          metadata: {
            invitationType: invitationData.type || 'contributor',
            invitationRole: invitationData.role || 'contributor',
            generationContext: 'contributor_invitation'
          }
        }
      );
      
      console.log('✅ Invitation agreement generated');
      return agreement;
      
    } catch (error) {
      console.error('❌ Failed to generate invitation agreement:', error);
      throw new Error(`Failed to generate invitation agreement: ${error.message}`);
    }
  }

  /**
   * Add contributor to venture team
   */
  async addContributorToVentureTeam(ventureId, contributorId, contributorData) {
    console.log('👥 Adding contributor to venture team...');
    
    try {
      const teamMemberRecord = {
        project_id: ventureId,
        user_id: contributorId,
        role: contributorData.role || 'contributor',
        permissions: contributorData.permissions || ['view', 'contribute'],
        contribution_type: contributorData.contributionType || 'general',
        start_date: new Date().toISOString(),
        status: 'active',
        created_at: new Date().toISOString()
      };
      
      const { data: teamMember, error } = await supabase
        .from('project_team_members')
        .insert([teamMemberRecord])
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('✅ Contributor added to venture team');
      return teamMember;
      
    } catch (error) {
      console.error('❌ Failed to add contributor to team:', error);
      throw new Error(`Failed to add contributor to team: ${error.message}`);
    }
  }

  /**
   * Finalize contributor agreement
   */
  async finalizeContributorAgreement(agreementId, contributorData) {
    console.log('📋 Finalizing contributor agreement...');
    
    try {
      // Update agreement status and add contributor-specific data
      const { data: agreement, error } = await supabase
        .from('generated_agreements')
        .update({
          status: 'active',
          finalized_at: new Date().toISOString(),
          contributor_acceptance_data: contributorData,
          is_active: true
        })
        .eq('id', agreementId)
        .select()
        .single();
      
      if (error) throw error;
      
      // Log agreement activation
      await this.agreementService.logAgreementActivity(
        agreement.venture_id,
        agreement.contributor_id,
        agreementId,
        'activated'
      );
      
      console.log('✅ Contributor agreement finalized');
      return agreement;
      
    } catch (error) {
      console.error('❌ Failed to finalize agreement:', error);
      throw new Error(`Failed to finalize agreement: ${error.message}`);
    }
  }

  /**
   * Set up contributor workspace
   */
  async setupContributorWorkspace(ventureId, contributorId) {
    console.log('🏗️  Setting up contributor workspace...');
    
    try {
      // Create workspace configuration
      const workspaceConfig = {
        venture_id: ventureId,
        contributor_id: contributorId,
        workspace_type: 'contributor',
        access_level: 'standard',
        tools_enabled: ['tasks', 'milestones', 'communication', 'file_sharing'],
        notifications_enabled: true,
        created_at: new Date().toISOString()
      };
      
      const { data: workspace, error } = await supabase
        .from('contributor_workspaces')
        .insert([workspaceConfig])
        .select()
        .single();
      
      if (error) throw error;
      
      console.log('✅ Contributor workspace set up');
      return workspace;
      
    } catch (error) {
      console.error('❌ Failed to set up workspace:', error);
      throw new Error(`Failed to set up workspace: ${error.message}`);
    }
  }

  /**
   * Send invitation email
   */
  async sendInvitationEmail(invitation, agreement, contributor) {
    console.log('📧 Sending invitation email...');
    
    try {
      // This would integrate with your email service
      const emailData = {
        to: contributor.email,
        template: 'contributor_invitation',
        data: {
          invitation,
          agreement: {
            id: agreement.storedAgreement?.id,
            preview: agreement.agreement.substring(0, 500) + '...'
          },
          contributor,
          acceptanceUrl: `${process.env.FRONTEND_URL}/invitations/${invitation.id}/accept`
        }
      };
      
      // Placeholder for actual email sending
      console.log('📧 Email would be sent with data:', emailData);
      
      // Update invitation with email sent status
      await supabase
        .from('contributor_invitations')
        .update({
          email_sent_at: new Date().toISOString(),
          email_status: 'sent'
        })
        .eq('id', invitation.id);
      
      console.log('✅ Invitation email sent');
      
    } catch (error) {
      console.error('❌ Failed to send invitation email:', error);
      // Don't throw - email failure shouldn't break the flow
    }
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(invitation, agreement) {
    console.log('🎉 Sending welcome email...');
    
    try {
      const emailData = {
        to: invitation.contributor.email,
        template: 'contributor_welcome',
        data: {
          invitation,
          agreement,
          workspaceUrl: `${process.env.FRONTEND_URL}/ventures/${invitation.venture_id}/workspace`
        }
      };
      
      // Placeholder for actual email sending
      console.log('📧 Welcome email would be sent with data:', emailData);
      
      console.log('✅ Welcome email sent');
      
    } catch (error) {
      console.error('❌ Failed to send welcome email:', error);
      // Don't throw - email failure shouldn't break the flow
    }
  }

  /**
   * Handle contributor rejection of invitation
   */
  async handleContributorRejection(invitationId, rejectionReason = '') {
    console.log('❌ Processing contributor rejection...');
    
    try {
      // Update invitation status
      const { data: invitation, error } = await supabase
        .from('contributor_invitations')
        .update({
          status: 'rejected',
          rejected_at: new Date().toISOString(),
          rejection_reason: rejectionReason
        })
        .eq('id', invitationId)
        .select()
        .single();
      
      if (error) throw error;
      
      // Mark associated agreement as inactive
      if (invitation.agreement_id) {
        await supabase
          .from('generated_agreements')
          .update({
            status: 'rejected',
            is_active: false
          })
          .eq('id', invitation.agreement_id);
      }
      
      console.log('✅ Contributor rejection processed');
      return invitation;
      
    } catch (error) {
      console.error('❌ Failed to process rejection:', error);
      throw new Error(`Failed to process rejection: ${error.message}`);
    }
  }

  /**
   * Get default expiration date for invitations
   */
  getDefaultExpirationDate() {
    const date = new Date();
    date.setDate(date.getDate() + 14); // 14 days from now
    return date.toISOString();
  }

  /**
   * Get contributor onboarding integration status
   */
  getIntegrationStatus() {
    return {
      version: '2.0.0',
      capabilities: {
        contributorInvitation: true,
        automaticAgreementGeneration: true,
        invitationManagement: true,
        workspaceSetup: true,
        emailIntegration: true,
        acceptanceProcessing: true,
        rejectionHandling: true
      },
      status: 'active'
    };
  }
}
