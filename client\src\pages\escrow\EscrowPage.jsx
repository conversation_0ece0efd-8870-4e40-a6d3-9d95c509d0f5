import React, { useState, useContext } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody, CardHeader, Button, Progress, Chip, Tabs, Tab } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';

/**
 * Escrow Page Component
 *
 * Manages escrow accounts, release conditions, transaction logs, and dispute resolution.
 * Provides transparency and security for revenue distribution.
 */
const EscrowPage = () => {
  const { currentUser } = useContext(UserContext);
  const [activeTab, setActiveTab] = useState('accounts');

  // Mock escrow data
  const escrowAccounts = [
    {
      id: 1,
      project: 'Game Engine Project',
      balance: 2500.00,
      pending: 500.00,
      released: 1200.00,
      releaseCondition: 'Milestone 3 completion',
      progress: 75
    },
    {
      id: 2,
      project: 'Mobile App Development',
      balance: 1800.00,
      pending: 300.00,
      released: 800.00,
      releaseCondition: 'Beta testing complete',
      progress: 60
    }
  ];

  const transactions = [
    {
      id: 1,
      type: 'deposit',
      amount: 500.00,
      project: 'Game Engine Project',
      date: '2024-01-15',
      status: 'completed'
    },
    {
      id: 2,
      type: 'release',
      amount: 300.00,
      project: 'Mobile App Development',
      date: '2024-01-14',
      status: 'completed'
    },
    {
      id: 3,
      type: 'pending',
      amount: 200.00,
      project: 'Game Engine Project',
      date: '2024-01-13',
      status: 'pending'
    }
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'deposit': return '⬇️';
      case 'release': return '⬆️';
      case 'pending': return '⏳';
      default: return '💰';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'danger';
      default: return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-indigo-900 to-purple-900">
      {/* Header */}
      <motion.div
        className="relative z-10 pt-8 pb-6"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              🏦
            </motion.div>
            <h1 className="text-4xl font-bold text-white mb-2">
              Escrow Management
            </h1>
            <p className="text-white/80 text-lg max-w-2xl mx-auto">
              Secure revenue holding and transparent release conditions for fair distribution.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="container mx-auto px-6 pb-12">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {/* Navigation Tabs */}
          <Card className="mb-8 bg-white/5 border border-white/10">
            <CardBody className="p-6">
              <Tabs
                selectedKey={activeTab}
                onSelectionChange={setActiveTab}
                variant="underlined"
                classNames={{
                  tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                  cursor: "w-full bg-gradient-to-r from-indigo-500 to-purple-500",
                  tab: "max-w-fit px-0 h-12",
                  tabContent: "group-data-[selected=true]:text-white text-white/70"
                }}
              >
                <Tab key="accounts" title="🏦 Accounts" />
                <Tab key="conditions" title="📋 Conditions" />
                <Tab key="transactions" title="📊 Transactions" />
                <Tab key="disputes" title="⚖️ Disputes" />
              </Tabs>
            </CardBody>
          </Card>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'accounts' && (
              <div className="space-y-6">
                {escrowAccounts.map((account) => (
                  <Card key={account.id} className="bg-white/5 border border-white/10">
                    <CardHeader className="pb-3">
                      <div className="flex justify-between items-center w-full">
                        <h3 className="text-xl font-semibold text-white">{account.project}</h3>
                        <Chip color="primary" variant="flat">
                          {account.progress}% Complete
                        </Chip>
                      </div>
                    </CardHeader>
                    <CardBody className="space-y-6">
                      {/* Balance Overview */}
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-400">
                            {formatCurrency(account.balance)}
                          </div>
                          <div className="text-white/70 text-sm">Total Balance</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-400">
                            {formatCurrency(account.pending)}
                          </div>
                          <div className="text-white/70 text-sm">Pending Release</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-400">
                            {formatCurrency(account.released)}
                          </div>
                          <div className="text-white/70 text-sm">Released</div>
                        </div>
                      </div>

                      {/* Release Progress */}
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span className="text-white/70">Release Condition</span>
                          <span className="text-white">{account.releaseCondition}</span>
                        </div>
                        <Progress
                          value={account.progress}
                          color="primary"
                          className="max-w-full"
                        />
                      </div>

                      {/* Actions */}
                      <div className="flex gap-3">
                        <Button className="bg-blue-500 hover:bg-blue-600 text-white">
                          View Details
                        </Button>
                        <Button variant="ghost" className="text-white/70 hover:text-white hover:bg-white/10">
                          Transaction History
                        </Button>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            )}

            {activeTab === 'conditions' && (
              <Card className="bg-white/5 border border-white/10">
                <CardBody className="p-6">
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">📋</div>
                    <h3 className="text-xl font-semibold text-white mb-2">Release Conditions</h3>
                    <p className="text-white/70 mb-6">
                      Configure automatic release conditions for escrow funds
                    </p>
                    <Button className="bg-indigo-500 hover:bg-indigo-600 text-white">
                      Manage Conditions
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}

            {activeTab === 'transactions' && (
              <Card className="bg-white/5 border border-white/10">
                <CardHeader className="pb-3">
                  <h3 className="text-xl font-semibold text-white">Transaction Log</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  {transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="text-2xl">
                          {getTransactionIcon(transaction.type)}
                        </div>
                        <div>
                          <div className="text-white font-medium">
                            {transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)} - {transaction.project}
                          </div>
                          <div className="text-white/60 text-sm">
                            {new Date(transaction.date).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-bold">
                          {formatCurrency(transaction.amount)}
                        </div>
                        <Chip size="sm" color={getStatusColor(transaction.status)} variant="flat">
                          {transaction.status}
                        </Chip>
                      </div>
                    </div>
                  ))}
                </CardBody>
              </Card>
            )}

            {activeTab === 'disputes' && (
              <Card className="bg-white/5 border border-white/10">
                <CardBody className="p-6">
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">⚖️</div>
                    <h3 className="text-xl font-semibold text-white mb-2">Dispute Resolution</h3>
                    <p className="text-white/70 mb-6">
                      No active disputes. Our escrow system helps prevent conflicts.
                    </p>
                    <Button className="bg-red-500 hover:bg-red-600 text-white">
                      Report Issue
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default EscrowPage;
