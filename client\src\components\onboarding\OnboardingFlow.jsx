import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import OnboardingStep from './OnboardingStep.jsx';
import OnboardingProgress from './OnboardingProgress.jsx';
import OnboardingWizard from './OnboardingWizard.jsx';
import OnboardingSuccess from './OnboardingSuccess.jsx';
import onboardingService from '../../services/onboardingService';
import { toast } from 'react-hot-toast';

/**
 * OnboardingFlow Component
 *
 * Complete new user journey following immersive pattern
 * Gets users to first meaningful action in <5 minutes
 * Follows exact wireframe specifications from onboarding-flow.md
 */
const OnboardingFlow = ({ onComplete, onNavigationAction }) => {
  const { currentUser } = useContext(UserContext);
  const [currentStep, setCurrentStep] = useState(1);
  const [userGoal, setUserGoal] = useState(null);
  const [projectType, setProjectType] = useState(null);
  const [teamChoice, setTeamChoice] = useState(null);
  const [experienceLevel, setExperienceLevel] = useState(null);
  const [timeStarted] = useState(Date.now());
  const [completedActions, setCompletedActions] = useState([]);
  const [showWizard, setShowWizard] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successResult, setSuccessResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Initialize onboarding session on component mount
  useEffect(() => {
    const initializeSession = async () => {
      if (!currentUser || hasInitialized) return;

      try {
        setIsLoading(true);

        // Check if user has already completed onboarding
        const status = await onboardingService.getOnboardingStatus();

        if (status.onboarding_completed) {
          // User has already completed onboarding, redirect to dashboard
          onComplete({
            userGoal: status.preferences?.onboarding_goal,
            alreadyCompleted: true
          });
          return;
        }

        // Check for existing incomplete session
        if (status.current_session) {
          setSessionId(status.current_session.id);
          setCurrentStep(status.current_step || 1);

          // Restore state from session if available
          if (status.current_session.completion_path) {
            const path = status.current_session.completion_path;
            setUserGoal(path.userGoal || null);
            setProjectType(path.projectType || null);
            setTeamChoice(path.teamChoice || null);
            setExperienceLevel(path.experienceLevel || null);
          }
        } else {
          // Initialize new session
          const result = await onboardingService.initializeOnboarding();
          setSessionId(result.session_id);
        }

        setHasInitialized(true);
      } catch (error) {
        console.error('Error initializing onboarding:', error);
        toast.error('Failed to initialize onboarding. Please refresh the page.');
      } finally {
        setIsLoading(false);
      }
    };

    initializeSession();
  }, [currentUser, hasInitialized, onComplete]);

  // Save state to service when it changes
  useEffect(() => {
    if (sessionId && hasInitialized) {
      const state = {
        currentStep,
        userGoal,
        projectType,
        teamChoice,
        experienceLevel,
        completedActions,
        timeStarted
      };

      onboardingService.saveStateToLocalStorage(state);
    }
  }, [sessionId, hasInitialized, currentStep, userGoal, projectType, teamChoice, experienceLevel, completedActions, timeStarted]);

  // Onboarding state management following specification
  const onboardingState = {
    currentStep,
    userGoal, // 'project', 'work', 'learn', 'skip'
    projectType,
    teamChoice, // 'solo', 'team'
    experienceLevel,
    completedActions,
    timeStarted,
    sessionId,
    templates: {
      quickVenture: {
        name: 'My Awesome Software',
        type: 'software',
        settings: ['standard_settings', 'basic_tracking', 'first_mission']
      },
      quickAlliance: {
        name: 'Dream Team Studios',
        settings: ['invite_members', 'shared_workspace', 'first_venture']
      }
    }
  };

  const steps = {
    1: {
      id: 'welcome',
      title: 'Welcome to Royaltea',
      subtitle: 'Where collaboration meets compensation',
      type: 'welcome'
    },
    2: {
      id: 'goal-selection',
      title: 'What brings you here today?',
      type: 'selection',
      options: [
        {
          id: 'project',
          title: 'Start a Project',
          description: 'Create and manage collaborative projects',
          icon: '🚀'
        },
        {
          id: 'work',
          title: 'Find Work & Opportunities',
          description: 'Discover bounties and gigs',
          icon: '💼'
        },
        {
          id: 'learn',
          title: 'Learn How It Works',
          description: 'Understand the platform',
          icon: '📚'
        }
      ]
    },
    3: {
      id: 'project-type',
      title: 'What are you building?',
      type: 'selection',
      condition: userGoal === 'project',
      options: [
        {
          id: 'software',
          title: 'Software/App',
          icon: '💻'
        },
        {
          id: 'creative',
          title: 'Creative Project',
          icon: '🎨'
        },
        {
          id: 'business',
          title: 'Business Service',
          icon: '🏢'
        }
      ]
    },
    4: {
      id: 'team-choice',
      title: 'Working solo or with a team?',
      type: 'selection',
      condition: userGoal === 'project',
      options: [
        {
          id: 'solo',
          title: 'Solo Project',
          description: "I'll handle this myself",
          icon: '👤'
        },
        {
          id: 'team',
          title: 'Team Project',
          description: 'I need collaborators',
          icon: '👥'
        }
      ]
    },
    5: {
      id: 'experience-level',
      title: "What's your experience level?",
      type: 'selection',
      condition: userGoal === 'work',
      options: [
        {
          id: 'beginner',
          title: 'Just Getting Started',
          icon: '🌱'
        },
        {
          id: 'experienced',
          title: 'Experienced Professional',
          icon: '⚡'
        },
        {
          id: 'expert',
          title: 'Expert Specialist',
          icon: '🏆'
        }
      ]
    }
  };

  // Get current step configuration
  const getCurrentStep = () => {
    const step = steps[currentStep];
    if (step && step.condition && !step.condition) {
      // Skip conditional steps that don't apply
      return getNextStep();
    }
    return step;
  };

  const getNextStep = () => {
    let nextStep = currentStep + 1;
    while (nextStep <= 5) {
      const step = steps[nextStep];
      if (!step.condition || step.condition) {
        return step;
      }
      nextStep++;
    }
    return null;
  };

  // Handle option selection with database integration
  const handleOptionSelect = async (option) => {
    const step = getCurrentStep();

    try {
      setIsLoading(true);

      let stepData = {
        optionSelected: option.id,
        optionTitle: option.title,
        stepId: step.id
      };

      if (step.id === 'goal-selection') {
        setUserGoal(option.id);
        stepData.userGoal = option.id;
        if (option.id === 'skip') {
          handleSkipSetup();
          return;
        }
      } else if (step.id === 'project-type') {
        setProjectType(option.id);
        stepData.projectType = option.id;
      } else if (step.id === 'team-choice') {
        setTeamChoice(option.id);
        stepData.teamChoice = option.id;
      } else if (step.id === 'experience-level') {
        setExperienceLevel(option.id);
        stepData.experienceLevel = option.id;
      }

      // Update progress in database
      if (sessionId) {
        await onboardingService.updateProgress(currentStep, stepData);
      }

      // Move to next step or complete onboarding
      const nextStep = getNextStep();
      if (nextStep) {
        setCurrentStep(currentStep + 1);
      } else {
        handleComplete();
      }
    } catch (error) {
      console.error('Error updating onboarding progress:', error);
      toast.error('Failed to save progress. Your selection has been saved locally.');

      // Continue with the flow even if database update fails
      const nextStep = getNextStep();
      if (nextStep) {
        setCurrentStep(currentStep + 1);
      } else {
        handleComplete();
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkipSetup = () => {
    // Skip directly to dashboard with template setup
    const defaultPath = {
      canvas: 'home',
      userGoal: 'skip',
      showOverlayTips: true
    };

    localStorage.setItem('onboardingSelectedPath', JSON.stringify(defaultPath));
    onComplete(defaultPath);
  };

  const handleComplete = async () => {
    // Create completion path based on user choices
    let completionPath = {
      userGoal,
      projectType,
      teamChoice,
      experienceLevel,
      timeToComplete: Date.now() - timeStarted
    };

    try {
      setIsLoading(true);

      // Complete onboarding in database
      if (sessionId) {
        await onboardingService.completeOnboarding(completionPath);
        onboardingService.clearLocalStorageBackup();
      }

      if (userGoal === 'project') {
        // Show wizard for Studio/Project creation
        setShowWizard(true);
        return;
      } else if (userGoal === 'work') {
        completionPath.action = 'show_opportunities';
        completionPath.canvas = 'contributions';
        // Show skill-matched opportunities
        setSuccessResult({
          type: 'opportunities_found',
          experienceLevel,
          opportunities: [] // Would be populated with real data
        });
        setShowSuccess(true);
        return;
      } else if (userGoal === 'learn') {
        completionPath.action = 'show_tutorial';
        completionPath.canvas = 'learn';
        // Show tutorial completion
        setSuccessResult({
          type: 'tutorial_complete'
        });
        setShowSuccess(true);
        return;
      }

      localStorage.setItem('onboardingSelectedPath', JSON.stringify(completionPath));
      setCompletedActions([...completedActions, 'onboarding_complete']);
      onComplete(completionPath);
    } catch (error) {
      console.error('Error completing onboarding:', error);
      toast.error('Failed to save completion status. Continuing anyway.');

      // Continue with the flow even if database update fails
      localStorage.setItem('onboardingSelectedPath', JSON.stringify(completionPath));
      setCompletedActions([...completedActions, 'onboarding_complete']);
      onComplete(completionPath);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle wizard completion
  const handleWizardComplete = (result) => {
    setShowWizard(false);
    setSuccessResult(result);
    setShowSuccess(true);
  };

  // Handle success screen actions
  const handleSuccessAction = (action) => {
    const completionPath = {
      userGoal,
      projectType,
      teamChoice,
      experienceLevel,
      timeToComplete: Date.now() - timeStarted,
      action,
      result: successResult
    };

    localStorage.setItem('onboardingSelectedPath', JSON.stringify(completionPath));
    setCompletedActions([...completedActions, 'onboarding_complete', action]);
    onComplete(completionPath);
  };

  const handleGoToDashboard = () => {
    const completionPath = {
      userGoal,
      projectType,
      teamChoice,
      experienceLevel,
      timeToComplete: Date.now() - timeStarted,
      action: 'dashboard',
      result: successResult
    };

    localStorage.setItem('onboardingSelectedPath', JSON.stringify(completionPath));
    setCompletedActions([...completedActions, 'onboarding_complete']);
    onComplete(completionPath);
  };


  // Handle back navigation
  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Handle continue for welcome screen
  const handleContinue = () => {
    setCurrentStep(2);
  };

  // Show wizard if needed
  if (showWizard) {
    return (
      <OnboardingWizard
        selectedPath={{
          userGoal,
          projectType,
          teamChoice,
          template: onboardingState.templates[teamChoice === 'team' ? 'quickAlliance' : 'quickVenture']
        }}
        onComplete={handleWizardComplete}
        onCancel={() => setShowWizard(false)}
      />
    );
  }

  // Show success screen if needed
  if (showSuccess) {
    return (
      <OnboardingSuccess
        result={successResult}
        onContinue={handleSuccessAction}
        onGoToDashboard={handleGoToDashboard}
      />
    );
  }

  const step = getCurrentStep();

  if (!step || !hasInitialized) {
    return (
      <div className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white text-lg">Initializing your onboarding...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-50 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
      {/* Exit Button */}
      <motion.div
        className="fixed top-4 right-4 z-60"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5 }}
      >
        <Button
          onClick={handleSkipSetup}
          variant="ghost"
          className="text-white text-opacity-70 hover:text-opacity-100 bg-transparent hover:bg-white hover:bg-opacity-10"
          size="sm"
        >
          ✕
        </Button>
      </motion.div>

      {/* Progress Indicator */}
      <motion.div
        className="fixed top-8 left-1/2 transform -translate-x-1/2 z-60"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <OnboardingProgress
          currentStep={currentStep}
          totalSteps={4}
          userGoal={userGoal}
          className="w-80"
        />
      </motion.div>

      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-4xl mx-auto px-8 mt-24"
        >
          {/* Use OnboardingStep component */}
          <OnboardingStep
            title={step.title}
            subtitle={step.subtitle}
            type={step.type}
            options={step.options}
            onOptionSelect={handleOptionSelect}
            onContinue={handleContinue}
            onBack={handleBack}
            showBack={currentStep > 2}
            showSkip={step.id === 'goal-selection'}
            onSkip={handleSkipSetup}
            isLoading={isLoading}
          />
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default OnboardingFlow;
