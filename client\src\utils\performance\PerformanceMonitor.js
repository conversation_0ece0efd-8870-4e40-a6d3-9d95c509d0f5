// Performance Monitor Utility
// Integration & Services Agent: Real-time performance monitoring and optimization

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = new Map();
    this.isEnabled = process.env.NODE_ENV === 'development' || window.location.search.includes('perf=true');
    this.reportingEndpoint = '/.netlify/functions/analytics-service/performance';
    
    if (this.isEnabled) {
      this.initializeObservers();
      this.startPerformanceTracking();
    }
  }

  // Initialize performance observers
  initializeObservers() {
    try {
      // Performance Observer for navigation timing
      if ('PerformanceObserver' in window) {
        // Navigation timing
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('navigation', {
              type: entry.type,
              name: entry.name,
              duration: entry.duration,
              startTime: entry.startTime,
              loadEventEnd: entry.loadEventEnd,
              domContentLoadedEventEnd: entry.domContentLoadedEventEnd
            });
          }
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.set('navigation', navObserver);

        // Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('lcp', {
              value: entry.startTime,
              element: entry.element?.tagName,
              url: entry.url,
              size: entry.size
            });
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('lcp', lcpObserver);

        // First Input Delay
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('fid', {
              value: entry.processingStart - entry.startTime,
              name: entry.name,
              startTime: entry.startTime
            });
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('fid', fidObserver);

        // Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0;
          for (const entry of list.getEntries()) {
            if (\!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          this.recordMetric('cls', { value: clsValue });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.set('cls', clsObserver);
      }
    } catch (error) {
      console.warn('Performance observers not supported:', error);
    }
  }

  // Record a performance metric
  recordMetric(type, data) {
    const timestamp = Date.now();
    const metric = {
      type,
      data,
      timestamp,
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    if (\!this.metrics.has(type)) {
      this.metrics.set(type, []);
    }
    
    this.metrics.get(type).push(metric);
    
    // Log in development
    if (this.isEnabled && process.env.NODE_ENV === 'development') {
      console.log(`📊 Performance [${type}]:`, data);
    }
  }

  // Track component render time
  trackComponentRender(componentName, renderTime) {
    this.recordMetric('component-render', {
      component: componentName,
      renderTime,
      timestamp: Date.now()
    });
  }

  // Track API call performance
  trackApiCall(endpoint, duration, status, size = 0) {
    this.recordMetric('api-call', {
      endpoint,
      duration,
      status,
      size,
      timestamp: Date.now()
    });
  }

  // Manually track custom performance metrics
  trackCustomMetric(name, value, metadata = {}) {
    this.recordMetric('custom', {
      name,
      value,
      metadata,
      timestamp: Date.now()
    });
  }

  // Get current performance summary
  getPerformanceSummary() {
    const summary = {};
    
    for (const [type, metrics] of this.metrics.entries()) {
      if (metrics.length > 0) {
        const latest = metrics[metrics.length - 1];
        summary[type] = latest.data;
      }
    }
    
    return summary;
  }

  // Cleanup observers
  destroy() {
    for (const observer of this.observers.values()) {
      observer.disconnect();
    }
    this.observers.clear();
    this.metrics.clear();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Export utilities
export const trackCustomMetric = (name, value, metadata) => 
  performanceMonitor.trackCustomMetric(name, value, metadata);

export const trackComponentRender = (componentName, renderTime) => 
  performanceMonitor.trackComponentRender(componentName, renderTime);

export const trackApiCall = (endpoint, duration, status, size) => 
  performanceMonitor.trackApiCall(endpoint, duration, status, size);

export const getPerformanceSummary = () => 
  performanceMonitor.getPerformanceSummary();

export default performanceMonitor;
