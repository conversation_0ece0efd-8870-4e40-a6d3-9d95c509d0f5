import axios from "axios";
import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import LoadingAnimation from "../../components/layout/LoadingAnimation";

const UserIndex = () => {
  const [users, setUsers] = useState(null);
  useEffect(() => {
    if (!users) {
      axios
        .get("/user/index")
        .then(({ data }) => {
          setUsers(data);
        })
        .catch((error) => {
          console.error("Error fetching user data:", error);
        });
    }
  }, []);

  // Function to copy User ID to clipboard
  const copyToClipboard = (userId) => {
    navigator.clipboard
      .writeText(userId)
      .then(() => {
        alert(`User ID ${userId} copied to clipboard!`); // Optional: Show a confirmation message
      })
      .catch((err) => {
        console.error("Could not copy text: ", err);
      });
  };

  return (
    <>
      <div className="container mt-5">
        <div className="d-flex justify-content-between align-items-center mb-4">
          <h1>Users</h1>
          <Link to="/register" className="btn btn-primary btn-lg fw-bold">
            Add User
          </Link>
        </div>

        {/* Ensure users is not null or undefined before accessing users[0].name */}
        {users && users.length > 0 ? (
          <ul className="list-group">
            {/* Header Row */}
            <li
              className="list-group-item"
              style={{
                backgroundColor: "#f8f9fa",
                color: "#495057",
                padding: "15px",
                borderRadius: "0.5rem 0.5rem 0 0",
                boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
              }}
            >
              <div className="row fw-bold">
                <div className="col-md-6">Email</div>
                <div className="col-md-6">User ID</div>
              </div>
            </li>

            {users.map((user) => (
              <li key={user._id} className="list-group-item">
                <div className="row">
                  {/* Email */}
                  <div className="col-md-6">
                    <Link
                      to={`/user/${user._id}`}
                      className="text-decoration-none text-primary"
                    >
                      {user.email}
                    </Link>
                  </div>

                  {/* User ID */}
                  <div className="col-md-6">
                    <span
                      className="fw-bold text-body"
                      role="button"
                      onClick={() => copyToClipboard(user._id)}
                    >
                      {user._id}
                    </span>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <LoadingAnimation />
        )}
      </div>
    </>
  );
};

export default UserIndex;

// NEXT, CREATE NEW DATA BASE AND ADD ERRORS FOR ADDING DATA FROM SCRATCH
