import React, { useState, useEffect } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { formatCurrency, formatPercentage } from '../../utils/royalty/royalty-calculator';

const RoyaltyDistributionList = ({ projectId }) => {
  const [loading, setLoading] = useState(true);
  const [distributions, setDistributions] = useState([]);
  const [revenue, setRevenue] = useState({});
  const [contributors, setContributors] = useState({});
  const [expandedDistributionId, setExpandedDistributionId] = useState(null);

  // Fetch distributions, revenue, and contributors
  useEffect(() => {
    const fetchData = async () => {
      if (!projectId) return;

      try {
        setLoading(true);

        // Fetch royalty distributions
        const { data: distributionsData, error: distributionsError } = await supabase
          .from('royalty_distributions')
          .select('*, payments:royalty_payments(*)')
          .eq('project_id', projectId)
          .order('created_at', { ascending: false });

        if (distributionsError) throw distributionsError;

        setDistributions(distributionsData || []);

        // Get unique revenue IDs
        const revenueIds = [...new Set(distributionsData
          .filter(d => d.revenue_id)
          .map(d => d.revenue_id))];

        // Get unique contributor IDs from the distribution details
        const contributorIds = new Set();
        distributionsData.forEach(distribution => {
          if (distribution.calculation_data && distribution.calculation_data.distribution_details) {
            distribution.calculation_data.distribution_details.forEach(detail => {
              if (detail.contributor_id) {
                contributorIds.add(detail.contributor_id);
              }
            });
          }

          // Also add contributor IDs from payments
          if (distribution.payments) {
            distribution.payments.forEach(payment => {
              if (payment.recipient_id) {
                contributorIds.add(payment.recipient_id);
              }
            });
          }
        });

        // Fetch revenue data
        if (revenueIds.length > 0) {
          const { data: revenueData, error: revenueError } = await supabase
            .from('revenue_entries')
            .select('*')
            .in('id', revenueIds);

          if (revenueError) throw revenueError;

          // Create a map of revenue ID to revenue data
          const revenueMap = {};
          revenueData.forEach(r => {
            revenueMap[r.id] = r;
          });

          setRevenue(revenueMap);
        }

        // Fetch contributor data
        if (contributorIds.size > 0) {
          const { data: contributorData, error: contributorError } = await supabase
            .from('project_contributors')
            .select('*')
            .in('id', Array.from(contributorIds));

          if (contributorError) throw contributorError;

          // Fetch user data for all contributors
          const userIds = contributorData.map(c => c.user_id).filter(Boolean);
          let userData = {};

          if (userIds.length > 0) {
            const { data: usersData, error: usersError } = await supabase
              .from('users')
              .select('id, display_name, email')
              .in('id', userIds);

            if (usersError) throw usersError;

            // Create a map of user data by ID
            userData = usersData.reduce((acc, user) => {
              acc[user.id] = user;
              return acc;
            }, {});
          }

          // Create a map of contributor ID to contributor data with user info
          const contributorMap = {};
          contributorData.forEach(c => {
            contributorMap[c.id] = {
              ...c,
              user: c.user_id ? userData[c.user_id] : null
            };
          });

          setContributors(contributorMap);
        }
      } catch (error) {
        console.error('Error fetching distributions:', error);
        toast.error('Failed to load distribution data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId]);

  // No need to group distributions as each royalty_distributions record already represents a group

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get contributor name
  const getContributorName = (contributorId) => {
    const contributor = contributors[contributorId];
    if (!contributor || !contributor.user) return 'Unknown';
    return contributor.user.display_name || contributor.user.email || 'Unknown';
  };

  // Toggle expanded distribution
  const toggleExpanded = (revenueId) => {
    setExpandedDistributionId(expandedDistributionId === revenueId ? null : revenueId);
  };

  // Get calculation method display name
  const getCalculationMethodName = (method) => {
    switch (method) {
      case 'equal_split':
        return 'Equal Split';
      case 'task_based':
        return 'Task-Based';
      case 'time_based':
        return 'Time-Based';
      case 'role_based':
        return 'Role-Based';
      case 'cog_model':
        return 'CoG Model (Tasks-Time-Difficulty)';
      default:
        return method;
    }
  };

  if (loading) {
    return <div className="loading-spinner">Loading distributions...</div>;
  }

  if (distributions.length === 0) {
    return (
      <div className="no-distributions">
        <p>No royalty distributions have been calculated yet.</p>
      </div>
    );
  }

  return (
    <div className="royalty-distribution-list">
      {distributions.map((distribution) => {
        const revenueData = revenue[distribution.revenue_id] || {};
        const isExpanded = expandedDistributionId === distribution.id;

        return (
          <div
            key={distribution.id}
            className={`distribution-group ${isExpanded ? 'expanded' : ''}`}
          >
            <div
              className="distribution-group-header"
              onClick={() => toggleExpanded(distribution.id)}
            >
              <div className="distribution-group-info">
                <h4 className="distribution-group-title">
                  {revenueData.source_id ? 'Revenue' : 'Manual Distribution'} - {formatDate(distribution.distribution_date)}
                </h4>
                <div className="distribution-group-meta">
                  <span className="distribution-total">
                    {formatCurrency(distribution.total_amount, distribution.currency || 'USD')}
                  </span>
                  <span className="distribution-method">
                    {getCalculationMethodName(distribution.calculation_method)}
                  </span>
                  <span className={`distribution-status status-${distribution.status}`}>
                    {distribution.status}
                  </span>
                </div>
              </div>
              <div className="expand-toggle">
                <i className={`bi ${isExpanded ? 'bi-chevron-up' : 'bi-chevron-down'}`}></i>
              </div>
            </div>

            {isExpanded && (
              <div className="distribution-details">
                <table className="distribution-table">
                  <thead>
                    <tr>
                      <th>Contributor</th>
                      <th>Amount</th>
                      <th>Percentage</th>
                      <th>Payment Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {distribution.calculation_data?.distribution_details?.map((detail, index) => {
                      // Find the corresponding payment
                      const payment = distribution.payments?.find(p => p.recipient_id === detail.user_id);

                      return (
                        <tr key={`${distribution.id}-${detail.contributor_id || index}`}>
                          <td className="contributor-name">
                            {getContributorName(detail.contributor_id)}
                          </td>
                          <td className="amount">
                            {formatCurrency(detail.amount, distribution.currency)}
                          </td>
                          <td className="percentage">
                            {formatPercentage(detail.percentage)}
                          </td>
                          <td className="status">
                            <span className={`status-badge ${payment?.status || 'pending'}`}>
                              {payment?.status || 'pending'}
                            </span>
                          </td>
                          <td className="actions">
                            {payment && payment.status === 'pending' && (
                              <button
                                className="mark-paid-button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Mark payment as paid
                                  supabase
                                    .from('royalty_payments')
                                    .update({
                                      status: 'paid',
                                      payment_date: new Date().toISOString(),
                                      payment_method: 'manual',
                                      notes: 'Marked as paid manually'
                                    })
                                    .eq('id', payment.id)
                                    .then(({ error }) => {
                                      if (error) {
                                        console.error('Error updating payment:', error);
                                        toast.error('Failed to update payment status');
                                      } else {
                                        toast.success('Payment marked as paid');
                                        // Refresh data
                                        window.location.reload();
                                      }
                                    });
                                }}
                              >
                                Mark as Paid
                              </button>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>

                <div className="distribution-notes">
                  <h5>Distribution Notes</h5>
                  <p>
                    This distribution was calculated using the {getCalculationMethodName(distribution.calculation_method)} method
                    on {formatDate(distribution.created_at)}.
                  </p>
                  {distribution.notes && (
                    <div className="distribution-notes-content">
                      <h6>Notes:</h6>
                      <p>{distribution.notes}</p>
                    </div>
                  )}
                  {revenueData.description && (
                    <div className="revenue-description">
                      <h6>Revenue Description:</h6>
                      <p>{revenueData.description}</p>
                    </div>
                  )}
                </div>

                {/* Distribution Actions */}
                <div className="distribution-actions">
                  {distribution.status === 'calculated' && (
                    <button
                      className="approve-distribution-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Approve distribution
                        supabase
                          .from('royalty_distributions')
                          .update({
                            status: 'approved',
                            approved_by: supabase.auth.user()?.id,
                            approved_at: new Date().toISOString()
                          })
                          .eq('id', distribution.id)
                          .then(({ error }) => {
                            if (error) {
                              console.error('Error approving distribution:', error);
                              toast.error('Failed to approve distribution');
                            } else {
                              toast.success('Distribution approved');
                              // Refresh data
                              window.location.reload();
                            }
                          });
                      }}
                    >
                      Approve Distribution
                    </button>
                  )}

                  {distribution.status === 'approved' && (
                    <button
                      className="mark-distributed-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Mark all payments as paid
                        Promise.all(
                          distribution.payments.map(payment =>
                            supabase
                              .from('royalty_payments')
                              .update({
                                status: 'paid',
                                payment_date: new Date().toISOString(),
                                payment_method: 'batch',
                                notes: 'Marked as paid in batch'
                              })
                              .eq('id', payment.id)
                          )
                        ).then(results => {
                          const errors = results.filter(r => r.error).map(r => r.error);
                          if (errors.length > 0) {
                            console.error('Errors updating payments:', errors);
                            toast.error('Some payments could not be updated');
                          } else {
                            // Update distribution status
                            supabase
                              .from('royalty_distributions')
                              .update({ status: 'distributed' })
                              .eq('id', distribution.id)
                              .then(({ error }) => {
                                if (error) {
                                  console.error('Error updating distribution:', error);
                                  toast.error('Failed to update distribution status');
                                } else {
                                  // Also update the revenue entry status
                                  supabase
                                    .from('revenue_entries')
                                    .update({
                                      status: 'distributed',
                                      distribution_status: 'completed',
                                      distributed_at: new Date().toISOString()
                                    })
                                    .eq('id', distribution.revenue_id)
                                    .then(({ error: revenueError }) => {
                                      if (revenueError) {
                                        console.error('Error updating revenue status:', revenueError);
                                        toast.error('Distribution marked as complete but revenue status update failed');
                                      } else {
                                        toast.success('All payments marked as paid and distribution completed');
                                      }
                                      // Refresh data
                                      window.location.reload();
                                    });
                                }
                              });
                          }
                        });
                      }}
                    >
                      Mark All Payments as Paid
                    </button>
                  )}

                  {distribution.status === 'calculated' && (
                    <button
                      className="approve-distribution-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Approve the distribution
                        supabase
                          .from('royalty_distributions')
                          .update({ status: 'approved' })
                          .eq('id', distribution.id)
                          .then(({ error }) => {
                            if (error) {
                              console.error('Error approving distribution:', error);
                              toast.error('Failed to approve distribution');
                            } else {
                              toast.success('Distribution approved');
                              // Refresh data
                              window.location.reload();
                            }
                          });
                      }}
                    >
                      Approve Distribution
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default RoyaltyDistributionList;
