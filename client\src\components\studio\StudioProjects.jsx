import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Badge } from '@heroui/react';
import { Plus, Calendar, Users } from 'lucide-react';

const StudioProjects = ({ studioId, projects = [], onProjectsUpdate }) => {
  const handleCreateProject = () => {
    // TODO: Navigate to project creation
    console.log('Create new project for studio:', studioId);
  };

  const handleProjectClick = (projectId) => {
    // TODO: Navigate to project details
    console.log('Navigate to project:', projectId);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'planning': return 'warning';
      case 'completed': return 'primary';
      case 'on_hold': return 'default';
      default: return 'default';
    }
  };

  return (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Studio Projects</h3>
        <Button
          size="sm"
          color="primary"
          startContent={<Plus size={16} />}
          onClick={handleCreateProject}
        >
          New Project
        </Button>
      </CardHeader>
      <CardBody>
        {projects.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">🚀</div>
            <p className="text-gray-600 mb-4">No projects yet</p>
            <Button
              color="primary"
              variant="flat"
              onClick={handleCreateProject}
            >
              Create First Project
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {projects.map((project) => (
              <div
                key={project.id}
                className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleProjectClick(project.id)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{project.name}</h4>
                  <Badge color={getStatusColor(project.status)} variant="flat" size="sm">
                    {project.status}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {project.description}
                </p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <Calendar size={12} />
                      <span>{new Date(project.created_at).toLocaleDateString()}</span>
                    </div>
                    {project.project_type && (
                      <Badge variant="flat" size="sm">
                        {project.project_type}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default StudioProjects;
