import React, { useState } from 'react';

/**
 * VersionHistoryModal Component
 * 
 * Displays the version history of an agreement
 */
const VersionHistoryModal = ({ agreement, onClose, formatDate }) => {
  const [selectedVersion, setSelectedVersion] = useState(null);
  const [compareMode, setCompareMode] = useState(false);
  
  // Get all versions including the current one
  const getAllVersions = () => {
    const previousVersions = agreement.previous_versions || [];
    const currentVersion = {
      version: agreement.version || 1,
      agreement_text: agreement.agreement_text,
      updated_at: agreement.updated_at,
      status: agreement.status
    };
    
    return [...previousVersions, currentVersion].sort((a, b) => b.version - a.version);
  };
  
  const versions = getAllVersions();
  
  // Handle version selection
  const handleVersionSelect = (version) => {
    setSelectedVersion(version);
    setCompareMode(false);
  };
  
  // Toggle compare mode
  const toggleCompareMode = () => {
    if (compareMode) {
      setCompareMode(false);
    } else {
      // Only enable compare mode if a version is selected and it's not the latest
      if (selectedVersion && selectedVersion.version !== versions[0].version) {
        setCompareMode(true);
      }
    }
  };
  
  // Get diff between two versions
  const getDiff = (oldText, newText) => {
    if (!oldText || !newText) return [];
    
    const oldLines = oldText.split('\n');
    const newLines = newText.split('\n');
    const diff = [];
    
    // Simple line-by-line diff
    const maxLines = Math.max(oldLines.length, newLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const oldLine = i < oldLines.length ? oldLines[i] : '';
      const newLine = i < newLines.length ? newLines[i] : '';
      
      if (oldLine !== newLine) {
        diff.push({
          lineNumber: i + 1,
          oldLine,
          newLine,
          type: !oldLine ? 'added' : !newLine ? 'removed' : 'changed'
        });
      }
    }
    
    return diff;
  };
  
  // Get the next version for comparison
  const getNextVersion = (version) => {
    const index = versions.findIndex(v => v.version === version.version);
    return index > 0 ? versions[index - 1] : null;
  };
  
  return (
    <div className="modal-overlay">
      <div className="version-history-modal">
        <div className="modal-header">
          <h3>Agreement Version History</h3>
          <button className="close-button" onClick={onClose}>
            &times;
          </button>
        </div>
        
        <div className="modal-body">
          <div className="version-history-container">
            <div className="version-list">
              <h4>Versions</h4>
              <ul>
                {versions.map((version) => (
                  <li 
                    key={version.version}
                    className={`version-item ${selectedVersion?.version === version.version ? 'selected' : ''}`}
                    onClick={() => handleVersionSelect(version)}
                  >
                    <div className="version-number">Version {version.version}</div>
                    <div className="version-date">{formatDate(version.updated_at)}</div>
                    <div className="version-status">{version.status}</div>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="version-content">
              {selectedVersion ? (
                <>
                  <div className="version-content-header">
                    <h4>
                      Version {selectedVersion.version} 
                      {selectedVersion.version === versions[0].version ? ' (Current)' : ''}
                    </h4>
                    <div className="version-actions">
                      {selectedVersion.version !== versions[0].version && (
                        <button 
                          className={`compare-button ${compareMode ? 'active' : ''}`}
                          onClick={toggleCompareMode}
                          disabled={selectedVersion.version === versions[0].version}
                        >
                          {compareMode ? 'Hide Diff' : 'Show Changes'}
                        </button>
                      )}
                    </div>
                  </div>
                  
                  {compareMode ? (
                    <div className="version-diff">
                      <h5>Changes from Version {selectedVersion.version} to {getNextVersion(selectedVersion)?.version}</h5>
                      <div className="diff-content">
                        {getDiff(
                          selectedVersion.agreement_text,
                          getNextVersion(selectedVersion)?.agreement_text
                        ).map((diff, index) => (
                          <div key={index} className={`diff-line ${diff.type}`}>
                            <span className="line-number">{diff.lineNumber}</span>
                            <div className="line-content">
                              {diff.type === 'added' ? (
                                <div className="added-line">+ {diff.newLine}</div>
                              ) : diff.type === 'removed' ? (
                                <div className="removed-line">- {diff.oldLine}</div>
                              ) : (
                                <>
                                  <div className="removed-line">- {diff.oldLine}</div>
                                  <div className="added-line">+ {diff.newLine}</div>
                                </>
                              )}
                            </div>
                          </div>
                        ))}
                        
                        {getDiff(
                          selectedVersion.agreement_text,
                          getNextVersion(selectedVersion)?.agreement_text
                        ).length === 0 && (
                          <div className="no-changes">No changes detected</div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="version-text">
                      <pre>{selectedVersion.agreement_text}</pre>
                    </div>
                  )}
                </>
              ) : (
                <div className="no-version-selected">
                  <p>Select a version to view details</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VersionHistoryModal;
