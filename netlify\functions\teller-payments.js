// Teller Payment Processing API
// Integration & Services Agent: Core payment processing with Teller integration

const { createClient } = require('@supabase/supabase-js');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Teller configuration
const TELLER_CONFIG = {
  environment: process.env.TELLER_ENVIRONMENT || 'sandbox',
  applicationId: process.env.TELLER_APPLICATION_ID,
  baseUrl: process.env.TELLER_ENVIRONMENT === 'production' 
    ? 'https://api.teller.io' 
    : 'https://api.teller.io',
  certificatePath: process.env.TELLER_CERTIFICATE_PATH || './teller/certificate.pem',
  privateKeyPath: process.env.TELLER_PRIVATE_KEY_PATH || './teller/private_key.pem'
};

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Create authenticated HTTPS agent for Teller API
const createTellerAgent = () => {
  try {
    const cert = fs.readFileSync(path.resolve(TELLER_CONFIG.certificatePath));
    const key = fs.readFileSync(path.resolve(TELLER_CONFIG.privateKeyPath));
    
    return new https.Agent({
      cert,
      key,
      rejectUnauthorized: true
    });
  } catch (error) {
    console.error('Failed to create Teller agent:', error);
    throw new Error('Teller certificate configuration error');
  }
};

// Make authenticated request to Teller API
const tellerRequest = async (endpoint, options = {}) => {
  const agent = createTellerAgent();
  const url = `${TELLER_CONFIG.baseUrl}${endpoint}`;
  
  const requestOptions = {
    method: options.method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Teller-Application-Id': TELLER_CONFIG.applicationId,
      ...options.headers
    },
    agent
  };

  if (options.body) {
    requestOptions.body = JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url, requestOptions);
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`Teller API error: ${data.error || response.statusText}`);
    }
    
    return data;
  } catch (error) {
    console.error('Teller request failed:', error);
    throw error;
  }
};

// Authenticate user from JWT token
const authenticateUser = async (authHeader) => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.substring(7);
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    
    if (error || !user) {
      throw new Error('Invalid authentication token');
    }
    
    return user;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Authentication failed');
  }
};

// Initiate payment transfer
const initiateTransfer = async (user, transferData) => {
  try {
    const {
      from_account_id,
      to_account_id,
      amount,
      currency = 'USD',
      description,
      reference_id,
      metadata = {}
    } = transferData;

    // Validate required fields
    if (!from_account_id || !to_account_id || !amount) {
      throw new Error('Missing required transfer fields');
    }

    // Get source account details
    const { data: fromAccount, error: fromError } = await supabase
      .from('teller_accounts')
      .select('*')
      .eq('id', from_account_id)
      .eq('user_id', user.id)
      .single();

    if (fromError || !fromAccount) {
      throw new Error('Source account not found or unauthorized');
    }

    // Get destination account details
    const { data: toAccount, error: toError } = await supabase
      .from('teller_accounts')
      .select('*')
      .eq('id', to_account_id)
      .single();

    if (toError || !toAccount) {
      throw new Error('Destination account not found');
    }

    // Create transfer with Teller
    const transferRequest = {
      account_id: fromAccount.teller_account_id,
      counterparty: {
        account_id: toAccount.teller_account_id
      },
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toUpperCase(),
      description: description || 'Royaltea Platform Transfer',
      external_id: reference_id || `transfer_${Date.now()}`
    };

    const tellerTransfer = await tellerRequest('/transfers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${fromAccount.access_token}`
      },
      body: transferRequest
    });

    // Create transaction record in database
    const transactionData = {
      from_user_id: user.id,
      to_user_id: toAccount.user_id,
      from_account_id: from_account_id,
      to_account_id: to_account_id,
      teller_transaction_id: tellerTransfer.id,
      amount: amount,
      currency: currency.toUpperCase(),
      description: description,
      reference_id: reference_id,
      status: 'pending'
    };

    const { data: transaction, error: transactionError } = await supabase
      .from('payment_transactions')
      .insert(transactionData)
      .select()
      .single();

    if (transactionError) {
      throw new Error(`Failed to create transaction record: ${transactionError.message}`);
    }

    // Create financial transaction record
    const financialTransactionData = {
      transaction_type: 'transfer',
      transaction_category: 'peer_to_peer',
      gross_amount: amount,
      net_amount: amount,
      currency: currency.toUpperCase(),
      from_user_id: user.id,
      to_user_id: toAccount.user_id,
      description: description,
      reference_number: tellerTransfer.id,
      status: 'pending',
      created_by: user.id
    };

    const { error: financialError } = await supabase
      .from('financial_transactions')
      .insert(financialTransactionData);

    if (financialError) {
      console.error('Failed to create financial transaction:', financialError);
    }

    return {
      transaction_id: transaction.id,
      teller_transfer_id: tellerTransfer.id,
      status: tellerTransfer.status,
      amount: amount,
      currency: currency,
      estimated_completion: tellerTransfer.estimated_completion_date
    };

  } catch (error) {
    console.error('Initiate transfer error:', error);
    throw error;
  }
};

// Get transfer status
const getTransferStatus = async (user, transferId) => {
  try {
    // Get transaction from database
    const { data: transaction, error } = await supabase
      .from('payment_transactions')
      .select(`
        *,
        from_account:teller_accounts!payment_transactions_from_account_id_fkey(*),
        to_account:teller_accounts!payment_transactions_to_account_id_fkey(*)
      `)
      .eq('id', transferId)
      .eq('user_id', user.id)
      .single();

    if (error || !transaction) {
      throw new Error('Transfer not found');
    }

    // Get latest status from Teller
    const tellerTransfer = await tellerRequest(`/transfers/${transaction.teller_transfer_id}`, {
      headers: {
        'Authorization': `Bearer ${transaction.from_account.access_token}`
      }
    });

    // Update status if changed
    if (tellerTransfer.status !== transaction.teller_status) {
      await supabase
        .from('payment_transactions')
        .update({
          teller_status: tellerTransfer.status,
          status: mapTellerStatusToInternal(tellerTransfer.status),
          updated_at: new Date().toISOString()
        })
        .eq('id', transferId);
    }

    return {
      transaction_id: transaction.id,
      teller_transfer_id: transaction.teller_transfer_id,
      status: mapTellerStatusToInternal(tellerTransfer.status),
      teller_status: tellerTransfer.status,
      amount: transaction.amount,
      currency: transaction.currency,
      description: transaction.description,
      created_at: transaction.created_at,
      estimated_completion: tellerTransfer.estimated_completion_date,
      failure_reason: tellerTransfer.failure_reason
    };

  } catch (error) {
    console.error('Get transfer status error:', error);
    throw error;
  }
};

// List user's transfers
const listTransfers = async (user, queryParams) => {
  try {
    let query = supabase
      .from('payment_transactions')
      .select(`
        *,
        from_account:teller_accounts!payment_transactions_from_account_id_fkey(account_name, institution_name),
        to_account:teller_accounts!payment_transactions_to_account_id_fkey(account_name, institution_name)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    // Apply filters
    if (queryParams.get('status')) {
      query = query.eq('status', queryParams.get('status'));
    }

    if (queryParams.get('from_date')) {
      query = query.gte('created_at', queryParams.get('from_date'));
    }

    if (queryParams.get('to_date')) {
      query = query.lte('created_at', queryParams.get('to_date'));
    }

    // Apply pagination
    const limit = Math.min(parseInt(queryParams.get('limit')) || 50, 100);
    const offset = parseInt(queryParams.get('offset')) || 0;
    
    query = query.range(offset, offset + limit - 1);

    const { data: transfers, error } = await query;

    if (error) {
      throw new Error(`Failed to fetch transfers: ${error.message}`);
    }

    return transfers || [];

  } catch (error) {
    console.error('List transfers error:', error);
    throw error;
  }
};

// Map Teller status to internal status
const mapTellerStatusToInternal = (tellerStatus) => {
  const statusMap = {
    'pending': 'pending',
    'processing': 'processing',
    'posted': 'completed',
    'failed': 'failed',
    'cancelled': 'cancelled'
  };
  
  return statusMap[tellerStatus] || 'unknown';
};

// Main handler function
exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: corsHeaders,
      body: ''
    };
  }

  try {
    // Authenticate user
    const user = await authenticateUser(event.headers.authorization);
    
    // Parse request path and method
    const pathParts = event.path.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    const httpMethod = event.httpMethod;
    const body = event.body ? JSON.parse(event.body) : {};
    const queryParams = new URLSearchParams(event.queryStringParameters || {});

    let result;

    switch (action) {
      case 'initiate':
        if (httpMethod !== 'POST') {
          throw new Error('Method not allowed');
        }
        result = await initiateTransfer(user, body);
        break;

      case 'status':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        const transferId = queryParams.get('transfer_id');
        if (!transferId) {
          throw new Error('Transfer ID is required');
        }
        result = await getTransferStatus(user, transferId);
        break;

      case 'list':
        if (httpMethod !== 'GET') {
          throw new Error('Method not allowed');
        }
        result = await listTransfers(user, queryParams);
        break;

      default:
        throw new Error('Invalid action');
    }

    return {
      statusCode: 200,
      headers: corsHeaders,
      body: JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    };

  } catch (error) {
    console.error('Teller Payments API error:', error);
    
    return {
      statusCode: error.message.includes('Authentication') ? 401 : 
                  error.message.includes('not allowed') ? 405 : 400,
      headers: corsHeaders,
      body: JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      })
    };
  }
};
