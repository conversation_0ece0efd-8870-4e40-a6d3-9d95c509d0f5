import React, { useState } from 'react';
import { Card, CardBody, Button, Input, Textarea, Select, SelectItem } from '@heroui/react';
import { motion } from 'framer-motion';

const StudioCreationWizard = ({ onComplete, onCancel }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    studio_type: 'emerging',
    industry: '',
    is_public: true
  });

  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // TODO: Implement studio creation API call
      console.log('Creating studio:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onComplete({
        id: Date.now().toString(),
        ...formData,
        created_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error creating studio:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card>
          <CardBody className="p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Create New Studio</h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                label="Studio Name"
                placeholder="Enter your studio name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />

              <Textarea
                label="Description"
                placeholder="Describe your studio's mission and goals"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={4}
              />

              <Select
                label="Studio Type"
                value={formData.studio_type}
                onChange={(e) => setFormData({ ...formData, studio_type: e.target.value })}
              >
                <SelectItem key="emerging" value="emerging">🌱 Emerging</SelectItem>
                <SelectItem key="established" value="established">🏰 Established</SelectItem>
                <SelectItem key="solo" value="solo">⚔️ Solo</SelectItem>
              </Select>

              <Input
                label="Industry"
                placeholder="e.g., Software, Gaming, Film, Music"
                value={formData.industry}
                onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
              />

              <div className="flex justify-end space-x-4">
                <Button
                  variant="flat"
                  onClick={onCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  color="primary"
                  loading={loading}
                  disabled={!formData.name.trim()}
                >
                  Create Studio
                </Button>
              </div>
            </form>
          </CardBody>
        </Card>
      </motion.div>
    </div>
  );
};

export default StudioCreationWizard;
