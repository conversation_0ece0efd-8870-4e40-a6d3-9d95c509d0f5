import React, { useState, useEffect } from 'react';
import { Button, Input, Select, SelectItem, Checkbox, Chip } from '@heroui/react';
import { FunnelIcon, PlusIcon, XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

const KanbanToolbar = ({ 
  onAddTask, 
  onFilterChange, 
  onSearchChange, 
  contributors, 
  taskTypes, 
  difficultyLevels 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    assignee: '',
    taskType: '',
    difficulty: '',
    showCompleted: true
  });
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);

  // Handle search input change
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearchChange(value);
  };

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  // Toggle filter panel visibility
  const toggleFilterPanel = () => {
    setIsFilterExpanded(!isFilterExpanded);
  };

  // Clear all filters
  const clearFilters = () => {
    const resetFilters = {
      assignee: '',
      taskType: '',
      difficulty: '',
      showCompleted: true
    };
    setFilters(resetFilters);
    onFilterChange(resetFilters);
  };

  return (
    <div className="p-4 border-b border-white/10 bg-white/5">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <Input
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={handleSearchChange}
            startContent={<MagnifyingGlassIcon className="w-4 h-4 text-white/60" />}
            endContent={
              searchTerm && (
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={() => {
                    setSearchTerm('');
                    onSearchChange('');
                  }}
                >
                  <XMarkIcon className="w-4 h-4" />
                </Button>
              )
            }
            className="bg-white/10 border-white/20"
            classNames={{
              input: "text-white placeholder:text-white/60",
              inputWrapper: "bg-white/10 border-white/20 hover:border-white/30"
            }}
          />
        </div>

        <div className="flex gap-3">
          <Button
            variant={isFilterExpanded ? "solid" : "bordered"}
            color={isFilterExpanded ? "primary" : "default"}
            onPress={toggleFilterPanel}
            startContent={<FunnelIcon className="w-4 h-4" />}
            className={isFilterExpanded ? "" : "border-white/20 text-white hover:border-white/30"}
          >
            Filter
          </Button>

          <Button
            color="primary"
            onPress={onAddTask}
            startContent={<PlusIcon className="w-4 h-4" />}
          >
            Add Task
          </Button>
        </div>
      </div>

      {isFilterExpanded && (
        <div className="mt-4 p-4 bg-white/5 rounded-lg border border-white/10">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-white/80">Assignee</label>
              <Select
                placeholder="All Assignees"
                selectedKeys={filters.assignee ? [filters.assignee] : []}
                onSelectionChange={(keys) => handleFilterChange('assignee', Array.from(keys)[0] || '')}
                className="w-full"
                classNames={{
                  trigger: "bg-white/10 border-white/20 hover:border-white/30",
                  value: "text-white",
                  popoverContent: "bg-gray-900 border-white/20"
                }}
              >
                <SelectItem key="unassigned" value="unassigned">Unassigned</SelectItem>
                {contributors.map(contributor => (
                  <SelectItem key={contributor.id} value={contributor.id}>
                    {contributor.display_name || contributor.email}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white/80">Task Type</label>
              <Select
                placeholder="All Types"
                selectedKeys={filters.taskType ? [filters.taskType] : []}
                onSelectionChange={(keys) => handleFilterChange('taskType', Array.from(keys)[0] || '')}
                className="w-full"
                classNames={{
                  trigger: "bg-white/10 border-white/20 hover:border-white/30",
                  value: "text-white",
                  popoverContent: "bg-gray-900 border-white/20"
                }}
              >
                {taskTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.name}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white/80">Difficulty</label>
              <Select
                placeholder="All Difficulties"
                selectedKeys={filters.difficulty ? [filters.difficulty] : []}
                onSelectionChange={(keys) => handleFilterChange('difficulty', Array.from(keys)[0] || '')}
                className="w-full"
                classNames={{
                  trigger: "bg-white/10 border-white/20 hover:border-white/30",
                  value: "text-white",
                  popoverContent: "bg-gray-900 border-white/20"
                }}
              >
                {difficultyLevels.map(level => (
                  <SelectItem key={level.value} value={level.value}>
                    {level.name}
                  </SelectItem>
                ))}
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-white/80">Options</label>
              <div className="flex flex-col gap-2">
                <Checkbox
                  isSelected={filters.showCompleted}
                  onValueChange={(checked) => handleFilterChange('showCompleted', checked)}
                  classNames={{
                    label: "text-white/80 text-sm"
                  }}
                >
                  Show Completed
                </Checkbox>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              variant="flat"
              color="warning"
              onPress={clearFilters}
              size="sm"
            >
              Clear Filters
            </Button>
          </div>
        </div>
      )}

      {(filters.assignee || filters.taskType || filters.difficulty || !filters.showCompleted) && (
        <div className="mt-3 p-3 bg-white/5 rounded-lg border border-white/10">
          <div className="flex flex-wrap items-center gap-2">
            <span className="text-sm font-medium text-white/80 mr-2">Active Filters:</span>
            {filters.assignee && (
              <Chip
                size="sm"
                variant="flat"
                color="primary"
                onClose={() => handleFilterChange('assignee', '')}
              >
                Assignee: {filters.assignee === 'unassigned' ? 'Unassigned' :
                  contributors.find(c => c.id === filters.assignee)?.display_name || 'Unknown'}
              </Chip>
            )}
            {filters.taskType && (
              <Chip
                size="sm"
                variant="flat"
                color="secondary"
                onClose={() => handleFilterChange('taskType', '')}
              >
                Type: {taskTypes.find(t => t.value === filters.taskType)?.name || filters.taskType}
              </Chip>
            )}
            {filters.difficulty && (
              <Chip
                size="sm"
                variant="flat"
                color="warning"
                onClose={() => handleFilterChange('difficulty', '')}
              >
                Difficulty: {difficultyLevels.find(d => d.value === filters.difficulty)?.name || filters.difficulty}
              </Chip>
            )}
            {!filters.showCompleted && (
              <Chip
                size="sm"
                variant="flat"
                color="danger"
                onClose={() => handleFilterChange('showCompleted', true)}
              >
                Hiding Completed
              </Chip>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default KanbanToolbar;
