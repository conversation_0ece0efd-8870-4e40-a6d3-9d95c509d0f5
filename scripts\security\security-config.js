/**
 * Security Configuration for Enhanced Security Scanner
 */

const SECURITY_CONFIG = {
  // Patterns that indicate actual secrets (high confidence)
  criticalSecretPatterns: [
    /sk_live_[a-zA-Z0-9]{24,}/g,     // Stripe live secret keys
    /sk_test_[a-zA-Z0-9]{24,}/g,     // Stripe test secret keys
    /AKIA[0-9A-Z]{16}/g,             // AWS Access Key IDs
    /AIza[0-9A-Za-z\\-_]{35}/g,      // Google API Keys
    /ghp_[a-zA-Z0-9]{36}/g,          // GitHub Personal Access Tokens
    /xox[baprs]-[0-9a-zA-Z]{10,48}/g, // Slack Tokens
    /-----BEGIN [A-Z ]+-----[\s\S]*?-----END [A-Z ]+-----/g, // Private Keys
  ],

  // Known safe strings that might trigger false positives
  knownSafeStrings: [
    'localhost',
    'example.com',
    'test.com',
    'demo.com',
    'placeholder',
    'sample',
    'default',
    'undefined',
    'null',
    'function',
    'import',
    'export',
    'const',
    'let',
    'var',
  ],

  // Minimum length for strings to be considered potential secrets
  minSecretLength: 20,

  // Maximum length for strings to be considered potential secrets
  maxSecretLength: 200,

  // File size threshold - files larger than this are likely minified
  minifiedFileThreshold: 50 * 1024, // 50KB
};

/**
 * Enhanced secret detection function
 */
function isLikelySecret(str, context = '', filePath = '') {
  // Skip if string is too short or too long
  if (str.length < SECURITY_CONFIG.minSecretLength || 
      str.length > SECURITY_CONFIG.maxSecretLength) {
    return false;
  }

  // Check if it matches critical secret patterns
  for (const pattern of SECURITY_CONFIG.criticalSecretPatterns) {
    if (pattern.test(str)) {
      return true;
    }
  }

  // Check if it's a known safe string
  if (SECURITY_CONFIG.knownSafeStrings.some(safe => str.toLowerCase().includes(safe.toLowerCase()))) {
    return false;
  }

  return false;
}

/**
 * Scan file content for potential secrets
 */
function scanFileForSecrets(filePath, content) {
  const issues = [];
  const fileSize = Buffer.byteLength(content, 'utf8');
  
  // If file is very large, it's likely minified - use different rules
  const isMinified = fileSize > SECURITY_CONFIG.minifiedFileThreshold;
  
  if (isMinified) {
    // For minified files, only check for critical patterns
    for (const pattern of SECURITY_CONFIG.criticalSecretPatterns) {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          issues.push({
            type: 'critical',
            pattern: pattern.source,
            match: match,
            confidence: 'high',
            file: filePath
          });
        });
      }
    }
  } else {
    // For regular files, use full scanning
    const lines = content.split('\n');
    lines.forEach((line, lineNumber) => {
      // Check for critical patterns
      for (const pattern of SECURITY_CONFIG.criticalSecretPatterns) {
        const matches = line.match(pattern);
        if (matches) {
          matches.forEach(match => {
            issues.push({
              type: 'critical',
              pattern: pattern.source,
              match: match,
              confidence: 'high',
              file: filePath,
              line: lineNumber + 1
            });
          });
        }
      }
    });
  }
  
  return issues;
}

module.exports = {
  SECURITY_CONFIG,
  isLikelySecret,
  scanFileForSecrets
};
