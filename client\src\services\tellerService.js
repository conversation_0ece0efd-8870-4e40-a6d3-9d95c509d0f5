import { supabase } from '../utils/supabase/supabase.utils';

class TellerService {
  constructor() {
    this.tellerEnv = process.env.REACT_APP_TELLER_ENV || 'sandbox';
    this.tellerApplicationId = process.env.REACT_APP_TELLER_APPLICATION_ID;
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
  }

  /**
   * Initialize Teller Link for account connection
   */
  async initializeTellerLink(userId, products = ['auth', 'transactions', 'transfer']) {
    try {
      const response = await fetch(`${this.baseUrl}/api/teller/link-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          user_id: userId,
          products,
          country_codes: ['US'],
          language: 'en'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create link token');
      }

      const data = await response.json();
      return data.link_token;
    } catch (error) {
      console.error('Error initializing Teller Link:', error);
      throw error;
    }
  }

  /**
   * Exchange public token for access token and store account info
   */
  async exchangePublicToken(publicToken, userId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/teller/exchange-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          public_token: publicToken,
          user_id: userId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to exchange public token');
      }

      const data = await response.json();

      // Store account information in Supabase
      await this.storeAccountInfo(data.accounts, data.access_token, data.item_id, userId);

      return data;
    } catch (error) {
      console.error('Error exchanging public token:', error);
      throw error;
    }
  }

  /**
   * Store account information in Supabase
   */
  async storeAccountInfo(accounts, accessToken, itemId, userId) {
    try {
      const accountsToStore = accounts.map(account => ({
        user_id: userId,
        teller_account_id: account.account_id,
        teller_item_id: itemId,
        teller_access_token: accessToken,
        account_name: account.name,
        account_type: account.subtype,
        institution_name: account.institution_name || 'Unknown',
        institution_id: account.institution_id || 'unknown',
        supports_ach: true, // Teller supports ACH by default
        supports_same_day_ach: account.supports_same_day_ach || false,
        is_verified: true,
        available_balance: account.balances?.available || 0,
        current_balance: account.balances?.current || 0,
        account_mask: account.mask || '0000'
      }));

      const { data, error } = await supabase
        .from('teller_accounts')
        .insert(accountsToStore)
        .select();

      if (error) {
        throw new Error(`Failed to store account info: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('Error storing account info:', error);
      throw error;
    }
  }

  /**
   * Get user's connected accounts
   */
  async getUserAccounts(userId) {
    try {
      const { data, error } = await supabase
        .from('teller_accounts')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (error) {
        throw new Error(`Failed to fetch accounts: ${error.message}`);
      }

      return data;
    } catch (error) {
      console.error('Error fetching user accounts:', error);
      throw error;
    }
  }

  /**
   * Get account balances
   */
  async getAccountBalances(accountId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/teller/balances`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          account_id: accountId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to fetch account balances');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching account balances:', error);
      throw error;
    }
  }

  /**
   * Initiate ACH transfer
   */
  async initiateTransfer(transferData) {
    try {
      const response = await fetch(`${this.baseUrl}/api/teller/transfer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify(transferData)
      });

      if (!response.ok) {
        throw new Error('Failed to initiate transfer');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error initiating transfer:', error);
      throw error;
    }
  }

  /**
   * Get transaction history
   */
  async getTransactions(accountId, options = {}) {
    try {
      const queryParams = new URLSearchParams({
        account_id: accountId,
        ...options
      });

      const response = await fetch(`${this.baseUrl}/api/teller/transactions?${queryParams}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${await this.getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch transactions');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  }

  /**
   * Remove account connection
   */
  async removeAccount(accountId) {
    try {
      const { error } = await supabase
        .from('teller_accounts')
        .update({ is_active: false })
        .eq('id', accountId);

      if (error) {
        throw new Error(`Failed to remove account: ${error.message}`);
      }

      return { success: true };
    } catch (error) {
      console.error('Error removing account:', error);
      throw error;
    }
  }

  /**
   * Get authentication token from Supabase
   */
  async getAuthToken() {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      return session?.access_token;
    } catch (error) {
      console.error('Error getting auth token:', error);
      throw new Error('Authentication required');
    }
  }

  /**
   * Validate account for payments
   */
  async validateAccount(accountId) {
    try {
      const response = await fetch(`${this.baseUrl}/api/teller/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await this.getAuthToken()}`
        },
        body: JSON.stringify({
          account_id: accountId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to validate account');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error validating account:', error);
      throw error;
    }
  }
}

export default new TellerService();
